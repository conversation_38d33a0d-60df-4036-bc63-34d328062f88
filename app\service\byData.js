/* eslint-disable no-constant-condition */
const Service = require('egg').Service;
// const fs = require('fs');
// const path = require('path');
const moment = require('moment');
// const mkdirp = require('mkdirp');
const axios = require('axios');
const shortid = require('shortid');
const _ = require('lodash');
// 根api域名 获取config.default.js中的配置的baseUrl
const baseApiUrl = process.env.BY_BASE_API_URL || 'http://10.20.182.16:8310';
// const baseApiUrl2 = 'http://10.20.189.31:8310';
const baseApiUrl2 = process.env.BY_BASE_API_URL2 || 'http://10.20.189.31:8310';
const apis = {
  getVerifyInfo: {
    baseUrl: baseApiUrl,
    url: '/oauth/token',
    method: 'get',
    name: '获取token',
    query: {
      client_id: {
        type: 'string',
        name: 'client_id',
        required: true,
      },
      client_secret: {
        type: 'string',
        name: 'client_secret',
        required: true,
      },
    },
  },
  getVerifyInfo2: {
    baseUrl: baseApiUrl2,
    url: '/oauth/token',
    method: 'get',
    name: '获取token',
    query: {
      client_id: {
        type: 'string',
        name: 'client_id',
        required: true,
      },
      client_secret: {
        type: 'string',
        name: 'client_secret',
        required: true,
      },
    },
  },
  getZzjg: {
    baseUrl: baseApiUrl,
    url: '/service/api/dwd_yy_zzjg',
    method: 'get',
    name: '获取组织机构数据',
    query: {
      client_id: {
        type: 'string',
        name: 'client_id',
        required: true,
      },
      access_token: {
        type: 'string',
        name: 'access_token',
        required: true,
      },
      dt: {
        type: 'string',
        name: 'dt',
        reg: /^\d{4}-\d{2}-\d{2}$/,
        required: false,
      },
    },
  },
  getBmxx: {
    baseUrl: baseApiUrl,
    url: '/service/api/dwd_yy_bmxx',
    method: 'get',
    name: '获取部门信息',
    query: {
      client_id: {
        type: 'string',
        name: 'client_id',
        required: true,
      },
      access_token: {
        type: 'string',
        name: 'access_token',
        required: true,
      },
      dt: {
        type: 'string',
        name: 'dt',
        reg: /^\d{4}-\d{2}-\d{2}$/,
        required: false,
      },
    },
  },
  getPost: {
    baseUrl: baseApiUrl,
    url: '/service/api/dwd_yy_gwxx',
    method: 'get',
    name: '获取岗位信息',
    query: {
      client_id: {
        type: 'string',
        name: 'client_id',
        required: true,
      },
      access_token: {
        type: 'string',
        name: 'access_token',
        required: true,
      },
      dt: {
        type: 'string',
        name: 'dt',
        reg: /^\d{4}-\d{2}-\d{2}$/,
        required: false,
      },
      pageSize: {
        type: 'number',
        name: 'pageSize',
        required: true,
      },
      pageNumber: {
        type: 'number',
        name: 'pageNumber',
        required: true,
      },
    },
  },
  getPeople: {
    baseUrl: baseApiUrl,
    url: '/service/api/dwd_yy_ygjbxx',
    method: 'get',
    name: '获取人员信息',
    query: {
      client_id: {
        type: 'string',
        name: 'client_id',
        required: true,
      },
      access_token: {
        type: 'string',
        name: 'access_token',
        required: true,
      },
      dt: {
        type: 'string',
        name: 'dt',
        reg: /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/,
        required: false,
      },
      pageSize: {
        type: 'number',
        name: 'pageSize',
        required: true,
      },
      pageNumber: {
        type: 'number',
        name: 'pageNumber',
        required: true,
      },
    },
  },
  getKszsj: {
    baseUrl: baseApiUrl,
    url: '/service/api/dm_mdm_kszsj',
    method: 'get',
    name: '获取客商数据',
    query: {
      pageSize: 10,
      pageNumber: 1,
    },
  },
  getPsnjob: {
    baseUrl: baseApiUrl,
    url: '/service/api/ods_yy_hi_psnjob',
    method: 'get',
    name: '获取人员工作记录',
    query: {
      client_id: {
        type: 'string',
        name: 'client_id',
        required: true,
      },
      access_token: {
        type: 'string',
        name: 'access_token',
        required: true,
      },
      pageSize: {
        type: 'number',
        name: 'pageSize',
        required: true,
      },
      pageNumber: {
        type: 'number',
        name: 'pageNumber',
        required: true,
      },
    },
  },
  // 获取双预防隐患排查接口
  getYhpc: {
    baseUrl: baseApiUrl,
    url: '/service/api/ads_syf_zyjk_yhxx',
    method: 'get',
    name: '获取双预防隐患排查',
    query: {
      client_id: {
        type: 'string',
        name: 'client_id',
        required: true,
      },
      access_token: {
        type: 'string',
        name: 'access_token',
        required: true,
      },
      pageSize: {
        type: 'number',
        name: 'pageSize',
        required: true,
      },
      pageNumber: {
        type: 'number',
        name: 'pageNumber',
        required: true,
      },
    },
  },
  // 获取培训班（培训计划）
  getTrainPlan: {
    baseUrl: baseApiUrl2,
    url: '/service/api/dwd_yspt_zyjk_jklpxb',
    method: 'get',
    name: '获取培训班（培训计划）',
    query: {
      client_id: {
        type: 'string',
        name: 'client_id',
        required: true,
      },
      access_token: {
        type: 'string',
        name: 'access_token',
        required: true,
      },
      pageSize: {
        type: 'number',
        name: 'pageSize',
        required: true,
      },
      pageNumber: {
        type: 'number',
        name: 'pageNumber',
        required: true,
      },
    },
  },
  // 获取培训个人记录
  getTrainDetail: {
    baseUrl: baseApiUrl2,
    url: '/service/api/dwd_yspt_zyjk_yhxxjl',
    method: 'get',
    name: '获取培训个人记录）',
    query: {
      client_id: {
        type: 'string',
        name: 'client_id',
        required: true,
      },
      access_token: {
        type: 'string',
        name: 'access_token',
        required: true,
      },
      pageSize: {
        type: 'number',
        name: 'pageSize',
        required: true,
      },
      pageNumber: {
        type: 'number',
        name: 'pageNumber',
        required: true,
      },
    },
  },
  // 获取培训个人培训进度
  getTrainPersonProgress: {
    baseUrl: baseApiUrl2,
    url: '/service/api/dwd_yspt_zyjk_yhxxjd',
    method: 'get',
    name: '获取个人培训进度',
    query: {
      client_id: {
        type: 'string',
        name: 'client_id',
        required: true,
      },
      access_token: {
        type: 'string',
        name: 'access_token',
        required: true,
      },
      pageSize: {
        type: 'number',
        name: 'pageSize',
        required: true,
      },
      pageNumber: {
        type: 'number',
        name: 'pageNumber',
        required: true,
      },
    },
  },
  getTrainKscj: {
    baseUrl: baseApiUrl2,
    url: '/service/api/dwd_yspt_zyjk_kscj',
    method: 'get',
    name: '获取考试成绩',
    query: {
      client_id: {
        type: 'string',
        name: 'client_id',
        required: true,
      },
      access_token: {
        type: 'string',
        name: 'access_token',
        required: true,
      },
      pageSize: {
        type: 'number',
        name: 'pageSize',
        required: true,
      },
      pageNumber: {
        type: 'number',
        name: 'pageNumber',
        required: true,
      },
    },
  },
  getTrainKsxx: {
    baseUrl: baseApiUrl2,
    url: '/service/api/dwd_yspt_zyjk_pxbksxx',
    method: 'get',
    name: '获取考试成绩',
    query: {
      client_id: {
        type: 'string',
        name: 'client_id',
        required: true,
      },
      access_token: {
        type: 'string',
        name: 'access_token',
        required: true,
      },
      pageSize: {
        type: 'number',
        name: 'pageSize',
        required: true,
      },
      pageNumber: {
        type: 'number',
        name: 'pageNumber',
        required: true,
      },
    },
  },
};
/**
 * @description 北元数据对接
 */
class ByDataService extends Service {
  /**
   * @param apiName
   * @param queryObj
   * @description axios 封装方法
   */
  async byAxios(apiName, queryObj) {
    const { ctx } = this;
    try {
      console.log(apiName, apis[apiName], Object.keys(apis));
      const { url, method, name, query, baseUrl } = apis[apiName];
      // 处理params 和 paramsObj
      const validateFlg = this.validate(query, queryObj);
      ctx.auditLog(
        '北元集团请求方法',
        `路径为${url}, 参数为${JSON.stringify(queryObj)}`,
        'info'
      );
      if (!validateFlg) {
        throw new Error(apiName + '参数校验失败');
      }
      let result = {};
      try {
        result = await axios({
          url: `${baseUrl}${url}`,
          method,
          params: queryObj,
        });
        ctx.auditLog('北元集团请求方法', `${name} 成功。`, 'info');
        return result.data;
      } catch (error) {
        ctx.auditLog('北元集团请求方法', `$${name} 失败${error}`, 'error');
        if (
          JSON.stringify(error).indexOf('401') > -1 ||
          (result.code && result.code.indexOf('401') > -1)
        ) {
          throw new Error(result.message || '权限校验失败');
        }
        throw new Error('请求失败');
      }
    } catch (error) {
      ctx.auditLog('北元集团请求方法出错', `${error} 。`, 'error');
      return false;
    }
  }

  /**
   * 校验参数方法
   *
   * @param {Object} query 校验规则
   * @param {Object} params 要校验的参数
   * @return {boolean} 如果所有参数校验通过，返回true；否则返回false。
   */
  validate(query, params) {
    const { ctx } = this;
    try {
      const queryKeys = Object.keys(query);
      const isValid = queryKeys.every(key => {
        const queryItem = query[key];
        if (queryItem.required && !params[key]) {
          throw new Error(`缺少参数${key}`);
        }
        if (!queryItem.required && !params[key]) {
          return true;
        }
        const paramsItem = params[key];
        if (typeof paramsItem !== queryItem.type) {
          throw new Error(`参数${key}类型错误`);
        }
        if (queryItem.reg && !queryItem.reg.test(paramsItem)) {
          throw new Error(`参数${key}格式错误`);
        }
        return true;
      });
      return isValid;
    } catch (error) {
      ctx.auditLog('北元集团校验参数方法出错', `${error} 。`, 'error');
      return false;
    }
  }
  /** 获取组织信息
   *
   * @param params
   * @param params.dt 日期 yyyy-mm-dd 非必填
   */
  async getOrganizationList(params = {}) {
    const { ctx } = this;
    try {
      const client_id = 'c0a2ff65a6db44ac8f8a6bea2733327b';
      const client_secret = 'VXwpKgB!';
      // 每次都要获取token算了省事
      const tokenRest = await this.byAxios('getVerifyInfo', {
        client_id,
        client_secret,
      });
      const { access_token } = tokenRest;
      if (!access_token) {
        throw new Error('获取token失败');
      }
      const toParams = {
        client_id,
        access_token,
      };
      if (params.dt) {
        toParams.dt = moment(params.dt).format('YYYY-MM-DD');
      }
      const res = await this.byAxios('getZzjg', toParams);
      const organizationList = res.result;
      // 存到根目录下 zzjg124.json
      // const filePath = path.join(__dirname, '/data/zzjg38.json');
      // fs.writeFileSync(filePath, JSON.stringify(organizationList));
      return organizationList;
    } catch (error) {
      ctx.auditLog('北元集团获取组织信息出错', `${error} 。`, 'error');
    }
  }
  /** 获取部门信息
   * @param params
   * @param params.dt 日期 yyyy-mm-dd 非必填
   */
  async getDepartmentList(params = {}) {
    const { ctx } = this;
    try {
      const client_id = 'c0a2ff65a6db44ac8f8a6bea2733327b';
      const client_secret = 'VXwpKgB!';
      // 每次都要获取token算了省事
      const tokenRest = await this.byAxios('getVerifyInfo', {
        client_id,
        client_secret,
      });
      const { access_token } = tokenRest;
      if (!access_token) {
        throw new Error('获取token失败');
      }
      const toParams = {
        client_id,
        access_token,
      };
      if (params.dt) {
        toParams.dt = moment(params.dt).format('YYYY-MM-DD');
      }
      const res = await this.byAxios('getBmxx', toParams);
      const departmentList = res.result;
      // 存到根目录下 bumen124.json
      // const filePath = path.join(__dirname, '/data/bumen38.json');
      // fs.writeFileSync(filePath, JSON.stringify(departmentList));
      return departmentList;
    } catch (error) {
      ctx.auditLog('北元集团获取部门信息出错', `${error} 。`, 'error');
    }
  }

  /** 获取岗位信息
   *
   * @param params
   * @param params.dt 日期 yyyy-mm-dd 非必填
   */
  async getPostList(params = {}) {
    const { ctx } = this;
    try {
      const client_id = 'c0a2ff65a6db44ac8f8a6bea2733327b';
      const client_secret = 'VXwpKgB!';
      // 每次都要获取token算了省事
      const tokenRest = await this.byAxios('getVerifyInfo', {
        client_id,
        client_secret,
      });
      const { access_token } = tokenRest;
      if (!access_token) {
        throw new Error('获取token失败');
      }
      const toParams = {
        client_id,
        access_token,
      };
      if (params.dt) {
        toParams.dt = moment(params.dt).format('YYYY-MM-DD');
      }
      let pageNumber = 1;
      const pageSize = 1000;
      let nowLength = pageSize;
      const postList = [];
      while (nowLength >= pageSize) {
        toParams.pageNumber = pageNumber++;
        toParams.pageSize = pageSize;
        const res = await this.byAxios('getPost', toParams);
        const subPostList = res.result;
        postList.push(...subPostList);
        nowLength = subPostList.length;
        console.log(
          '🚀 ~ file: byData.js:292 ~ ByDataService ~ getPostList ~ nowLength:',
          pageNumber,
          pageSize,
          nowLength
        );
      }
      // 存到根目录下 gangwei124.json
      // const filePath = path.join(__dirname, '/data/gangwei38.json');
      // fs.writeFileSync(filePath, JSON.stringify(postList));
      return postList;
    } catch (error) {
      ctx.auditLog('北元集团获取岗位信息出错', `${error} 。`, 'error');
    }
  }
  /** 获取人员信息
   *
   * @param params
   * @param params.dt 日期 yyyy-mm-dd 非必填
   */
  async getPeopleList(params = {}) {
    const { ctx } = this;
    try {
      const client_id = 'c0a2ff65a6db44ac8f8a6bea2733327b';
      const client_secret = 'VXwpKgB!';
      // 每次都要获取token算了省事
      const tokenRest = await this.byAxios('getVerifyInfo', {
        client_id,
        client_secret,
      });
      const { access_token } = tokenRest;
      if (!access_token) {
        throw new Error('获取token失败');
      }
      const toParams = {
        client_id,
        access_token,
      };
      if (params.dt) {
        toParams.dt = moment(params.dt).format('YYYY-MM-DD HH:mm:ss');
      }
      let pageNumber = 1;
      const pageSize = +params.pageSize || 1000;
      let nowLength = pageSize;
      const PeopleList = [];
      let isError = false;
      while (nowLength >= pageSize && !isError) {
        try {
          toParams.pageNumber = pageNumber++;
          toParams.pageSize = pageSize;
          const res = await this.byAxios('getPeople', toParams);
          const subPeopleList = res.result;
          PeopleList.push(...subPeopleList);
          nowLength = subPeopleList.length;
        } catch (error) {
          ctx.auditLog('北元集团获取人员信息出错', `${error} 。`, 'error');
          isError = true;
        }
      }
      // 存到根目录下 renyuan124.json
      // const filePath = path.join(__dirname, '/data/renyuan38.json');
      // fs.writeFileSync(filePath, JSON.stringify(PeopleList));
      return PeopleList;
    } catch (error) {
      ctx.auditLog('北元集团获取人员信息出错', `${error} 。`, 'error');
    }
  }

  /**
   * 获取人员工作记录
   * @param {*} params
   *
   */
  async getPeopleWorkList(params = {}) {
    const { ctx } = this;
    try {
      const client_id = 'c0a2ff65a6db44ac8f8a6bea2733327b';
      const client_secret = 'VXwpKgB!';
      // 每次都要获取token算了省事
      const tokenRest = await this.byAxios('getVerifyInfo', {
        client_id,
        client_secret,
      });
      const { access_token } = tokenRest;
      if (!access_token) {
        throw new Error('获取token失败');
      }
      const toParams = {
        client_id,
        access_token,
      };
      let pageNumber = 1;
      const pageSize = +params.pageSize || 1000;
      let totalPage = -1;
      const PeopleList = [];
      let isError = false;
      while ((pageNumber <= totalPage || totalPage === -1) && !isError) {
        try {
          toParams.pageNumber = pageNumber++;
          toParams.pageSize = pageSize;
          const subPeopleList = await this.byAxios('getPsnjob', toParams);
          PeopleList.push(...subPeopleList.result);
          totalPage = subPeopleList.totalPage;
        } catch (error) {
          ctx.auditLog('北元集团获取人员工作记录出错', `${error} 。`, 'error');
          isError = true;
        }
      }
      // 存到根目录下 renyuan124.json
      // const filePath = path.join(__dirname, '/data/peopleWork38.json');
      // fs.writeFileSync(filePath, JSON.stringify(PeopleList));
      return PeopleList;
    } catch (error) {
      ctx.auditLog('北元集团获取人员工作记录出错', `${error} 。`, 'error');
    }
  }
  /**
   * 获取双预防隐患排查
   * @param {*} params
   *
   */
  async getDoublePreventionList(params = {}) {
    const { ctx } = this;
    try {
      const client_id = 'c0a2ff65a6db44ac8f8a6bea2733327b';
      const client_secret = 'VXwpKgB!';
      // 每次都要获取token算了省事
      const tokenRest = await this.byAxios('getVerifyInfo', {
        client_id,
        client_secret,
      });
      const { access_token } = tokenRest;
      if (!access_token) {
        throw new Error('获取token失败');
      }
      const toParams = {
        client_id,
        access_token,
      };
      if (params.gxsj) {
        toParams.gxsj = moment(params.gxsj).format('YYYY-MM-DD');
      }
      let pageNumber = 1;
      const pageSize = +params.pageSize || 1000;
      let totalPage = -1;
      const DoublePreventionLis = [];
      let isError = false;
      while ((pageNumber <= totalPage || totalPage === -1) && !isError) {
        try {
          toParams.pageNumber = pageNumber++;
          toParams.pageSize = pageSize;
          const subPeopleList = await this.byAxios('getYhpc', toParams);
          DoublePreventionLis.push(...subPeopleList.result);
          totalPage = subPeopleList.totalPage;
        } catch (error) {
          ctx.auditLog(
            '北元集团获取双预防排查记录出错',
            `${error} 。`,
            'error'
          );
          isError = true;
        }
      }
      return DoublePreventionLis;
    } catch (error) {
      ctx.auditLog('北元集团获取双预防排查出错', `${error} 。`, 'error');
    }
  }

  /**
   * 获取培训班
   * @param {*} params
   *
   */
  async getTrainPlanList(params = {}) {
    const { ctx } = this;
    try {
      const client_id = 'c0a2ff65a6db44ac8f8a6bea2733327b';
      const client_secret = 'VXwpKgB!';
      // 每次都要获取token算了省事
      const tokenRest = await this.byAxios('getVerifyInfo2', {
        client_id,
        client_secret,
      });
      const { access_token } = tokenRest;
      if (!access_token) {
        throw new Error('获取token失败');
      }
      const toParams = {
        client_id,
        access_token,
      };
      if (params.gxsj) {
        toParams.gxsj = moment(params.gxsj).format('YYYY-MM-DD');
      }
      let pageNumber = 1;
      const pageSize = +params.pageSize || 1000;
      let totalPage = -1;
      const trainPLanList = [];
      let isError = false;
      while ((pageNumber <= totalPage || totalPage === -1) && !isError) {
        try {
          toParams.pageNumber = pageNumber++;
          toParams.pageSize = pageSize;
          const subTrainPLanList = await this.byAxios('getTrainPlan', toParams);
          trainPLanList.push(...subTrainPLanList.result);
          totalPage = trainPLanList.totalPage;
        } catch (error) {
          ctx.auditLog('北元集团获取培训计划记录出错', `${error} 。`, 'error');
          isError = true;
        }
      }
      return trainPLanList;
    } catch (error) {
      ctx.auditLog('北元集团获取培训计划记录出错', `${error} 。`, 'error');
    }
  }

  /**
   * 获取培训详细数据
   * @param {*} params
   *
   */
  async getTrainPlanDetails(params = {}) {
    const { ctx } = this;
    try {
      const client_id = 'c0a2ff65a6db44ac8f8a6bea2733327b';
      const client_secret = 'VXwpKgB!';
      // 每次都要获取token算了省事
      const tokenRest = await this.byAxios('getVerifyInfo2', {
        client_id,
        client_secret,
      });
      const { access_token } = tokenRest;
      if (!access_token) {
        throw new Error('获取token失败');
      }
      const toParams = {
        client_id,
        access_token,
      };
      if (params.gxsj) {
        toParams.gxsj = moment(params.gxsj).format('YYYY-MM-DD');
      }
      let pageNumber = 1;
      const pageSize = +params.pageSize || 1000;
      let totalPage = -1;
      const trainPLanDetailList = [];
      let isError = false;
      while ((pageNumber <= totalPage || totalPage === -1) && !isError) {
        try {
          toParams.pageNumber = pageNumber++;
          toParams.pageSize = pageSize;
          const subTrainPLanDetail = await this.byAxios(
            'getTrainDetail',
            toParams
          );
          trainPLanDetailList.push(...subTrainPLanDetail.result);
          totalPage = subTrainPLanDetail.totalPage;
        } catch (error) {
          ctx.auditLog('北元集团获取培训记录出错', `${error} 。`, 'error');
          isError = true;
        }
      }
      // 将trainPLanDetailList 写入到当前目录下的 details.json
      // const filePath = path.join(__dirname, '/detail.json');
      // fs.writeFileSync(filePath, JSON.stringify(trainPLanDetailList));
      return trainPLanDetailList;
    } catch (error) {
      ctx.auditLog('北元集团获取培训记录出错', `${error} 。`, 'error');
    }
  }

  /**
   * 获取培训详细数据
   * @param {*} params
   *
   */
  async getTrainPersonProgress(params = {}) {
    const { ctx } = this;
    try {
      const client_id = 'c0a2ff65a6db44ac8f8a6bea2733327b';
      const client_secret = 'VXwpKgB!';
      // 每次都要获取token算了省事
      const tokenRest = await this.byAxios('getVerifyInfo2', {
        client_id,
        client_secret,
      });
      const { access_token } = tokenRest;
      if (!access_token) {
        throw new Error('获取token失败');
      }
      const toParams = {
        client_id,
        access_token,
      };
      if (params.gxsj) {
        toParams.gxsj = moment(params.gxsj).format('YYYY-MM-DD');
      }
      let pageNumber = 1;
      const pageSize = +params.pageSize || 1000;
      let totalPage = -1;
      const getTrainPersonProgressList = [];
      let isError = false;
      while ((pageNumber <= totalPage || totalPage === -1) && !isError) {
        try {
          toParams.pageNumber = pageNumber++;
          toParams.pageSize = pageSize;
          const subTrainPLanDetail = await this.byAxios(
            'getTrainPersonProgress',
            toParams
          );
          getTrainPersonProgressList.push(...subTrainPLanDetail.result);
          totalPage = subTrainPLanDetail.totalPage;
        } catch (error) {
          ctx.auditLog('北元集团获取培训记录出错', `${error} 。`, 'error');
          isError = true;
        }
      }
      // 将trainPLanDetailList 写入到当前目录下的 details.json
      // const filePath = path.join(__dirname, '/getTrainPersonProgress.json');
      // fs.writeFileSync(filePath, JSON.stringify(getTrainPersonProgressList));
      return getTrainPersonProgressList;
    } catch (error) {
      ctx.auditLog('北元集团获取培训记录出错', `${error} 。`, 'error');
    }
  }

  /**
   * 获取培训考试记录
   * @param {*} params
   *
   */
  async getTrainKscj(params = {}) {
    const { ctx } = this;
    try {
      const client_id = 'c0a2ff65a6db44ac8f8a6bea2733327b';
      const client_secret = 'VXwpKgB!';
      // 每次都要获取token算了省事
      const tokenRest = await this.byAxios('getVerifyInfo2', {
        client_id,
        client_secret,
      });
      const { access_token } = tokenRest;
      if (!access_token) {
        throw new Error('获取token失败');
      }
      const toParams = {
        client_id,
        access_token,
      };
      let pageNumber = 1;
      const pageSize = +params.pageSize || 1000;
      let totalPage = -1;
      const trainKscjList = [];
      let isError = false;
      while ((pageNumber <= totalPage || totalPage === -1) && !isError) {
        try {
          toParams.pageNumber = pageNumber++;
          toParams.pageSize = pageSize;
          const subTrainPLanDetail = await this.byAxios(
            'getTrainKscj',
            toParams
          );
          trainKscjList.push(...subTrainPLanDetail.result);
          totalPage = subTrainPLanDetail.totalPage;
        } catch (error) {
          ctx.auditLog('北元集团获取考试成绩出错', `${error} 。`, 'error');
          isError = true;
        }
      }
      // 将trainPLanDetailList 写入到当前目录下的 trainKscjList.json
      // const filePath = path.join(__dirname, '/trainKscjList.json');
      // fs.writeFileSync(filePath, JSON.stringify(trainKscjList));
      return trainKscjList;
    } catch (error) {
      ctx.auditLog(
        '北元集团获取培训记录trainKscjList出错',
        `${error} 。`,
        'error'
      );
    }
  }
  /**
   * 获取培训考试记录
   * @param {*} params
   *
   */
  async getTrainKsxx(params = {}) {
    const { ctx } = this;
    try {
      const client_id = 'c0a2ff65a6db44ac8f8a6bea2733327b';
      const client_secret = 'VXwpKgB!';
      // 每次都要获取token算了省事
      const tokenRest = await this.byAxios('getVerifyInfo2', {
        client_id,
        client_secret,
      });
      const { access_token } = tokenRest;
      if (!access_token) {
        throw new Error('获取token失败');
      }
      const toParams = {
        client_id,
        access_token,
      };
      if (params.gxsj) {
        toParams.gxsj = moment(params.gxsj).format('YYYY-MM-DD');
      }
      let pageNumber = 1;
      const pageSize = +params.pageSize || 1000;
      let totalPage = -1;
      const getTrainPersonProgressList = [];
      let isError = false;
      while ((pageNumber <= totalPage || totalPage === -1) && !isError) {
        try {
          toParams.pageNumber = pageNumber++;
          toParams.pageSize = pageSize;
          console.log('getTrainKsxx pageNumber', pageNumber);
          const subTrainPLanDetail = await this.byAxios(
            'getTrainKsxx',
            toParams
          );
          getTrainPersonProgressList.push(...subTrainPLanDetail.result);
          totalPage = subTrainPLanDetail.totalPage;
          console.log('getTrainKsxx totalPage', subTrainPLanDetail.length);
        } catch (error) {
          ctx.auditLog(
            '北元集团获取培训记录getTrainKsxx出错',
            `${error} 。`,
            'error'
          );
          isError = true;
        }
      }
      // 将trainPLanDetailList 写入到当前目录下的 details.json
      // const filePath = path.join(__dirname, '/getTrainKsxx.json');
      // fs.writeFileSync(filePath, JSON.stringify(getTrainPersonProgressList));
      return getTrainPersonProgressList;
    } catch (error) {
      ctx.auditLog('北元集团获取培训记录出错', `${error} 。`, 'error');
    }
  }
  /**
   *
   * 处理组织机构数据
   * @param {*} organizationList
   * @param trainList
   */
  async clearTrainList(trainList) {
    const { ctx } = this;
    try {
      console.log(trainList.length);
      const haveArray = await ctx.model.Propagate.find().select('othersId');
      // 已存在的 走更新，不存在的走创建
      console.log('haveArray', haveArray.length);
      const inDbArray = [];
      const notInDbArray = [];

      // 给trainList 过滤 如果item.CLASSID 在haveArray 中存在，就扔到inDbArray中，否则扔到notInDbArray中
      for (const item of trainList) {
        const isHave = haveArray.some(haveItem => {
          return haveItem.othersId === item.CLASSID;
        });
        console.log(
          '判断 item.CLASSID',
          item.CLASSID,
          isHave ? '存在' : '不存在'
        );
        if (isHave) {
          // 去重
          console.log('inDbArray 往里加', item.CLASSID);
          if (
            !inDbArray.some(inDbItem => inDbItem.othersId === item.CLASSID)
          ) {
            inDbArray.push({
              _id: item.CLASSID,
              othersId: item.CLASSID,
              EnterpriseID:
                this.cleanPKOrg(item.PK_ORG) || '0001T21000000000E1MJ',
              createTime: new Date(item.CREATETIME),
              content: item.CLASSNAME,
              type: '劳动者培训',
              year: new Date(item.CREATETIME).getFullYear(),
              implementData: new Date(item.BEGINTIME),
              hours: item.HOURS,
              startTime: new Date(item.BEGINTIME),
              endTime: new Date(item.ENDTIME),
            });
          }
        } else {
          // 去重
          if (
            !notInDbArray.some(
              notInDbItem => notInDbItem._id === item.CLASSID
            )
          ) {
            console.log('notInDbArray 不存在往里加', item.CLASSID);
            notInDbArray.push({
              _id: item.CLASSID,
              othersId: item.CLASSID,
              EnterpriseID: this.cleanPKOrg(item.PK_ORG),
              createTime: new Date(item.CREATETIME),
              content: item.CLASSNAME,
              type: '劳动者培训',
              year: new Date(item.CREATETIME).getFullYear(),
              implementData: new Date(item.BEGINTIME),
              hours: item.HOURS,
            });
          } else {
            console.log('有了notInDbArray', item.CLASSID);
          }
        }
      }
      console.log('inDbArray', inDbArray.length);
      console.log('notInDbArray', notInDbArray.length);
      // inDbArray 走更新
      for (const item of inDbArray) {
        console.log('更新', item._id);
        await ctx.model.Propagate.updateOne(
          { _id: item._id },
          {
            $set: {
              ...item,
            },
          }
        );
      }
      // notInDbArray 走创建
      const res = await ctx.model.Propagate.insertMany(notInDbArray);
      return res;
    } catch (error) {
      ctx.auditLog('北元集团处理组织机构数据出错', `${error} 。`, 'error');
    }
  }
  cleanPKOrg(pkOrg) {
    return pkOrg ? pkOrg.replace(/\r?\n|\r/g, '') : '0001T21000000000E1MJ';
  }

  /**
   *
   * 处理培训详细数据
   * @param {*} organizationList
   * @param trainPersonProgress
   */
  async clearTrainPersonProgress(trainPersonProgress) {
    const { ctx } = this;
    try {
      // console.log(trainDetailList.length);
      // 从const filePath = path.join(__dirname, '/detail.json');读取
      // const filePath = path.join(__dirname, '/trainPersonProgress.json');
      // trainPersonProgress = JSON.parse(fs.readFileSync(filePath));
      // 已存在的 走更新，不存在的走创建
      const idMap = {};
      const bulkOps = trainPersonProgress.map(item => ({
        updateOne: {
          filter: {
            userId: item.PK_PSNDOC,
            unitTrain: item.CLASSID,
          },
          update: {
            $setOnInsert: { _id: shortid.generate() },
            $set: {
              userId: item.PK_PSNDOC,
              unitTrain: item.CLASSID,
              EnterpriseID: this.cleanPKOrg(item.PK_ORG),
              completeState: +item.PROGRESS === 1,
              trainingType: 3,
              duration: item.DURATION,
              validStudyTime: item.VALIDSTUDYTIME,
              studyTime: item.STUDYTIME,
              source: 'oapi',
            },
          },
          upsert: true,
        },
      }));
      console.log('bulkOps', bulkOps.length);
      await ctx.model.PersonalTraining.bulkWrite(bulkOps);
      for (const item of trainPersonProgress) {
        console.log('item.CLASSID', item.REALNAME, item.CLASSID);
        if (!idMap[item.CLASSID]) {
          idMap[item.CLASSID] = [];
        }
        idMap[item.CLASSID].push({
          name: item.REALNAME,
          unitCode: item.USERNAME,
        });
      }
      for (const key in idMap) {
        idMap[key] = [ ...new Set(idMap[key]) ];
        console.log('idMap[key]', idMap[key].length);
        await ctx.model.Propagate.updateOne(
          { _id: key },
          {
            $addToSet: {
              personnel: {
                $each: idMap[key],
              },
            },
          }
        );
      }

    } catch (error) {
      ctx.auditLog('北元集团处理培训数据出错', `${error} 。`, 'error');
    }
  }
  /**
   *
   * 处理培训往propagate表添加人
   * @param {*} organizationList
   * @param trainList
   * @param trainDetailList
   */
  async clearTrainDetailList(trainDetailList) {
    const { ctx } = this;
    try {
      // console.log(trainDetailList.length);
      // const filePath = path.join(__dirname, '/detail.json');
      // trainDetailList = JSON.parse(fs.readFileSync(filePath));
      const idMap = {};
      for (const item of trainDetailList) {
        idMap[item.CLASSID] = idMap[item.CLASSID] || [];
        idMap[item.CLASSID].push({
          name: item.REALNAME,
          unitCode: item.USERNAME,
        });
      }
      const bulkOps = [];
      for (const key in idMap) {
        idMap[key] = [ ...new Set(idMap[key]) ];
        bulkOps.push({
          updateOne: {
            filter: {
              _id: key,
            },
            update: {
              $addToSet: {
                personnel: {
                  $each: idMap[key],
                },
              },
            },
          },
        });
      }
      console.log('bulkOps', bulkOps.length);
      ctx.model.Propagate.bulkWrite(bulkOps);
    } catch (error) {
      ctx.auditLog('北元集团处理个人培训记录数据出错', `${error} 。`, 'error');
    }
  }
  /**
   *
   * 处理培训考试
   * @param testcjList
   * @param testList
   */
  async clearTrainKsList(testcjList, testList) {
    const { ctx } = this;
    try {
      // console.log(trainDetailList.length);
      // 从const filePath = path.join(__dirname, '/detail.json');读取
      // const filePath = path.join(__dirname, '/tesjcjList.json');
      // testcjList = JSON.parse(fs.readFileSync(filePath));
      // const filePath2 = path.join(__dirname, '/testList.json');
      // testList = JSON.parse(fs.readFileSync(filePath2));
      // 先给testList 处理一下 项目id和classid的对应关系
      const projectIdClassId = {};
      for (const item of testList) {
        projectIdClassId[item.PROJECTID] = item.CLASSID;
      }
      // 循环testcjList
      const cjMap = {};
      for (const item of testcjList) {
        // 人员id + classid  就能查到个人培训记录了
        // 如果cjMap 里有item.PK_PSNDOC + '_' + projectIdClassId[item.PROJECTID]的key，那就比较一下FINALSCORE分数，如果比cjMap里的分数高，就更新FINALSCORE和QUESTIONSCORE
        if (cjMap[item.PK_PSNDOC + '_' + projectIdClassId[item.PROJECTID]]) {
          if (cjMap[item.PK_PSNDOC + '_' + projectIdClassId[item.PROJECTID]].FINALSCORE < item.FINALSCORE) {
            cjMap[item.PK_PSNDOC + '_' + projectIdClassId[item.PROJECTID]] = {
              FINALSCORE: item.FINALSCORE,
              QUESTIONSCORE: item.QUESTIONSCORE,
              BEGINTIME: item.BEGINTIME,
              ENDTIME: item.ENDTIME,
            };
          }
        } else {
          cjMap[item.PK_PSNDOC + '_' + projectIdClassId[item.PROJECTID]] = {
            FINALSCORE: item.FINALSCORE,
            QUESTIONSCORE: item.QUESTIONSCORE,
            BEGINTIME: item.BEGINTIME,
            ENDTIME: item.ENDTIME,
          };
        }
      }
      // 循环cjMap 里的数据，更新到PersonalTraining
      const bulkOps = Object.keys(cjMap).map(
        key => {
          const [ userId, unitTrain ] = key.split('_');
          return {
            updateOne: {
              filter: {
                userId,
                unitTrain,
              },
              update: {
                $setOnInsert: { _id: shortid.generate() },
                $set: {
                  userId,
                  unitTrain,
                  actualScore: cjMap[key].FINALSCORE,
                  validScore: cjMap[key].QUESTIONSCORE,
                  examStartTime: new Date(cjMap[key].BEGINTIME),
                  examEndTime: new Date(cjMap[key].ENDTIME),
                },
              },
              upsert: true,
            },
          };
        });
      console.log('bulkOps', bulkOps.length);
      await ctx.model.PersonalTraining.bulkWrite(bulkOps);
    } catch (error) {
      ctx.auditLog('北元集团处理组织机构数据出错', `${error} 。`, 'error');
    }
  }
  dealUnitCode(unitCode) {
    const underscoreIndex = unitCode.indexOf('_');
    if (underscoreIndex !== -1) {
      const [ prefix ] = unitCode.split('_');
      return `${prefix.slice(0, 4)}L${prefix.slice(4)}`;
    }
    return unitCode;
  }
  /**
   *
   * 处理组织机构数据
   * @param {*} organizationList
   */
  async clearOrganizationList(organizationList) {
    const { ctx } = this;
    try {
      // 将organizationList 写入到gen
      // 将organizationList按照 CODE 进行排序
      organizationList.sort((a, b) => {
        return +a.CODE - +b.CODE;
      });
      const parentId = [];
      const childrenId = [];
      for (const byjt of organizationList) {
        if (byjt.CODE === '100') {
          parentId.push(byjt.PK_ORG);
        } else {
          childrenId.push(byjt.PK_ORG);
        }
      }
      let parentDingTree = '';
      const needToUpdateDingTrees = [];
      for (const byjt of organizationList) {
        const byjtAdminUser = await ctx.service.importToDb.createAdminuser({
          group: ctx.app.config.groupID.adminGroupID,
          userName: 'byjt' + byjt.CODE,
          name: byjt.CREATOR,
          password: 'Tc666888.',
        });
        const toCreatAdminorg = {
          _id: byjt.PK_ORG,
          cname: byjt.NAME,
          shortName: byjt.SHORTNAME,
          adminUserId: byjtAdminUser._id,
          companyCategory: byjt.CODE === '100' ? 'group' : byjt.CORP_CODE === '100' ? 'branch' : 'subsidiary', // 判断公司类型：100编码为集团，100公司代码为分公司，其他为子公司
          isactive: 1,
          adminArray: [ byjtAdminUser._id ],
          unitCode: byjt.CODE,
        };
        // 组织关系的处理
        if (byjt.CODE === '100') {
          toCreatAdminorg.childrenId = childrenId;
        } else {
          toCreatAdminorg.parentId = parentId;
        }
        const byjtCompany = await ctx.service.importToDb.createAdminorg(
          toCreatAdminorg
        );
        // 创建顶级部门
        const byjtDingTrees = await ctx.service.importToDb.createDingTrees({
          name: byjt.SHORTNAME,
          EnterpriseID: byjtCompany._id,
          unitCode: byjt.CODE,
          type: 'org',
          topLevelOfTheEnterprise: true,
        });
        if (byjt.CODE === '100') {
          parentDingTree = byjtDingTrees._id;
        } else {
          needToUpdateDingTrees.push(byjtDingTrees._id);
        }
      }
      for (const byjt of organizationList) {
        if (byjt.CODE === '100') {
          // 给他创建一个集团本部的部门
          await ctx.model.Dingtree.create({
            EnterpriseID: byjt.PK_ORG,
            parentId: parentDingTree,
            unitCode: byjt.CODE,
            name: '集团本部',
          });
          continue;
        }
        await ctx.model.GroupEnterprisesRecord.create({
          EnterpriseID: byjt.PK_ORG,
          targetEnterpriseID: parentId[0],
          messageStatus: 2,
        });
      }
      await ctx.model.Dingtree.updateMany(
        { _id: { $in: needToUpdateDingTrees } },
        { parentid: parentDingTree }
      );
    } catch (error) {
      ctx.auditLog('北元集团处理组织机构数据出错', `${error} 。`, 'error');
    }
  }

  /** 处理部门数据
   * @param {*} departmentList
   */
  async clearDepartmentList(departmentList) {
    const { ctx } = this;
    try {
      console.log('开始处理部门数据');
      for (const depart of departmentList) {
        console.log('depart.PK_ORG', depart.PK_ORG);
        try {
          let parentId = depart.FATHERORG_CODE;
          console.log('parentId', parentId);
          if (!parentId) {
            // 没有父级部门的情况
            let TopParentIdInfo = {};
            if (depart.PK_ORG === '0001T21000000000E1MJ') {
              // 集团本部
              try {
                TopParentIdInfo = await ctx.model.Dingtree.findOne({
                  EnterpriseID: depart.PK_ORG,
                  name: '集团本部',
                });
              } catch (error) {
                console.log('1111elseerror', depart.PK_OR);
                ctx.auditLog(
                  '北元集团处理部门数据局部出错',
                  `${error} 。`,
                  'error'
                );
              }
            } else {
              try {
                TopParentIdInfo = await ctx.model.Dingtree.findOne({
                  // 其他公司的情况
                  EnterpriseID: depart.PK_ORG,
                  topLevelOfTheEnterprise: true,
                });
              } catch (error) {
                console.log('2222elseerror', depart.PK_OR);
                ctx.auditLog(
                  '北元集团处理部门数据局部出错',
                  `${error} 。`,
                  'error'
                );
              }
            }

            parentId = TopParentIdInfo._id; // 顶级部门的id
          }
          console.log('parentId2222222', parentId);
          const toCreateDingTree = {
            _id: depart.CODE,
            unitCode: depart.PK_DEPT,
            name: depart.NAME,
            parentid: parentId,
            EnterpriseID: depart.PK_ORG,
          };
          console.log('北元集团处理部门数据toCreateDingTree', toCreateDingTree);
          await ctx.service.importToDb.createDingTrees(toCreateDingTree);
        } catch (error) {
          ctx.auditLog('北元集团处理部门数据局部出错', `${error} 。`, 'error');
        }
      }
    } catch (error) {
      ctx.auditLog('北元集团处理部门数据出错', `${error} 。`, 'error');
    }
  }

  /** 处理岗位数据
   * @param {*} postList
   */
  async clearPostList(postList) {
    const { ctx } = this;
    console.log('开始处理岗位数据2');
    try {
      // const filePath = path.join(__dirname, '/data/gangwei.json');
      let i = 0;
      console.log('postList', postList.length);
      // 将postList 写入到当前目录 下的 renyuan.json
      // fs.writeFileSync(filePath, JSON.stringify(postList));
      const manyInsert = [];
      for (const post of postList) {
        const toCreateDingTreePost = {
          _id: post.POSTCODE,
          unitCode: post.PK_POST,
          name: post.POSTNAME,
          EnterpriseID: post.PK_ORG,
          parentid: post.PK_DEPT_CODE,
        };
        console.log('post', i++, toCreateDingTreePost);
        manyInsert.push(toCreateDingTreePost);
        // await ctx.service.importToDb.createDingTrees(toCreateDingTreePost);
      }
      const insertRes = await ctx.model.Dingtree.insertMany(manyInsert);
      console.log('结束处理岗位数据', insertRes);
    } catch (error) {
      ctx.auditLog('北元集团处理岗位数据出错', `${error} 。`, 'error');
    }
  }
  /** 处理人员数据
   * @param {*} peopleList
   */
  async clearPeopleList(peopleList) {
    const { ctx } = this;
    try {
      // const filePath = path.join(__dirname, 'renyuan.json');
      // 从当前目录下的 renyuan.json 读取数据
      // peopleList = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
      // 将postList 写入到当前目录 下的 renyuan.json
      // fs.writeFileSync(filePath, JSON.stringify(peopleList));
      console.log('开始处理人员数据', peopleList.length);
      const employeeBatch = [];
      const userBatch = [];
      const updateBatch = [];
      const EmployeeStatusChangeBatch = [];
      const havePhone = [];
      for (const people of peopleList) {
        let MOBILE = people.MOBILE;
        if (!havePhone.includes(people.MOBILE)) {
          havePhone.push(people.MOBILE);
        } else {
          MOBILE = '';
        }
        let employee = {
          _id: people.PK_PSNDOC,
          unitCode: people.CODE,
          name: people.NAME,
          IDNum: people.ID,
          gender: people.SEX === '1' ? '0' : '1',
          departs: [ people.POSTCODE || people.DEPT_CODE ],
          EnterpriseID: people.PK_ORG,
          nativePlace: people.NATIVEPLACE_NAME,
          phoneNum: MOBILE,
          status: people.ENDFLAG === 'N' ? 1 : 0,
          deathDate: people.DEATHDATE ? new Date(people.DEATHDATE) : null,
          survivalStatus: !people.DEATHDATE,
          age: people.AGE,
          education: people.EDU_NAME,
          workYears: people.WORKAGE,
          workStart: people.JOINSYSDATE ? new Date(people.JOINSYSDATE) : '',
          marriage: people.MARITAL_NAME,
          station: people.POSTNAME,
          laborDispatching: people.CODE.indexOf('L') > -1 ? '是' : '否',
        };
        let userInfo = {
          _id: people.PK_PSNDOC,
          enable: true,
          name: people.NAME,
          unitCode: people.CODE,
          userName: people.CODE,
          countryCode: '86',
          employeeId: people.PK_PSNDOC,
          phoneNum: MOBILE,
          idNo: people.ID,
          idType: '1',
          companyStatus: 2,
          birth: people.BIRTHDAY ? new Date(people.BIRTHDAY) : '',
          gender: people.SEX === '1' ? '0' : '1',
          password: 'Supos@123',
        };
        employee = this.filterEmptyFields(employee);
        employeeBatch.push(employee);
        userInfo = this.filterEmptyFields(userInfo);
        userBatch.push(userInfo);
        // const employeeRes = await ctx.service.importToDb.createEmployee(employee);
        // const userRes = await ctx.service.importToDb.createUser(userInfo);
        // console.log(userRes);
        // 将人放到对应的部门下面
        updateBatch.push({
          filter: { _id: people.POSTCODE || people.DEPT_CODE },
          update: { $addToSet: { staff: people.PK_PSNDOC } },
        });
        // await ctx.model.Dingtree.updateOne(
        //   { _id: people.POSTCODE || people.DEPT_CODE },
        //   { $addToSet: { staff: people.PK_PSNDOC } }
        // );
        // 进行初次入职
        EmployeeStatusChangeBatch.push({
          employee: people.PK_PSNDOC,
          statusChanges: [
            {
              changType: 4,
              EnterpriseID: people.PK_ORG,
              timestamp: people.JOINSYSDATE,
              message: '入职',
            },
          ],
        });
        // await ctx.model.EmployeeStatusChange.create({
        //   employee: people.PK_PSNDOC,
        //   statusChanges: [
        //     {
        //       changType: 4,
        //       EnterpriseID: people.PK_ORG,
        //       timestamp: employeeRes.workStart,
        //       message: '入职',
        //     },
        //   ],
        // });
        // console.log('人员数据', i++, employeeRes);
      }
      // 批量创建人员
      const employeeRes = await ctx.model.Employee.insertMany(employeeBatch);
      console.log('employeeRes', employeeRes);
      // 批量创建用户
      const userRes = await ctx.model.User.insertMany(userBatch);
      console.log('userRes', userRes);
      // 批量创建入职
      await ctx.model.EmployeeStatusChange.insertMany(
        EmployeeStatusChangeBatch
      );
      // 批量更新部门
      // await ctx.model.Dingtree.bulkWrite(updateBatch);
      let a = 0;
      for (const updateData of updateBatch) {
        console.log('updateData', a++);
        await ctx.model.Dingtree.updateOne(
          updateData.filter,
          updateData.update
        );
      }
    } catch (error) {
      ctx.auditLog('北元集团处理人员数据出错', `${error} 。`, 'error');
    }
  }
  /**
   * 过滤对象中的空字段
   * @param {Object} obj - 需要过滤的对象
   * @return {Object} - 过滤后的对象
   */
  filterEmptyFields(obj) {
    const result = {};
    for (const key in obj) {
      if (obj[key] || obj[key] === 0) {
        result[key] = obj[key];
      }
    }
    return result;
  }
  /**
   * 增量获取并处理组织机构 简单的处理了一下，删除，新增，和更新（name和类别）完成
   * @param {*} params
   */
  async incrementOrgs(params = {}) {
    const { ctx } = this;
    try {
      console.log('开始增量获取并处理组织机构');
      const changeList = await this.getOrganizationList(params);
      // const changeList = require('./data/zzjg38.json');
      console.log('需要更新的文档数', changeList.length);
      const needToUpdateDingTrees = [];
      const parentIdDingTrees = await ctx.model.Dingtree.find({
        EnterpriseID: '0001T21000000000E1MJ',
        topLevelOfTheEnterprise: true,
      });
      for (const org of changeList) {
        try {
          // 首先判断是否是删除的 启用状态1=未启用,2=已启用,3=已停用
          if (+org.ENABLESTATE === 3) {
            // 进行删除操作 假删
            const deleteOrg = await ctx.model.Adminorg.updateOne(
              { _id: org.PK_ORG },
              {
                $set: {
                  isDelete: true,
                },
              }
            );
            ctx.auditLog(
              '北元集团增量获取并处理组织机构',
              `${org.PK_ORG} 删除结果${deleteOrg}。`,
              'info'
            );
          } else {
            // 判断是否是新增的
            const ourOrg = await ctx.model.Adminorg.findOne({
              _id: org.PK_ORG,
            });
            if (!ourOrg) {
              // 新增 创建一个管理员账户，判断上下级关系，创建顶级部门
              const byjtAdminUser =
                await ctx.service.importToDb.createAdminuser({
                  group: ctx.app.config.groupID.adminGroupID,
                  userName: 'byjt' + org.CODE,
                  name: org.CREATOR,
                  password: 'Tc666888.',
                });
              const toCreatAdminorg = {
                _id: org.PK_ORG,
                cname: org.NAME,
                shortName: org.SHORTNAME,
                adminUserId: byjtAdminUser._id,
                companyCategory: org.CODE === '100' ? 'group' : org.CORP_CODE === '100' ? 'branch' : 'subsidiary', // 判断公司类型：100编码为集团，100公司代码为分公司，其他为子公司

                isactive: 1,
                adminArray: [ byjtAdminUser._id ],
                unitCode: org.CODE,
              };
              toCreatAdminorg.parentId = [ '0001T21000000000E1MJ' ]; // 默认集团下属子公司
              console.log('toCreatAdminorg', toCreatAdminorg);
              const byjtCompany = await ctx.service.importToDb.createAdminorg(
                toCreatAdminorg
              );
              const dingTreeInfo = {
                name: org.SHORTNAME,
                unitCode: org.CODE,
                EnterpriseID: byjtCompany._id,
                type: 'org',
                topLevelOfTheEnterprise: true,
              };
              console.log('dingTreeInfo', dingTreeInfo);
              const byjtDingTrees =
                await ctx.service.importToDb.createDingTrees(dingTreeInfo);
              needToUpdateDingTrees.push(byjtDingTrees._id);
            } else {
              // 修改
              const toUpdateAdminorg = {
                cname: org.NAME,
                shortName: org.SHORTNAME,
                companyCategory: org.CODE === '100' ? 'group' : org.CORP_CODE === '100' ? 'branch' : 'subsidiary', // 判断公司类型：100编码为集团，100公司代码为分公司，其他为子公司
                unitCode: org.CODE,
              };
              console.log('增量更新企业toUpdateAdminorg', toUpdateAdminorg);
              await ctx.model.Adminorg.updateOne(
                { _id: org.PK_ORG },
                toUpdateAdminorg
              );
              const toUpdateDingTrees = {
                name: org.SHORTNAME ? org.SHORTNAME : org.NAME,
              };
              console.log(
                '增量更新组织机构toUpdateDingTrees',
                toUpdateDingTrees
              );
              await ctx.model.Dingtree.updateOne(
                { EnterpriseID: org.PK_ORG, topLevelOfTheEnterprise: true },
                toUpdateDingTrees
              );
            }
          }
        } catch (error) {
          ctx.auditLog(
            '北元集团增量获取并处理组织机构局部出错',
            `${error} 。`,
            'error'
          );
        }
      }
      console.log(
        '增量更新组织机构needToUpdateDingTrees',
        needToUpdateDingTrees
      );
      await ctx.model.Dingtree.updateMany(
        { _id: { $in: needToUpdateDingTrees } },
        { parentid: parentIdDingTrees._id }
      );
    } catch (error) {
      ctx.auditLog(
        '北元集团增量获取并处理组织机构出错',
        `${error} 。`,
        'error'
      );
    }
  }
  /**
   * 增量获取并处理部门 简单的处理了一下，删除，新增，和更新（name和类别）
   * @param {*} params
   */
  async incrementDepart(params = {}) {
    const { ctx } = this;
    try {
      const changeList = await this.getDepartmentList(params);
      // const changeList = require('./data/bumen38.json');
      for (const depart of changeList) {
        try {
          // 首先判断是否是删除的 启用状态1=未启用,2=已启用,3=已停用
          if (+depart.ENABLESTATE === 3) {
            // 进行删除操作 假删
            const deleteOrg = await ctx.model.Dingtree.updateOne(
              { unitCode: depart.PK_DEPT },
              {
                $set: {
                  isDelete: true,
                },
              }
            );
            ctx.auditLog(
              '北元集团增量获取并处理部门',
              `${depart.PK_DEPT} 删除结果${deleteOrg}。`,
              'info'
            );
          } else {
            // 判断是否是新增的
            const inOurDepart = await ctx.model.Dingtree.findOne({
              _id: depart.CODE,
            });
            let parentId = depart.FATHERORG_CODE;
            if (!parentId) {
              // 判断是不是顶级部门
              let TopParentIdInfo = {};
              if (depart.PK_ORG === '0001T21000000000E1MJ') {
                // 集团本部
                try {
                  TopParentIdInfo = await ctx.model.Dingtree.findOne({
                    EnterpriseID: depart.PK_ORG,
                    topLevelOfTheEnterprise: false,
                    name: '集团本部',
                  });
                } catch (error) {
                  ctx.auditLog(
                    '北元集团处理部门数据局部出错',
                    `${error} 。`,
                    'error'
                  );
                }
              } else {
                try {
                  TopParentIdInfo = await ctx.model.Dingtree.findOne({
                    // 其他公司的情况
                    EnterpriseID: depart.PK_ORG,
                    topLevelOfTheEnterprise: true,
                  });
                } catch (error) {
                  ctx.auditLog(
                    '北元集团处理部门数据局部出错',
                    `${error} 。`,
                    'error'
                  );
                }
              }
              parentId = TopParentIdInfo._id; // 顶级部门的id
            }
            if (!inOurDepart) {
              // 没有的情况下

              const toCreateDingTree = {
                _id: depart.CODE,
                unitCode: depart.PK_DEPT,
                name: depart.NAME,
                parentid: parentId,
                EnterpriseID: depart.PK_ORG,
              };
              console.log(
                '北元集团处理部门数据toCreateDingTree',
                toCreateDingTree
              );
              await ctx.service.importToDb.createDingTrees(toCreateDingTree);
            } else {
              // 修改

              const toUpdateDingTrees = {
                name: depart.NAME,
                unitCode: depart.PK_DEPT,
                EnterpriseID: depart.PK_ORG,
                parentid: parentId,
              };
              console.log(
                '北元集团处理部门数据toUpdateDingTrees',
                toUpdateDingTrees
              );
              await ctx.model.Dingtree.updateOne(
                { _id: depart.CODE },
                toUpdateDingTrees
              );
            }
          }
        } catch (error) {
          ctx.auditLog(
            '北元集团增量获取并处理部门局部出错',
            `${error} 。`,
            'error'
          );
        }
      }
    } catch (error) {
      ctx.auditLog('北元集团增量获取并处理部门出错', `${error} 。`, 'error');
    }
  }
  /**
   * 增量获取并处理岗位 简单的处理了一下，删除，新增，和更新 未写完
   * @param {*} params
   */
  async incrementPost(params = {}) {
    const { ctx } = this;
    try {
      console.log('开始增量获取并处理岗位');
      const changeList = await this.getPostList(params);
      // const changeList = require('./data/gangwei38.json');
      console.log('需要更新的文档数', changeList.length);
      for (const post of changeList) {
        try {
          // 首先判断是否是删除的 启用状态1=未启用,2=已启用,3=已停用
          if (+post.ENABLESTATE === 3) {
            // 进行删除操作 假删
            const deletePost = await ctx.model.Dingtree.updateOne(
              { unitCode: post.POSTCODE },
              { isDelete: true }
            );
            ctx.auditLog(
              '北元集团增量获取并处理岗位',
              `${post.POSTCODE} 假删除结果${deletePost}。`,
              'info'
            );
          } else {
            // 判断是否是新增的
            const ourPost = await ctx.model.Dingtree.findOne({
              _id: post.POSTCODE,
            });
            if (!ourPost) {
              // 新增 创建一个管理员账户，判断上下级关系，创建顶级部门
              const byjtDingTrees =
                await ctx.service.importToDb.createDingTrees({
                  _id: post.POSTCODE,
                  unitCode: post.PK_POST,
                  name: post.POSTNAME,
                  EnterpriseID: post.PK_ORG,
                  parentid: post.PK_DEPT_CODE,
                });
              console.log('byjtDingTrees', byjtDingTrees);
            } else {
              // 修改 flag1
              const toUpdateDingTrees = {
                unitCode: post.PK_POST,
                name: post.POSTNAME,
                EnterpriseID: post.PK_ORG,
                parentid: post.PK_DEPT_CODE,
              };
              console.log('toUpdateDingTrees', toUpdateDingTrees);
              await ctx.model.Dingtree.updateOne(
                { _id: post.POSTCODE },
                { $set: toUpdateDingTrees }
              );
            }
          }
        } catch (error) {
          ctx.auditLog(
            '北元集团增量获取并处理岗位局部出错',
            `${error} 。`,
            'error'
          );
        }
      }
    } catch (error) {
      ctx.auditLog('北元集团增量获取并处理岗位出错', `${error} 。`, 'error');
    }
  }
  /**
   * 增量获取并处理人员 简单的处理了一下，删除，新增，和更新 未写完
   * @param {*} params
   */
  async incrementPeople(params = {}) {
    const { ctx } = this;
    try {
      const changeList = await this.getPeopleList(params);
      // const changeList = require('./renyuan.json');

      const createList = [];
      const updateList = [];
      const byReviewNotice = {};
      let count = 0;
      for (const people of changeList) {
        console.log('people', count++, people.PK_PSNDOC);
        try {
          // 首先判断是否是删除的 启用状态1=未启用,2=已启用,3=已停用
          let MOBILE = people.MOBILE;
          const havePhone = await ctx.model.Employee.countDocuments({
            phoneNum: MOBILE,
          });
          if (havePhone) MOBILE = '';
          // 判断是否是新增的
          const ourPeople = await ctx.model.Employee.findOne({
            _id: people.PK_PSNDOC,
          }).lean();
          console.log('ourPeople', ourPeople ? ourPeople.name : people.NAME);
          if (!ourPeople) {
            let employee = {
              _id: people.PK_PSNDOC,
              unitCode: people.CODE,
              name: people.NAME,
              IDNum: people.ID,
              gender: people.SEX === '1' ? '0' : '1',
              departs: [ people.POSTCODE || people.DEPT_CODE ],
              EnterpriseID: people.PK_ORG,
              nativePlace: people.NATIVEPLACE_NAME,
              phoneNum: MOBILE,
              status: people.ENDFLAG === 'N' ? 1 : 0,
              deathDate: people.DEATHDATE ? new Date(people.DEATHDATE) : null,
              survivalStatus: !people.DEATHDATE,
              age: people.AGE,
              education: people.EDU_NAME,
              workYears: people.WORKAGE,
              workStart: people.JOINSYSDATE
                ? new Date(people.JOINSYSDATE)
                : '',
              marriage: people.MARITAL_NAME,
              station: people.POSTNAME,
              laborDispatching: people.CODE.indexOf('L') > -1 ? '是' : '否',
            };
            let userInfo = {
              _id: people.PK_PSNDOC,
              enable: true,
              name: people.NAME,
              unitCode: people.CODE,
              userName: people.CODE,
              countryCode: '86',
              employeeId: people.PK_PSNDOC,
              companyId: [ people.PK_ORG ],
              company: people.ORG_NAME,
              phoneNum: MOBILE,
              idNo: people.ID,
              idType: '1',
              companyStatus: 2,
              birth: people.BIRTHDAY ? new Date(people.BIRTHDAY) : '',
              gender: people.SEX === '1' ? '0' : '1',
              password: 'Supos@123',
            };
            employee = this.filterEmptyFields(employee);
            userInfo = this.filterEmptyFields(userInfo);
            const employeeRes = await ctx.model.Employee.create(employee);
            const userRes = await ctx.model.User.create(userInfo);
            // 将人放到对应的部门下面
            const toAddDingTree = await ctx.model.Dingtree.updateOne(
              { _id: people.POSTCODE || people.DEPT_CODE },
              { $addToSet: { staff: people.PK_PSNDOC } }
            );
            if (people.JOINSYSDATE) {
              // 进行初次入职
              const EmployeeStatusChangeBatch = {
                employee: people.PK_PSNDOC,
                statusChanges: [
                  {
                    changType: 4,
                    EnterpriseID: people.PK_ORG,
                    timestamp: people.JOINSYSDATE,
                    message: '入职',
                  },
                ],
              };
              await ctx.model.EmployeeStatusChange.create(
                EmployeeStatusChangeBatch
              );
              // console.log('changeRes', changeRes);
              if (!people.POSTCODE || !people.DEPT_CODE) return;
              // 生成新上岗审核记录
              const newDeparts = employee?.departs;
              const nowStationIds = await this.millSearch({
                departId: newDeparts[0],
                EnterpriseID: employee.EnterpriseID,
              });
              if (nowStationIds.length === 0) return;
              const auditStationChangeInfo = {
                _id: shortid.generate(),
                employeeId: employee._id,
                nowEnterpriseID: employee.EnterpriseID,
                status: 0,
                nowDingtreeIds: newDeparts[0],
                nowStationIds,
                stationStatus: 4,
                unitCode: employee.unitCode,
                employeeName: employee.name,
                files: null,
              };
              await this.ctx.model.AuditStationChange.create(
                auditStationChangeInfo
              );
              byReviewNotice[employee.EnterpriseID]
                ? ++byReviewNotice[employee.EnterpriseID]
                : (byReviewNotice[employee.EnterpriseID] = 1);
            }
            console.log('toAddDingTree', toAddDingTree, employeeRes, userRes);
            createList.push(people.PK_PSNDOC);
          } else {
            // 更新
            let employee = {
              unitCode: people.CODE,
              name: people.NAME,
              IDNum: people.ID,
              gender: people.SEX === '1' ? '0' : '1',
              departs: [ people.POSTCODE || people.DEPT_CODE ],
              EnterpriseID: people.PK_ORG,
              nativePlace: people.NATIVEPLACE_NAME,
              phoneNum: MOBILE,
              status: people.ENDFLAG === 'N' ? 1 : 0,
              deathDate: people.DEATHDATE ? new Date(people.DEATHDATE) : null,
              survivalStatus: !people.DEATHDATE,
              age: people.AGE,
              education: people.EDU_NAME,
              workYears: people.WORKAGE,
              workStart: people.JOINSYSDATE
                ? new Date(people.JOINSYSDATE)
                : '',
              marriage: people.MARITAL_NAME,
              station: people.POSTNAME,
              laborDispatching: people.CODE.indexOf('L') > -1 ? '是' : '否',
            };
            let userInfo = {
              enable: true,
              name: people.NAME,
              unitCode: people.CODE,
              userName: people.CODE,
              countryCode: '86',
              employeeId: people.PK_PSNDOC,
              companyId: [ people.PK_ORG ],
              company: people.ORG_NAME,
              phoneNum: MOBILE,
              idNo: people.ID,
              idType: '1',
              companyStatus: 2,
              birth: people.BIRTHDAY ? new Date(people.BIRTHDAY) : '',
              gender: people.SEX === '1' ? '0' : '1',
              password: 'Supos@123',
            };
            employee = this.filterEmptyFields(employee);
            userInfo = this.filterEmptyFields(userInfo);
            const updateEmployee = await ctx.model.Employee.updateOne(
              { _id: people.PK_PSNDOC },
              {
                $set: employee,
              }
            );
            const updateUser = await ctx.model.User.updateOne(
              { _id: people.PK_PSNDOC },
              {
                $set: userInfo,
              }
            );
            console.log('updateEmployee', updateEmployee, updateUser);
            if (people.JOINSYSDATE) {
              const changeRes =
                  await ctx.model.EmployeeStatusChange.findOneAndUpdate(
                    {
                      employee: people.PK_PSNDOC,
                      'statusChanges.changType': 4,
                    },
                    {
                      $set: {
                        'statusChanges.$': {
                          changType: 4,
                          EnterpriseID: people.PK_ORG,
                          timestamp: people.JOINSYSDATE,
                          message: '入职',
                        },
                      },
                    },
                    { new: true }
                  );

              if (!changeRes) {
                await ctx.model.EmployeeStatusChange.findOneAndUpdate(
                  { employee: people.PK_PSNDOC },
                  {
                    $setOnInsert: { _id: shortid.generate() },
                    $push: {
                      statusChanges: {
                        changType: 4,
                        EnterpriseID: people.PK_ORG,
                        timestamp: people.JOINSYSDATE,
                        message: '入职',
                      },
                    },
                  },
                  { upsert: true, new: true }
                );
              }
              // console.log('changeRes', changeRes);
            } else {
              // 删除入职记录 statusChanges里changeType=4的 从statusChanges里删除
              await ctx.model.EmployeeStatusChange.updateOne(
                { employee: people.PK_PSNDOC },
                {
                  $pull: {
                    statusChanges: {
                      changType: 4,
                    },
                  },
                }
              );
            }
            updateList.push(people.PK_PSNDOC);
            // 计算是否需要审核车间岗位信息：转岗、离岗
            let stationStatus = 2;
            if (ourPeople.status === 1 && people.ENDFLAG !== 'N') {
              stationStatus = 1;
            }
            const isPostcodeInDeparts = ourPeople.departs.includes(
              people.POSTCODE
            );
            const isDeptCodeInDeparts = ourPeople.departs.includes(
              people.DEPT_CODE
            );
            if ((isPostcodeInDeparts || isDeptCodeInDeparts) && people.ENDFLAG === 'N') continue;
            await this.calculateIsNeedReview(
              ourPeople,
              employee,
              people.PK_PSNDOC,
              stationStatus
            );
            byReviewNotice[employee.EnterpriseID]
              ? ++byReviewNotice[employee.EnterpriseID]
              : (byReviewNotice[employee.EnterpriseID] = 1);
          }
          if (+people.ENABLESTATE === 3) {
            // 进行删除操作 假删
            const deleteOrg = await ctx.model.Employee.updateOne(
              { _id: people.PK_PSNDOC },
              { enable: true }
            );
            ctx.auditLog(
              '北元集团增量获取并处理人员',
              `${people.PK_PSNDOC} 删除结果${deleteOrg}。`,
              'info'
            );
          }
        } catch (error) {
          ctx.auditLog(
            '北元集团增量获取并处理人员局部出错',
            `${error} 。`,
            'error'
          );
        }
      }
      console.log('byReviewNotice', byReviewNotice);
      for (const key in byReviewNotice) {
        const res = await ctx.model.Adminorg.aggregate([
          {
            $match: {
              _id: key,
            },
          },
          {
            $project: {
              adminArray: 1,
            },
          },
          {
            $unwind: '$adminArray',
          },
          {
            $lookup: {
              from: 'employees',
              localField: 'adminArray',
              foreignField: '_id',
              as: 'employeeInfo',
            },
          },
          {
            $unwind: '$employeeInfo',
          },
          {
            $group: {
              _id: null,
              unitCodes: {
                $push: '$employeeInfo.unitCode',
              },
            },
          },
          {
            $project: {
              _id: 0,
            },
          },
        ]);
        const unitCodes = res[0]?.unitCodes;
        if (unitCodes && unitCodes.length > 0) {
          this.ctx.helper.signAkSk({
            apiName: 'sendNotification',
            params: {
              businessCode: 'zyjk',
              businessName: '职业健康',
              receivers: [
                {
                  rangeType: 'STAFF',
                  unitCodes,
                },
              ],
              contents: [
                {
                  protocol: 'wechat',
                  content: `职位卫生健康人员信息对接提醒您有${byReviewNotice[key]}个新的车间岗位人员审核名单，请及时前往车间岗位页面进行审核。`,
                },
              ],
            },
          });
        }
      }
      ctx.auditLog('北元集团增量获取并处理人员', `新增${createList}。`, 'info');
      ctx.auditLog('北元集团增量获取并处理人员', `更新${updateList}。`, 'info');
    } catch (error) {
      ctx.auditLog('北元集团增量获取并处理人员出错', `${error} 。`, 'error');
    }
  }
  /**
   * 增量获取并处理人员工作记录 简单的处理了一下，删除，新增，和更新 未写完
   * @param {*} params
   */
  async incermentPeopleWork(params = {}) {
    const { ctx } = this;
    try {
      const changeList = await this.getPeopleWorkList(params);
      // const changeList = require('./data/peopleWork38.json');
      let count = 0;
      for (const work of changeList) {
        console.log('work', count++, work);
        try {
          const dingTreeInfo = await ctx.model.Dingtree.findOne({
            unitCode: work.PK_POST === '~' ? work.PK_DEPT : work.PK_POST,
          });
          const date = work.BEGINDATE;
          const message = '转入部门';
          const needToIndo = {
            changType: 3,
            EnterpriseID: work.PK_ORG,
            departsTo: [[ dingTreeInfo._id ]],
            timestamp: date,
            message,
          };
          const changeRes =
            await ctx.model.EmployeeStatusChange.findOneAndUpdate(
              {
                employee: work.PK_PSNDOC,
                'statusChanges.timestamp': new Date(date),
              },
              {
                $set: {
                  'statusChanges.$': needToIndo,
                },
              },
              { new: true }
            );

          if (!changeRes) {
            await ctx.model.EmployeeStatusChange.findOneAndUpdate(
              { employee: work.PK_PSNDOC },
              {
                $setOnInsert: { _id: shortid.generate() },
                $push: {
                  statusChanges: needToIndo,
                },
              },
              { upsert: true, new: true }
            );
          }
        } catch (error) {
          ctx.auditLog(
            '北元集团增量获取并处理人员工作记录局部出错',
            `${error} 。`,
            'error'
          );
        }
      }
    } catch (error) {
      ctx.auditLog(
        '北元集团增量获取并处理人员工作记录出错',
        `${error} 。`,
        'error'
      );
    }
  }

  /**
   *  增量获取并处理双预防隐患排查记录
   * @param {*} params
   */
  async incermentHiddenDanger(params = {}) {
    const { ctx } = this;
    try {
      const DoublePreventionList = await this.getDoublePreventionList(params);
      console.log(DoublePreventionList.length, '长度多少个');
      for (const item of DoublePreventionList) {
        try {
          const toSet = JSON.parse(JSON.stringify(item));
          delete toSet.ZJ;
          toSet.unitCode = item.ZJ;
          let dingTreeInfo = await ctx.model.Dingtree.findOne({
            _id: item.DWFXFXDXSSBMCODE,
          }).select('EnterpriseID');
          if (!dingTreeInfo) {
            dingTreeInfo = await ctx.model.Adminorg.findOne({
              unitCode: item.DWFXFXDXSSBMCODE,
            });
            toSet.EnterpriseID = dingTreeInfo._id;
          } else {
            toSet.EnterpriseID = dingTreeInfo.EnterpriseID;
          }

          console.log(item, 'item.ZJ');
          await ctx.model.DoublePrevention.findOneAndUpdate(
            {
              unitCode: item.ZJ,
            },
            {
              $set: toSet,
              $setOnInsert: { _id: shortid.generate() },
            },
            { new: true, useFindAndModify: false, upsert: true }
          );
        } catch (error) {
          ctx.auditLog(
            '北元集团增量获取并处理双预防隐患记录局部出错',
            `${error} 。${JSON.stringify(item)}`,
            'error'
          );
        }
      }
    } catch (error) {
      ctx.auditLog(
        '北元集团增量获取并处理双预防隐患记录出错',
        `${error} 。`,
        'error'
      );
    }
  }
  async calculateIsNeedReview(
    oldEmployeeInfo,
    newEmployeeInfo,
    employeeId,
    stationStatus
  ) {
    try {
      const newDeparts = newEmployeeInfo?.departs;
      const oldDeparts = oldEmployeeInfo?.departs;
      if (!newDeparts || newDeparts.length === 0) return;
      const oldDepart = oldDeparts[0];
      const newDepart = newDeparts[0];
      const oldDepartInfo = await this.ctx.model.Dingtree.findOne({
        _id: oldDepart,
      }).lean();
      const newDepartInfo = await this.ctx.model.Dingtree.findOne({
        _id: newDepart,
      }).lean();
      if (!oldDepartInfo || !newDepartInfo) return;
      const oldEnterpriseID = oldDepartInfo.EnterpriseID;
      const newEnterpriseID = newDepartInfo.EnterpriseID;
      // const allNewDepartsIds = await this.ctx.model.Dingtree.aggregate([
      //   {
      //     $match: {
      //       _id: newDeparts[0],
      //     },
      //   },
      //   {
      //     $graphLookup: {
      //       from: 'dingtrees',
      //       startWith: newDeparts[0],
      //       connectFromField: 'parentid',
      //       connectToField: '_id',
      //       as: 'ancestors',
      //       depthField: 'depth',
      //     },
      //   },
      //   {
      //     $unwind: '$ancestors',
      //   },
      //   {
      //     $sort: {
      //       'ancestors.depth': -1,
      //     },
      //   },
      //   {
      //     $match: {
      //       'ancestors.parentid': { $exists: true },
      //     },
      //   },
      //   {
      //     $group: {
      //       _id: null,
      //       ancestorIds: {
      //         $push: '$ancestors._id',
      //       },
      //     },
      //   },
      //   {
      //     $project: {
      //       _id: 0,
      //       ancestorIds: 1,
      //     },
      //   },
      // ]);
      // const allOldDepartsIds = await this.ctx.model.Dingtree.aggregate([
      //   {
      //     $match: {
      //       _id: newDeparts[0],
      //     },
      //   },
      //   {
      //     $graphLookup: {
      //       from: 'dingtrees',
      //       startWith: oldDeparts[0],
      //       connectFromField: 'parentid',
      //       connectToField: '_id',
      //       as: 'ancestors',
      //       depthField: 'depth',
      //     },
      //   },
      //   {
      //     $unwind: '$ancestors',
      //   },
      //   {
      //     $sort: {
      //       'ancestors.depth': -1,
      //     },
      //   },
      //   {
      //     $match: {
      //       'ancestors.parentid': { $exists: true },
      //     },
      //   },
      //   {
      //     $group: {
      //       _id: null,
      //       ancestorIds: {
      //         $push: '$ancestors._id',
      //       },
      //     },
      //   },
      //   {
      //     $project: {
      //       _id: 0,
      //       ancestorIds: 1,
      //     },
      //   },
      // ]);
      // if (allNewDepartsIds.length === 0) return;
      // if (allOldDepartsIds.length === 0) return;
      // const nowStationParams = {
      //   EnterpriseID: newEmployeeInfo.EnterpriseID,
      //   departsIds: allNewDepartsIds[0].ancestorIds,
      // };
      // const originStationParams = {
      //   EnterpriseID: oldEmployeeInfo.EnterpriseID,
      //   departsIds: allOldDepartsIds[0].ancestorIds,
      // };
      const nowStationIds = await this.millSearch({
        departId: newDepart,
        EnterpriseID: newEnterpriseID,
      });
      if (nowStationIds.length === 0) {
        stationStatus = 1;
      }
      const originStationIds = await this.millSearch({
        departId: oldDepart,
        EnterpriseID: oldEnterpriseID,
      });
      // 判断nowStationIds和originStationIds是否相等
      if (_.isEqual(nowStationIds, originStationIds)) {
        return;
      }
      const auditStationChangeInfo = {
        _id: shortid.generate(),
        employeeId,
        nowEnterpriseID: newEmployeeInfo.EnterpriseID,
        originEnterpriseID: oldEmployeeInfo.EnterpriseID,
        status: 0,
        originDingtreeIds: oldDepart,
        nowDingtreeIds: newDepart,
        originStationIds,
        nowStationIds,
        stationStatus,
        unitCode: newEmployeeInfo.unitCode,
        employeeName: newEmployeeInfo.name,
        files: null,
      };
      // await this.ctx.model.AuditStationChange.findOneAndUpdate(
      //   { employeeId }, // 查询条件
      //   {
      //     $set: {
      //       ...auditStationChangeInfo,
      //     },
      //     $setOnInsert: {
      //       _id: shortid.generate(),
      //     },
      //   }, // 使用 $set 更新字段
      //   {
      //     upsert: true, // 如果找不到匹配条件的文档，是否插入新文档
      //     new: true, // 返回更新后的文档
      //   }
      // );
      await this.ctx.model.AuditStationChange.create(auditStationChangeInfo);
      this.ctx.auditLog(
        '北元集团处理人员部门绑定岗位数据',
        `需要审核岗位变动${employeeId}。`,
        'info'
      );
    } catch (error) {
      this.ctx.auditLog(
        '北元集团处理人员部门绑定岗位数据出错',
        `${error} 。`,
        'error'
      );
    }
  }
  // 存储员工岗位信息
  storageEmployeeStationAndDepart(data) {
    if (data.length === 0) return [];
    const stationInfo = [];
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      if (item.category === 'workspaces') {
        stationInfo.push([
          {
            category: item.category,
            specificId: item._id,
          },
        ]);

        if (item.children) {
          if (item.children.category === 'stations') {
            stationInfo[i].push({
              category: item.children.category,
              specificId: item.children._id,
            });
          }
        }
      } else if (item.category === 'mill') {
        stationInfo.push([
          {
            category: item.category,
            specificId: item._id,
          },
        ]);
        if (item.children) {
          if (item.children.category === 'workspaces') {
            stationInfo[i].push({
              category: item.children.category,
              specificId: item.children._id,
            });
            if (item.children.children) {
              if (item.children.children.category === 'stations') {
                stationInfo[i].push({
                  category: item.children.children.category,
                  specificId: item.children.children._id,
                });
              }
            }
          }
        }
      }
    }
    return stationInfo;
  }
  // 厂房查询
  async millSearch(params) {
    const { EnterpriseID, departId } = params;
    let millInfo = [];
    const categories = [ 'mill', 'workspaces' ]; // 依次查询 mill 和 workspaces

    for (const category of categories) {
      const pipeline = [
        {
          $match: {
            EnterpriseID,
            category,
          },
        },
      ];

      if (category === 'mill') {
        // `mill` 需要展开两层
        pipeline.push(
          { $unwind: '$children' },
          { $unwind: '$children.children' },
          {
            $unwind: {
              path: '$children.children.newLinkDeparts.departs',
              preserveNullAndEmptyArrays: true, // 允许空值
            },
          },
          {
            $match: {
              'children.children.newLinkDeparts.EnterpriseID': EnterpriseID,
              'children.children.newLinkDeparts.departs.departId': departId,
            },
          }
        );
      } else if (category === 'workspaces') {
        // `workspaces` 只需展开一层
        pipeline.push(
          { $unwind: '$children' },
          {
            $unwind: {
              path: '$children.newLinkDeparts.departs',
              preserveNullAndEmptyArrays: true, // 允许空值
            },
          },
          {
            $match: {
              'children.newLinkDeparts.EnterpriseID': EnterpriseID,
              'children.newLinkDeparts.departs.departId': departId,
            },
          }
        );
      }

      const res = await this.ctx.model.MillConstruction.aggregate(pipeline);

      if (res.length > 0) {
        const stationInfo = this.storageEmployeeStationAndDepart(res);
        millInfo = millInfo.concat(stationInfo);
      }
    }
    return millInfo;
  }

  // #region supos调用的service

  /** 获取所有噪声粉尘的设备列表
   * @param {*} params
   * @return
   * @memberof ByDataService
   * @description 获取所有噪声粉尘的设备列表
   **/
  async getNoiseDustEquipmentList() {
    const { ctx } = this;
    try {
      // 循环如果出错或者signAkSkRes.paginationd.pageSize*current>signAkSkRes.paginationd.total才结束
      let current = 1;
      let noiseDustEquipmentList = [];
      while (true) {
        try {
          const signAkSkRes = await ctx.helper.signAkSk({
            apiName: 'getNoiseDustEquipmentList',
            params: { current, pageSize: 100 },
          });
          noiseDustEquipmentList = noiseDustEquipmentList.concat(
            signAkSkRes.data.list
          );
          if (
            signAkSkRes.data.paginationd.pageSize * current >=
            signAkSkRes.data.paginationd.total
          ) {
            break;
          }
        } catch (error) {
          break;
        }
        current++;
      }
      console.log('getNoiseDustEquipmentList', noiseDustEquipmentList);
      // 从数据库中筛选出来不存在的设备，然后插入数据库
      // 查找并更新，如果不存在则创建
      // 更新全部设备状态为false
      if (noiseDustEquipmentList.length === 0) return [];
      // 可以改为内存 判断减少查询
      await ctx.model.Devices.updateMany({}, { $set: { status: false } });
      for (const item of noiseDustEquipmentList) {
        await ctx.model.Devices.findOneAndUpdate(
          { deviceID: 'ZYFC_' + item.code },
          {
            $set: {
              status: true,
              connectionStatus: true,
              name: item.displayName + '检测设备',
              deviceID: 'ZYFC_' + item.code,
              description: item.displayName,
              deviceType: 'GJZRu-diy0',
            },
            $setOnInsert: {
              _id: shortid.generate(),
            },
          },
          { new: true, upsert: true }
        );
      }
      return noiseDustEquipmentList;
    } catch (error) {
      ctx.auditLog('获取所有噪声粉尘的设备列表出错', `${error} 。`, 'error');
      return [];
    }
  }
  /** 获取设备的实时数据Pi
   * @param {*} params
   */
  async getPIDeviceRealTimeData() {
    const { ctx } = this;
    try {
      // 从数据库中获取所有name包含PIPoint开头的设备

      const PIDeviceList = await ctx.model.Devices.aggregate([
        {
          $match: {
            name: {
              $regex: /^PIPoint/,
            },
            connectionStatus: true,
          },
        },
        {
          $lookup: {
            from: 'deviceTypes',
            localField: 'deviceType',
            foreignField: '_id',
            as: 'deviceType',
          },
        },
        {
          $unwind: {
            path: '$deviceType',
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $lookup: {
            from: 'deviceMonitorFactors',
            let: {
              param: '$deviceType.parameters',
              deviceData: '$deviceData',
            }, // 使用let定义一个变量param，它将用于lookup条件
            pipeline: [
              {
                $match: {
                  $expr: { $in: [ '$_id', '$$param' ] }, // 使用$expr和$in来匹配parameters数组中的元素
                },
              },
              {
                $addFields: {
                  kv: {
                    $arrayElemAt: [
                      {
                        $filter: {
                          input: {
                            $objectToArray: '$$deviceData',
                          },
                          as: 'item',
                          cond: {
                            $eq: [ '$$item.k', '$key' ],
                          },
                        },
                      },
                      0,
                    ],
                  },
                },
              },
              {
                $project: {
                  key: 1,
                },
              },
            ],
            as: 'deviceType.parameters', // 将匹配到的文档存储在matchedFactors字段
          },
        },
        {
          $unwind: {
            path: '$deviceType.parameters',
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $project: {
            deviceID: 1,
            key: '$deviceType.parameters.key',
          },
        },
      ]);
      const PIDeviceListIds = PIDeviceList.map(item => item.deviceID);
      const PIDeviceObj = PIDeviceList.reduce((acc, cur) => {
        acc[cur.deviceID] = cur.key;
        return acc;
      }, {});
      const base_host = ctx.app.config.byMonitor.piApi;
      const piResult = await axios({
        method: 'post',
        url: base_host + '/StdDataService/REST/ReadTagsSync',
        data: {
          names: PIDeviceListIds,
        },
      });
      const toInsert = [];
      if (piResult.status === 200) {
        if (piResult.data.ReadTagsSyncResult) {
          const ReadTagsSyncResult = piResult.data.ReadTagsSyncResult;
          for (const item of ReadTagsSyncResult) {
            const mid = {
              quality: item.Quality,
              hTag: item.HTag,
              value: item.Value,
              bResult: item.bResult,
            };
            // 这个地方要做处理
            const keyMonitor = await ctx.model.DeviceMonitorFactor.findOne({
              key: PIDeviceObj[item.Name],
            }).lean();
            mid[PIDeviceObj[item.Name]] =
              item.Value > 0 ? this.convertPPMtoMGM3(keyMonitor, item.Value) : 0;
            toInsert.push({
              timestamp: new Date(),
              metadata: {},
              data: mid,
              deviceID: item.Name,
            });
          }
        }
      }
      // 插入数据库
      await ctx.model.MonitorData.insertMany(toInsert);
      return toInsert;
    } catch (error) {
      ctx.auditLog('获取设备的实时数据PI出错', `${error} 。`, 'error');
      return [];
    }
  }
  /** 获取设备的实时数据噪声+粉尘
   * @param {*} params
   * @param num
   * @memberof ByDataService
   */
  async getEquipmentRealTimeData() {
    const { ctx } = this;
    try {
      const params = {
        inputs: [],
      };
      const NoiseDustEquipmentList = await this.getNoiseDustEquipmentList();
      for (const item of NoiseDustEquipmentList) {
        params.inputs = params.inputs.concat([
          `system.NoiseDust.ZYFC_${item.code}.system.Noise`,
          `system.NoiseDust.ZYFC_${item.code}.system.Dust`,
          `system.NoiseDust.ZYFC_${item.code}.system.Temp`,
          `system.NoiseDust.ZYFC_${item.code}.system.Pressure`,
          `system.NoiseDust.ZYFC_${item.code}.system.Heating`,
          `system.NoiseDust.ZYFC_${item.code}.system.EquNo`,
        ]);
      }

      const equipmentRealTimeData = await ctx.helper.signAkSk({
        apiName: 'getEquipmentRealTimeData',
        params,
      });

      const catedData = this.categorizeData(equipmentRealTimeData.data.data);

      for (const key in catedData) {
        if (catedData.hasOwnProperty(key)) {
          const data = {
            timestamp: new Date(),
            metadata: {},
            data: { ...catedData[key] },
            deviceID: 'ZYFC_' + catedData[key].EquNo,
          };
          // 噪声为随机噪声值 改为是否mock
          if (ctx.app.config.byMonitor.isMock === 'Y') {
            data.data.Noise = Math.floor(Math.random() * 100);
            // 粉尘为随机粉尘值 2-30
            data.data.Dust = Math.floor(Math.random() * 30) + 2;
          }
          await ctx.service.monitorData.create(data);
        }
      }
      return catedData;
    } catch (error) {
      ctx.auditLog('获取设备的实时数据出错', `${error} 。`, 'error');
      return [];
    }
  }

  /** 获取设备的实时数据H2SPH3
   * @param {*} params
   * @param num
   * @memberof ByDataService
   */
  async getEquipmentRealTimeDataH2SPH3() {
    const { ctx } = this;
    try {
      const params = {
        inputs: [],
      };
      const NoiseDustEquipmentList = await this.getH2SPH3EquipmentList();
      for (const item of NoiseDustEquipmentList) {
        params.inputs = params.inputs.concat([
          `system.H2S_PH3.H2SPH3_${item.code}.system.H2S`,
          `system.H2S_PH3.H2SPH3_${item.code}.system.PH3`,
          // `system.H2S_PH3.H2SPH3_${item.code}.system.Temp`,
          `system.H2S_PH3.H2SPH3_${item.code}.system.EquNo`,
          // `system.H2S_PH3.H2SPH3_${item.code}.system.Pressure`,
          // `system.H2S_PH3.H2SPH3_${item.code}.system.Heating`,
          // `system.H2S_PH3.H2SPH3_${item.code}.system.Fan`,
          // `system.H2S_PH3.H2SPH3_${item.code}.system.Error`,
          // `system.H2S_PH3.H2SPH3_${item.code}.system.Time`,
        ]);
      }
      const equipmentRealTimeData = await ctx.helper.signAkSk({
        apiName: 'getEquipmentRealTimeData',
        params,
      });
      // const equipmentRealTimeData = {
      //   data: {
      //     code: 200, message: 'ok', data: { 'system.H2S_PH3.H2SPH3_24051607.system.EquNo': { name: 'system.H2S_PH3.H2SPH3_24051607.system.EquNo', value: '24051607', status: '0', timeStamp: 1727422985748 }, 'system.H2S_PH3.H2SPH3_24051607.system.H2S': { name: 'system.H2S_PH3.H2SPH3_24051607.system.H2S', value: 0, status: '0', timeStamp: 1727422985748 }, 'system.H2S_PH3.H2SPH3_24051607.system.PH3': { name: 'system.H2S_PH3.H2SPH3_24051607.system.PH3', value: 0.33, status: '0', timeStamp: 1727422985748 } },
      //   },
      // };

      const catedData = this.categorizeData(
        Object.keys(equipmentRealTimeData.data.data).length > 0
          ? equipmentRealTimeData.data.data
          : this.getEquipmentRealTimeDataH2SPH3Mock(params.inputs)
      );
      for (const key in catedData) {
        if (catedData.hasOwnProperty(key)) {
          const data = {
            timestamp: new Date(),
            metadata: {},
            data: { ...catedData[key] },
            deviceID: 'H2SPH3_' + catedData[key].EquNo,
          };
          // 获取h2sph3的设备列表
          const H2sPh3Monitor = await ctx.model.DeviceMonitorFactor.find({
            key: {
              $in: [ 'H2S', 'PH3' ],
            },
          }).lean();
          for (const item of H2sPh3Monitor) {
            data.data[item.key] = this.convertPPMtoMGM3(
              item,
              data.data[item.key]
            );
          }
          // 随机值 ，Mock
          if (ctx.app.config.byMonitor.isMock === 'Y') {
            data.data.PH3 = Math.floor(Math.random() * 10) / 10;
            data.data.H2S = Math.floor(Math.random() * 150) / 10;
          }
          await ctx.service.monitorData.create(data);
        }
      }
      return catedData;
    } catch (error) {
      ctx.auditLog('获取H2SPH3设备的实时数据出错', `${error} 。`, 'error');
      return [];
    }
  }
  // 转换ppm到mg/m3
  convertPPMtoMGM3(item, ppm) {
    try {
      if (item.isConversion && item.quality) {
        const R = 22.4;
        // const T = 数字格式化item.quality
        const T = Number(item.quality);
        const result = (T / R) * ppm;
        const roundedResult = result.toFixed(2); // 保留两位小数
        return roundedResult;
      }
      return ppm;
    } catch (error) {
      console.log('error', error);
    }
    return ppm;
  }
  // mock数据
  getEquipmentRealTimeDataH2SPH3Mock(inputs) {
    const data = {};
    for (const key of inputs) {
      const last = key.split('.').pop();
      // 随机H2S的数值 1位小数  0-15
      const PH3 = Math.floor(Math.random() * 10) / 10;
      // 随机PH3的数值 1位小数 0-1
      const H2S = Math.floor(Math.random() * 150) / 10;
      const EquNo = key.replace('H2SPH3_', '').split('.')[2];
      data[key] = {
        value:
          last === 'H2S'
            ? H2S
            : last === 'PH3'
              ? PH3
              : Math.floor(Math.random() * 100),
      };
      if (last === 'EquNo') data[key].value = EquNo;
    }
    return data;
  }
  /** 获取所有H2SPH3的设备列表
   * @param {*} params
   * @return
   * @memberof ByDataService
   * @description 获取所有H2SPH3的设备列表
   **/
  async getH2SPH3EquipmentList() {
    const { ctx } = this;
    try {
      let current = 1;
      let h2sPh3QuipmentList = [];
      while (true) {
        try {
          const signAkSkRes = await ctx.helper.signAkSk({
            apiName: 'getH2SPH3EquipmentList',
            params: { current, pageSize: 100 },
          });
          // console.log('signAkSkRes', signAkSkRes);
          h2sPh3QuipmentList = h2sPh3QuipmentList.concat(signAkSkRes.data.list);
          if (
            signAkSkRes.data.paginationd.pageSize * current >=
            signAkSkRes.data.paginationd.total
          ) {
            break;
          }
        } catch (error) {
          break;
        }
        current++;
      }
      console.log('getH2sPh3QuipmentList', h2sPh3QuipmentList);
      // 从数据库中筛选出来不存在的设备，然后插入数据库
      // 查找并更新，如果不存在则创建
      // 更新全部设备状态为false
      if (h2sPh3QuipmentList.length === 0) return [];
      // 可以改为内存 判断减少查询
      await ctx.model.Devices.updateMany({}, { $set: { status: false } });
      for (const item of h2sPh3QuipmentList) {
        try {
          await ctx.model.Devices.findOneAndUpdate(
            { deviceID: 'H2SPH3_' + item.code },
            {
              $set: {
                status: true,
                connectionStatus: true,
                name: item.displayName + '检测设备',
                deviceID: 'H2SPH3_' + item.code,
                deviceType: 'QYeKFFwky',
                description: item.displayName,
              },
              $setOnInsert: {
                _id: shortid.generate(),
              },
            },
            { new: true, upsert: true }
          );
        } catch (error) {
          ctx.auditLog('更新H2SPH3设备列表出错', `${error} 。`, 'error');
        }
      }
      return h2sPh3QuipmentList;
    } catch (error) {
      ctx.auditLog('获取所有的H2SPH3设备列表出错', `${error} 。`, 'error');
      return [];
    }
  }
  /**
   * 对获取到的数据进行分类 通用
   * @param {*} data
   * @return
   */
  categorizeData(data) {
    const categorized = {};
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const parts = key.split('.');
        if (
          parts.length >= 3 &&
          parts[0] === 'system' &&
          (parts[1] === 'H2S_PH3' || parts[1] === 'NoiseDust')
        ) {
          const category = parts[2].replace('_', ''); // 去掉下划线
          if (!categorized[category]) {
            categorized[category] = {};
          }
          const subKey = parts.slice(3).join('.').replace('system.', ''); // 去掉system前缀
          categorized[category][subKey] = data[key].value;
        }
      }
    }

    return categorized;
  }
  // #endregion
}
module.exports = ByDataService;
