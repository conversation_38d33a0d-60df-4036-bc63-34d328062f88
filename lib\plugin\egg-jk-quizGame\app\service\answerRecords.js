const Service = require('egg').Service;

class AnswerRecordsService extends Service {
  // 创建答题记录
  async create(payload) {
    const { ctx } = this;
    try {
      const { gameRecordId, topicId, answers = [], isRight = false, score = 0 } = payload;
      return ctx.model.AnswerRecords.create({ gameRecordId, topicId, answers, isRight, score, createdAt: new Date().getTime() });
    } catch (error) {
      console.log(444444, error);
      ctx.auditLog('创建答题记录失败', error.message, 'error');
    }
  }

}

module.exports = AnswerRecordsService;
