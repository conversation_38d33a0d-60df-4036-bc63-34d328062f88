

const path = require('path');
const modulesPath = path.resolve(__dirname, '../');
const shell = require('shelljs');
const {
  scanforder,
} = require('./utils');

// 指定打包模块
const designatedModule = [];
console.log(__dirname); // E:\demo\test1\backstage\build
console.log(modulesPath); // E:\demo\test1\backstage

let targetBuildModules = scanforder(modulesPath);
if (designatedModule.length > 0) {
  targetBuildModules = designatedModule;
}
targetBuildModules.forEach(function(name) {
  if (name !== '.git' && name !== 'build' && name !== 'publicMethods' && name !== 'dist') {
    shell.cd(`${modulesPath}/${name}`);
    console.log(`\r\n>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\r\nStart Install：${modulesPath}\\${name}`);
    shell.exec('cnpm install');
    // shell.exec('npm install --registry=https://registry.npm.taobao.org');
  }
});
