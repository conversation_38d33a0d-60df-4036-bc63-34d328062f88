/*
 * @Author: 系统重构
 * @Date: 2024-01-01
 * @Description: 防护用品产品表 - 完全平化设计
 * 替代原有的protectiveSuppliesList嵌套结构
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const protectiveProductSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },

    // 基础信息
    EnterpriseID: {
      type: String,
      required: true,
      index: true,
    },
    warehouseId: {
      type: String,
      required: true,
      index: true,
    },

    // 分类关联（与protectionCategory直接关联）
    categoryId: {
      type: String,
      ref: 'protectionCategory',
      index: true,
    },
    categoryPath: String, // 冗余字段，便于快速查询和显示
    categoryPathIds: [ String ], // 分类路径ID数组
    categoryName: String, // 冗余字段，便于显示

    // 产品基础信息
    materialCode: {
      type: String,
      index: true,
      trim: true,
    }, // 物料编码
    product: {
      type: String,
      required: true,
      trim: true,
    }, // 产品名称
    productSpec: {
      type: String,
      trim: true,
    }, // 产品规格
    modelNumber: {
      type: String,
      trim: true,
    }, // 型号
    style: {
      type: String,
      trim: true,
    }, // 样式

    // 防护属性
    protectionType: {
      type: String,
      trim: true,
    }, // 防护类型
    function: {
      type: String,
      trim: true,
    }, // 防护用途
    harmFactors: [{
      type: String,
      trim: true,
    }], // 危害因素数组

    // 技术参数
    APF: {
      type: String,
      trim: true,
    }, // APF值
    NRR: {
      type: String,
      trim: true,
    }, // NRR（dB）
    SNR: {
      type: String,
      trim: true,
    }, // SNR（dB）
    useMethod: {
      type: String,
      trim: true,
    }, // 使用方式
    accessoryInfo: {
      type: String,
      trim: true,
    }, // 可配滤盒数
    screenThickness: {
      type: String,
      trim: true,
    }, // 面屏厚度

    // 供应商信息
    vender: {
      type: String,
      trim: true,
    }, // 厂家
    characteristic: {
      type: String,
      trim: true,
    }, // 特点
    industryEnvironment: {
      type: String,
      trim: true,
    }, // 使用行业或环境
    packing: {
      type: String,
      trim: true,
    }, // 包装

    // 库存和状态
    surplus: {
      type: Number,
      default: 0,
      min: 0,
    }, // 库存数量
    isActive: {
      type: Boolean,
      default: true,
    }, // 是否启用

    // 有效期管理
    hasExpiry: {
      type: Boolean,
      default: false,
    }, // 是否有有效期
    expiryPeriod: {
      type: Number,
      min: 1,
    }, // 有效期长度
    expiryUnit: {
      type: String,
      enum: [ 'days', 'months', 'years' ],
      default: 'days',
    }, // 有效期单位
    needProductionDate: {
      type: Boolean,
      default: false,
    }, // 是否需要记录生产日期

    // 媒体文件
    picture: {
      type: String,
      trim: true,
    }, // 图片文件路径

    // 自定义扩展属性（支持动态字段）
    customAttributes: [{
      key: {
        type: String,
        required: true,
        trim: true,
      }, // 属性键（如：filterType, protectionLevel）
      value: Schema.Types.Mixed, // 属性值（支持任意类型）
      label: {
        type: String,
        trim: true,
      }, // 显示标签
      dataType: {
        type: String,
        enum: [ 'string', 'number', 'boolean', 'array', 'object' ],
        default: 'string',
      }, // 数据类型
      required: {
        type: Boolean,
        default: false,
      }, // 是否必填
      options: [ String ], // 可选值列表（用于下拉选择）
      unit: {
        type: String,
        trim: true,
      }, // 单位
      description: {
        type: String,
        trim: true,
      }, // 属性描述
    }],

    // 扩展字段
    remark: {
      type: String,
      trim: true,
    }, // 备注

    // 版本控制
    version: {
      type: Number,
      default: 1,
    }, // 数据版本号

  }, {
    timestamps: true,
    // 启用版本控制
    versionKey: '__v',
  });

  // 复合索引 - 企业和仓库查询（最常用的查询组合）
  protectiveProductSchema.index({ EnterpriseID: 1, warehouseId: 1 });

  // 分类查询索引
  protectiveProductSchema.index({ EnterpriseID: 1, categoryId: 1 });

  // 物料编码唯一索引（同一企业同一仓库内物料编码唯一）
  protectiveProductSchema.index({
    EnterpriseID: 1,
    warehouseId: 1,
    materialCode: 1,
  }, {
    unique: true,
    sparse: true,
    name: 'unique_material_code',
  });

  // 产品名称和型号组合索引（用于产品查找）
  protectiveProductSchema.index({
    EnterpriseID: 1,
    product: 1,
    modelNumber: 1,
  });

  // 仓库内产品重复检查索引（用于导入时的重复校验）
  protectiveProductSchema.index({
    EnterpriseID: 1,
    warehouseId: 1,
    product: 1,
    modelNumber: 1,
  }, {
    name: 'warehouse_product_duplicate_check',
  });

  // 危害因素查询索引（用于根据危害因素查找防护用品）
  protectiveProductSchema.index({
    EnterpriseID: 1,
    harmFactors: 1,
  });

  // 状态查询索引（用于查询启用的产品）
  protectiveProductSchema.index({
    EnterpriseID: 1,
    warehouseId: 1,
    isActive: 1,
  });

  // 分类路径查询索引
  protectiveProductSchema.index({
    EnterpriseID: 1,
    categoryPath: 1,
  });

  // 虚拟字段 - 完整的产品标识
  protectiveProductSchema.virtual('fullProductName').get(function() {
    const parts = [ this.product ];
    if (this.modelNumber) parts.push(this.modelNumber);
    if (this.productSpec) parts.push(this.productSpec);
    return parts.join(' - ');
  });

  // 中间件：保存前数据验证和处理
  protectiveProductSchema.pre('save', function(next) {
    // 确保产品名称不为空
    if (!this.product || this.product.trim() === '') {
      return next(new Error('产品名称不能为空'));
    }

    // 自动生成物料编码（如果没有提供）
    if (!this.materialCode && this.product) {
      // 简单的编码生成规则：产品名称首字母 + 时间戳后6位
      const productCode = this.product.charAt(0).toUpperCase();
      const timestamp = Date.now().toString().slice(-6);
      this.materialCode = `${productCode}${timestamp}`;
    }

    next();
  });

  // 静态方法：根据分类查找产品
  protectiveProductSchema.statics.findByCategory = function(enterpriseId, categoryId, warehouseId) {
    return this.find({
      EnterpriseID: enterpriseId,
      warehouseId,
      categoryId,
      isActive: true,
    }).sort({ product: 1, modelNumber: 1 });
  };

  // 静态方法：根据危害因素查找产品
  protectiveProductSchema.statics.findByHarmFactors = function(enterpriseId, harmFactors, warehouseId) {
    return this.find({
      EnterpriseID: enterpriseId,
      warehouseId,
      harmFactors: { $in: harmFactors },
      isActive: true,
    }).sort({ categoryPath: 1, product: 1 });
  };

  return mongoose.model('protectiveProduct', protectiveProductSchema);
};
