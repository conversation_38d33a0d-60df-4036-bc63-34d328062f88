// 定时任务 同步福州数据
module.exports = app => {
  class FzData extends app.Subscription {
    static get schedule() {
      return {
        // 每天凌晨2点执行一次
        cron: '0 0 2 * * *',
        disable: !(app.config.branch === 'fz' && app.config.isGetFZData),
        immediate: true, // 启动服务时就立即执行一次任务
        type: 'worker', // 指定每次只有一个 随机的worker 执行
      };
    } // 定时执行的任务
    async subscribe() {
      try {
        console.log('FZDATA定时任务开始');
        console.time('FZDATA定时任务耗时');
        await this.ctx.service.fzDataFix.getALLZipFromApi();

        console.timeEnd('FZDATA定时任务耗时');
      } catch (error) {
        this.ctx.auditLog('同步福州数据失败', JSON.stringify(error), 'error');
      }
    }
  }
  return FzData;
};
