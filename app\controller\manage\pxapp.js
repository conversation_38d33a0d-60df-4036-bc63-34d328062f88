// const _ = require('lodash');
const Controller = require('egg').Controller;
const fs = require('fs');
const path = require('path');

class PxappController extends Controller {
  // 获取培训机构列表
  async getPxOrgList() {
    const { ctx } = this;
    try {
      const params = ctx.query;
      // console.log(11111111, params)
      const res = await ctx.service.pxapp.getPxOrgList(params);
      ctx.helper.renderSuccess(ctx, {
        message: '培训机构列表获取成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error.message,
      });
    }
  }

  // 获取首页培训记录列表
  async getHomePxRecords() {
    const { ctx } = this;
    try {
      const params = ctx.query;
      const res = await ctx.service.pxapp.getHomePxRecords(params);
      ctx.helper.renderSuccess(ctx, {
        message: '培训记录列表获取成功',
        data: res,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }

  }

  // 获取培训记录列表
  async getPxRecords() {
    const { ctx } = this;
    try {
      const params = ctx.query;
      const res = await ctx.service.pxapp.getPxRecords(params);
      ctx.helper.renderSuccess(ctx, {
        message: '培训记录列表获取成功',
        data: res,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }

  }
  async getPxDetail() {
    const { ctx } = this;
    try {
      const params = ctx.query;
      console.log(params);
      const res = await ctx.service.pxapp.getPxDetail(params);
      ctx.helper.renderSuccess(ctx, {
        message: '培训记录详情获取成功',
        data: res,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }

  }
  // 获取个人培训统计数据
  async getMyStatistics() {
    const { ctx } = this;
    try {
      const res = await ctx.service.pxapp.getMyStatistics();
      ctx.helper.renderSuccess(ctx, {
        message: '培训统计数据获取成功',
        data: res,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }

  // 获取培训班信息
  async getTrainingClass() {
    const { ctx } = this;
    try {
      const params = ctx.request.body;
      const res = await ctx.service.pxapp.getTrainingClass(params);
      ctx.helper.renderSuccess(ctx, {
        message: '培训班列表获取成功',
        data: res,
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }

  }

  // 获取培训班信息
  async getCertificate() {
    // 读取 abc.text 文件的内容
    const filePath = path.join(
      this.config.baseDir,
      'app/public/certificate/sinaold5ty1720688678987.pdf'
    );
    const content = fs.readFileSync(filePath, 'utf8');
    this.ctx.set('Content-Type', 'application/pdf');
    this.ctx.set('Content-Disposition', 'attachment; filename=sinaold5ty1720688678987.pdf');
    this.ctx.body = content;
    // const { ctx } = this;
    // try {
    //   const params = ctx.query;
    //   console.log(params, 'paramsparams')
    //   const res = await ctx.service.pxapp.getCertificate(params);
    //   ctx.helper.renderSuccess(ctx, {
    //     message: '培训证书获取成功',
    //     data: res,
    //   });
    // } catch (error) {
    //   console.log(error);
    //   ctx.helper.renderFail(ctx, {
    //     message: error,
    //   });
    // }
  }
}

module.exports = PxappController;
