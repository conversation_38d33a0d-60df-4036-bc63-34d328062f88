
const Service = require('egg').Service;
// 员工培训
class EmployeeTrainingService extends Service {
  async getList(data) {
    const { ctx } = this;
    // 获取列表
    const query = {
      EnterpriseID: data.EnterpriseID,
      employees: data.employeesId,
    };
    if (data.keyWord) query.name = { $regex: data.keyWord };
    const res = await ctx.model.EmployeesTrainingPlan.find(query)
      .sort({ createdAt: -1 })
      .skip((data.pageCurrent - 1) * data.size)
      .limit(data.size);

    // 查询培训状态
    // console.log(2222, res);
    const newRes = JSON.parse(JSON.stringify(res));
    const len = res.length;
    for (let i = 0; i < len; i++) {
      const personalTrainingDetail = await this.ctx.model.PersonalTraining.findOne({
        employeesTrainingPlanId: res[i]._id,
        employeesId: data.employeesId,
        EnterpriseID: data.EnterpriseID,
      },
      { completeState: 1 });
      // 0 是未开始  1 是进行中  2 是已完成
      newRes[i].personalTrainingStatus = !personalTrainingDetail ? 0 : personalTrainingDetail.completeState ? 2 : 1;
      newRes[i].personalTrainingId = personalTrainingDetail ? personalTrainingDetail._id : '';

      // 查询培训中首个课程的详情
      if (data.size <= 4 && res[i].coursesID && res[i].coursesID[0]) {
        newRes[i].firstCourseDetail = await this.ctx.model.Courses.findOne(
          { _id: res[i].coursesID[0] },
          { cover: 1, labels: 1, views: 1, likes: 1, commentLength: 1 }
        );
      }

    }

    // 获取分页信息
    const pageInfo = await this.getPageInfo('EmployeesTrainingPlan', data.size, data.pageCurrent, query);
    return {
      res: newRes,
      pageInfo,
    };
  }
  // 分页统计
  async getPageInfo(collection, size, pageCurrent, query = {}) {
    const total = await this.ctx.model[collection].find(query).count();
    const pageInfo = {
      total,
      size,
      pageCurrent,
    };
    return pageInfo;
  }


}

module.exports = EmployeeTrainingService;
