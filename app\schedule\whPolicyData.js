module.exports = app => {
  return {
    schedule: app.config.whPolicyData,

    async task(ctx) {
      try {
        ctx.auditLog('处理万华amp权限管理组织权限范围定时任务开始', '', 'info');
        await ctx.service.whData.processOrgExistInfo();

        ctx.auditLog('处理万华amp权限管理组织权限范围定时任务完成', '', 'info');
      } catch (error) {
        ctx.auditLog(
          '处理万华amp权限管理组织权限范围定时任务失败',
          error.message,
          'error'
        );
        throw error;
      }
    },
  };
};
