'use strict';

const { parentPort } = require('worker_threads');
const fs = require('fs');

// 初始化加密工具
let EncryptUtil;
try {
  EncryptUtil = require('../utils/encryptUtil');
} catch (error) {
  console.error('加载加密工具失败:', error.message);
}

const encryptionKey = 'chiscdc@bhkdownload#$%^';

/**
 * Worker线程：处理单个福州数据文件
 */
class FzDataWorker {

  /**
   * 从zip文件中获取并解密数据
   * @param {string} filePath - zip文件的完整路径
   * @return {Promise<Object|null>} 解析后的JSON内容，失败时返回null
   */
  async getOldDataFromZip(filePath) {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
      }

      const zipContent = fs.readFileSync(filePath);
      const JSZip = require('jszip');
      const zip = new JSZip();
      const zipData = await zip.loadAsync(zipContent);

      const data = zipData.files['data.json'];
      if (!data) {
        throw new Error(`zip文件中不存在data.json: ${filePath}`);
      }

      const content = await data.async('string');
      const decryptContent = EncryptUtil.decrypt(content, encryptionKey);
      const jsonContent = JSON.parse(decryptContent);

      return jsonContent;
    } catch (error) {
      throw new Error(`从zip文件获取数据失败: ${filePath}, 错误: ${error.message}`);
    }
  }

  /**
   * 分析数据类型和基本信息
   * @param {Object} decryptedData - 解密后的数据
   * @param {string} taskType - 任务类型
   * @return {Object} 数据分析结果
   */
  analyzeData(decryptedData, taskType) {
    if (!decryptedData || typeof decryptedData !== 'object') {
      return {
        isValid: false,
        error: '数据格式错误：非对象类型',
        dataType: typeof decryptedData,
      };
    }

    const analysis = {
      isValid: true,
      dataType: Array.isArray(decryptedData) ? 'array' : 'object',
      keys: Array.isArray(decryptedData) ? [ 'array' ] : Object.keys(decryptedData),
    };

    // 检查特殊响应格式
    if (decryptedData.mess && decryptedData.type) {
      if (decryptedData.mess === '未查询到相关数据！' && decryptedData.type === '03') {
        analysis.specialResponse = 'no_data';
        analysis.responseMessage = decryptedData.mess;
        analysis.responseType = decryptedData.type;
        return analysis;
      }

      // 修复：对所有任务类型都检查type "02"
      if (decryptedData.mess === '认证TokenId无效，请重新获取TokenId！' && decryptedData.type === '02') {
        analysis.specialResponse = 'invalid_token';
        analysis.responseMessage = decryptedData.mess;
        analysis.responseType = decryptedData.type;
        return analysis;
      }
    }

    // 根据任务类型检查数据列表
    switch (taskType) {
      case 'company':
        if (decryptedData.crptList && Array.isArray(decryptedData.crptList)) {
          analysis.dataList = 'crptList';
          analysis.dataCount = decryptedData.crptList.length;
        } else if (decryptedData.ZoneCompanyList && Array.isArray(decryptedData.ZoneCompanyList)) {
          analysis.dataList = 'ZoneCompanyList';
          analysis.dataCount = decryptedData.ZoneCompanyList.length;
        } else {
          analysis.isValid = false;
          analysis.error = '企业数据列表不存在或格式错误';
        }
        break;

      case 'healthCheck':
        if (decryptedData.bhkList && Array.isArray(decryptedData.bhkList)) {
          analysis.dataList = 'bhkList';
          analysis.dataCount = decryptedData.bhkList.length;
        } else {
          analysis.isValid = false;
          analysis.error = '体检数据列表不存在或格式错误';
        }
        break;

      case 'diagnosis':
        if (decryptedData.occdiscaseList && Array.isArray(decryptedData.occdiscaseList)) {
          analysis.dataList = 'occdiscaseList';
          analysis.dataCount = decryptedData.occdiscaseList.length;
        } else {
          analysis.isValid = false;
          analysis.error = '诊断数据列表不存在或格式错误';
        }
        break;

      default:
        analysis.isValid = false;
        analysis.error = `未知的任务类型: ${taskType}`;
    }

    return analysis;
  }

  /**
   * 处理单个文件
   * @param {Object} task - 任务信息
   * @return {Promise<Object>} 处理结果
   */
  async processFile(task) {
    const startTime = Date.now();

    try {
      const { filePath, taskType, fileName } = task;

      // 解密文件
      const decryptedData = await this.getOldDataFromZip(filePath);

      // 分析数据
      const analysis = this.analyzeData(decryptedData, taskType);

      const duration = Date.now() - startTime;

      // 检查是否是特殊响应（需要跳过处理）
      if (analysis.specialResponse) {
        return {
          status: 'skipped', // 明确的跳过状态
          success: false, // 保留向后兼容性
          skipped: true, // 保留向后兼容性
          skipReason: analysis.specialResponse,
          fileName,
          filePath,
          taskType,
          message: `跳过处理: ${analysis.responseMessage}`,
          analysis,
          decryptedData: analysis.specialResponse ? decryptedData : null,
          performance: {
            duration,
            fileSize: this.getFileSize(filePath),
          },
        };
      }

      // 正常处理结果
      const hasData = analysis.dataCount > 0;
      let status;
      let message;

      if (hasData) {
        status = 'success';
        message = `处理成功: ${analysis.dataCount} 条记录`;
      } else {
        status = 'failed';
        message = '没有有效数据需要处理';
      }

      return {
        status, // 明确的状态字段：'success', 'failed'
        success: hasData, // 保留向后兼容性
        skipped: false, // 保留向后兼容性
        fileName,
        filePath,
        taskType,
        message,
        analysis,
        decryptedData,
        performance: {
          duration,
          fileSize: this.getFileSize(filePath),
        },
      };

    } catch (error) {
      const duration = Date.now() - startTime;

      return {
        status: 'failed',
        success: false,
        skipped: false,
        fileName: task.fileName,
        filePath: task.filePath,
        taskType: task.taskType,
        error: error.message,
        performance: {
          duration,
          fileSize: this.getFileSize(task.filePath),
        },
      };
    }
  }

  /**
   * 获取数据预览
   * @param {Object} data - 数据对象
   * @param {string} listKey - 数据列表的键名
   * @return {Array} 预览数据
   */
  getDataPreview(data, listKey) {
    try {
      const list = data[listKey];
      if (Array.isArray(list) && list.length > 0) {
        return list.slice(0, 3);
      }
      return [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 获取文件大小
   * @param {string} filePath - 文件路径
   * @return {number} 文件大小（字节）
   */
  getFileSize(filePath) {
    try {
      const stats = fs.statSync(filePath);
      return stats.size;
    } catch (error) {
      return 0;
    }
  }
}

// Worker线程主逻辑
const worker = new FzDataWorker();

parentPort.on('message', async task => {
  try {
    const result = await worker.processFile(task);
    parentPort.postMessage({ success: true, result });
  } catch (error) {
    parentPort.postMessage({
      success: false,
      error: error.message,
      task: task.fileName || 'unknown',
    });
  }
});

// 处理未捕获的异常
process.on('uncaughtException', error => {
  parentPort.postMessage({
    success: false,
    error: `Worker未捕获异常: ${error.message}`,
    fatal: true,
  });
  process.exit(1);
});

process.on('unhandledRejection', reason => {
  parentPort.postMessage({
    success: false,
    error: `Worker未处理的Promise拒绝: ${reason}`,
    fatal: true,
  });
  process.exit(1);
});
