const {
  encrypt,
  decrypt,
  updateEncryption,
  processUpdate,
  splitAndEncrypt,
  processUpdateHmac,
} = require('./encryption');

function encryptionPlugin(schema, options) {
  const { fields, model = '', ctx } = options;
  const fieldsArr = Object.keys(fields);

  schema.pre('save', async function(next) {
    if (this.isModified() && !this.isProcessed) {
      await processUpdate(this, fieldsArr, model);
    }
    this.encryptionAlgorithm = process.env.ENCRYPTION_ALGORITHM;
    next();
  });
  schema.post('save', async function(doc, next) {
    if (!doc.isProcessed) {
      doc.isProcessed = true; // 自定义标志，避免递归
      if (doc._id && doc.group) {
        const conditions = { _id: doc._id };
        // const docToUpdate = await this.constructor
        //   .findOne(conditions)
        //   .populate('group');
        await doc.populate('group').execPopulate();
        await processUpdateHmac(doc, model, conditions, doc.group);
        ctx.auditLog(
          '保存文档进入hamc计算,当前用户信息',
          JSON.stringify(doc),
          'info'
        );

        // 保存处理后的文档
        await doc.save();
      }
    }
    next();
  });
  schema.post([ 'find', 'findOne' ], async function(docs, next) {
    const options = this.getOptions();
    const { returnOptions = {}, specialFields = {} } = options;

    if (
      !Object.keys(returnOptions).length &&
      !Object.keys(specialFields).length
    ) {
      return next();
    }
    if (!docs) return next();

    docs = Array.isArray(docs) ? docs : [ docs ];

    for (const doc of docs) {
      for (const field of fieldsArr) {
        const returnPlaintext =
          returnOptions[field] && returnOptions[field].returnPlaintext;

        if (returnPlaintext && doc.get(`${field}ForStore`)) {
          const decryptedValue = await decrypt(doc.get(`${field}ForStore`));
          doc.set(field, decryptedValue); // 使用 set() 方法
          doc.set(`${field}ForStore`, undefined); // 使用 undefined 来删除字段
        }

        if (model === 'SuperUser' && Object.keys(specialFields).length) {
          for (const key in specialFields) {
            if (Array.isArray(doc.get(key))) {
              for (let i = 0; i < doc.get(key).length; i++) {
                const item = doc.get(`${key}.${i}`);
                if (item && item[`${field}ForStore`]) {
                  const decryptedValue = await decrypt(
                    item[`${field}ForStore`]
                  );
                  doc.set(`${key}.${i}.${field}`, decryptedValue); // 解密后赋值
                  doc.set(`${key}.${i}.${field}ForStore`, undefined); // 删除字段
                }
              }
            }
          }
        }
      }
    }

    next();
  });
  schema.pre('findOneAndUpdate', async function(next) {
    const update = this.getUpdate();
    const conditions = this.getQuery();
    const docToUpdate = await this.model.findOne(conditions).populate('group');
    if (update.$set) {
      await processUpdate(update.$set, fieldsArr, model);
      if (docToUpdate && docToUpdate.group) {
        await processUpdateHmac(
          update.$set,
          model,
          conditions,
          docToUpdate.group
        );
      }
    } else {
      await processUpdate(update, fieldsArr, model);
      if (docToUpdate && docToUpdate.group) {
        await processUpdateHmac(
          update,
          model,
          conditions,
          docToUpdate.group
        );
      }
    }
    next();
  });

  schema.pre('updateOne', async function(next) {
    const update = this.getUpdate();
    const conditions = this.getQuery();
    const docToUpdate = await this.model.findOne(conditions).populate('group');
    if (update.$set) {
      await processUpdate(update.$set, fieldsArr, model);
      if (docToUpdate && docToUpdate.group) {
        await processUpdateHmac(
          update.$set,
          model,
          conditions,
          docToUpdate.group
        );
      }
    } else {
      await processUpdate(update, fieldsArr, model);
      if (docToUpdate && docToUpdate.group) {
        await processUpdateHmac(
          update,
          model,
          conditions,
          docToUpdate.group
        );
      }
    }
    next();
  });

  schema.pre('countDocuments', async function(next) {
    const conditions = this.getQuery();
    await handleEncryptedFields(conditions);
    this.setQuery(conditions);
    next();
  });

  schema.pre('find', async function(next) {
    const conditions = this.getQuery();
    await handleEncryptedFields(conditions, ctx);
    this.setQuery(conditions);
    next();
  });
  schema.pre('findOne', async function(next) {
    const conditions = this.getQuery();
    await handleEncryptedFields(conditions, ctx);
    this.setQuery(conditions);
    next();
  });
  schema.pre('aggregate', async function(next) {
    await handleAggregateEncryptedFields.call(this, ctx);
    next();
  });
  async function handleEncryptedFields(conditions) {
    for (const key in conditions) {
      if (conditions.hasOwnProperty(key)) {
        const value = conditions[key];
        if (!value) {
          continue;
        }

        if (key in fields) {
          // 当前字段需要加密
          if (
            typeof value === 'object' &&
            value !== null &&
            !Array.isArray(value)
          ) {
            // 处理嵌套对象
            if (value instanceof RegExp) {
              // conditions[`${key}ForStore`] = encrypt(value.source);
              const encryptAes = await encryptValue(
                value.source,
                fields[key],
                key
              );
              if (conditions.$or) {
                conditions.$or = conditions.$or.concat(encryptAes);
              } else {
                conditions.$or = encryptAes;
              }
            } else {
              for (const subKey in value) {
                if (value.hasOwnProperty(subKey)) {
                  if (subKey === '$in') {
                    // 处理 $in 操作符
                    const encryptAes = [];
                    for (let i = 0; i < value[subKey].length; i++) {
                      const encryptedValues = await encryptValue(
                        value[subKey][i],
                        fields[key],
                        key
                      );
                      for (let j = 0; j < encryptedValues.length; j++) {
                        encryptAes.push(encryptedValues[j]);
                      }
                    }
                    if (conditions.$or) {
                      conditions.$or = conditions.$or.concat(encryptAes);
                    } else {
                      conditions.$or = encryptAes;
                    }
                  } else if (subKey === '$regex') {
                    // 处理 $regex 操作符
                    const encryptAes = await encryptValue(
                      value[subKey],
                      fields[key],
                      key
                    );
                    if (conditions.$or) {
                      conditions.$or = conditions.$or.concat(encryptAes);
                    } else {
                      conditions.$or = encryptAes;
                    }
                  } else {
                    // 递归处理其他嵌套对象
                    await handleEncryptedFields(value);
                  }
                }
              }
            }
          } else if (Array.isArray(value)) {
            // conditions[`${key}ForStore`] = value.map(val => encrypt(val));
          } else {
            // 处理单个值
            // conditions[`${fields[key]}ForStore`] = encrypt(value);
            const encryptAes = await encryptValue(value, fields[key], key);
            if (conditions.$or) {
              conditions.$or = conditions.$or.concat(encryptAes);
            } else {
              conditions.$or = encryptAes;
            }
          }
          delete conditions[key];
        } else if (typeof value === 'object' && value !== null) {
          // 递归处理非加密字段的嵌套对象
          await handleEncryptedFields(value);
        }
      }
    }
    ctx.auditLog('重构查询参数', JSON.stringify(conditions), 'info');
  }

  // 处理聚合管道的加密字段
  async function handleAggregateEncryptedFields(ctx) {
    const pipeline = this.pipeline();

    for (const stage of pipeline) {
      if (stage.$match) {
        await processMatchStage(stage.$match);
      }
    }

    ctx.auditLog('聚合重构match查询参数', JSON.stringify(pipeline), 'info');
  }

  // 加密值并返回加密条件
  async function encryptValue(value, expectedLength, field) {
    const encryptedConditions = [];
    const algorithms = [ 'aes-256-gcm', 'GM' ];

    if (process.env.ALLOW_FZGM === 'true') {
      algorithms.push('FZGM');
    }

    for (const algorithm of algorithms) {
      let condition;
      if (value.length === expectedLength) {
        condition = await encrypt(value, algorithm);
        encryptedConditions.push({
          [`${field}ForStore`]: condition,
        });
      } else {
        condition = await splitAndEncrypt(value, algorithm);
        encryptedConditions.push({
          [`${field}SplitEncrypted`]: { $regex: condition },
        });
      }
    }

    return encryptedConditions;
  }

  // 处理 $match 阶段
  async function processMatchStage(matchStage) {
    for (const field in fields) {
      if (matchStage[field]) {
        const value = matchStage[field];
        let encryptedConditions;

        if (value && value.$regex) {
          if (typeof value.$regex === 'string') {
            encryptedConditions = await encryptValue(
              value.$regex,
              fields[field],
              field
            );
            matchStage.$or = (matchStage.$or || []).concat(encryptedConditions);
          } else if (value.$regex instanceof RegExp) {
            const regexSource = value.$regex.source;
            const encryptedRegexSource = await encryptValue(
              regexSource,
              fields[field],
              field
            );
            matchStage.$or = (matchStage.$or || []).concat({
              [field]: {
                $regex: encryptedRegexSource,
              },
            });
          }
        } else if (value && value.$in && Array.isArray(value.$in)) {
          encryptedConditions = [];
          for (const item of value.$in) {
            const encryptedValues = await encryptValue(
              item,
              fields[field],
              field
            );
            encryptedConditions.push(...encryptedValues);
          }
          matchStage.$or = (matchStage.$or || []).concat({
            $or: encryptedConditions,
          });
        } else {
          encryptedConditions = await encryptValue(value, fields[field], field);
          matchStage.$or = (matchStage.$or || []).concat(encryptedConditions);
        }

        delete matchStage[field];
      }
    }

    // 处理 $or、$and 和 $nor 子条件
    async function processSubConditions(subConditions) {
      for (const condition of subConditions) {
        if (condition.$or) {
          await processSubConditions(condition.$or);
        }
        if (condition.$and) {
          await processSubConditions(condition.$and);
        }
        if (condition.$nor) {
          await processSubConditions(condition.$nor);
        }

        const keys = Object.keys(condition);
        for (const key of keys) {
          if (
            ![ '$or', '$and', '$nor' ].includes(key) &&
            condition[key] &&
            typeof condition[key] === 'object'
          ) {
            await processMatchStage(condition);
            break;
          }
        }
      }
    }

    if (matchStage.$or) {
      await processSubConditions(matchStage.$or);
    }
    if (matchStage.$and) {
      await processSubConditions(matchStage.$and);
    }
    if (matchStage.$nor) {
      await processSubConditions(matchStage.$nor);
    }
  }

  schema.methods.decryptField = async function(field, extraParams) {
    const algorithm = this.encryptionAlgorithm;
    const newAlgorithm = process.env.ENCRYPTION_ALGORITHM;

    if (algorithm !== newAlgorithm) {
      for (const field in fields) {
        if (this[field] && this[`${field}ForStore`]) {
          const { updatedData, splitEncryptedData } = await updateEncryption(
            this[`${field}ForStore`],
            algorithm,
            newAlgorithm
          );
          this[`${field}ForStore`] = updatedData;
          this[`${field}SplitEncrypted`] = splitEncryptedData;
        }
      }
      this.encryptionAlgorithm = newAlgorithm;
      const updateData = {
        encryptionAlgorithm: newAlgorithm,
        ...Object.keys(fields).reduce((updates, f) => {
          if (this[`${f}ForStore`]) {
            updates[`${f}ForStore`] = this[`${f}ForStore`];
            updates[`${f}SplitEncrypted`] = this[`${f}SplitEncrypted`];
          }
          return updates;
        }, {}),
      };
      await this.constructor.updateOne(
        { _id: this._id },
        {
          $set: updateData,
        }
      );
    }
    if (field) {
      if (extraParams && extraParams.extraFields) {
        if (extraParams.rowId) {
          const decryptData = this[extraParams.extraFields].find(item => {
            return item._id === extraParams.rowId;
          });
          return await decrypt(
            decryptData[`${field}ForStore`],
            this.encryptionAlgorithm
          );
        }
      }
      return await decrypt(this[`${field}ForStore`], this.encryptionAlgorithm);
    }
    throw new Error(`字段 ${field} 未设置为可解密`);
  };
}

module.exports = encryptionPlugin;
