const path = require('path');
// const isDocker = process.env.BUILD_ENV === 'docker';
// const mongohost = isDocker ? 'mongodb' : 'mdb:27017';
// const mongobin = isDocker ? '' : '/Users/<USER>/Documents/frame/softs/mongodb/bin/';

module.exports = appInfo => {
  return {
    proxy: false,

    middleware: [ 'notfoundHandler', 'crossHeader', 'compress' ],
    mongoose: {
      client: {
        url: 'mongodb://mdb',
        options: {
          dbName: 'frameData',
        },
      },
      tools: {
        url: 'mongodb://mdb',
        options: {
          dbName: 'tools',
        },
      },
    },
    // mongodb相关路径
    mongodb: {
      binPath: '',
      backUpPath: path.join(appInfo.baseDir, 'databak/'),
    },
    redis: {
      client: {
        host: '**************',
        port: 31379,
        password: '',
        db: 0,
      },
    },
    rabbitMq: {
      url: 'amqp://su:<EMAIL>:5672',
      queues: [],
    },
    whDepartData: {
      // disable: process.env.whDepartData !== 'true',
      disable: true,
      immediate: true,
      interval: '20m',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    whPolicyData: {
      disable: true,
      immediate: true,
      interval: '1d',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    whBaseData: {
      // disable: process.env.whBaseData !== 'true',
      disable: true,
      immediate: true,
      interval: '60m',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
  };
};
