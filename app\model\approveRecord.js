/*
 * @Author: 黄婷婷
 * @Date: 2020-07-19 09:00
 * @LastEditors: 黄婷婷
 * @LastEditTime: 2022-07-27 15:52
 * @Description: 钉钉审批记录表 数据来源为钉钉接口，全流程相关人员可以修改部分信息
 * 相关人员修改逻辑：
 *     只有在审核完成的情况下才能修改
 *     修改时会添加一个审批记录类型为已修改审批记录(recordType:updated)的新的审批记录
 *     嵌套数组的id和原纪录保持一致（方便更新）
 *     原有钉钉审批记录不变
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const approveRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    recordType: {
      type: String,
      default: 'origin',
      enum: [ 'origin', 'updated' ],
    },
    title: { // 审批实例标题
      type: String,
      require: true,
    },
    create_time: { // 开始时间
      type: Date,
      require: true,
    },
    finish_time: { // 结束时间
      type: Date,
      require: true,
    },
    originator_userid: { // 发起人钉钉userid
      type: String,
      require: true,
    },
    originator_dept_id: { // 发起人部门id -1表示根部门
      type: String,
      require: true,
    },
    status: {
      /**
         * 审批状态：
         * NEW：新创建
         * RUNNING：审批中
         * TERMINATED：被终止
         * COMPLETED：完成
         * CANCELED：取消
         */
      type: String,
      require: true,
    },
    cc_userids: [
      { type: String },
    ], // 抄送人钉钉userid
    result: { // 审批结果
      type: String,
      require: true,
    },
    business_id: { // 审批实例业务编号。
      type: String,
    },
    operation_records: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      userid: { // 操作人id
        type: String,
        require: true,
      },
      serviceEmployeeId: String, // 员工id
      date: { // 操作时间
        type: String,
        require: true,
      },
      /**
         * 操作类型：
         * EXECUTE_TASK_NORMAL：正常执行任务
         * EXECUTE_TASK_AGENT：代理人执行任务
         * APPEND_TASK_BEFORE：前加签任务
         * APPEND_TASK_AFTER：后加签任务
         * REDIRECT_TASK：转交任务
         * START_PROCESS_INSTANCE：发起流程实例
         * TERMINATE_PROCESS_INSTANCE：终止(撤销)流程实例
         * FINISH_PROCESS_INSTANCE：结束流程实例
         * ADD_REMARK：添加评论
         * REDIRECT_PROCESS：审批退回
         * PROCESS_CC：抄送
         */
      operation_type: {
        type: String,
        require: true,
      },
      operation_result: { // 操作结果 AGREE：同意 REFUSE：拒绝 NONE
        type: String,
        require: true,
      },
      remark: { // 评论内容。审批操作附带评论时才返回该字段。
        type: String,
      },
    }],
    tasks: [{ // 任务列表
      _id: {
        type: String,
        default: shortid.generate,
      },
      userid: { // 任务处理人
        type: String,
        require: true,
      },
      /**
         * 任务状态：
         * NEW：未启动
         * RUNNING：处理中
         * PAUSED：暂停
         * CANCELED：取消
         * COMPLETED：完成
         * TERMINATED：终止
         */
      task_status: {
        type: String,
        require: true,
      },
      task_result: { // 结果：AGREE：同意 REFUSE：拒绝 REDIRECTED：转交
        type: String,
        require: true,
      },
      create_time: { // 开始时间
        type: Date,
        require: true,
      },
      finish_time: { // 结束时间
        type: Date,
        require: true,
      },
      taskid: { // 任务节点id
        type: String,
        require: true,
      },
      url: { // 任务url
        type: String,
        require: true,
      },
    }],
    originator_dept_name: { // 发起部门名称
      type: String,
    },
    /**
     * 审批实例业务动作：
     * MODIFY：表示该审批实例是基于原来的实例修改而来
     * REVOKE：表示该审批实例是由原来的实例撤销后重新发起的
     * NONE表示正常发起
     */
    biz_action: {
      type: String,
      require: true,
    },
    /**
     * 审批附属实例列表。
     * 当已经通过的审批实例被修改或撤销，
     * 会生成一个新的实例，作为原有审批实例的附属。
     * 如果想知道当前已经通过的审批实例的状态，可以依次遍历它的附属列表，查询里面每个实例的biz_action。
     */
    attached_process_instance_ids: [{
      type: String,
    }],
    form_component_values: [{ // 表单详情列表
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { // 标签名
        type: String,
        require: true,
      },
      value: { // 标签值
        type: String,
        require: true,
      },
      ext_value: { // 标签扩展值。
        type: String,
      },
      id: { // 组件ID
        type: String,
      },
      component_type: { // 组件类型
        type: String,
      },
    }],
    processInstanceId: { // 主流程实例标识。
      type: String,
      require: true,
    },
    processCode: String, // 审批流code
  }, { timestamps: true });
  return mongoose.model('approveRecord', approveRecordSchema, 'approveRecord');
};

