const Service = require('egg').Service;
// const shortid = require('shortid');
class VideoLibService extends Service {
  // 更细阿里云信息
  async updateALi(data) {
    const { ctx } = this;
    try {
      const {
        // 阿里云内
        VideoId,
        Title,
        Description,
        // 数据库
        author,
        classHours,
      } = data;
      // 更新阿里云
      const res = await ctx.helper.request_alivod('UpdateVideoInfo', {
        VideoId,
        Title,
        Description,
      }, {});
      // 先拿以前的数据
      const oldLocalVideo = await ctx.model.VideoInfos.findOne({
        VideoId,
      });
      // 更新数据
      await ctx.model.VideoInfos.updateOne({
        VideoId,
      }, {
        $set: {
          name: Title,
          author,
          classHours,
        },
      });
      // 更新课程列表所存数据
      if (classHours !== oldLocalVideo.classHours) {
        await ctx.model.Courses.update({
          videoInfos: {
            $in: [ oldLocalVideo._id ],
          },
        }, {
          $inc: {
            classHours: Number(classHours) - Number(oldLocalVideo.classHours),
          },
        });
      }
      await this.sleep(4000);// 因为阿里云更新数据有延时，为了防止前端获取不到最新列表信息
      return res;

    } catch (error) {
      this.ctx.logger.error(error);
      console.error(error);
    }

  }
  // 延迟
  sleep(seconds) {
    return new Promise(resolve => setTimeout(resolve, seconds));
  }
  // 删除阿里视频-同步删除数据库，解绑绑定课程
  async delALi(VideoIds) {
    const { ctx } = this;
    try {
      const localVideo = await this.videoAndCourse(VideoIds);
      const videoId = localVideo._id;
      const courses = localVideo.coursesInfo;
      const courseSuccess = [];
      let trainCount = 0;
      let canDelFlag = true;
      for (let i = 0; i < courses.length; i++) { // 遍历所绑定所有课程
        let sortIndex = -1;
        let index = -1;
        const course = courses[i];
        const adminPlanCount = await ctx.model.AdminTraining.countDocuments({
          coursesID: {
            $in: [ course._id ],
          },
        });
        const employeePlanCount = await ctx.model.EmployeesTrainingPlan.countDocuments({
          coursesID: {
            $in: [ course._id ],
          },
        });
        trainCount += (adminPlanCount + employeePlanCount);
        if (trainCount > 0) {
          canDelFlag = false;
          break;
        }
        for (let j = 0; j < course.videoInfos.length; j++) { // 遍历课程内所绑定的所有videoid
          if (course.videoInfos[j] === videoId) {
            index = j;
            break;
          }
        }
        for (let n = 0; n < course.sort.length; n++) {
          if (course.sort[n].ID === videoId && course.sort[n].contentType === 'videoInfos') {
            sortIndex = n;
            break;
          }
        }
        if (index >= 0) {
          try {
            course.videoInfos.splice(index, 1);
            course.sort.splice(sortIndex, 1);
            const backData = await ctx.model.Courses.updateOne({
              _id: course._id,
            }, {
              $set: {
                sort: course.sort,
                videoInfos: course.videoInfos,
              },
              $inc: {
                classHours: (0 - Number(localVideo.classHours)),
              },
            });
            console.log(727, backData);
            courseSuccess.push({ id: course._id, name: course.name });
            await this.ctx.model.VideoInfos.deleteOne({
              _id: videoId,
            });
          } catch (response) {
            console.error(response);
            ctx.helper.renderFail(ctx, {
              message: '视频删除失败',
            });
            return;
          }
        }
      }
      if (canDelFlag) {
        const res = await ctx.helper.request_alivod('DeleteVideo', {
          VideoIds,
        }, {});
        res.courseSuccess = courseSuccess;
        this.ctx.auditLog('删除阿里视频成功', `${VideoIds}`, 'info');
        await this.sleep(4000);
        return res;
      }
      return {
        data: {
          Message: '该视频已绑定培训计划，禁止删除',
        },
      };

    } catch (error) {
      this.ctx.auditLog('删除阿里视频出错', `${VideoIds}`, 'info');
      this.ctx.logger.error(error);
      console.error(error);
    }
  }
  // 获取本地库列表
  async getList(data) {
    const { ctx } = this;
    if (data.keyWord) data.keyWord = data.keyWord.trim();
    const reg = new RegExp(data.keyWord); // 不区分大小写
    const query = {
      $or: [ // 多条件，数组
        { name: { $regex: reg } },
        { author: { $regex: reg } },
      ],
    };
    const res = await ctx.model.VideoInfos.aggregate([
      {
        $lookup: {
          from: 'Courses',
          localField: '_id',
          foreignField: 'videoInfos',
          as: 'coursesInfo',
        },
      },
      {
        $match: {
          $or: [ // 多条件，数组
            { name: { $regex: reg } },
            { _id: { $regex: reg } },
            { author: { $regex: reg } },
          ],
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
      {
        $skip: (data.pageCurrent - 1) * data.size,

      },
      {
        $limit: data.size,
      },
    ]);
    const resData = JSON.parse(JSON.stringify(res));
    // 获取阿里云内数据
    for (const item of resData) {
      const Video = await ctx.helper.request_alivod('GetVideoInfo', {
        VideoId: item.VideoId,
      }, {});
      item.aliInfo = Video.Video || [];
    }
    const pageInfo = await this.getPageInfo('VideoInfos', data.size, data.pageCurrent, query);
    return {
      data: resData,
      pageInfo,
    };
  }
  // 获取阿里库列表
  async getALiList(data) {
    const { ctx } = this;
    let searchStr = '';
    data.keyWord = data.keyWord.trim();
    if (data.CateId && !data.keyWord) { // 只查分类
      searchStr = 'CateId = ' + data.CateId;
    } else if (!data.CateId && data.keyWord) { // 只查名字
      searchStr = "(Title = '" + data.keyWord + "' or VideoId = '" + data.keyWord + "')";
    } else if (data.CateId && data.keyWord) { // 又查分类又查名
      searchStr = "(Title = '" + data.keyWord + "' or VideoId = '" + data.keyWord + "') and CateId = " + data.CateId;
    }
    console.log(searchStr, 999);
    // 获取阿里云内数据
    const Video = await ctx.helper.request_alivod('SearchMedia ', {
      SearchType: 'video',
      SortBy: 'CreationTime:Desc',
      Match: searchStr,
      PageSize: data.size,
      PageNo: data.pageCurrent,
      Fields: 'CreationTime,Status,StorageLocation,CateId,VideoId,Tags,ModificationTime,CateName,Description,Size,CoverURL,Duration,Title,CoverURL,AuditStatus,Tags',
    }, {});
    const resList = [];
    for (const item of Video.MediaList) {
      const mongoVideo = await this.videoAndCourse(item.Video.VideoId);
      item.Video.have = !!mongoVideo;
      if (item.Video.have) item.Video.Courses = mongoVideo.coursesInfo;
      resList.push(item.Video);
    }
    const pageInfo = {
      total: Video.Total,
      size: data.size,
      pageCurrent: data.pageCurrent,
      RequestId: Video.RequestId,
      ScrollToken: Video.ScrollToken,
    };
    return {
      list: resList,
      pageInfo,
    };
  }
  // 获取详细信息--编辑时
  async getInfo(VideoId) {
    const { ctx } = this;
    const { Video: ALiVideoInfo } = await ctx.helper.request_alivod('GetVideoInfo', {
      VideoId,
    }, {});
    const res = await this.getPlayUrl(VideoId);
    const PlayURL = res.PlayInfoList.PlayInfo[0].PlayURL;
    ALiVideoInfo.PlayURL = PlayURL;
    const LocalVideoInfo = await this.videoAndCourse(VideoId);
    ALiVideoInfo.haveLocal = !!LocalVideoInfo;
    return {
      ALiVideoInfo,
      LocalVideoInfo,
    };
  }
  // 按照videoid获取视频信息和绑定的相关课程
  async videoAndCourse(VideoId) {
    const { ctx } = this;
    const reg = new RegExp(VideoId);
    const [ first ] = await ctx.model.VideoInfos.aggregate([
      {
        $lookup: {
          from: 'Courses',
          localField: '_id',
          foreignField: 'videoInfos',
          as: 'coursesInfo',
        },
      },
      {
        $match: {
          $or: [ // 多条件，数组
            { VideoId: { $regex: reg } },
          ],
        },
      },
    ]);
    return first;

  }
  // 获取分页信息
  async getPageInfo(collection, size, pageCurrent, query = {}) {
    const total = await this.ctx.model[collection].find(query).count();
    const pageInfo = {
      total,
      size,
      pageCurrent,
    };
    return pageInfo;
  }
  // 创建培训计划
  async getVideoList(query, current = 1, pageSize = 10) {
    return {
      count: await this.ctx.model.VideoInfos.countDocuments(query),
      list: await this.ctx.model.VideoInfos.find(query).limit(Number(pageSize)).skip((Number(current) - 1) * Number(current))
        .sort({
          date: -1,
        }),
    };
  }

  async Quoted(_id) {
    return await this.ctx.model.Courses.countDocuments({
      videoInfos: {
        $in: [ _id ],
      },
    });
  }
  // 从阿里云同步到数据库
  async pullALi(VideoId) {
    const { ctx } = this;
    try {
      const havaFlag = await ctx.model.VideoInfos.findOne({ VideoId });
      if (havaFlag) {
        return 'Existing in the database';
      }
      const { Video: ALiVideo } = await ctx.helper.request_alivod('GetVideoInfo', {
        VideoId,
      }, {});
      const userName = await ctx.model.User.findOne({ _id: ctx.session.user._id }, { name: 1 });
      const localInfo = {
        // _id: shortid.generate(),
        duration: ALiVideo.Duration || 0,
        source: 'user',
        VideoId: ALiVideo.VideoId,
        Size: ALiVideo.Size,
        name: ALiVideo.Title,
        uploader: ctx.session.user._id,
        date: ALiVideo.CreateTime,
        author: userName && userName.name ? userName.name : '',
        // classHours,
      };
      const res = await ctx.model.VideoInfos.create(localInfo);
      return res;
    } catch (error) {
      this.ctx.auditLog('同步数据库出错', `${VideoId}`, 'info');
      this.ctx.logger.error(error);
      console.error(error);
    }
  }
  async getPlayUrl(VideoId) {
    try {
      const res = await this.ctx.helper.request_alivod('GetPlayInfo', {
        VideoId,
        Formats: 'mp4',
      }, {});
      return res;
    } catch (error) {
      this.ctx.logger.error(error);
      console.error(error);
    }

  }
}
module.exports = VideoLibService;
