const { authToken } = require('@utils');
const moment = require('moment');

const AdminuserController = {
  // // 获取劳动者是否可以编辑个人职业史
  async getLaborIsEdit(ctx) {
    const params = ctx.request.query;
    const res = await ctx.service.adminuser.getLaborIsEdit(params);
    ctx.body =
      res === 500
        ? { code: 500, message: '服务器出错' }
        : { code: 200, data: res };
  },
  async getHistoryList(ctx) {
    const params = ctx.request.query;
    const res = await ctx.service.adminuser.getHistoryList(params);
    ctx.body =
      res === 500
        ? { code: 500, message: '服务器出错' }
        : { code: 200, data: res };
  },
  async addHistoryList(ctx) {

    const params = ctx.request.body;

    console.log(params, 'ctx.request.body');
    // 调用 getLaborIsEdit 方法进行校验
    const editPermission = await ctx.service.adminuser.getLaborIsEdit({ companyId: params.companyId });
    console.log(editPermission, 'editPermission');
    if (!editPermission || editPermission.length === 0 || !editPermission[0].lobarIsEdit) {
      ctx.body = { code: 403, message: '无权限编辑职业史' };
      return;
    }
    const res = await ctx.service.adminuser.addHistoryList(params);
    ctx.body =
      res === 500
        ? { code: 500, message: '服务器出错' }
        : { code: 200, data: res };
  },
  async deleteHistoryList(ctx) {
    const params = ctx.request.body;
    const res = await ctx.service.adminuser.deleteHistoryList(params);
    ctx.body =
      res === 500
        ? { code: 500, message: '服务器出错' }
        : { code: 200, data: res };
  },
  async editHistoryList(ctx) {
    const params = ctx.request.body;
    console.log(params, 'ctx.request.body');

    // 调用 getLaborIsEdit 方法进行校验
    const editPermission = await ctx.service.adminuser.getLaborIsEdit({ companyId: params.newData.companyId });
    if (!editPermission || editPermission.length === 0 || !editPermission[0].lobarIsEdit) {
      ctx.body = { code: 403, message: '无权限编辑职业史' };
      return;
    }
    const res = await ctx.service.adminuser.editHistoryList(params);
    ctx.body =
      res === 500
        ? { code: 500, message: '服务器出错' }
        : { code: 200, data: res };
  },

  // 获取岗位信息
  async stationInfo(ctx) {
    try {
      const IDNum = ctx.request.body.idNo;
      const companyId = ctx.request.body.companyId;
      let employeeId = ctx.request.body.employeeId;
      if (!employeeId) {
        const employee = await ctx.model.Employee.findOne(
          { IDNum },
          { _id: 1 }
        );
        employeeId = employee ? employee._id : '';
      }

      const stations = await ctx.service.adminuser.findEmployeeStation({
        companyId,
        employeeId,
      });
      console.log(stations, '岗位信息');
      ctx.helper.renderSuccess(ctx, {
        data: stations,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  // 查询岗位的危害检测结果
  async getCheckResult(ctx) {
    try {
      // const IDNum = ctx.request.body.idNo;
      const companyId = ctx.request.body.companyId;
      const employeeId = ctx.request.body.employeeId;
      // const employeeId = await ctx.model.Employee.findOne({ IDNum }, { _id: 1 });
      const stations = await ctx.service.adminuser.findEmployeeStation({
        companyId,
        employeeId,
      });
      const stationInfo =
        (await ctx.service.adminuser.getCheckResult(stations, companyId)) || '';
      // console.log(stationInfo, '要返回的数据');
      ctx.helper.renderSuccess(ctx, {
        data: stationInfo,
      });
    } catch (error) {
      console.log(error);
      ctx.body = { code: 500, message: '服务器出错' };
    }
  },

  // 获取防护用具-劳动者小程序（适配新数据结构）
  async getDefendproducts(ctx) {
    try {
      // 从token中获取用户信息，而不是依赖前端传递
      const userToken = ctx.get('Authorization');
      if (!userToken) {
        throw new Error('未提供认证token');
      }

      // 解析token获取用户ID和企业ID
      const { authToken } = require('@utils');
      const tokenData = await authToken.checkToken(userToken, ctx.app.config.encrypt_key, ctx);
      if (!tokenData || !tokenData._id) {
        throw new Error('token无效或已过期');
      }

      const employeeId = tokenData._id;
      console.log('从token解析的用户信息:', tokenData);

      // 优先从token中获取EnterpriseID，如果没有则从员工记录中获取
      let EnterpriseID = tokenData.EnterpriseID;

      if (!EnterpriseID) {
        console.log('token中没有EnterpriseID，从员工记录中获取');
        // 获取员工信息（包含企业ID）
        const employee = await ctx.model.Employee.findOne({ _id: employeeId }, {
          category: 1,
          EnterpriseID: 1,
        }).lean();

        if (!employee) {
          throw new Error('员工不存在');
        }

        EnterpriseID = employee.EnterpriseID;
      }

      console.log('最终使用的企业ID:', EnterpriseID);

      // 获取员工分类信息
      const employee = await ctx.model.Employee.findOne({ _id: employeeId }, {
        category: 1,
      }).lean();

      if (!employee) {
        throw new Error('员工不存在');
      }

      // 获取员工所属仓库信息
      const employeeWarehouse = await ctx.service.adminuser.getEmployeeWarehouse(employeeId, EnterpriseID);

      // 获取配发标准（使用新的扁平化查询）
      const allProtectionPlan = await ctx.service.adminuser.findProtectionPlan(employeeId, EnterpriseID, employee.category);

      // 领用记录
      const records = await ctx.model.ReceiveRecord.aggregate([
        {
          $match: {
            EnterpriseID,
            employee: employeeId,
          },
        },
        {
          $sort: {
            createAt: -1,
            receiveDate: -1,
          },
        },
      ]);

      // 格式化日期
      records.forEach(record => {
        if (record.receiveDate) {
          record.receiveDate = moment(record.receiveDate).format('YYYY-MM-DD');
        }
        if (record.warningDate) {
          record.warningDate = moment(record.warningDate).format('YYYY-MM-DD');
        }
        if (record.receiveStartDate) {
          record.receiveStartDate = moment(record.receiveStartDate).format('YYYY-MM-DD');
        }
      });

      // 处理配发标准和领用记录的关联
      for (let i = 0; i < allProtectionPlan.length; i++) {
        const plan = allProtectionPlan[i];
        plan.employee = employeeId;

        for (let j = 0; j < plan.products.length; j++) {
          const product = plan.products[j];
          const targetRecord = records.find(e => {
            if (
              e.planId === plan._id &&
              e.products[0].product === product.product &&
              (!e.sign)
            ) {
              return true;
            }
            return false;
          });

          if (targetRecord) {
            product.todo = targetRecord;
          } else {
            // 生成新的领用记录
            const newReceiveRecord = await ctx.service.adminuser.updateNewReceiveRecord(plan, product);
            product.todo = newReceiveRecord;
          }
        }
      }

      // 申请记录
      const applications = await ctx.model.ApplicationProduct.aggregate([
        {
          $match: {
            EnterpriseID,
            employee: employeeId,
          },
        },
        {
          $sort: {
            applicationTime: -1,
          },
        },
      ]);

      applications.forEach(record => {
        if (record.updateAt) {
          record.updateAt = moment(record.updateAt).format('YYYY-MM-DD HH:mm');
          record.applicationTime = moment(record.applicationTime).format('YYYY-MM-DD HH:mm');
        }
      });

      // 获取防护用品分类数据（适配新的产品模型）
      const typeList = await ctx.service.adminuser.getProtectiveProductCategories(EnterpriseID, employeeWarehouse.warehouseId);

      ctx.helper.renderSuccess(ctx, {
        data: {
          records,
          typeList,
          applications,
          allProtectionPlan,
          // 新增仓库信息
          employeeWarehouse,
        },
      });
    } catch (err) {
      console.error('获取防护用品数据失败:', err);
      ctx.helper.renderFail(ctx, {
        message: err.message || '获取防护用品数据失败',
      });
    }
  },

  // 领取或拒绝防护用具-劳动者小程序
  async receiveProducts(ctx) {
    try {
      const params = ctx.request.body;
      const res = await ctx.service.adminuser.receiveProducts(params);
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },

  /**
   * @param ctx
   * @Router GET /manage/adminuser/getStationChange
   * @summary 转岗记录-劳动者小程序
   * @Description 请求参数 employeeId
   * @Request query string employeeId EnterpriseID
   * @response 200 baseResponse 创建成功
   */
  async getStationChange(ctx) {
    try {
      const employeeId = ctx.query.employeeId;
      const EnterpriseID = ctx.query.EnterpriseID;
      if (!employeeId) throw new Error('employeeId不能为空');
      if (!EnterpriseID) throw new Error('EnterpriseID不能为空');
      const infos = await ctx.service.adminuser.getStationChange({
        employeeId,
        EnterpriseID,
      });
      console.log(infos, '转岗信息');
      ctx.helper.renderSuccess(ctx, {
        data: infos,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
  /**
   * @param ctx
   * @Router GET /manage/adminuser/getMessageNotice
   * @summary 消息通知-劳动者小程序
   * @Description 请求参数
   * @response 200 baseResponse 创建成功
   */
  async getMessageNotice(ctx) {
    try {
      // 从token中获取userId
      const payload = ctx.query;
      const userId = ctx.session.user
        ? ctx.session.user._id
        : (
          await authToken.checkToken(
            ctx.cookies.get('admin_' + ctx.app.config.auth_toolscookie_name),
            ctx.app.config.encrypt_key,
            ctx
          )
        )._id;
      if (!userId) throw new Error('userId不能为空');
      payload.userId = userId;
      if (payload.pageNum) payload.pageNum = parseInt(payload.pageNum);
      if (payload.pageSize) payload.pageSize = parseInt(payload.pageSize);
      const infos = await ctx.service.adminuser.getMessageNotice(payload);
      ctx.helper.renderSuccess(ctx, {
        data: infos,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  },
  /**
   * @param ctx
   * @Router post /manage/adminuser/confirmMessageNotice
   * @summary 确认消息通知-劳动者小程序
   * @Description 请求参数
   * @response 200 baseResponse 创建成功
   */
  async confirmMessageNotice(ctx) {
    try {
      const payload = ctx.request.body;
      const userId = ctx.session.user
        ? ctx.session.user._id
        : (
          await authToken.checkToken(
            ctx.cookies.get('admin_' + ctx.app.config.auth_toolscookie_name),
            ctx.app.config.encrypt_key,
            ctx
          )
        )._id;
      if (!userId) throw new Error('userId不能为空');
      if (!payload.messageIds || !Array.isArray(payload.messageIds) || payload.messageIds.length === 0) {
        throw new Error('messageIds不能为空且必须是数组');
      }
      payload.userId = userId;
      const confirmRes = await ctx.service.adminuser.confirmMessageNotice(
        payload
      );
      ctx.helper.renderSuccess(ctx, {
        data: confirmRes,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  /**
   * @param ctx
   * @Router get /manage/adminuser/getViolationNotice
   * @summary 获取违章通知-劳动者小程序
   * @Description 请求参数
   * @response 200 baseResponse 创建成功
   */
  async getViolationNotice(ctx) {
    try {
      const payload = ctx.query;
      const userId = ctx.session.user
        ? ctx.session.user._id
        : (
          await authToken.checkToken(
            ctx.cookies.get('admin_' + ctx.app.config.auth_toolscookie_name),
            ctx.app.config.encrypt_key,
            ctx
          )
        )._id;
      if (!userId) throw new Error('userId不能为空');
      if (!payload.messageId) throw new Error('messageId不能为空');
      const violationInfoRes = await ctx.service.adminuser.getViolationNotice(
        payload
      );
      violationInfoRes.attachment = violationInfoRes.attachment.map(item => {
        return {
          name: item.originName,
          url: `static${ctx.app.config.upload_http_path}/${violationInfoRes.punishmentUnit}/${item.staticName}`,
          staticName: item.staticName,
        };
      });
      ctx.helper.renderSuccess(ctx, {
        data: violationInfoRes,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  // 获取防护用品审核列表
  async getDefendproductsAuditList(ctx) {
    try {
      const payload = ctx.request.body;
      // todo 因为万华赶时间以及集团特殊性，user和employee和adminuseID统一，此处_id直接使用userId
      const userId = ctx.session.user
        ? ctx.session.user._id
        : (
          await authToken.checkToken(
            ctx.cookies.get('admin_' + ctx.app.config.auth_toolscookie_name),
            ctx.app.config.encrypt_key,
            ctx
          )
        )._id;
      if (!userId) throw new Error('userId不能为空');
      payload.userId = userId;
      const res = await ctx.service.approvalPpe.getDefendproductsAuditList(payload);
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
  // 防护用品审批
  async selectApplication(ctx) {
    try {
      const payload = ctx.request.body;
      const res = await ctx.service.approvalPpe.selectApplication(payload);
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
};

module.exports = AdminuserController;
