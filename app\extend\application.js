

const fs = require('fs');
const path = require('path');
const _ = require('lodash');


module.exports = {

  // 获取插件api白名单
  getExtendApiList() {
    const app = this;
    const pluginFile = path.join(app.config.baseDir, 'config/plugin.js');
    const pluginInfo = require(pluginFile);
    const plugins = [];
    const pluginAdminApiWhiteList = [];
    for (const pluginItem in pluginInfo) {

      // 1、开启插件，2、已成功加载，3、内部(jk)插件
      // hashOwnProperty 判断自身属性是否存在
      if (pluginInfo.hasOwnProperty(pluginItem) && pluginInfo[pluginItem].enable && !_.isEmpty(app.config[pluginItem]) && pluginItem.indexOf('jk') === 0) {

        const {
          adminApi,
        } = app.config[pluginItem];

        // 获取后台接口白名单
        for (const item of adminApi) {
          if (item.noPower && item.url) {
            pluginAdminApiWhiteList.push(item.url);
          }
        }
        plugins.push(pluginItem);
      }
    }
    return {
      plugins,
      adminApiWhiteList: pluginAdminApiWhiteList,
    };
  },

  // 获取对外开放接口白名单
  getExtendFontApiList() {
    const app = this;
    const pluginFile = path.join(app.config.baseDir, 'config/plugin.js');
    const pluginInfo = require(pluginFile);
    const plugins = [];
    const pluginFontApiWhiteList = [];
    for (const pluginItem in pluginInfo) {

      // 1、开启插件，2、已成功加载，3、内部(jk)插件
      // hashOwnProperty 判断自身属性是否存在
      if (pluginInfo.hasOwnProperty(pluginItem) && pluginInfo[pluginItem].enable && !_.isEmpty(app.config[pluginItem]) && pluginItem.indexOf('jk') === 0) {

        const {
          fontApi,
        } = app.config[pluginItem];

        // 获取后台接口白名单
        for (const item of fontApi) {
          if (item.noPower && item.url) {
            pluginFontApiWhiteList.push(item.url);
          }
        }
        plugins.push(pluginItem);
      }
    }
    return {
      plugins,
      fontApiWhiteList: pluginFontApiWhiteList,
    };
  },

  // 初始化数据模型
  initExtendModel(modelsPath) {
    const app = this;
    fs.readdirSync(modelsPath).forEach(function(extendName) {
      console.log(`挂载 ${path.basename(extendName, '.js')} 模型成功！`);
      if (extendName) {
        const filePath = `${modelsPath}/${extendName}`;
        if (fs.existsSync(filePath)) {
          const modelKey = path.basename(extendName.charAt(0).toUpperCase() + extendName.slice(1), '.js');
          if (_.isEmpty(app.model[modelKey])) {
            const targetModel = app.loader.loadFile(filePath);
            app.model[modelKey] = targetModel;
          }
        }
      }
    });
  },

  // 初始化插件路由
  async initPluginRouter(ctx, pluginConfig = {}, pluginManageController = {}, pluginApiController = {}, next = {}) {
    const app = this;
    let isFontApi = false;
    let isAdminApi = false;
    let targetControllerName = '';
    let targetApiItem = {};
    if (!_.isEmpty(pluginConfig)) {
      const {
        adminApi,
        fontApi,
      } = pluginConfig;

      const targetRequestUrl = ctx.request.url;
      ctx.auditLog('Debug输出', `请求路径：${targetRequestUrl}`, 'debug');
      if (targetRequestUrl.indexOf('/api/') >= 0 || targetRequestUrl.indexOf('/chenkSystem/') >= 0 || targetRequestUrl.indexOf('/crm/') >= 0 || targetRequestUrl.indexOf('/app/') >= 0) {
        for (const fontApiItem of fontApi) {
          const {
            url,
            method,
            controllerName,
          } = fontApiItem;

          const targetApi = targetRequestUrl.indexOf('/api/') >= 0 ? targetRequestUrl.replace('/api/', '').split('?')[0] : targetRequestUrl.indexOf('/chenkSystem/') >= 0 ? targetRequestUrl.replace('/chenkSystem/', '').split('?')[0] : targetRequestUrl.indexOf('/app/') >= 0 ? targetRequestUrl.replace('/app/', '').split('?')[0] : targetRequestUrl.replace('/crm/', '').split('?')[0];
          console.log(targetApi);
          if (ctx.request.method === method.toUpperCase() && targetApi === url && controllerName) {
            isFontApi = true;
            targetControllerName = controllerName;
            targetApiItem = fontApiItem;
            break;
          }

        }

      } else if (targetRequestUrl.indexOf('/manage/') >= 0) {
        ctx.auditLog('Debug输出', 'manage路径，进行adminApi处理', 'debug');
        for (const adminApiItem of adminApi) {

          const {
            url,
            method,
            controllerName,
          } = adminApiItem;

          const targetApi = targetRequestUrl.replace('/manage/', '').split('?')[0];

          if (ctx.request.method === method.toUpperCase() && targetApi === url && controllerName) {
            isAdminApi = true;
            targetControllerName = controllerName;
            targetApiItem = adminApiItem;
            break;
          }
        }
      }
    }

    if (isAdminApi && !_.isEmpty(pluginManageController) && targetControllerName) {
      ctx.auditLog('Debug输出', '处理成功！请求AdminApi', 'debug');
      await pluginManageController[targetControllerName](ctx, app);
    } else if (isFontApi && !_.isEmpty(pluginApiController) && targetControllerName) {
      if (targetApiItem.authToken) {
        if (ctx.session.user) {
          ctx.auditLog('Debug输出', '处理成功(authToken)！请求FontApi', 'debug');
          await pluginApiController[targetControllerName](ctx, app, next);
        } else {
          ctx.helper.renderFail(ctx, {
            message: '请先登录',
          });
        }
      } else {
        ctx.auditLog('Debug输出', '处理成功！请求FontApi', 'debug');
        await pluginApiController[targetControllerName](ctx, app, next);
      }
    }
  },

};
