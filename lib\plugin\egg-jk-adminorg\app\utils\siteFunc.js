const path = require('path');
const url = require('url');
const siteFunc = {

  setFullPath(dest, ctx) {
    const date = new Date();

    const map = {
      t: date.getTime(), // 时间戳
      m: date.getMonth() + 1, // 月份
      d: date.getDate(), // 日
      h: date.getHours(), // 时
      i: date.getMinutes(), // 分
      s: date.getSeconds(), // 秒
    };

    dest = dest.replace(/\{([ymdhis])+\}|\{time\}|\{rand:(\d+)\}/g, function(all, t, r) {
      let v = map[t];
      if (v !== undefined) {
        if (all.length > 1) {
          v = '0' + v;
          v = v.substr(v.length - 2);
        }
        return v;
      } else if (t === 'y') {
        return (date.getFullYear() + '').substr(6 - all.length);
      } else if (all === '{time}') {
        return map.t;
      } else if (r >= 0) {
        return Math.random().toString().substr(2, r);
      }
      return all;
    });

    const orgid = url.parse(ctx.url).query.split('=')[1];
    dest = dest.replace('{ORGID}', (orgid && orgid !== 'undefined') ? orgid : 'default');
    return dest;
  },
  getFileInfoByStream(urlFormat, stream, ctx) {
    const pathFormat = this.setFullPath(urlFormat, ctx).split('/');
    const newFileName = pathFormat.pop();

    const uploadForder = path.join('.', ...pathFormat);
    // 所有表单字段都能通过 `stream.fields` 获取到
    const fileName = path.basename(stream.filename); // 文件名称
    const extname = path.extname(stream.filename).toLowerCase(); // 文件扩展名称
    if (!extname) {
      throw new Error(this.res.__('validate_error_params'));
    }
    // 生成文件名
    // let ms = (new Date()).getTime().toString() + extname;
    return {
      uploadForder,
      uploadFileName: newFileName,
      fileName,
      fileType: extname,
    };
  },

  async buildIndustry(array, keyarray, index = 0, result = []) {
    for (let i = 0; i < array.length; i++) {
      const element = array[i];
      if (element.value === keyarray[index]) {
        if (element.children) {
          result.push(element.label);
          return siteFunc.buildIndustry(element.children, keyarray, ++index, result);
        }
        result.push(element.label);
        return result;
      }
    }
  },
};
module.exports = siteFunc;
