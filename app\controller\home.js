const Controller = require('egg').Controller;
const fs = require('fs');
const path = require('path');

class HomeController extends Controller {
  // 钉钉订阅调用的主接口
  async dingSubscript() {
    try {
      // const qlcProcessCodes = this.app.config.qlcProcessCodes; // 审批配置信息
      // const radProcessCodes = this.app.config.radProcessCodes; // 放射审批配置
      // const personProcessCodes = this.app.config.personProcessCodes; // 个人剂量审批配置
      const { ctx } = this;
      const query = ctx.query;
      console.log(query, 234235346);
      const body = ctx.request.body;
      let dingInfo;
      if (query.serviceOrgId) { // 机构id
        const serviceOrg = await ctx.model.ServiceOrg.findOne({ _id: query.serviceOrgId }).lean();
        // ctx.session.serviceUserInfo = { adminOrgId: query.serviceOrgId };
        if (serviceOrg === null) return;
        dingInfo = serviceOrg.dingInfo || {};
        console.log(dingInfo, '钉钉配置信息');
      }
      const { token, aesKey, AppKey, AppSecret } = dingInfo;
      // 参考：钉钉开发文档-业务事件回调
      const DingTalkEncryptor = require('dingtalk-encrypt');
      const encryptor = new DingTalkEncryptor(token, aesKey, AppKey);
      // 解密钉钉回调数据
      const plainText = await encryptor.getDecryptMsg(
        query.msg_signature,
        query.timestamp,
        query.nonce,
        body.encrypt
      );
      const data = await encryptor.getEncryptedMap(
        'success',
        query.timestamp,
        query.nonce
      );
      const obj = JSON.parse(plainText);
      const result = await ctx.curl(
        `https://oapi.dingtalk.com/gettoken?appkey=${AppKey}&appsecret=${AppSecret}`,
        {
          method: 'GET',
          rejectUnauthorized: true,
          dataType: 'json',
        }
      );

      const access_token = result.data.access_token;
      const UserIds = obj.UserId ? await this.sliceArr(obj.UserId, 50) : '';
      const DeptId = obj.DeptId ? obj.DeptId : '';
      // 回调事件类型，根据事件类型和业务数据处理相应业务
      const eventType = obj.EventType;
      // 4. 根据EventType分类处理
      const processCode = obj.processCode;
      // console.log(111, eventType);
      if (eventType === 'check_url') {
        console.log('验证成功====');
      } else if (eventType === 'user_add_org') {
        await this.user_add_org(access_token, UserIds, query._id);
        console.log('通讯录用户增加');
      } else if (eventType === 'user_modify_org') {
        await this.user_add_org(
          access_token,
          UserIds,
          query._id,
          'user_modify_org'
        );
        console.log('通讯录用户修改');
      } else if (eventType === 'user_leave_org') {
        await this.user_leave_org(access_token, UserIds);
        console.log('通讯录用户离职');
      } else if (eventType === 'org_dept_modify') {
        await this.org_dept_modify(access_token, DeptId);
        console.log('通讯录企业部门修改');
      } else if (eventType === 'org_dept_remove') {
        await this.org_dept_remove(DeptId);
        console.log('通讯录企业部门删除');
      } else if (eventType === 'bpms_task_change') {
        // 审核任务开始、结束、转交
        // 获取审批节点
        const approvalTemplate = await ctx.model.ApprovalTemplate.findOne({
          dingApprovalCode: processCode,
        }).lean();
        console.log(processCode, '审批模版');
        if (!approvalTemplate) {
          console.log('未配置模版编号，模版编号：', processCode);
          ctx.body = JSON.stringify(data);
          return;
        }
        const res = await ctx.service.dingTalkWorkFlow.getProcessinstance(
          obj.processInstanceId,
          AppKey,
          AppSecret
        );
        if (res.errmsg) {
          console.log(
            '钉钉获取审批详情错误返回',
            JSON.stringify(res.errmsg)
          );
          ctx.body = JSON.stringify(data);
          return;
        }
        const { nodes } = approvalTemplate;
        // let radapproveInfo = radProcessCodes.filter(
        //   item => item.code === processCode
        // );
        // let personapproveInfo = personProcessCodes.filter(
        //   item => item.code === processCode
        // );
        // personapproveInfo = personapproveInfo[0];
        // radapproveInfo = radapproveInfo[0];

        // // 全流程
        // let approveInfo = qlcProcessCodes.filter(
        //   item => item.code === processCode
        // );
        // approveInfo = approveInfo[0];
        // if (!radapproveInfo && !personapproveInfo && !approveInfo) {
        //   ctx.body = JSON.stringify(data);
        //   return;
        // }
        // const operation_results = res.info.operation_records.filter(item => item.operation_result === 'AGREE');
        // if (!operation_results.length) {
        //   ctx.body = JSON.stringify(data);
        //   return;
        // }
        // const signIndex = operation_results.length - 1;
        // console.log(signIndex, '审批序号？？？？', obj.processInstanceId, '实例id');
        // const currentEmployeeId = operation_results[operation_results.length - 1].serviceEmployeeId;
        res.info.processCode = processCode;
        await ctx.service.dingTalkWorkFlow.handleDingApproval({ nodes, obj, res });
        // this.ctx.service.rabbitmq.produce(
        //   {personapproveInfo,radapproveInfo,approveInfo,obj,res,currentEmployeeId,signIndex},
        //   'dingApproval',
        //   'dingTalkWorkFlow'
        // );
        // this.ctx.service.rabbitmq.produce(
        //   {
        //     message: { personapproveInfo, radapproveInfo, approveInfo, obj, res, currentEmployeeId, signIndex },
        //     exchange: 'dingApproval',
        //     routingKey: 'dingTalkWorkFlow',
        //   }
        // );
      }
      ctx.body = JSON.stringify(data);
    } catch (error) {
      console.log(error, 22222);
    }
  }

  async org_dept_modify(access_token, dingId) {
    const dept = await this.ctx.curl(
      `https://oapi.dingtalk.com/department/get?access_token=${access_token}&id=${dingId}`,
      {
        method: 'GET',
        rejectUnauthorized: true,
        dataType: 'json',
      }
    );
    console.log(dept.data, '===');
    const name = dept.data.name;
    console.log(dingId);
    console.log(name);
    const res = await this.ctx.model.Dingtree.updateOne(
      { dingId },
      { $set: { name } }
    );
    console.log(res);
  }
  async org_dept_remove(dingId) {
    const res = await this.ctx.model.Dingtree.deleteMany({ dingId });
    console.log(res);
  }
  /**
   * 人员表离岗存在人员转正 不存在新增人员
   * @param {*string} access_token
   * @param {*array} UserIds
   * @param {*} CorpId
   * @param {*} type
   */
  async user_add_org(access_token, UserIds, CorpId, type) {
    const dept = await this.ctx.curl(
      `https://oapi.dingtalk.com/department/list?access_token=${access_token}`,
      {
        method: 'GET',
        rejectUnauthorized: true,
        dataType: 'json',
      }
    );
    const user = await this.check_user_detail(access_token, UserIds);
    const arr = user.data.result;
    // console.log(arr);
    const final_result = [];
    for (let index = 0; index < arr.length; index++) {
      const obj = {};
      const field_list = arr[index].field_list;
      for (let index = 0; index < field_list.length; index++) {
        const item = field_list[index];
        if (item.field_code === 'sys00-name') {
          obj.name = item.label;
        }
        if (item.field_code === 'sys02-certNo') {
          obj.IDNum = item.label;
        }
        if (item.field_code === 'sys00-mobile') {
          obj.phoneNum =
            item.label.indexOf('-') !== -1 ? item.label.split('-')[1] : '';
        }
        if (item.field_code === 'sys03-highestEdu') {
          obj.education = item.label;
        }
        if (item.field_code === 'sys00-confirmJoinTime') {
          obj.workStart = item.label;
        }
        if (item.field_code === 'sys02-marriage') {
          obj.marriage = item.label;
        }
        if (item.field_code === 'sys00-email') {
          obj.email = item.label;
        }
        if (item.field_code === 'sys00-deptIds') {
          const data = await this.ctx.service.employee.concatdepart(
            item.label,
            dept.data.department
          );
          obj.departs = data.departs;
          const arr = data.departIds.split(',');
          for (let i = 0; i < arr.length; i++) {
            if (!arr[i]) {
              arr.splice(i, 1);
            } else {
              arr[i] = arr[i].split(/-|_|\//gi);
            }
          }
          obj.departIds = arr;
        }
      }
      obj.dingId = arr[index].userid;
      final_result.push(obj);
    }
    console.log(final_result, '最终结果', type);

    const dingId = user.data.result[0].userid;
    const res = await this.ctx.model.Employee.findOne({ dingId });
    if (res === null || type === 'user_modify_org') {
      console.log('新增人员 || 修改');
      await this.ctx.service.employee.clearup(final_result, CorpId);
    } else {
      console.log('转在岗');
      await this.ctx.model.Employee.update({ dingId }, { $set: { status: 1 } });
    }
  }
  async user_leave_org(access_token, UserIds) {
    const res = await this.check_user_detail(access_token, UserIds);
    const dingId = res.data.result[0].userid;
    console.log(dingId, '要删除的id');
    const result = await this.ctx.model.Employee.update(
      { dingId },
      { $set: { status: 0 } }
    );
    console.log(result, '删除结果');
  }
  /**
   * 查看钉钉用户详情
   * @param {*string} access_token
   * @param {*array} userid_list 用户id
   * @return
   */
  async check_user_detail(access_token, userid_list) {
    const { ctx } = this;
    return await ctx.curl(
      `https://oapi.dingtalk.com/topapi/smartwork/hrm/employee/list?access_token=${access_token}&userid_list=${userid_list}`,
      {
        method: 'post',
        rejectUnauthorized: true,
        dataType: 'json',
      }
    );
  }
  /**
   * 数组分批次
   * @param {*array} array 原数组
   * @param {*number} subGroupLength  想要分批的数量
   * @return
   */
  async sliceArr(array, subGroupLength) {
    // 将数据分为等份
    console.log(array);
    console.log(array.length, '分批的数组总长');
    let index = 0;
    const newArray = [];
    while (index < array.length) {
      newArray.push(array.slice(index, (index += subGroupLength)));
    }
    return newArray;
  }
  async GFbLsgrBx2() {
    // 读取 abc.text 文件的内容
    const filePath = path.join(
      this.config.baseDir,
      'app/public/GFbLsgrBx2.txt'
    );
    const content = fs.readFileSync(filePath, 'utf8');
    this.ctx.body = content;
    // 将内容返回给客户端
  }
  async WW_verify_5F27NpogfHZeGJaa() {
    // 读取 abc.text 文件的内容
    const filePath = path.join(
      this.config.baseDir,
      'app/public/WW_verify_5F27NpogfHZeGJaa.txt'
    );
    const content = fs.readFileSync(filePath, 'utf8');
    this.ctx.body = content;
    // 将内容返回给客户端
  }
  async WW_verify_hdQcbJlYi2xY4dH9() {
    // 读取 abc.text 文件的内容
    const filePath = path.join(
      this.config.baseDir,
      'app/public/WW_verify_hdQcbJlYi2xY4dH9.txt'
    );
    const content = fs.readFileSync(filePath, 'utf8');
    this.ctx.body = content;
  }
  async index() {
    this.ctx.body = `HELLO,OPEN API  ${this.config.version}`;
  }
  async fzDataHtml() {
    await this.ctx.render('/fzData.html', {
      staticRootPath: this.config.static.prefix,
    });
  }
}

module.exports = HomeController;
