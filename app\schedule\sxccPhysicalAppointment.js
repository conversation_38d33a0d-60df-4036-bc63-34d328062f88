// 定时任务 山西焦煤体检预约提醒
module.exports = app => {
  class SxccPhysicalAppointment extends app.Subscription {
    static get schedule() {
      return app.config.sxccPhysicalAppointmentCron;
    }

    async subscribe() {
      const { ctx } = this;
      console.log('山西焦煤体检预约提醒定时任务开始');
      ctx.auditLog('山西焦煤体检预约提醒定时任务开始', '山西焦煤体检预约提醒定时任务开始', 'info');
      console.time('山西焦煤体检预约提醒定时任务耗时');

      try {
        await ctx.service.sxccPhysicalAppointment.remind();
        ctx.auditLog('山西焦煤体检预约提醒定时任务成功', '山西焦煤体检预约提醒定时任务成功', 'info');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.stack : JSON.stringify(error);
        ctx.auditLog('山西焦煤体检预约提醒定时任务失败', errorMessage, 'error');
      }

      ctx.auditLog('山西焦煤体检预约提醒定时任务结束', '山西焦煤体检预约提醒定时任务结束', 'info');
      console.timeEnd('山西焦煤体检预约提醒定时任务耗时');
    }
  }
  return SxccPhysicalAppointment;
};
