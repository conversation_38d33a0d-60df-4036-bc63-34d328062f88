const path = require('path');
const port = 7009;

module.exports = appInfo => {

  return {
    // 插件路径
    admin_root_path: '/static',
    // 数据库连接
    mongoose: {
      client: {
        url: `${process.env.mdbSrv}/?readPreference=nearest&ssl=false`,
        options: {
          authSource: 'admin',
          dbName: process.env.mdbName,
          user: process.env.mdbUser,
          pass: process.env.mdbPass,
          useNewUrlParser: true,
          useUnifiedTopology: true,

        },
      },
      tools: {
        url: `${process.env.mdbSrv}/?readPreference=nearest&ssl=false`,
        options: {
          authSource: 'admin',
          dbName: process.env.mdbToolsName,
          user: process.env.mdbUser,
          pass: process.env.mdbPass,
          useNewUrlParser: true,
          useUnifiedTopology: true,

        },
      },
    },

    dingTalk: {
      // 钉钉
      AgentId: '869258467',
      AppKey: 'dingfrf5xbl2uhcecjpn',
      AppSecret:
        'bKNHDi4E6YUoPVCubcDsWbVuPwtFkHwfCZQguYxEUZG1t-wt_XKEb_V8EfUf-MhY',
      CorpId: 'ding2fa4df5d78220543',
    },

    // 静态目录
    static: {
      prefix: '/static',
      dir: [
        path.join(appInfo.baseDir, 'backstage/dist'),
        '/opt/public/cms',
        '/opt/public/super',
        '/opt/public/jc',
        '/opt/public/operate',
        '/opt/public',
        path.join(appInfo.baseDir, 'app/public'),
      ],
      maxAge: 31536000,
    },
    // 加密解密
    session_secret: 'duopu_secret',
    auth_cookie_name: 'zyws_hm_oapi',
    auth_govcookie_name: 'zyws_hm_super',
    auth_jcqlccookie_name: 'zyws_hm_jcqlc',
    encrypt_key: '243d477d-d6bb-4ab1-b0a9-a1b1c9419562',
    cms_encrypt_key: 'duopu_jkqy',
    salt_aes_key: 'duopu_jkqy',
    salt_sha2_key: '7a58917b-d493-4940-87d4-ab2b0fa8a3b7',
    encryptApp_key: '751f621ea5c8f930',
    encryptApp_vi: '2624750004598718',
    enterpriseUserGroup: 'vGEfBpfsv',
    enterpriseUpload_path: '/opt/public/upload/enterprise',
    // 用户自行上传文件的文件系统目录，代码拼接: upload_path + /EnterpriseID/ + 文件名
    upload_path: '/opt/public/upload/enterprise',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_http_path + /EnterpriseID/ + 文件名
    upload_http_path: '/upload/enterprise',

    // CMS上传文件的文件系统目录
    upload_cms_path: {
      upload_path: '/opt/public/cms',
      static_root_path: 'cms', // 针对云存储可设置
    },

    // 存放培训网站相关文件系统目录，代码拼接：trainSitFile_path + 文件名
    trainSitFile_path: '/opt/public/trainSitFile',
    // 浏览培训网站相关http路径，代码拼接：/static+ trainSitFile_http_path + 文件名
    trainSitFile_http_path: '/trainSitFile',

    // 存放生成的企业档案的文件系统目录
    enterprise_path: '/opt/public/enterprise',
    // 下载企业档案的http路径
    enterprise_http_path: '/enterprise',
    // 课程封面目录，代码拼接: upload_courses_path + /课程ID/ + 文件名
    upload_courses_path: '/opt/public/courses',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_courses_http_path + /课程ID/ + 文件名
    upload_courses_http_path: '/courses',

    // 存放生成的培训证书的文件系统目录，代码拼接：certificate_path + 文件名
    certificate_path: '/opt/public/certificate',
    // 下载培训证书的http路径，代码拼接：/static+ certificate_http_path + 文件名
    certificate_http_path: '/certificate',

    // 上传体检报告目录，代码拼接: upload_phy_report_path + /体检机构ID/ + 文件名
    upload_phy_report_path: '/opt/public/upload/tjReport',
    // 上行上传体检报告的http路径， 代码拼接：/static + upload_http_phy_report_path + /体检机构ID/ + 文件名
    upload_http_phy_report_path: '/upload/tjReport',

    // 用于生成报告的word模板存放位置，直接获取文件系统目录
    report_template_path: process.cwd() + '/app/public/reportTemplate',
    // 存放生成的报告的文件系统目录，代码拼接：report_path + /EnterpriseID/ + 文件名
    report_path: '/opt/public/report',
    // 用于下载生成报告的文件目录，代码拼接：/static + report_http_path + /EnterpriseID/ + 文件名
    report_http_path: '/report',

    sign_path: '/opt/public/upload/sign',
    sign_http_path: '/upload/sign',

    // 个人剂量
    personProcessCodes: [
      {
        code: 'PROC-95950656-4C1D-42B5-820A-1643990CBD43',
        _idField: 'reportProcessInstanceId', // 报告审批
        statusField: 'reportApproved',
      },
    ],
    radProcessCodes: [
      // 放射流程钉钉审批
      {
        node: 'contractReview',
        _idField: 'process_instance_id', // 合同审批
        statusField: 'approved',
      },
      {
        node: 'reportReview',
        _idField: 'reportProcessInstanceId', // 放射报告审核
        statusField: 'reportReview',
      },
      {
        node: 'officialDraftReview',
        _idField: 'reportProcessInstanceId', // 正式稿审核
        statusField: 'reportReview',
      },
      {
        node: 'radiateMApply',
        // code: 'PROC-E6151FA9-2259-4E14-AF23-133AE80C33A7',
        _idField: 'MApplyId', // 修改审批
        statusField: 'MApplyStatus',
      },
      {
        node: 'schemeReview',
        name: '方案审核',
        _idField: 'samplingPlanApproveId',
        statusField: 'samplingSchemeReview',
      },
    ],
    qlcProcessCodes: [
      // 全流程钉钉审批
      {
        code: 'PROC-DD246565-6848-41F1-BA75-C444423DB890',
        _idField: 'evaluationSchemeId', // 评价方案审核记录
        statusField: 'samplingPlanApprove',
      },
      {
        code: 'PROC-7469F564-0226-4FC8-A84A-33DBC2DD6C7A',
        _idField: 'reportProcessInstanceId', // 实验室审批单
        statusField: 'reportApproved',
        approveField: 'labreport',
      },
      {
        code: 'PROC-F4F751C3-042F-4CC1-8B26-A86B51A94E95',
        _idField: 'process_instance_id', // 合同审批
        statusField: 'approved',
      },
      {
        code: 'PROC-830E46E0-A824-4182-A205-287B3BB17A2C',
        _idField: 'reportFinalProcessInstanceId', // 报告审批
        statusField: 'reportFinalApproved',
      },
      {
        code: 'PROC-664B3239-B3D9-4ADC-AEB3-C076CDE1A682', // 方案审批
        _idField: 'samplingPlanApproveId',
        statusField: 'samplingPlanApprove',
      },
      {
        code: 'PROC-DC18A91D-894B-4020-BFE0-91BF8C9D94C3', // 计划单数据修改审批
        _idField: 'samplingPlanProcessInstanceId',
        statusField: 'samplingPlanAmendApproved',
        approveField: 'samplingPlan',
      },
      {
        code: 'PROC-9168B8A0-188E-4FBB-8AF3-3CE870E3C2FD', // 现场检测数据修改审批
        _idField: 'spotRecordProcessInstanceId',
        statusField: 'spotRecordAmendApproved',
        approveField: 'spotRecord',
      },
      {
        code: 'PROC-72E298B9-9C47-40D3-A975-F7CD0BD17654', // 实验室数据修改审批
        _idField: 'labProcessInstanceId',
        statusField: 'labAmendApproved',
        approveField: 'lab',
      },
      {
        code: 'PROC-BE13D12E-FC3C-459B-A5C2-F87C40EFD0BE', // 完成项目审批
        _idField: 'finishProjectProcessInstanceId',
        statusField: 'finishProjectApproved',
        approveField: 'finishProject',
      },
      {
        code: 'PROC-EF895647-42CC-4586-8931-447F80E353BE', // 标准物质领用审批
        _idField: 'applyAcceptanceProcessInstanceId',
        statusField: 'applyAcceptanceApproved',
        approveField: 'applyAcceptance',
      },
    ],

    dingInfo: {
      AppKey: 'dingfrf5xbl2uhcecjpn',
      AppSecret:
        'bKNHDi4E6YUoPVCubcDsWbVuPwtFkHwfCZQguYxEUZG1t-wt_XKEb_V8EfUf-MhY',
      CorpId: 'ding2fa4df5d78220543',
      aes_key: 't2HG2iFMiyAMNnYCUCm7o57x2ME2lvUwdndd8WvCykB',
      token: 'rMgCZ5FEKTK0a75BCNRl1P3o5P9VCsxXGaENfljYl2sf6uAqnFCE8',
    },

    // 阿里云短信接口配置
    aliMessage: {
      accessKeyId: process.env.aliMessage_accessKeyId,
      accessKeySecret: process.env.aliMessage_accessKeySecret,
      endpoint: 'https://dysmsapi.aliyuncs.com',
      apiVersion: '2017-05-25',
    },

    // 阿里云视频
    aliVideo: {
      accessKeyId: process.env.aliVideo_accessKeyId,
      secretAccessKey: process.env.aliVideo_secretAccessKey,
      // userId: 'process.env.aliVideo_userId',
    },
    // 人脸识别
    facebody: {
      accessKeyId: process.env.facebody_accessKeyId,
      accessKeySecret: process.env.facebody_accessKeySecret,
    },

    cluster: {
      listen: {
        port,
        hostname: '',
      },
    },
    // 不同端的域名
    domainNames: {
      px: 'https://px.zyws.cn',
    },
    // cn环境：文档类别id设置 对应的是表contentCategory的id
    categories: {
      learning: [
        { name: '法律', id: 'E1lagiaw' }, // law
        { name: '法规', id: 'V1U2-a9le' }, // legislation
        { name: '规章', id: '4JknAqTv' }, // regulation
        { name: '规范性文件', id: 'N7c-AjP_' }, // normativeDocs
      ],
      industryNews: {
        // 首页上的
        name: '行业动态',
        id: 'PUc7czJc',
      },
      informationPlatform: {
        name: '健康企业信息平台', // 首页上的
        id: '293sdcDe',
      },
      trainingEducation: {
        // 培训首页上的
        name: '培训教育平台',
        id: '6HiSgZEGi',
      },
      health: [
        { name: '健康达人', id: 'DBw994Lmx' },
        { name: '健康企业', id: '5VWyuEgVZ' },
      ],
    },

    // // alinode performance monitoring
    // alinode: {
    //   server: 'wss://agentserver.node.aliyun.com:8080',
    //   appid: '88988',
    //   secret: '12f829f7b48c5d18b328ec2a42921a451abc3964',
    //   logdir: '/opt/log/oapi/',
    //   error_log: [
    //     '/opt/log/oapi/master-stderr.log',
    //     '/opt/log/oapi/common-error.log',
    //   ],
    // },

    // 服务地址配置
    server_path: `http://localhost:${port}`,
    server_api: `http://localhost:${port}/api`,
    // mqtt服务端口配置
    mqtt: {
      ip: 'tcp://mqtt.zyws.cn',
      port: 1883,
    },
    iServiceHost: 'http://iservice',
    iService2Host: 'http://iservicev2',
    rabbitmq_url: `amqp://${process.env.rmqUser}:${process.env.rmqPass}@${process.env.rmqHost}:${process.env.rmqPort}`,

    pay: {
      wxpay: {
        appid: 'wx8d9eada10facd67a', // 这里填写微信小程序的id，用于掉起支付
      },
    },
    // 微信支付宝回调地址
    wxpay_notify_url: 'https://oapi.zyws.cn/app/pay/wxNotify',
    wxpay_refund_notify_url: 'https://oapi.zyws.cn/app/pay/wxRefundsNotify',
    alipay_notify_url: 'https://oapi.zyws.cn/app/pay/alipaysNotify',

    subscriptionVerification: true,

    // 新增配置请放在此行上方，branch放在最后
    branch: 'hm',
  };
};
