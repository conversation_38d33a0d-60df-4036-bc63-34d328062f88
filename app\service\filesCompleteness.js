
const Service = require('egg').Service;
// 企业档案完成度

class FilesCompletenessService extends Service {

  // eg: ctx.service.filesCompleteness.update({
  //   adminTraining: { completion: 100, validPeriod: moment().add(1, 'y') }, // 1年有效期
  // });
  async update(params) {
    const EnterpriseFiles = await this.get();
    // 更新数据
    return await this.ctx.model.FilesCompleteness.update(
      { _id: EnterpriseFiles._id },
      params,
      { new: true }
    );
  }

  // 跑库专用方法
  async update2(params, EnterpriseID) {
    // 更新数据
    return await this.ctx.model.FilesCompleteness.update(
      { EnterpriseID },
      params,
      { new: true }
    );
  }

  // 获取当前企业的档案完成情况
  async get() {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;
    let EnterpriseFiles = await ctx.model.FilesCompleteness.findOne(
      { EnterpriseID }
    );
    if (!EnterpriseFiles) { // 创建
      EnterpriseFiles = await ctx.model.FilesCompleteness.create(
        { EnterpriseID }
      );
    }
    return EnterpriseFiles;
  }

}

module.exports = FilesCompletenessService;
