// 机构端 -- 机构表
const Service = require('egg').Service;
// const OrgModel = require('../model/serviceOrg');
// const _ = require('lodash');
const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _safeDelete,
} = require('./general');

class OrgService extends Service {
  async createOrg(params) {
    const { ctx, service } = this;
    // 创建user
    const createServiceUserRes = await service.serviceUser.createServiceUser({
      phoneNum: params.phoneNum, // 电话号码
      userName: params.userName, // 用户名
    });
    if (createServiceUserRes.errCod !== 0) {
      return createServiceUserRes;
    }
    const userId = createServiceUserRes.data._id;

    // 根据统一社会信用代码查找机构
    const findOrg = await ctx.model.ServiceOrg.findOne({ organization: params.organization });
    if (findOrg) {
      // ctx.body = {
      //   status: 400,
      //   msg: '该组织机构已注册',
      // };
      // 更新管理人员
      await ctx.model.ServiceOrg.updateOne({ organization: params.organization }, { $addToSet: { managers: userId } });
      return {
        data: findOrg,
        message: '该组织机构已注册',
        errCode: '201',
      };
    }
    params.managers = [ userId ]; // 管理人员
    params.managersAndArea = [];
    const UnregisteredResult = await ctx.model.UnregisteredServiceOrg.findOne({ organization: params.organization });
    if (UnregisteredResult && UnregisteredResult.managersAndArea) {
      for (let i = 0; i < UnregisteredResult.managersAndArea.length; i++) {
        const item = UnregisteredResult.managersAndArea[i];
        item.managers = userId;
      }
      params.managersAndArea = UnregisteredResult.managersAndArea;
      params.qualifies = UnregisteredResult.qualifies;
      await ctx.model.UnregisteredServiceOrg.remove({ organization: params.organization });
    }
    // if (ctx.request.hostname.includes('hzzyws')) {
    //   params.managersAndArea.push({
    //     AreaRegAddr: [
    //       '浙江省',
    //       '杭州市',
    //     ],
    //     isactive: true,
    //     companyIn: false,
    //     managers: params.managers[0],
    //   });
    // }
    // params.managersAndArea.push({
    //   AreaRegAddr: [ // 为什么是指定是浙江省的？？？？
    //     '浙江省',
    //     '杭州市',
    //   ],
    //   isactive: true,
    //   companyIn: false,
    //   managers: userId,
    // });
    const result = await ctx.model.ServiceOrg.create(params);
    if (result && result._id) {
      try {
        const formObj = {
          EnterpriseID: result._id,
          name: '普通用户',
          comments: '员工默认角色',
          isFirst: true,
        };
        await ctx.model.JcGroup.create(formObj);
        // await service.jcGroup.create(formObj);
        service.serviceOrg.updateOrgStatus(result._id, 1); // 更新机构状态
        if (userId) {
          service.serviceUser.inserOrgToUser({ // 给当前用户update org和org_id
            _id: userId,
            org_id: result._id,
            org: result.name,
          });
        }
      } catch (err) {
        console.log(err);
      }
      // 返回数据
      return { data: result, errCode: '0' };
    }
    return { message: '创建失败', errCode: '500' };

  }

  // 更新机构状态status
  async updateOrgStatus(org_id, org_status) {
    return new Promise((res, rej) => {
      this.ctx.model.ServiceOrg.update({ _id: org_id }, { status: +org_status, ctime: Date.now() }, (err, result) => {
        if (err) {
          rej(err);
        } else {
          res(result);
        }
      });
    });

  }
  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {

    const listdata = _list(this.ctx.model.ServiceOrg, payload, {
      query,
      searchKeys,
      populate,
      files,
    });
    return listdata;

  }


  async count(params = {}) {
    return _count(this.ctx.model.ServiceOrg, params);
  }

  async create(payload) {
    return _create(this.ctx.model.ServiceOrg, payload);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.ServiceOrg, values, key);
  }

  async safeDelete(res, values) {
    return _safeDelete(res, this.ctx.model.ServiceOrg, values);
  }

  async update(res, _id, payload) {
    return _update(res, this.ctx.model.ServiceOrg, _id, payload);
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.ServiceOrg, params);
  }

}

module.exports = OrgService;
