/**
 * <AUTHOR>
 * @description 同步晨科的数据，新增/更新客户信息；新增/更新项目信息
 * @updateTime 2022-06-16
 */
const { tools } = require('@utils');
const Axios = require('axios');
const SynchroChenKeProjectsController = {

  // 获取在formObj里面有的属性值，但是在oldData没有值的数据
  async getDiffFields(updateOpt, formObj, oldData) {
    Object.keys(formObj).forEach(key => {
      const item = formObj[key];
      if (item instanceof Array) {
        if (item && item.length && (!oldData[key] || oldData[key].length === 0)) {
          updateOpt[key] = item;
        }
      } else if (item instanceof Object) {
        if (item && JSON.stringify(item) !== '{}' && (!oldData[key] || JSON.stringify(oldData[key]) === '{}')) {
          updateOpt[key] = item;
        }
      } else {
        if (item && !oldData[key]) {
          updateOpt[key] = item;
        }
      }
    });
  },

  checkContact(enterprisePhone, enterprisContact, field = '', required = true) {
    if (required && !enterprisePhone) {
      return field + '联系人电话号码不能为空，请完善后重新推送';
    }
    if (enterprisePhone) {
      enterprisePhone = enterprisePhone.trim();
      if (enterprisePhone && !/^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/.test(enterprisePhone)) {
        return field + '联系人电话号码格式错误，请修正后重新推送';
      }
    }


    if (required && !enterprisContact) {
      return field + '联系人不能为空，请完善后重新推送';
    }
    if (enterprisContact) {
      enterprisContact = enterprisContact.trim();
      if (enterprisContact && !/^(([a-zA-Z+\.?\·?a-zA-Z+]{2,30}$)|([\u4e00-\u9fa5+\·?\u4e00-\u9fa5+]{2,30}$))/.test(enterprisContact)) {
        return field + '联系人格式错误，请修正后重新推送';
      }
    }

  },

  /**
   * @param {Object} ctx 上下文
   * @description 查询是否有该企业 没有的话创建企业，有的话更新，只更新原企业信息为空的部分字段以及部分必更新字段
   *    必更新的字段：
   *      企业联系人（serviceID）:如果此次传入的联系人在原企业信息中没有，那么插入
   *      企业名称（cname）:企业名称直接更新为报告传入的企业名称
   *      企业曾用名：如果原企业名称和传入企业名称不同，那么添加一条企业曾用名信息
   */
  // isvip字段未更新
  async customer(ctx) {
    try {
      let res;
      ctx.header.accept = 'crm1.0';// 测试使用
      if (ctx.header.accept.indexOf('1.0') !== -1) {
        const type = ctx.request.method;
        const iscrm = ctx.header.accept.indexOf('crm') !== -1; // 是否是八骏的接口 目前有八骏和晨科
        if (type === 'GET') {
          console.log('GET请求参数：', ctx.request.query);
          const params = ctx.request.query;
          params.enterpriseCode = params.enterpriseCode ? params.enterpriseCode.replace(/\s*/g, '') : '';
          ctx.auditLog('客户的信用代码', `${params.enterpriseCode} 。`, 'info');
          // console.log(params.enterpriseCode, '客户的信用代码');
          if (!/[A-Z0-9]{18}/g.test(params.enterpriseCode)) {
            console.log(params.enterpriseCode, '客户信用代码有误');
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: '客户信用代码有误，请完善该用户的信用代码',
            });
            return;
          }
          const oldcompany = await ctx.model.Adminorg.findOne({
            code: { $regex: params.enterpriseCode.trim(), $options: 'i' },
          });
          console.log(Boolean(oldcompany), '是否有该客户');
          if (oldcompany) {
            return ctx.helper.renderSuccess(ctx, {
              message: '已有该客户，请调用增加项目接口',
              status: 200,
            });
          }
          return ctx.helper.renderCustom(ctx, {
            message: '无该客户，请调用增加客户接口添加该客户',
            status: 404,
          });
        }
        const params = ctx.request.body || {};
        // const params = { // 测试数据
        //   employeeId: '干洁耀',
        //   serviceOrgCode: '91330106749463365L',
        //   enterpriseName: '嘉兴市鼎业新型建材有限公司',
        //   enterpriseCode: '91330400056851365H',
        //   enterpriseAddress: '平湖市乍浦镇乍王路555号',
        //   enterpriseAddresses: '["浙江省","嘉兴","平湖市",""]',
        //   enterprisInfo: '[{"enterprisContact":"孙其林","enterprisePhone":"***********"}]',
        //   isVIP: '一般客户',
        //   enterpriseRepresentative: '',
        //   bankName: '工商银行嘉兴乍浦支行',
        //   invoiceAddress: '平湖市乍浦镇乍王路555号',
        //   invoiceAccount: '1204080109200121434',
        //   invoicePhoneNum: '0573-8557582',
        // };
        ctx.auditLog('调用增加客户', '', 'info');
        console.log(type, '请求参数：', params);
        console.log(type || '没有', '传过来的类型');
        // if (ctx.header.accept.indexOf('crm') !== -1) { // crm的接口
        //   console.log('八骏接口调用');
        // }
        let { serviceOrgCode, enterpriseName, enterpriseCode, enterpriseRepresentative, enterpriseAddress, enterpriseAddresses, enterprisContact, enterprisePhone, bankName, invoiceAddress, invoiceAccount, invoicePhoneNum, ckId, salesName, salesPhone, enterprisInfo, employeeId } = params;
        console.log(`1.${ckId} 2.${serviceOrgCode} 3.${enterpriseName} 4.${enterpriseCode} 5.${enterpriseAddress},${enterpriseAddresses} 6.${enterprisInfo} 7.${employeeId}--八骏`, '增加客户必传参数');
        if (!(serviceOrgCode && enterpriseName && enterpriseCode)) {
          const param = [];
          if (!serviceOrgCode) param.push('机构信用代码');
          if (!enterpriseName) param.push('客户名称');
          if (!enterpriseCode) param.push('客户信用代码');
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: `请检查添加客户的必传参数${param.join('、')}`,
          });
          return;
        }
        console.log('增加客户正常调用');
        if (salesPhone && salesName && ckId) {
          await ctx.model.ServiceEmployee.updateOne({ name: salesName, phoneNum: salesPhone }, { $set: { ckId } });
        } else if (salesName && ckId) {
          await ctx.model.ServiceEmployee.updateOne({ name: salesName }, { $set: { ckId } });
        }
        if (!enterpriseAddress) {
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: '请完善客户地址',
          });
          return;
        }
        const regAdd = enterpriseAddress; // 详细地址
        if (!iscrm) { // 处理晨科的地址 先关闭，等八骏那边调整好再打开

          // 晨科的客户管理没有行业，项目里才有；
          enterpriseAddress = await ctx.service.synchroChenKeProjects.findRegAdd(enterpriseAddress);
        } else {
          // 校验
          if (!enterpriseAddresses) {
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: '必填字段：enterpriseAddresses，不能为空',
            });
            return;
          }
          try { // 异常捕获一下，防止传入的不是json标准格式
            enterpriseAddresses = JSON.parse(enterpriseAddresses);
          } catch (error) {
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: `enterpriseAddresses：${enterpriseAddresses} json格式错误，请检查`,
            });
            return;
          }
          if (!Array.isArray(enterpriseAddresses)) {
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: '字段enterpriseAddresses类型错误，请检查',
            });
            return;
          }
          if (!enterpriseAddresses.length) {
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: '请完善客户地址，客户地址：省份，城市，地区，街道暂未填写',
            });
            return;
          }
          // 八骏直辖市数据 [直辖市,上海市，xxx区] 需要转换成 [上海市,直辖区,xxx区]
          if (enterpriseAddresses[0] === '直辖市') {
            enterpriseAddresses[0] = enterpriseAddresses[1];
            enterpriseAddresses[1] = '直辖区';
          }
          // 处理八骏的地址
          enterpriseAddress = await ctx.service.synchroChenKeProjects.getAreaCode(enterpriseAddresses);
        }

        const serviceCompany = await ctx.model.ServiceOrg.findOne({ organization: serviceOrgCode });
        if (!serviceCompany) {
          console.log(serviceOrgCode, '请机构先注册service.zyws.cn账号');
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: '请机构先注册service.zyws.cn账号',
          });
          return;
        }

        // 处理企业联系人

        // 联系人校验
        if (enterprisInfo) {
          try { // 异常捕获一下，防止传入的不是json标准格式
            enterprisInfo = JSON.parse(enterprisInfo);
          } catch (error) {
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: `enterprisInfo：${enterprisInfo} json格式错误，请检查`,
            });
            return;
          }
          if (!Array.isArray(enterprisInfo)) {
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: '企业联系人字段（enterprisInfo）类型错误，请检查',
            });
            return;
          }
        }
        enterprisInfo = enterprisInfo && enterprisInfo.length > 0 ? enterprisInfo : (enterprisePhone ? [{ enterprisContact, enterprisePhone }] : []);
        if (!enterprisInfo.length) {
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: '企业联系人不能为空',
          });
          return;
        }
        for (let j = 0; j < enterprisInfo.length; j++) {
          const item = enterprisInfo[j];
          const checkres = this.checkContact(item.enterprisePhone, item.enterprisContact, '企业');
          if (checkres) {
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: checkres,
            });
            return;
          }
          item.enterprisePhone = item.enterprisePhone.trim();
          item.enterprisContact = item.enterprisContact.trim();
        }


        const oldcompany = await ctx.model.Adminorg.findOne({
          code: { $regex: enterpriseCode.trim(), $options: 'i' },
        });
        const serviceID = oldcompany && oldcompany.serviceID ? JSON.parse(JSON.stringify(oldcompany.serviceID)) : [];// qiyeduan
        const oldServiceIDs = oldcompany ? JSON.parse(JSON.stringify(oldcompany.serviceID || [])) : [];
        enterprisInfo = enterprisInfo && enterprisInfo.length > 0 ? enterprisInfo : (enterprisePhone ? [{ enterprisContact, enterprisePhone }] : []);
        for (let j = 0; j < enterprisInfo.length; j++) {
          const item = enterprisInfo[j];
          if (!oldServiceIDs.filter(item2 => item2._id === serviceCompany._id && item2.EnterprisePhoneNum === item.enterprisePhone).length) {
            serviceID.push({
              _id: serviceCompany._id,
              EnterpriseContractName: item.enterprisContact || '',
              EnterprisePhoneNum: item.enterprisePhone || '',
            });
          }
        }
        if (!oldcompany) {
          serviceID[0].mainContract = true;
        }
        // 处理部分信用代码
        const pattern = new RegExp('[0-9]+');
        const num = enterpriseCode.match(pattern);
        const index = enterpriseCode.indexOf(num);
        enterpriseCode = enterpriseCode.slice(index, enterpriseCode.length);

        if (!/[A-Z0-9]{18}/g.test(serviceOrgCode)) {
          console.log(serviceOrgCode, '1服务机构信用代码有误');
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: '服务机构信用代码有误',
          });
          return;
        }
        if (!/[A-Z0-9]{18}/g.test(enterpriseCode)) {
          console.log(enterpriseCode, '1.客户信用代码有误');
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: '客户信用代码有误',
          });
          return;
        }
        // console.log(JSON.stringify(enterpriseAddress), '工作地址');

        // // 处理工作地址
        // let workAddress = [];
        // if (enterpriseAddress && enterpriseAddress.areaNames.length) {
        //   const point = await ctx.service.adminorg.getPoint(enterpriseAddress.areaNames.join(''));
        //   workAddress = [{
        //     districts: enterpriseAddress.areaNames,
        //     point,
        //     address: enterpriseAddress.detailedAddress || '',
        //   }];
        // }
        // const serviceUser = await ctx.model.ServiceUser.findOne({ _id: serviceCompany.managers[0] });
        const formObj = { // adminOrg的信息
          serviceID,
          cname: enterpriseName || '',
          code: enterpriseCode.trim().toLocaleUpperCase() || '',
          corp: enterpriseRepresentative || '',
          districtRegAdd: enterpriseAddress.areaNames || [],
          regAdd: regAdd || '',
          contract: enterprisContact || enterprisePhone || '',
          phoneNum: enterprisePhone || '',
          accountsBank: bankName,
          accountsAddress: invoiceAddress,
          accountsNumber: invoiceAccount,
          accountsPhoneNum: invoicePhoneNum,
          adminUserId: '',
          leadIn: '4',
          workAddress: [],
          isactive: '1',
        };
        // 添加曾用名 生效日期用报告检测日期
        const nameUsedBefore = {
          name: formObj.cname,
          entryIntoForceAt: new Date(),
          note: '报告导入',
          sourceModel: 'ServiceOrg',
          orgId: serviceCompany._id,
        };
        // 为工作场所添加经纬度
        if (formObj.districtRegAdd.length > 0) {
          formObj.workAddress = await ctx.service.synchroChenKeProjects.dealWithWorkAddress([{ districts: enterpriseAddress.areaNames }]);
        } else {
          formObj.workAddress = [{
            districts: [],
            address: '',
            point: [],
          }];
        }
        let oldService = ''; // 该企业已有此服务机构
        if (oldcompany) {
          oldService = oldcompany.serviceID.filter(item =>
            item._id === serviceCompany._id);
        }
        if (enterprisePhone) { // 处理企业账号
          const createUserObj = { // adminUser的信息
            // group: 'vGEfBpfsv', // 企业用户权限
            enable: true,
            phoneNum: enterprisePhone ? enterprisePhone.trim() : '',
            countryCode: 86,
            userName: enterprisContact ? enterprisContact.trim() : '' || enterprisePhone ? enterprisePhone.trim() : '',
          };
          const olduser = await ctx.model.AdminUser.findOne({
            phoneNum: enterprisePhone,
          });
          if (!olduser && !oldcompany) {
            console.log('111111手机号不存在 信用代码也不存在 创建帐号和公司');
            const info = await tools.validPhoneAndIDNum(ctx, 'adminUser', {
              phoneNum: params.phoneNum, // 需要修改得电话号码 必填
            });
            if (info) {
              ctx.helper.renderCustom(ctx, {
                status: 406,
                message: info,
              });
              return;
            }
            const currentUser = await ctx.model.AdminUser.create(createUserObj);
            formObj.adminUserId = currentUser._id;
            formObj.nameUsedBefore = [ nameUsedBefore ];
            await ctx.model.Adminorg.create(formObj); // 创建adminorg
          } else if (olduser && !oldcompany) {
            formObj.adminUserId = olduser._id;
            formObj.nameUsedBefore = [ nameUsedBefore ];
            await ctx.model.Adminorg.create(formObj);
          } else if (!olduser && oldcompany) {
            const info = await tools.validPhoneAndIDNum(ctx, 'adminUser', {
              phoneNum: params.phoneNum, // 需要修改得电话号码 必填
            });
            if (info) {
              ctx.helper.renderCustom(ctx, {
                status: 406,
                message: info,
              });
              return;
            }
            const currentUser = await ctx.model.AdminUser.create(createUserObj);
            if (oldService.length === 0) {
              await ctx.model.Adminorg.updateOne({ _id: oldcompany._id }, { $push: { adminArray: currentUser._id } });
            } else {
              await ctx.model.Adminorg.updateOne({ _id: oldcompany._id }, { $addToSet: { adminArray: currentUser._id } });
            }
            // 如果需要修改企业名称的话 请把这个注释打开
            // if (formObj.cname && oldcompany.cname && formObj.cname !== oldcompany.cname) {
            //     // 添加记录
            //     await ctx.model.Adminorg.updateOne({ _id: oldcompany._id }, {
            //       $push: { nameUsedBefore },
            //     });
            // }
          }
          // 考虑新建的时候初始化机构id的结构 8.19
          res = await ctx.service.synchroChenKeProjects.handleContract(enterpriseCode, serviceCompany._id, enterprisContact, enterprisePhone);
        } else {
          // const oldcompany = await ctx.model.Adminorg.findOne({
          //   code: { $regex: enterpriseCode.trim(), $options: 'i' },
          // });
          if (!oldcompany) {
            formObj.nameUsedBefore = [ nameUsedBefore ];
            await ctx.model.Adminorg.create(formObj);
          }
        }
        if (oldcompany) { // 更新
          // const updateOpt = {};
          // 获取原企业没有的信息的字段数据存到updateOpt中
          // this.getDiffFields(updateOpt, formObj, oldcompany);


          if (serviceID.length) {
            if (!oldService || !oldService.length) {
              serviceID.some(item => {
                if (item._id === serviceCompany._id) {
                  item.mainContract = true;
                  return true;
                }
                item.mainContract = false;
                return false;
              });
              await ctx.model.Adminorg.updateOne({ _id: oldcompany._id }, { $addToSet: { serviceID: { $each: serviceID } } });
            }
          }
          if (formObj.cname && oldcompany.cname && formObj.cname !== oldcompany.cname) {
            // 添加记录
            oldcompany.nameUsedBefore.push(nameUsedBefore);
            formObj.nameUsedBefore = oldcompany.nameUsedBefore;
            // await ctx.model.Adminorg.updateOne({ _id: oldcompany._id }, { $push: { nameUsedBefore } });
            // updateOpt.cname = formObj.cname;
            // updateOpt.$push = { nameUsedBefore };
          }
          // 以最新的项目信息更新
          await ctx.model.Adminorg.updateOne({ _id: oldcompany._id }, { $set: formObj });
        }
        console.log('客户添加成功2');
        ctx.helper.renderSuccess(ctx, {
          data: res,
          message: '客户添加成功',
          status: 200,
        });
      } else {
        console.log('请检查请求头版本');
        ctx.helper.renderCustom(ctx, {
          status: 406,
          message: '请检查请求头版本',
        });
        return;
      }
    } catch (err) {
      console.log(err, '错误');
      ctx.helper.renderCustom(ctx, {
        status: 500,
        data: err,
        message: '后端错误',
      });
    }
  },
  checkBracketPair(str) {
    const stack = [];
    for (let i = 0; i < str.length; i++) {
      if (str[i] === '[') {
        stack.push('[');
      } else if (str[i] === ']') {
        if (stack.pop() !== '[') {
          return false; // 遇到右括号但栈顶不是左括号，说明配对不完整
        }
      }
    }
    return stack.length === 0; // 栈为空则说明方括号配对完整
  },
  // 将返回数据转化为可以解析的json数据
  fixJson(jsonString) {
    try {
      // 使用正则表达式匹配日期字段值并添加双引号
      // const fixedJsonString = jsonString.replace(/(?<!")(5\/\d{1,2}\/\d{4}\s\d{1,2}:\d{2}:\d{2}\s(?:AM|PM))(?!")/g, '"$1"');
      const fixedJsonString = jsonString.replace(/(?<!")(\d{1,2}\/\d{1,2}\/\d{4}\s\d{1,2}:\d{2}:\d{2}\s(?:AM|PM))/g, '"$1"');
      // 日期格式转化
      const regex = /(\d{1,2}\/\d{1,2}\/\d{4}\s\d{1,2}:\d{2}:\d{2}\s(?:AM|PM))/g;
      const convertedString = fixedJsonString.replace(regex, match => {
        // 使用正则表达式将日期字符串拆分为日期、时间和 AM/PM 标识
        const matches = match.match(/(\d{1,2})\/(\d{1,2})\/(\d{4})\s(\d{1,2}):(\d{2}):(\d{2})\s(AM|PM)/);
        if (matches[7] === 'PM') {
          matches[4] = (matches[4] === '12') ? '12' : (Number(matches[4]) + 12).toString();
        }
        // else {
        //   // 如果是上午（AM）且小时为12，则将小时设为0
        //   matches[4] = (matches[4] === '12') ? '0' : matches[4];
        // }
        // 使用解析后的组件创建新的日期字符串
        const formattedDate = `${matches[3]}-${matches[1].padStart(2, '0')}-${matches[2].padStart(2, '0')}T${matches[4].padStart(2, '0')}:${matches[5]}:${matches[6]}`;
        // 创建新的 Date 对象
        const date = new Date(`${formattedDate}Z`);
        return date.toISOString();
      });
      // 查找 response 后的所有数据，并用字符串方法添加缺失的双引号
      const index = convertedString.indexOf('"response":') + '"response":'.length;
      const endIndex = convertedString.lastIndexOf('}');
      const responsePart = convertedString.substring(index, endIndex);
      const fixedResponsePart = responsePart.replace(/:\s*,/g, ':"",'); // 将 ":," 替换为 ":\"\","
      // 使用修复后的 response 部分替换原始字符串中的相应部分
      const fixedJsonString2 = convertedString.substring(0, index) + fixedResponsePart + convertedString.substring(endIndex);
      const isPair = this.checkBracketPair(fixedJsonString2);
      const fixedJsonString3 = isPair ? fixedJsonString2 : fixedJsonString2.replace(']', '[]');
      const fixedJsonString4 = fixedJsonString3.replace(/\s/g, '');
      return fixedJsonString4;
    } catch (error) {
      console.error('Error fixing JSON:', error);
      return null;
    }
  },
  // 八骏LevelID转化未全流程isVip-----会员类型：战略客户-svip、vip客户-vip、普通客户-ordinary
  isVip(str) {
    if (str === '战略客户') {
      return 'svip';
    } else if (str === 'vip客户') {
      return 'vip';
    }
    return 'ordinary';
  },
  /**
   * <AUTHOR>
   * @param {Object} ctx 上下文
   * @description 八骏接口：消息通知
   * createdAt 2024-03-06
   */
  async message(ctx) {
    try {
      // 获取版本号
      const accept = ctx.header.accept;
      const version = ctx.helper.getAPIVersion(accept, 'application/crm.zyws.v', '+json');
      const versions = [ '1_0' ]; // 所有的版本号
      if (!version || !(versions.includes(version))) {
        ctx.helper.renderCustom(ctx, {
          status: 406,
          message: '版本号错误，请检查',
          data: {},
        });
        return;
      }

      const params = ctx.request.body;
      // console.log(params, '八骏消息推送参数');
      // const params = {
      //   "entityType": "1",
      //   "entityNumber": "ZJDPZR-222140",
      //   "messageType": "1",
      //   "content": "2323",
      // }
      // const requiredFields = ['entityType','entityNumber','messageType','content']
      const requiredFields = [
        { label: '实体类别', field: 'entityType' },
        { label: '实体编号', field: 'entityNumber' },
        { label: '消息类型', field: 'messageType' },
        { label: '消息内容', field: 'content' },
      ];
      for (let i = 0; i < requiredFields.length; i++) {
        const item = requiredFields[i];
        if (!params[item.field]) {
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: item.label + '不能为空',
            data: {},
          });
          return;
        }
      }

      const entityNumber = params.entityNumber;
      const res = await Axios.get('https://crm.duopu.cn/duopu/gettoken?appid=duopu'); // 获取token
      const token = res.data.response.token;
      console.log('token: ' + token);
      if (params.entityType === '1') { // 项目实体
        const res = await Axios.get(`https://crm.duopu.cn/duopu/GetItems?Token=${token}&AutoNum=${entityNumber}`); // 获取项目信息
        if (res.status === 200) {
          const json = this.fixJson(res.data);
          let response = {};
          try { // 异常捕获一下，防止传入的不是json标准格式
            response = JSON.parse(json).response;
            response.LevelID = this.isVip(response.LevelID);
            const query = { projectSN: response.XMBH };
            const setFieldObj = {
              projectName: response.Name,
              serviceType: response.PID,
              anthemCompanyName: response.CID,
              anthemCompanyContact: response.CIDLXR, anthemCompanyContactPhoneNumber: response.CIDMobile,
              isVIP: response.LevelID,
              // anthemCompanyAddress: response.CIDAddre,
              EnterpriseName: response.SJF,
              companyContact: response.LXR,
              companyContactPhoneNumber: response.SJFMobile,
              salesman: response.EmpID,
              expectStartTime: response.RequireDate,
              expectStopTime: response.CompleteDate,
              description: response.XMJJ,
              comment: response.Note,
              projectPrice: response.TotalAmount,
              expectedNetEarnings: response.YJJZ,
              netEarnings: response.SJJE,
              expectedReviewFee: response.YJPSF,
              reviewFee: response.SJPSF,
              expectedCooperationFee: response.YJHZF,
              cooperationFee: response.SJHZF,
              expectedOutsourceFee: response.YJFBF,
              outsourceFee: response.SJFBF,
              expectedOtherFee: response.YJQTFY,
              otherFee: response.SJQTFY,
            };
            const updateOpt = {};
            const oldData = await ctx.model.JcqlcProject.findOne(query, {
              projectName: 1,
              serviceType: 1,
              anthemCompanyName: 1,
              anthemCompanyContact: 1,
              isVIP: 1,
              EnterpriseName: 1,
              companyContact: 1,
              companyContactPhoneNumber: 1,
              salesman: 1,
              expectStartTime: 1,
              expectStopTime: 1,
              description: 1,
              comment: 1,
              projectPrice: 1,
              expectedNetEarnings: 1,
              netEarnings: 1,
              expectedReviewFee: 1,
              reviewFee: 1,
              expectedCooperationFee: 1,
              cooperationFee: 1,
              expectedOutsourceFee: 1,
              outsourceFee: 1,
              expectedOtherFee: 1,
              otherFee: 1,
            }).lean();
            this.getDiffFields(updateOpt, setFieldObj, oldData);
            const res2 = await ctx.service.synchroChenKeProjects.updateJcqlcProject(query, updateOpt);
            console.log(res2);
            if (res2.nModified === 1) {
              console.log('项目信息修改成功');
            }
          } catch (error) {
            console.log(`项目实体查询返回数据：${JSON.stringify(res.data)} json格式错误，请检查`);
            // ctx.helper.renderCustom(ctx, {
            //   status: 406,
            //   message: `项目实体查询返回数据：${res.data} json格式错误，请检查`,
            // });
            // return;
          }
          // response.LevelID = response.LevelID && response.LevelID !== '一般客户' ? 'vip' : 'ordinary';

        }
      } else if (params.entityType === '2') { // 客户实体
        const res = await Axios.get(`https://crm.duopu.cn/duopu/GetCustomer?Token=${token}&AutoNum=${entityNumber}`); // 获取客户信息
        if (res.status === 200) {
          const json = this.fixJson(res.data);
          let response = {};
          try { // 异常捕获一下，防止传入的不是json标准格式
            response = JSON.parse(json).response;
            response.LevelID = this.isVip(response.LevelID);
            response.districtRegAdd = [ response.ProvinceID, response.CityID, response.ZoneID, response.StreetID ];
            if (response.ProvinceID === '直辖市') {
              response.districtRegAdd = [ response.CityID, '直辖区', response.ZoneID, response.StreetID ];
            // enterpriseAddresses[0] = enterpriseAddresses[1];
            // enterpriseAddresses[1] = '直辖区';
            }
            // 处理八骏的地址
            const enterpriseAddress = await ctx.service.synchroChenKeProjects.getAreaCode(response.districtRegAdd);
            const query = { code: response.SocialCode };
            const setFieldObj = {
              cname: response.Name,
              // contract: response.EmpID,
              districtRegAdd: enterpriseAddress.areaNames,
              isVIP: response.LevelID,
              regAdd: response.Address,
              corp: response.Legal,
              accountsBank: response.Bank,
              accountsNumber: response.Account,
              accountsAddress: response.InvoiceAddre,
            };
            const updateOpt = {};
            const oldData = await ctx.model.Adminorg.findOne(query, {
              cname: 1,
              // contract: response.EmpID,
              districtRegAdd: 1,
              isVIP: 1,
              regAdd: 1,
              corp: 1,
              accountsBank: 1,
              accountsNumber: 1,
              accountsAddress: 1,
            }).lean();
            this.getDiffFields(updateOpt, setFieldObj, oldData);
            const res2 = await ctx.service.synchroChenKeProjects.updateAdminorg(query, updateOpt);
            console.log(res2);
            if (res2.nModified === 1) {
              console.log('客户信息修改成功');
            }
          } catch (error) {
            console.log(`客户实体查询返回数据：${JSON.stringify(res.data)} json格式错误，请检查`);
            // ctx.helper.renderCustom(ctx, {
            //   status: 406,
            //   message: `客户实体查询返回数据：${res.data} json格式错误，请检查`,
            // });
            // return;
          }
        }
      }

      ctx.helper.renderCustom(ctx, {
        status: 200,
        message: '消息推送成功',
      });
      // TODO推送成功后访问八骏查询接口更新全流程项目获企业信息
    } catch (error) {
      console.log(error, '错误');
      ctx.helper.renderCustom(ctx, {
        status: 500,
        data: error,
        message: '后端错误',
      });
    }
  },

  /**
   * <AUTHOR>
   * @param {Object} ctx 上下文
   * @description 八骏接口：更新项目实际金额
   * createdAt 2023-05-10
   */
  async projectFee(ctx) {
    try {
      // 获取版本号
      const accept = ctx.header.accept;
      const version = ctx.helper.getAPIVersion(accept, 'application/crm.zyws.v', '+json');
      const versions = [ '1_0' ]; // 所有的版本号
      if (!version || !(versions.includes(version))) {
        ctx.helper.renderCustom(ctx, {
          status: 406,
          message: '版本号错误，请检查',
          data: {},
        });
        return;
      }

      const params = ctx.request.body;
      if (!params.projectSN) {
        ctx.helper.renderCustom(ctx, {
          status: 406,
          message: '项目编号不能为空',
          data: {},
        });
        return;
      }
      const res = await ctx.service.synchroChenKeProjects['projectFeeV' + version](params);
      if (res && res.code === -1) {
        ctx.helper.renderCustom(ctx, {
          status: 406,
          message: res.errMsg,
        });
      } else {
        ctx.helper.renderSuccess(ctx, {
          message: '更新金额成功',
          status: 200,
        });
      }
    } catch (error) {
      console.log(error, '八骏更新项目金额错误');
    }
  },


  async project(ctx) {
    try {
      console.log(ctx.request.method, '请求参数：', ctx.request.body);
      let res;
      console.log('调用增加项目');
      // ctx.header.accept = '1.0crm';// 测试使用
      if (ctx.header.accept.indexOf('1.0') !== -1) {
        const iscrm = ctx.header.accept.indexOf('crm') !== -1; // 是否是八骏的接口 目前有八骏和晨科
        // const iscrm = true;
        const params = ctx.request.body || {};
        // // 测试数据
        // const params = {
        //   // token: '{"agentId":"T7jiyC3WL","appKey":"9CW19C0e67C2d9c1cf99","appSecret":"e95e67eca6ad35b0fdfb1897419e0496a5e1c1008a486adea90e3fbf7f4374ad"}',
        //   serviceOrgCode: '91330106749463365L',
        //   anthemCompanyName: '杭州金昌宝顺汽车销售服务有限公司',
        //   anthemCompanyCode: '913301855687647082',
        //   anthemCompanyContact: '周枫',
        //   anthemCompanyContactPhoneNumber: '***********',
        //   enterpriseName: '杭州金昌宝顺汽车销售服务有限公司',
        //   enterpriseCode: '913301855687647082',
        //   projectName: '杭州金昌宝顺汽车销售服务有限公司2023年度定期检测',
        //   projectSN: 'ZJDPFR-233054',
        //   projectAddress: '浙江省杭州市临安区',
        //   projectIndustry: '8111',
        //   serviceType: '放射现状',
        //   isVip: '一般客户',
        //   projectContact: '周枫',
        //   contactPhone: '***********',
        //   contactEmail: '',
        //   employeeId: '黄婷婷',
        //   entrustDate: '2023-10-07 08:50:00',
        //   expectStartDate: '2023-10-17 00:00:00',
        //   expectCompleteDate: '',
        //   isOutsource: '',
        //   subcontractParams: '',
        //   projectAmount: '2500.00',
        //   salesFee: '0.00',
        //   reviewFee: '0.00',
        //   otherFee: '0.00',
        //   outsourceFee: '0.00',
        //   netValue: '2500.00',
        //   bankName: '中国农业银行临安市支行',
        //   invoiceAddress: '浙江省杭州市临安区玲珑街道锦溪南路1008号',
        //   invoiceAccount: '*****************',
        //   invoicePhoneNum: '0571-********',
        //   description: '',
        //   comments: '',
        //   salesChannel: '老客户',
        //   electronicFileType: '',
        //   projectAddresses: '["浙江省","杭州","临安区"]',
        //   expectedSalesFee: '0.00',
        //   expectedReviewFee: '0.00',
        //   expectedOtherFee: '0.00',
        //   expectedOutsourceFee: '0.00',
        // };
        let { electronicFileType, serviceOrgCode, projectName, isVip, projectSN, serviceType, enterpriseName, enterpriseCode, projectAddress, expectStartDate, expectCompleteDate, entrustDate, projectManager, mailingAddress, recipientPhoneNum, recipient, projectContact, contactPhone, contactEmail, projectIndustry, isSubcontract, subcontractParams, bankName, invoiceAddress, invoiceAccount, invoicePhoneNum, description, comments, vipRequirement, salesChannel, projectAmount, salesFee, expectedSalesFee, reviewFee, expectedReviewFee, otherFee, expectedOtherFee, netValue, expectedNetValue, outsourceFee, expectedOutsourceFee, salesId, salesName, salesPhone, anthemCompanyName, anthemCompanyCode, anthemCompanyContact, anthemCompanyContactPhoneNumber, employeeId, contractDate } = params;
        console.log(`1.${salesId}, 2.${serviceOrgCode}, 3.${enterpriseName}, 4.${enterpriseCode}, 5.${projectName}, 6.${projectSN}, 7.${projectAddress}, 8.${projectIndustry}, 9.${serviceType}, 10.${projectContact}, 11.${contactPhone}, 12.${contractDate}`, salesName, salesPhone, salesId, '--业务员', projectManager, '增加项目必传参数');
        anthemCompanyCode = anthemCompanyCode ? anthemCompanyCode.trim() : '';
        if (!(serviceOrgCode && enterpriseName && enterpriseCode && projectName && projectSN && projectAddress && projectIndustry && serviceType && projectContact && contactPhone)) {
          const param = [];
          if (!serviceOrgCode) param.push('机构信用代码');
          if (!enterpriseName) param.push('客户名称');
          if (!enterpriseCode) param.push('客户信用代码');
          if (!projectName) param.push('项目名称');
          if (!projectSN) param.push('项目编号');
          if (!projectAddress) param.push('项目地址');
          if (!serviceType) param.push('项目服务类型');
          if (!projectIndustry) param.push('项目行业分类');
          if (!projectContact) param.push('受检单位联系人');
          if (!contactPhone) param.push('受检单位联系人电话');
          if (!contractDate) params.push('签订日期');
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: `请检查添加项目的必传参数${param.join('、')}`,
          });
          return;
        }

        // 校验受检单位联系人
        const checkres = this.checkContact(contactPhone, projectContact, '受检单位');
        if (checkres) {
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: checkres,
          });
          return;
        }
        contactPhone = contactPhone.trim();
        projectContact = projectContact.trim();

        // 校验委托单位联系人
        const checkres2 = this.checkContact(anthemCompanyContactPhoneNumber, anthemCompanyContact, '委托单位', false);
        if (checkres2) {
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: checkres2,
          });
          return;
        }
        if (anthemCompanyContactPhoneNumber) anthemCompanyContactPhoneNumber = anthemCompanyContactPhoneNumber.trim();
        if (anthemCompanyContact) anthemCompanyContact = anthemCompanyContact.trim();

        // 行业分类
        const industryCategory = await ctx.model.IndustryCategory.aggregate([
          { $match: { 'children.children.children.value': projectIndustry } },
          { $unwind: '$children' },
          { $unwind: '$children.children' },
          { $unwind: '$children.children.children' },
          { $match: { 'children.children.children.value': projectIndustry } },
          { $addFields: {
            code: [ '$value', '$children.value', '$children.children.value', '$children.children.children.value' ],
            names: [ '$label', '$children.label', '$children.children.label', '$children.children.children.label' ],
          } },
        ]);
        if (!industryCategory.length) {
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: `行业分类编码：${projectIndustry}错误`,
          });
          return;
        }
        const industryCategoryInfo = industryCategory[0].names.join('/');
        projectIndustry = industryCategory[0].code;

        // 获取行业分类风险等级
        let riskLevel = '一般';
        if (industryCategory[0].children.children.children.riskType === 2) {
          riskLevel = '严重';
        } else if (industryCategory[0].children.children.children.riskType === 1) {
          riskLevel = '较重';
        }


        enterpriseCode = enterpriseCode.trim();
        // 处理部分信用代码
        const pattern = new RegExp('[0-9]+');
        const num = enterpriseCode.match(pattern);
        const index = enterpriseCode.indexOf(num);
        enterpriseCode = enterpriseCode.slice(index, enterpriseCode.length);
        console.log('增加项目正常调用');
        if (!/[A-Z0-9]{18}/g.test(enterpriseCode)) {
          console.log(enterpriseCode, '客户信用代码有误');
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: '客户信用代码有误',
          });
          return;
        }
        if (!/[A-Z0-9]{18}/g.test(serviceOrgCode)) {
          console.log(serviceOrgCode, '服务机构信用代码有误');
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: '服务机构信用代码有误',
          });
          return;
        }
        const company = await ctx.model.Adminorg.findOne({ code: { $regex: enterpriseCode.trim(), $options: 'i' } }); // 受检单位
        let anthemCompany;
        if (anthemCompanyCode) {
          anthemCompany = await ctx.model.Adminorg.findOne({ code: { $regex: anthemCompanyCode.trim(), $options: 'i' } }); // 委托单位
        }
        if (!company) {
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: '请先添加此客户',
          });
          return;
        }
        // const serviceCompany = await ctx.model.ServiceOrg.findOne({ organization: serviceOrgCode });'91330106749463365L' 晨科那边写死
        const serviceCompany = await ctx.model.ServiceOrg.findOne({ organization: { $regex: serviceOrgCode.trim(), $options: 'i' } }); // 先用这个看看后面有没有问题；
        console.log(`客户${company.cname}, 服务机构${serviceCompany ? serviceCompany.name : '没有服务机构信息'}-${serviceOrgCode}`, '增加项目信用代码获取');
        if (!serviceCompany) {
          ctx.helper.renderCustom(ctx, {
            status: 406,
            message: '请机构先注册service.zyws.cn账号',
          });
          return;
        }

        projectManager = await ctx.model.ServiceEmployee.findOne({ name: projectManager });
        let salesman;
        // 但是在后面重新赋值了salesman = employeeId; 这一段if else还有意义吗？？？，看不懂了
        if (salesId) {
          salesman = await ctx.model.ServiceEmployee.findOne({ ckId: salesId });
          salesman = salesman ? salesman.name : '';
        }
        if (!salesman) {
          if (salesPhone && salesName) {
            salesman = await ctx.model.ServiceEmployee.updateOne({ name: salesName, phoneNum: salesPhone }, { $set: { ckId: salesId } });
            salesman = salesman ? salesman.name : '';
          } else if (salesName) {
            salesman = await ctx.model.ServiceEmployee.updateOne({ name: salesName }, { $set: { ckId: salesId } });
            salesman = salesman ? salesman.name : '';
          }
        }
        // const arr = [ '职卫监测', '职卫监督', '职卫控评', '职卫现状', '职卫预评', '职卫检评', '职卫专篇', '职卫档案制作', '其他服务' ];
        // if (arr.indexOf(serviceType) === -1) {
        //   ctx.helper.renderSuccess(ctx, {
        //     data: res,
        //     message: '全流程未开通此项目类型',
        //     status: 200,
        //   });
        //   return;
        // }
        const projectType = [{
          projectSNStartStr: 'ZJDPFR',
          model: 'RadiateqlcProject',
        }, {
          projectSNStartStr: 'ZJDPFG',
          model: 'PersonalDoseProject',
        }];
        let model;
        projectType.forEach(item => {
          if (projectSN.startsWith(item.projectSNStartStr)) {
            model = item.model || '';
          }
        });
        if (!model) {
          model = 'JcqlcProject';
        }
        if (!model) {
          ctx.helper.renderSuccess(ctx, {
            data: res,
            message: '项目编号有误',
            status: 406,
          });
          return;
        }
        let isSampling = true;

        let hasServiceType = true;
        if (model === 'JcqlcProject') {
          // 职业卫生
          const serviceTypeObj = {
            职卫监测: '职业病危害因素日常监测',
            职卫监督: '职业病危害因素监督检测',
            职卫控评: '职业病危害控制效果评价',
            职卫现状: '职业病危害现状评价',
            职卫预评: '职业病危害预评价',
            职卫检评: '职业病危害因素检测',
            职卫专篇: '职卫专篇',
            职卫档案制作: '职卫档案制作',
            其他服务: '其他',
            职卫其他: '其他',
          };
          if (serviceType === '职卫预评' || serviceType === '职卫专篇') {
            isSampling = false;
          }
          serviceType = serviceTypeObj[serviceType];
          if (!serviceType) {
            hasServiceType = false;
          }
        } else if (model === 'RadiateqlcProject') {
          // 放射项目
          const radiateType = [ '工业放射检评', '放射专篇', '放射控评', '放射检评', '放射现状', '放射预评', '放射其他', '辐射监测', '辐射验收' ];
          if (!radiateType.includes(serviceType)) {
            hasServiceType = false;
          }
        } else {
          // 个人剂量
          if (serviceType !== '个人剂量') {
            hasServiceType = false;
          }
        }

        if (!hasServiceType) {
          ctx.helper.renderSuccess(ctx, {
            data: res,
            message: '全流程未开通此项目类型',
            status: 406,
          });
          return;
        }

        // switch (serviceType) {
        //   case '职卫监测':
        //     serviceType = '职业病危害因素日常监测';
        //     break;
        //   case '职卫监督':
        //     serviceType = '职业病危害因素监督检测';
        //     break;
        //   case '职卫控评':
        //     serviceType = '职业病危害控制效果评价';
        //     break;
        //   case '职卫现状':
        //     serviceType = '职业病危害现状评价';
        //     break;
        //   case '职卫预评':
        //     serviceType = '职业病危害预评价';
        //     isSampling = false;
        //     break;
        //   case '职卫检评':
        //     serviceType = '职业病危害因素检测';
        //     break;
        //   case '职卫专篇':
        //     serviceType = '职卫专篇';
        //     isSampling = false;
        //     break;
        //   case '职卫档案制作':
        //     serviceType = '职卫档案制作';
        //     break;
        //   case '其他服务':
        //   case '职卫其他':
        //     serviceType = '其他';
        //     break;
        //   default:
        //     serviceType = '暂未开展';
        //     break;
        // }
        let requiredTime = new Date(new Date().setMonth(new Date().getMonth() + 1));
        console.log(`业务员${salesman}, 服务类型${serviceType}`);
        if (!iscrm) { // 非crm的接口
          const first = projectSN.substring(0, 2);// 编号有更改，八骏那边为全名
          const last = projectSN.slice(2);
          projectSN = `ZJDP${first}-${last}`;
          if (isVip) {
            isVip = 'vip';
          } else {
            isVip = 'ordinary';
          }
        } else { // crm八骏接口
          if (!anthemCompany) {
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: '请先添加此委托单位',
            });
            return;
          }
          if (!/[A-Z0-9]{18}/g.test(anthemCompanyCode)) {
            console.log(anthemCompanyCode, '委托单位信用代码有误');
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: '委托单位信用代码有误',
            });
            return;
          }
          switch (isVip) {
            case '战略客户':
              isVip = 'svip';
              break;
            case 'VIP客户':
              isVip = 'vip';
              break;
            case '普通客户':
              isVip = 'ordinary';
              break;
            default:
              isVip = 'ordinary';
              break;
          }
          salesman = employeeId;
        }
        if (projectSN &&
          (projectSN.slice(4, 6) === 'ZZ' ||
            projectSN.slice(4, 6) === 'ZY' ||
            projectSN.slice(4, 6) === 'ZS' ||
            projectSN.slice(4, 6) === 'ZJ')
        ) {
          requiredTime = new Date(
            new Date().setDate(new Date().getDate() + 45)
          );
        } else if (projectSN) {
          requiredTime = new Date(
            new Date().setDate(new Date().getDate() + 21)
          );
        }
        // const oldJobHealthProject = await ctx.model.JobHealth.findOne({ projectNumber: projectSN });

        const oldjcqlcProject = await ctx.model[model].findOne({ projectSN });


        // 处理地址
        const addressInfo = params.projectAddress;
        // projectAddress = await ctx.service.synchroChenKeProjects.findRegAdd(projectAddress);
        if (iscrm) {
        // 校验
          if (!params.projectAddresses) {
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: '必填字段：projectAddresses，不能为空',
            });
            return;
          }
          try { // 异常捕获一下，防止传入的不是json标准格式
            params.projectAddresses = JSON.parse(params.projectAddresses);
          } catch (error) {
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: `projectAddresses：${params.projectAddresses} json格式错误，请检查`,
            });
            return;
          }
          if (!Array.isArray(params.projectAddresses)) {
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: '字段projectAddresses类型错误，请检查',
            });
            return;
          }
          if (!params.projectAddresses.length) {
            ctx.helper.renderCustom(ctx, {
              status: 406,
              message: '请完善项目地址，项目地址：省份，城市，地区，街道暂未填写',
            });
            return;
          }
          // 八骏直辖市数据 [直辖市,上海市，xxx区] 需要转换成 [上海市,直辖区,xxx区]
          if (params.projectAddresses[0] === '直辖市') {
            params.projectAddresses[0] = params.projectAddresses[1];
            params.projectAddresses[1] = '直辖区';
          }
          // 处理八骏的地址，得到地址编码
          projectAddress = await ctx.service.synchroChenKeProjects.getAreaCode(params.projectAddresses);
        } else {
          projectAddress = await ctx.service.synchroChenKeProjects.findRegAdd(projectAddress);
        }

        const workPlaces = {
          workAddName: projectAddress.areaNames.join('/'),
          name: addressInfo,
          workAdd: projectAddress.codes,
        };

        const companyAddress = [{ address: addressInfo, districts: projectAddress.areaNames }];

        // 电子档案类型
        const electronicFileTypes = [ 1, 2, 3 ];
        electronicFileType = Number(electronicFileType);
        if (electronicFileType && !electronicFileTypes.includes(electronicFileType)) {
          return ctx.helper.renderCustom(ctx, {
            status: 406,
            message: '电子档案类型错误',
          });
        }
        // 企业规模
        const companyScale = {
          大型: '大型',
          中型: '中型',
          小型: '小型',
          微型: '微型',
          大: '大型',
          中: '中型',
          小: '小型',
          微: '微型',
          其他: '其他',
          大型企业: '大型',
          中型企业: '中型',
          小型企业: '小型',
          微型企业: '小型',
          其他企业: '其他',
        };
        if (company.companyScale) {
          company.companyScale = companyScale[company.companyScale] || '';
          if (!company.companyScale) {
            return ctx.helper.renderCustom(ctx, {
              status: 406,
              message: '企业规模有误',
            });
          }
        } else {
          company.companyScale = '';
        }
        console.log('企业规模', company.companyScale);
        const anthemCompanyAddress = anthemCompany ? (anthemCompany.workAddress || []) : (companyAddress || []);
        const anthemCompanyDistrictRegAdd = anthemCompanyAddress.length ? anthemCompanyAddress[0].districts : [];
        const jcqlcFields = {
          EnterpriseID: company._id, // 企业id
          anthemEnterpriseID: anthemCompany ? anthemCompany._id : company._id,
          anthemCompanyName: anthemCompanyName ? anthemCompanyName : enterpriseName, // 委托单位名称
          anthemCompanyID: anthemCompany ? anthemCompany.code : enterpriseCode, // 委托单位信用代码
          anthemCompanyContact: anthemCompany ? anthemCompanyContact : projectContact, // 委托单位联系人
          anthemCompanyAddress,
          anthemCompanyDistrictRegAdd,
          companyID: enterpriseCode, // 企业税号
          EnterpriseName: enterpriseName, // 企业名称
          companyAddress, // 企业地址
          companyContact: projectContact, // 企业联系人
          anthemCompanyContactPhoneNumber: anthemCompanyContactPhoneNumber || contactPhone, // 委托单位联系人电话
          companyContactPhoneNumber: contactPhone, // 联系人电话
          companyContactEmail: contactEmail || '', // email
          companyIndustry: projectIndustry || [], // 不确定格式，字符串要换成数组，八骏的行业需要匹配，注意兼容晨科   格式和多谱系统不统一！！！！
          // name: serviceCompany.name, // 机构名称
          serviceOrgId: serviceCompany._id, // 改成机构id
          projectName, // 项目名称
          projectSN, // 项目编号
          isVIP: isVip, // 会员类型
          serviceType, // 项目类型，只要职业卫生的几种
          // progress: {
          //   createProject_time: {
          //     status: 2,
          //     completedTime: Date.now(),
          //   },
          // },好像有默认值
          // date: Date.now(), // 创建日期
          electronicFileType, // 电子档案类型
          requiredTime,
          expectStartTime: expectStartDate, // 预计开始时间
          expectStopTime: expectCompleteDate, // 预计结束时间
          contractDate, // 签订日期
          entrustDate, // 委托日期
          salesman, // 业务员
          personInCharge: projectManager ? projectManager._id : '',
          farmOut: isSubcontract, // 是否分包
          farmOutParams: subcontractParams, // 分包参数
          workPlaces: workPlaces ? [ workPlaces ] : [], // 工作地址
          projectPrice: projectAmount, // 项目价格
          cooperationFee: salesFee, // 合作费
          expectedCooperationFee: expectedSalesFee, // 预计合作费
          reviewFee, // 评审费
          expectedReviewFee, // 预计评审费
          otherFee, // 其他费
          expectedOtherFee, // 预计其他费用
          netEarnings: netValue || 0, // 净赚
          expectedNetEarnings: expectedNetValue || netValue || 0, // 预计净赚
          outsourceFee, // 分包费
          expectedOutsourceFee, // 预计分包费
          mailingAddress, // 邮寄地址
          recipient, // 收件人
          recipientPhoneNum, // 收件号码
          accountsBank: bankName, // 开户行
          accountsAddress: invoiceAddress, // 开户地址
          accountsNumber: invoiceAccount, // 银行账户
          accountsPhoneNum: invoicePhoneNum, // 银行电话？？？
          // serviceCorp: serviceCompany.corp,机构法人
          description,
          comment: `${comments ? comments : ''};${salesChannel ? salesChannel : ''}`,
          vipRequirement,
          source: 'ck',
          districtRegAdd: company.districtRegAdd,
          regAdd: company.regAdd,
          companyScale: company.companyScale,
          regType: company.regType,
          corp: company.corp,
          isSampling, // 是否采样，评价项目默认false
          riskLevel,
          industryCategoryInfo,
        };
        // 处理放射项目数据
        if (model !== 'JcqlcProject') {
          if (model === 'RadiateqlcProject') {
            let detectionType = [];
            const options1 = [ '防护检测', '性能检测' ];
            const options2 = '防护检测';
            if (serviceType === '放射检评' || serviceType === '放射控评') {
              detectionType = options1;
            // if (!detectionType || !detectionType.length) {
            //   return ctx.helper.renderCustom(ctx, {
            //     status: 406,
            //     message: '放射项目检测类型不能为空',
            //   });
            // } else if (detectionType.filter(item => !options1.includes(item)).length) {
            //   return ctx.helper.renderCustom(ctx, {
            //     status: 406,
            //     message: '检测类型错误',
            //   });
            // }
            } else if (serviceType === '放射现状' || serviceType === '辐射验收' || serviceType === '工业放射检评') {
              detectionType = options2;
            // if (!detectionType || !detectionType.length) {
            //   return ctx.helper.renderCustom(ctx, {
            //     status: 406,
            //     message: '放射项目检测类型不能为空',
            //   });
            // } else if (detectionType.length > 1 || detectionType[0] !== options2) {
            //   return ctx.helper.renderCustom(ctx, {
            //     status: 406,
            //     message: '检测类型错误',
            //   });
            // }
            }
            jcqlcFields.detectionType = detectionType;

          }
          const salesmanInfo = await ctx.model.ServiceEmployee.findOne({ name: salesman }, { _id: 1 });
          jcqlcFields.salesman = salesmanInfo._id;
          jcqlcFields.anthemCompanyRegAdd = anthemCompany ? anthemCompany.regAdd : company.regAdd;
        }
        if (!oldjcqlcProject) {
          res = await ctx.model[model].create(jcqlcFields);
          // if (isRadiateqlcProject) { // 放射项目
          //   // 放射项目合同审批
          //   dingRes = await ctx.service.synchroChenKeProjects.projectCreateApprovalRad(jcqlcFields, salesman, res._id);
          // } else {
          //   dingRes = await ctx.service.synchroChenKeProjects.projectCreateApproval(jcqlcFields, salesman, res._id);
          // }
          console.log(res, '项目添加成功');
          ctx.helper.renderSuccess(ctx, {
            message: '项目添加成功',
            status: 200,
          });
          // if (dingRes.errMsg) {
          //   ctx.auditLog('发起钉钉合同审批', `项目编号：${projectSN}，发起合同审批失败，错误信息：${dingRes.errMsg}`, 'error');
          // } else {
          //   ctx.auditLog('发起钉钉合同审批', `项目编号：${projectSN}，发起合同审批成功，审批实例id：${dingRes.process_instance_id}`, 'info');
          // }
          if (model === 'RadiateqlcProject') {
            // 创建流程节点
            await ctx.service.radiateqlcProject.initProjectProgress(res._id, res.detectionType, serviceCompany._id);
          }


        } else {
          // 不更新实际费用
          delete jcqlcFields.cooperationFee;
          delete jcqlcFields.reviewFee;
          delete jcqlcFields.otherFee;
          delete jcqlcFields.netEarnings;
          delete jcqlcFields.outsourceFee;
          // const updateOpt = {};
          // this.getDiffFields(updateOpt, jcqlcFields, oldjcqlcProject);
          ctx.auditLog('八骏更新项目信息', `更新的数据：${jcqlcFields}`, 'info');
          await ctx.model[model].updateOne({ _id: oldjcqlcProject._id }, { $set: jcqlcFields });

          // 未创建审批或是拒绝或被终止(撤销)的审批才可以发起审批
          // if (!oldjcqlcProject.process_instance_id || !oldjcqlcProject.progress.approved || oldjcqlcProject.progress.approved.status > 2) {
          //   const dingRes = await ctx.service.synchroChenKeProjects.projectCreateApproval(jcqlcFields, salesman, oldjcqlcProject._id);
          //   if (dingRes.errMsg) {
          //     ctx.auditLog('发起钉钉合同审批', `项目编号：${oldjcqlcProject.projectSN}，发起合同审批失败，错误信息：${dingRes.errMsg}`, 'error');
          //   } else {
          //     ctx.auditLog('发起钉钉合同审批', `项目编号：${oldjcqlcProject.projectSN}，发起合同审批成功，审批实例id：${dingRes.process_instance_id}`, 'info');
          //   }
          // }
          // await ctx.model.JobHealth.findOneAndUpdate({ projectNumber: projectSN },{$set:});
          console.log('项目信息同步成功, 项目编号已添加过');
          ctx.helper.renderCustom(ctx, {
            status: 200,
            message: '项目信息同步成功, 项目编号已添加过',
          });
          return;
        }
      } else {
        console.log('请检查请求头版本');
        ctx.helper.renderCustom(ctx, {
          status: 406,
          message: '请检查请求头版本',
        });
        return;
      }
    } catch (err) {
      console.log(err, '错误');
      ctx.helper.renderCustom(ctx, {
        status: 500,
        data: err,
        message: '错误',
      });
    }
  },

  async projectStatus(ctx) {
    try {
      const params = ctx.request.body || {};
      const { projectSN, status } = params;
      const projectType = [{
        projectSNStartStr: 'ZJDPFR',
        model: 'RadiateqlcProject',
      }, {
        projectSNStartStr: 'ZJDPZ',
        model: 'JcqlcProject',
      }, {
        projectSNStartStr: 'ZJDPFG',
        model: 'PersonalDoseProject',
      }];
      let model;
      projectType.forEach(item => {
        if (projectSN.startsWith(item.projectSNStartStr)) {
          model = item.model;
        }
      });
      if (!model) return;
      const arr = [ 'normal', 'suspend', 'stop' ];
      if (!projectSN || !status || (arr.indexOf(status) === -1)) {
        ctx.helper.renderCustom(ctx, {
          status: 406,
          message: '请检查必传参数是否正确',
        });
        return;
      }
      const project = await ctx.model[model].findOne({ projectSN });
      let message = '该项目未同步全流程';
      if (project) {
        if (status === 'normal') await ctx.model[model].updateOne({ projectSN }, { $set: { projectStop: false, projectCancel: false } });
        if (status === 'suspend') await ctx.model[model].updateOne({ projectSN }, { $set: { projectStop: true } });
        if (status === 'stop') await ctx.model[model].updateOne({ projectSN }, { $set: { projectCancel: true } });
        ctx.helper.renderSuccess(ctx, {
          message: '状态同步成功',
          status: 200,
        });
      } else {
        if (status === 'normal') {
          message = '该项目未同步全流程,恢复后请重新提交至全流程';
        }
        ctx.helper.renderSuccess(ctx, {
          message,
          status: 200,
        });
      }
    } catch (err) {
      console.log(err, '同步全流程项目状态错误');
      ctx.auditLog('同步全流程项目状态错误', `${err} 。`, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 500,
        data: err,
        message: '错误',
      });
    }
  },
};

module.exports = SynchroChenKeProjectsController;

