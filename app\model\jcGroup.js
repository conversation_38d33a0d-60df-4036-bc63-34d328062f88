/**
 * 机构端-service 员工角色表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const JcGroupSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: String, // 机构id
    isFirst: {
      type: Boolean,
      default: false,
    },
    name: String,
    power: [{
      type: String,
      ref: 'AdminResource',
    }],
    date: {
      type: Date,
      default: Date.now,
    },
    comments: String,
  });

  return mongoose.model('JcGroup', JcGroupSchema, 'jcGroups');

};

