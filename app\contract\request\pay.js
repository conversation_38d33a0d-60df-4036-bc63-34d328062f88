'use strict';
module.exports = {
  // 示例响应参数
  baseRequest: {
    id: { type: 'string', description: 'id 唯一键', required: true, example: '1' },
  },
  payRequestAmount: {
    total: { type: 'number', description: '金额', required: true, example: 1 },
  },
  payRequest: {
    amount: { type: 'payRequestAmount' },
    description: { type: 'string', description: '商品描述', required: true, example: '职卫云公仔' },
    out_trade_no: { type: 'string', description: '订单号', required: true, example: '123123113' },
    type: {
      type: 'string',
      values: [ 'wxpay', 'alipay' ],
      required: true,
      description: '支付类型，可选值为 wxpay 或 alipay',
      example: 'wxpay',
    },
  },
};
