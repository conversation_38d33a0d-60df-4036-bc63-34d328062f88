const Controller = require('egg').Controller;

// const { request } = require('http');
// const moment = require('moment');

class SuperManageController extends Controller {

  // 退出登录 测试
  async logOutAction() {
    const ctx = this.ctx;
    ctx.auditLog('退出登录', '当前用户进行了退出登录操作。', 'info');
    ctx.session = null;
    ctx.cookies.set('admin_' + this.app.config.auth_jcqlccookie_name, null);
    ctx.cookies.set('admin_doracmsapi', null);
    ctx.helper.renderSuccess(ctx);
  }

  // 获取企业 测试
  async getOne() {
    const { ctx } = this;
    try {
      const _id = ctx.query.id;
      const targetItem = await ctx.service.adminorg.item(ctx, {
        query: {
          _id,
        },
      });
      ctx.helper.renderSuccess(ctx, {
        data: targetItem,
      });
    } catch (err) {
      console.log(err);
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }
}

module.exports = SuperManageController;
