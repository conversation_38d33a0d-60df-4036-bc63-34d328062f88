const Service = require('egg').Service;
const path = require('path');
const _ = require('lodash');

const {
  _unionQuery,
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _removeAll,
} = require(path.join(process.cwd(), 'app/service/general'));


class SuperUserService extends Service {

  async unionQuery(payload, {
    collections = [],
    unwindArray = [],
    query = {},
    searchKeys = [],
    files = null,
    sort = {},
    statisticsFiles = [],
  } = {}) {

    const listdata = _unionQuery(this.ctx.model.SuperUser, payload, {
      collections,
      unwindArray,
      query,
      searchKeys,
      files,
      sort,
      statisticsFiles,
    });
    return listdata;
  }

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
    sort = {},
  } = {}) {

    const listdata = _list(this.ctx.model.SuperUser, payload, {
      query,
      searchKeys,
      populate,
      files,
      sort,
    });
    return listdata;

  }


  async count(params = {}) {
    return _count(this.ctx.model.SuperUser, params);
  }

  async create(payload) {
    return _create(this.ctx.model.SuperUser, payload);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.SuperUser, values, key);
  }

  async removeAll() {
    return _removeAll(this.ctx.model.SuperUser);
  }

  async update(res, _id, payload) {
    return _update(res, this.ctx.model.SuperUser, _id, payload);
  }

  async item(res, {
    query = {},
    populate = [],
    files = null,
  } = {}) {
    return _item(res, this.ctx.model.SuperUser, {
      files: files ? files : {
        password: 0,
        email: 0,
      },
      query,
      populate: !_.isEmpty(populate) ? populate : [{
        path: 'group',
        select: 'power _id enable name',
      }],
    });
  }

}

module.exports = SuperUserService;
