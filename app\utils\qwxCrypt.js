// app/extend/wecomEncryptor.js
const { getSignature, decrypt } = require('@wecom/crypto');

class WecomEncryptor {
  constructor(token, encodingAESKey, corpId) {
    this.token = token;
    this.encodingAESKey = encodingAESKey;
    this.corpId = corpId;
  }

  verifyURL(msgSignature, timestamp, nonce, echostr) {
    const signature = getSignature(this.token, timestamp, nonce, echostr);

    if (signature === msgSignature) {
      // 解密 echostr
      const decrypted = decrypt(this.encodingAESKey, echostr);
      if (decrypted && decrypted.id === this.corpId) {
        return decrypted.message;
      }
      throw new Error('Invalid CorpID');

    } else {
      throw new Error('Invalid signature');
    }

  }
}

module.exports = WecomEncryptor;
