const Service = require('egg').Service;
const dingApprovalData = require('../utils/dingApprovalData');
class DingTalkWorkFlowService extends Service {
  // 通过电话号码获取钉钉用户信息 htt++++
  async dingTalkGteUserInfoByPhone(phoneNumber, access_token) {
    const {
      ctx,
    } = this;
    const oapiHost = 'https://oapi.dingtalk.com';
    try {
      // const access_token = (await this.dingTalkApiGettoken(AppKey,AppSecret)).access_token;
      const getUserIdOption = {
        data: {
          mobile: phoneNumber,
        },
        dataType: 'json',
        method: 'POST',
      };
      const dingTalkUserIdBack = await ctx.curl('https://oapi.dingtalk.com/topapi/v2/user/getbymobile?access_token=' + access_token, getUserIdOption);
      const dingTalkUserId = dingTalkUserIdBack.data.result && dingTalkUserIdBack.data.result.userid;
      const getUserInfoOption = {
        data: {
          userid: dingTalkUserId,
        },
        dataType: 'json',
        method: 'get',
      };
      const dingTalkUserInfoBack = await ctx.curl(oapiHost + '/user/get?access_token=' + access_token, getUserInfoOption);
      if (dingTalkUserInfoBack.data.errcode === 60020) {
        return {
          errmsg: dingTalkUserInfoBack.data.errmsg,
        };
      } else if (dingTalkUserInfoBack.data.errcode !== 0) {
        return {
          errmsg: dingTalkUserInfoBack.data.sub_msg,
        };
      }
      return dingTalkUserInfoBack.data;
    } catch (error) {
      console.log(error);
    }
  }

  // 获取钉钉token凭据
  async dingTalkApiGettoken(AppKey, AppSecret) {
    const {
      ctx,
    } = this;
    let access_token = ctx.helper.getCache('dingTalkToken');
    const oapiHost = 'https://oapi.dingtalk.com';
    // 多谱
    // CorpId:'ding2fa4df5d78220543',
    // AgentId: 869258467 ,
    // AppKey: dingfrf5xbl2uhcecjpn,
    // AppSecret: bKNHDi4E6YUoPVCubcDsWbVuPwtFkHwfCZQguYxEUZG1t-wt_XKEb_V8EfUf-MhY
    return new Promise(async (resolve, rejected) => {
      try {
        if (!access_token) {
          // const { AppKey, AppSecret } = this.app.config.dingInfo;
          const getOptions = {
            data: {
              appkey: AppKey,
              appsecret: AppSecret,
            },
            timeout: 8000,
            dataType: 'json',
            method: 'GET',
          };
          const tokenBack = await ctx.curl(oapiHost + '/gettoken', getOptions);
          access_token = tokenBack.data.access_token;
          ctx.helper.setCache('dingTalkToken', access_token, 1000 * tokenBack.data.expires_in);
        }
        resolve({
          access_token,
        });
      } catch (error) {
        console.log('钉钉取凭证接口出错！', error);
        const err = {
          errcode: 1,
          errmsg: error,
        };
        rejected(err);
      }
    });
  }

  // // 处理钉钉审批回调
  // async handleDingApproval({ nodes, obj, res }) {
  //   const { ctx } = this;
  //   const operation_results = res.info.operation_records.filter(item => item.operation_result === 'AGREE');
  //   // if (!operation_results.length) {
  //   //   ctx.body = JSON.stringify(data);
  //   //   return;
  //   // }
  //   const signIndex = operation_results.length - 1;
  //   console.log(signIndex, '审批序号？？？？', obj.processInstanceId, '实例id');
  //   const currentEmployeeId = signIndex !== -1 ? operation_results[operation_results.length - 1].serviceEmployeeId : '';

  //   const qlcProcessCodes = this.app.config.qlcProcessCodes; // 审批配置信息
  //   const radProcessCodes = this.app.config.radProcessCodes; // 放射审批配置
  //   // 查询当前的审批记录
  //   const approveRecord = await ctx.model.ApproveRecord.findOne({ processInstanceId: obj.processInstanceId });
  //   let approveRecords;
  //   if (approveRecord) {
  //     // operation_records.length = 1 signIndex = 1
  //     approveRecords = approveRecord.operation_records.filter(item => item.operation_result === 'AGREE');
  //     console.log(approveRecords.length - 1, signIndex, 8768956875);
  //   } else {
  //     console.log('没记录？？？？');
  //   }

  //   // 如果审批记录小于等于1，说明是第一次审批，需要创建一条审批记录
  //   if (res.info.operation_records.length <= 1) await this.createApproveRecord(res.info);
  //   // if (nodes[0] === 'personapproveInfo') {
  //   //   // 个人剂量
  //   //   if (!await ctx.model.ApproveRecord.findOne({ processInstanceId: obj.processInstanceId })) {
  //   //     await ctx.service.dingTalkWorkFlow.createApproveRecord(res.info);
  //   //   }
  //   //   if (!approveRecord || approveRecords.length - 1 < signIndex) {
  //   //     if (res.info.result === 'agree') {
  //   //       const originatorEmployeeId = res.info.operation_records[0].serviceEmployeeId;
  //   //       await ctx.service.personalDose.signPdf({ originatorEmployeeId, statusField: personapproveInfo.statusField, currentEmployeeId, processInstanceField: personapproveInfo._idField, processInstanceId: obj.processInstanceId, signIndex });
  //   //     }
  //   //     // 审核通过后,给相应的pdf添加盖章和骑缝章并添加电子签名
  //   //     if (res.qlcProjectStatus === 2) {
  //   //       // 项目审批状态未更新为2，才执行这个逻辑
  //   //       const personalDoseData = await ctx.model.PersonalDoseProject.findOne({ [personapproveInfo._idField]: obj.processInstanceId });
  //   //       if (personalDoseData && personalDoseData.progress[personapproveInfo.statusField].status < 2) {
  //   //         await ctx.service.personalDose.signPdf({ statusField: personapproveInfo.statusField, currentEmployeeId, processInstanceField: personapproveInfo._idField, processInstanceId: obj.processInstanceId, isComplete: true });
  //   //       }

  //   //     }
  //   //     await this.updateApproveRecord({ processInstanceId: obj.processInstanceId }, res.info);
  //   //     await ctx.service.personalDose.updateModifystatus(res, personapproveInfo.statusField);
  //   //   }
  //   // } else
  //   console.log(nodes, '审批节点111');
  //   if (nodes[0] === 'RadiateqlcProject') {
  //     // 放射审批
  //     let radapproveInfo = radProcessCodes.filter(item => item.node === nodes[1]);
  //     radapproveInfo = radapproveInfo[0];
  //     // 因为放射公用一个审批模板，所以需要根据不同的业务类型来查询不同的项目
  //     const personalProject = await ctx.model.PersonalDoseProject.findOne({ [radapproveInfo._idField]: obj.processInstanceId }).lean();
  //     const industrialProject = await ctx.model.IndustrialRayProject.findOne({ [radapproveInfo._idField]: obj.processInstanceId }).lean();
  //     const radiateqlcProject = await ctx.model.RadiateqlcProject.findOne({ [radapproveInfo._idField]: obj.processInstanceId }).lean();
  //     console.log(!!personalProject, '个人剂量项目', !!industrialProject, '工业放射项目', !!radiateqlcProject, '放射项目');

  //     if (personalProject) {
  //       const { projectSN, progress } = personalProject;
  //       // 使用放射审批模板的个人剂量审批
  //       const personapproveInfo = radapproveInfo;
  //       if (res.info.result === 'agree' && (!approveRecord || approveRecords.length - 1 < signIndex)) {
  //         const originatorEmployeeId = res.info.operation_records[0].serviceEmployeeId;
  //         try {
  //           await ctx.service.personalDose.signPdf({ originatorEmployeeId, statusField: personapproveInfo.statusField, currentEmployeeId, processInstanceField: personapproveInfo._idField, processInstanceId: obj.processInstanceId, signIndex });
  //         } catch (error) {
  //           console.log(error, '个人剂量项目' + projectSN + '签名失败!');
  //         }
  //         // 审核通过后，项目审批状态未更新为2，给相应的pdf添加盖章和骑缝章并添加电子签名
  //         if (res.qlcProjectStatus === 2 && progress?.[personapproveInfo.statusField]?.status < 2) {
  //           try {
  //             await ctx.service.personalDose.signPdf({ statusField: personapproveInfo.statusField, currentEmployeeId, processInstanceField: personapproveInfo._idField, processInstanceId: obj.processInstanceId, isComplete: true });
  //           } catch (error) {
  //             console.log(error, '个人剂量项目' + projectSN + '盖章失败!');
  //           }
  //         }
  //       }
  //       await this.updateApproveRecord({ processInstanceId: obj.processInstanceId }, res.info);
  //       await ctx.service.personalDose.updateModifystatus(radapproveInfo._idField, res, personapproveInfo.statusField);
  //     } else if (industrialProject) {
  //       const { projectSN, progress } = industrialProject;
  //       // 使用放射审批模板的工业放射审批
  //       const industryapproveInfo = radapproveInfo;
  //       if (res.info.result === 'agree' && (!approveRecord || approveRecords.length - 1 < signIndex)) {
  //         const originatorEmployeeId = res.info.operation_records[0].serviceEmployeeId;
  //         try {
  //           await ctx.service.industrialRay.signPdf({ originatorEmployeeId, statusField: industryapproveInfo.statusField, currentEmployeeId, processInstanceField: industryapproveInfo._idField, processInstanceId: obj.processInstanceId, signIndex: res.info.operation_records.length - 2 });
  //         } catch (error) {
  //           console.log(error, '工业放射项目' + projectSN + '签名失败!');
  //         }
  //         // 审核通过后,给相应的pdf添加盖章和骑缝章并添加电子签名
  //         if (res.qlcProjectStatus === 2 && progress?.[industryapproveInfo.statusField]?.status < 2) {
  //           try {
  //             await ctx.service.industrialRay.signPdf({ statusField: industryapproveInfo.statusField, currentEmployeeId, processInstanceField: industryapproveInfo._idField, processInstanceId: obj.processInstanceId, isComplete: true });
  //           } catch (error) {
  //             console.log(error, '工业放射项目' + projectSN + '盖章失败!');
  //           }
  //         }
  //       }
  //       await this.updateApproveRecord({ processInstanceId: obj.processInstanceId }, res.info);
  //       await ctx.service.industrialRay.updateModifystatus(radapproveInfo._idField, res, industryapproveInfo.statusField);
  //     } else {
  //       // 使用放射审批模板的放射审批
  //       let radiateProgress = await ctx.model.RadiateqlcProgress.aggregate([
  //         { $match: { radiateqlcProjectId: radiateqlcProject._id } },
  //         { $unwind: '$progresses' },
  //         { $match: {
  //           'progresses.field': 'reportReview',
  //         } },
  //       ]);
  //       radiateProgress = radiateProgress?.[0] || null;
  //       if (!radiateProgress || radiateProgress?.progresses?.status >= 2) return;
  //       // 审核通过后,给相应的pdf添加签名
  //       console.log(obj.type, res.info.result, 239578348957);
  //       // 创建审批记录，更新状态
  //       await ctx.service.radiateqlcProject.handleApprove(
  //         radapproveInfo,
  //         obj,
  //         radapproveInfo.statusField,
  //         res
  //       );
  //       const { projectSN } = radiateqlcProject;
  //       // 审核通过后,给相应的pdf添加签名
  //       if (res.info.result === 'agree' && (!approveRecord || approveRecords.length - 1 < signIndex)) {
  //         const originatorEmployeeId = res.info.operation_records[0].serviceEmployeeId;
  //         try {
  //           await ctx.service.radiateqlcProject.signPdf({ originatorEmployeeId, statusField: radapproveInfo.statusField, currentEmployeeId, processInstanceField: radapproveInfo._idField, processInstanceId: obj.processInstanceId, signIndex });
  //         } catch (error) {
  //           console.log(error, '医疗放射项目' + projectSN + '签名失败!');
  //         }
  //         // 审核通过后,给相应的pdf添加盖章和骑缝章并添加电子签名
  //         if (res.qlcProjectStatus === 2) {
  //           // 项目审批状态未更新为2，才执行这个逻辑
  //           try {
  //             await ctx.service.radiateqlcProject.signPdf({ statusField: radapproveInfo.statusField, currentEmployeeId, processInstanceField: radapproveInfo._idField, processInstanceId: obj.processInstanceId, isComplete: true });
  //           } catch (error) {
  //             console.log(error, '医疗放射项目' + projectSN + '盖章失败!');
  //           }
  //         }
  //       }
  //       if (radapproveInfo.statusField === 'approved') {
  //         // 处理放射合同审批签名
  //         await ctx.service.radiateqlcProject.updateapproveSign(res);
  //       } else if (radapproveInfo.statusField === 'MApplyStatus') {
  //         // 更新修改审批状态
  //         await ctx.service.radiateqlcProject.updateModifystatus(res);
  //       }
  //     }
  //   } else {
  //     // 检测全流程审批
  //     let approveInfo = qlcProcessCodes.filter(item => item.node === nodes[1]);
  //     approveInfo = approveInfo[0];
  //     // 查询审核详情
  //     // const res = await ctx.service.dingTalkWorkFlow.getProcessinstance(
  //     //   obj.processInstanceId,
  //     //   AppKey,
  //     //   AppSecret
  //     // );
  //     // console.log(approveInfo, res.info.operation_records, res.info.form_component_values, '结果===');

  //     res.info.processInstanceId = obj.processInstanceId;

  //     // console.log(JSON.stringify(res), '全部结果');
  //     // console.log(res.qlcProjectStatus, '审批结果')

  //     // // 更新审批记录表
  //     // if (!await ctx.model.ApproveRecord.findOne({ processInstanceId: obj.processInstanceId })) {
  //     //   // 发起审批动作
  //     //   await ctx.service.dingTalkWorkFlow.createApproveRecord(res.info);
  //     //   // 更新全流程审批相关字段（在全流程端）
  //     // }
  //     // console.log(!approveRecord || approveRecords.length - 1 < signIndex, approveRecord, approveRecords.length, signIndex, 'approveRecords');
  //     // console.log(obj.type, 'obj.type');
  //     const jcqlcInfo = await ctx.model.JcqlcProject.findOne({ [approveInfo._idField]: obj.processInstanceId });
  //     if (!jcqlcInfo) {
  //       console.log(obj.processInstanceId, '审批id未绑定到项目表中111111');
  //       return;
  //     }
  //     if (res.info.result === 'agree' && (!approveRecord || approveRecords.length - 1 < signIndex)) { // 中间以及结束节点
  //       // 审核通过后,给相应的pdf添加签名
  //       const originatorEmployeeId = res.info.operation_records[0].serviceEmployeeId;
  //       await ctx.service.jcqlcProject.signPdf({ originatorEmployeeId, statusField: approveInfo.statusField, currentEmployeeId, processInstanceField: approveInfo._idField, processInstanceId: obj.processInstanceId, signIndex });
  //       // 审核通过后,给相应的pdf添加盖章和骑缝章并添加电子签名
  //       if (res.qlcProjectStatus === 2) {
  //         if (jcqlcInfo && jcqlcInfo.progress[approveInfo.statusField].status < 2) {
  //           await ctx.service.jcqlcProject.signPdf({ statusField: approveInfo.statusField, processInstanceField: approveInfo._idField, currentEmployeeId, processInstanceId: obj.processInstanceId, isComplete: true });
  //         }
  //       }
  //     }
  //     await this.updateApproveRecord({ processInstanceId: obj.processInstanceId }, res.info);
  //     // 如果检测报告已经通过审核，实验室数据无法再填写，其他状态还是可以填写 jhw ++
  //     if ([ 'labreport' ].includes(approveInfo.approveField)) {
  //       await ctx.service.jcqlcProject.uploadAmendStatus(
  //         res.qlcProjectStatus,
  //         res.info.form_component_values,
  //         'report'
  //       );
  //     }
  //     if (res.qlcProjectStatus > 1) {
  //       console.log('有没有进来', jcqlcInfo.projectSN, approveInfo._idField, obj.processInstanceId);
  //       // 更新全流程项目状态表
  //       // 只有不是创建的时候才走这个更新方法，因为如果是创建的话 无法绑定项目id
  //       await ctx.service.jcqlcProject.updateOne(
  //         { [approveInfo._idField]: obj.processInstanceId },
  //         {
  //           [`progress.${approveInfo.statusField}`]: {
  //             status: res.qlcProjectStatus,
  //             completedTime: obj.finishTime,
  //           },
  //         }
  //       );
  //     }
  //     // #标准物质领用审批
  //     if ([ 'applyAcceptance' ].includes(approveInfo.approveField)) {
  //       const data = await ctx.model.ReceiptRecord.findOne({
  //         applyAcceptanceProcessInstanceId: obj.processInstanceId,
  //       });
  //         // console.log('data是森么', data,obj.processInstanceId);
  //       let result;
  //       result = await ctx.model.ReceiptRecord.updateOne(
  //         { _id: data._id },
  //         {
  //           $set: {
  //             'applyAcceptanceApproved.status': res.qlcProjectStatus,
  //             'applyAcceptanceApproved.completedTime': obj.finishTime,
  //           },
  //         }
  //       );
  //       console.log(result, '审批状态更新');
  //       // 通过：1.更新库存 2.领用时间默认为通过当天、 3.储备液、质控样可以使用标准物质 4.更新审批状态
  //       if (res.qlcProjectStatus === 2) {
  //         if (data) {
  //           const tasks = res.info.tasks;
  //           const dingAuthorizerId = tasks[tasks.length - 1];
  //           const dingAuthorizer =
  //               await ctx.service.dingTalkWorkFlow.dingTalkGteUserInfoById(
  //                 dingAuthorizerId.userid
  //               );
  //             // console.log(dingAuthorizer,dingAuthorizer.data.mobile, 'ding审批人')
  //           const jcUser = await ctx.model.JcUser.findOne({
  //             phoneNum: dingAuthorizer.data.result.mobile,
  //           }); // 普通员工账号
  //             // console.log(jcUser, '审批人')
  //           result = await ctx.model.ReceiptRecord.updateOne(
  //             { _id: data._id },
  //             {
  //               $set: {
  //                 recipients_time: new Date(),
  //                 authorizer: jcUser.serviceEmployeeID,
  //               },
  //             }
  //           );
  //           // console.log('更新ReceiptRecord', result);
  //           // result = await ctx.model.LabInfoCopy.updateOne({ _id: data.standProductId }, { $pull: { stock: data.mark } });
  //           // console.log('更新LabInfoCopy', result);
  //         }
  //       }
  //     }

  //     // 从配置里面获取修改审批的类型（approveField字段）
  //     if (
  //       [ 'samplingPlan', 'spotRecord', 'lab' ].includes(
  //         approveInfo.approveField
  //       )
  //     ) {
  //       // 更新modifyApprovalRecord审批表的数据
  //       await ctx.service.jcqlcProject.updateModifyApprovalRecord(
  //         res.info,
  //         res.qlcProjectStatus,
  //         approveInfo.approveField
  //       );

  //       // 如果检测报告已经通过审核，实验室数据无法再填写，其他状态还是可以填写 jhw ++
  //       await ctx.service.jcqlcProject.uploadAmendStatus(
  //         res.qlcProjectStatus,
  //         res.info.form_component_values,
  //         'amend'
  //       );
  //     }
  //     // 如果是<点击完成项目>审批通过 更新归档时间与状态
  //     if ([ 'finishProject' ].includes(approveInfo.approveField)) {
  //       await ctx.service.jcqlcProject.uploadfinishProjectStatus(
  //         res.info.form_component_values,
  //         res.status
  //       );
  //     }
  //     if (approveInfo.approveField === 'samplingPlan' && res.qlcProjectStatus === 2) {
  //       console.log('生成方案审核记录单');
  //       await ctx.service.jcqlcProject.downSchemeReport(res);
  //     }
  //   }
  // }

  // 处理钉钉审批回调
  async handleDingApproval({ nodes, obj, res }) {
    const { ctx } = this;
    const operation_results = res.info.operation_records.filter(item => item.operation_result === 'AGREE');
    const signIndex = operation_results.length - 1;
    console.log(signIndex, nodes, '审批序号？？？？', obj.processInstanceId, '实例id');
    const currentEmployeeId = signIndex !== -1 ? operation_results[operation_results.length - 1].serviceEmployeeId : '';
    const zywsApprovalConfig = this.app.config.zywsApprovalConfig; // 审批配置
    // 查询当前的审批记录
    const approveRecord = await ctx.model.ApproveRecord.findOne({ processInstanceId: obj.processInstanceId });
    const approveRecords = approveRecord?.operation_records?.filter(item => item.operation_result === 'AGREE') || null;
    console.log(approveRecords?.length - 1, signIndex, 8768956875);

    // 如果审批记录小于等于1，说明是第一次审批，需要创建一条审批记录
    if (res.info.operation_records.length <= 1) await this.createApproveRecord(res.info);

    let node = nodes[0]; // 审批节点
    const approveInfo = zywsApprovalConfig[node]?.nodes?.filter(item => item.node === nodes[1])?.[0] || null; // 审批配置信息
    console.log('配置信息：', approveInfo);
    if (!approveInfo) return;
    if (node === 'RadiateqlcProject') {
      // 因为放射公用一个审批模板，所以需要根据不同的业务类型来查询不同的项目
      const personalProject = await ctx.model.PersonalDoseProject.findOne({ [approveInfo._idField]: obj.processInstanceId }, { projectSN: 1 }).lean();
      const industrialProject = await ctx.model.IndustrialRayProject.findOne({ [approveInfo._idField]: obj.processInstanceId }, { projectSN: 1 }).lean();
      const radiateqlcProject = await ctx.model.RadiateqlcProject.findOne({ [approveInfo._idField]: obj.processInstanceId }, { projectSN: 1 }).lean();
      console.log(!!personalProject, '个人剂量项目', !!industrialProject, '工业放射项目', !!radiateqlcProject, '放射项目');
      if (personalProject) { // 个人剂量
        node = 'PersonalDoseProject';
      } else if (industrialProject) { // 工业放射
        node = 'IndustrialRayProject';
      }
    }
    const projectInfo = await ctx.model[node]?.findOne({ [approveInfo._idField]: obj.processInstanceId }).lean(); // 项目信息
    console.log('项目信息：', !!projectInfo, nodes, '审批id：', obj.processInstanceId);
    const originatorEmployeeId = res.info.operation_records[0].serviceEmployeeId; // 发起人id
    const { projectSN, progress } = projectInfo;

    // 处理文件名称
    const fileNameObj = {
      JcqlcProject: 'jcqlcProject',
      PersonalDoseProject: 'personalDose',
      IndustrialRayProject: 'industrialRay',
      RadiateqlcProject: 'radiateqlcProject',
    };
    const fileName = fileNameObj[node];

    // 特殊处理不同项目
    if (node === 'RadiateqlcProject') {
      // 放射的项目进度特殊处理
      let radiateProgress = await ctx.model.RadiateqlcProgress.aggregate([
        { $match: { radiateqlcProjectId: projectInfo._id } },
        { $unwind: '$progresses' },
        { $match: { 'progresses.field': 'reportReview' } },
      ]);
      radiateProgress = radiateProgress?.[0] || null;
      if (!radiateProgress || radiateProgress?.progresses?.status >= 2) return;
      console.log(obj.type, res.info.result, 239578348957);
      // 创建审批记录，更新状态
      await ctx.service[fileName].handleApprove(approveInfo, obj, approveInfo.statusField, res);
      if (res.info.result === 'agree' && (!approveRecord || approveRecords.length - 1 < signIndex)) {
        try { // 审核通过后,给相应的pdf添加签名
          await ctx.service[fileName].signPdf({ originatorEmployeeId, statusField: approveInfo.statusField, currentEmployeeId, processInstanceField: approveInfo._idField, processInstanceId: obj.processInstanceId, signIndex });
        } catch (error) {
          console.log(error, '医疗放射项目' + projectSN + '签名失败!');
        }
        if (res.qlcProjectStatus === 2) { // 审核结束后,给相应的pdf添加盖章和骑缝章并添加电子签名
          try {
            await ctx.service[fileName].signPdf({ statusField: approveInfo.statusField, currentEmployeeId, processInstanceField: approveInfo._idField, processInstanceId: obj.processInstanceId, isComplete: true });
            await ctx.service[fileName].createReviewRecord({ _id: projectInfo._id, node: nodes[1] });
          } catch (error) {
            console.log(error, '医疗放射项目' + projectSN + '盖章失败!');
          }
        }
      }
      if (approveInfo.statusField === 'approved') { // 处理放射合同审批签名
        await ctx.service[fileName].updateapproveSign(res);
      } else if (approveInfo.statusField === 'MApplyStatus') { // 更新修改审批状态
        await ctx.service[fileName].updateModifystatus(res);
      }
    } else if (node === 'JcqlcProject') {
      res.info.processInstanceId = obj.processInstanceId;
      if (res.info.result === 'agree' && (!approveRecord || approveRecords.length - 1 < signIndex)) { // 中间以及结束节点
        try { // 审核通过后,给相应的pdf添加签名
          await ctx.service[fileName].signPdf({ originatorEmployeeId, statusField: approveInfo.statusField, currentEmployeeId, processInstanceField: approveInfo._idField, processInstanceId: obj.processInstanceId, signIndex });
        } catch (error) {
          console.log(error, '检测项目' + projectSN + '签名失败!');
        }
        if (res.qlcProjectStatus === 2) { // 审核结束后,给相应的pdf添加盖章和骑缝章并添加电子签名
          try {
            if (projectInfo && projectInfo.progress[approveInfo.statusField].status < 2) {
              await ctx.service[fileName].signPdf({ statusField: approveInfo.statusField, processInstanceField: approveInfo._idField, currentEmployeeId, processInstanceId: obj.processInstanceId, isComplete: true });
            }
          } catch (error) {
            console.log(error, '检测项目' + projectSN + '盖章失败!');
          }
        }
      }
      await this.updateApproveRecord({ processInstanceId: obj.processInstanceId }, res.info);
      // 如果检测报告已经通过审核，实验室数据无法再填写，其他状态还是可以填写 jhw ++
      if ([ 'labreport' ].includes(approveInfo.approveField)) {
        await ctx.service[fileName].uploadAmendStatus(
          res.qlcProjectStatus,
          res.info.form_component_values,
          'report'
        );
      }
      if (res.qlcProjectStatus > 1) {
        console.log('有没有进来', projectInfo.projectSN, approveInfo._idField, obj.processInstanceId);
        // 更新全流程项目状态表
        // 只有不是创建的时候才走这个更新方法，因为如果是创建的话 无法绑定项目id
        await ctx.service[fileName].updateOne(
          { [approveInfo._idField]: obj.processInstanceId },
          {
            [`progress.${approveInfo.statusField}`]: {
              status: res.qlcProjectStatus,
              completedTime: obj.finishTime,
            },
          }
        );
      }
      // #标准物质领用审批
      if ([ 'applyAcceptance' ].includes(approveInfo.approveField)) {
        const data = await ctx.model.ReceiptRecord.findOne({
          applyAcceptanceProcessInstanceId: obj.processInstanceId,
        });
          // console.log('data是森么', data,obj.processInstanceId);
        let result;
        result = await ctx.model.ReceiptRecord.updateOne(
          { _id: data._id },
          {
            $set: {
              'applyAcceptanceApproved.status': res.qlcProjectStatus,
              'applyAcceptanceApproved.completedTime': obj.finishTime,
            },
          }
        );
        console.log(result, '审批状态更新');
        // 通过：1.更新库存 2.领用时间默认为通过当天、 3.储备液、质控样可以使用标准物质 4.更新审批状态
        if (res.qlcProjectStatus === 2) {
          if (data) {
            const tasks = res.info.tasks;
            const dingAuthorizerId = tasks[tasks.length - 1];
            const dingAuthorizer =
                await ctx.service.dingTalkWorkFlow.dingTalkGteUserInfoById(
                  dingAuthorizerId.userid
                );
              // console.log(dingAuthorizer,dingAuthorizer.data.mobile, 'ding审批人')
            const jcUser = await ctx.model.JcUser.findOne({
              phoneNum: dingAuthorizer.data.result.mobile,
            }); // 普通员工账号
              // console.log(jcUser, '审批人')
            result = await ctx.model.ReceiptRecord.updateOne(
              { _id: data._id },
              {
                $set: {
                  recipients_time: new Date(),
                  authorizer: jcUser.serviceEmployeeID,
                },
              }
            );
            // console.log('更新ReceiptRecord', result);
            // result = await ctx.model.LabInfoCopy.updateOne({ _id: data.standProductId }, { $pull: { stock: data.mark } });
            // console.log('更新LabInfoCopy', result);
          }
        }
      }

      // 从配置里面获取修改审批的类型（approveField字段）
      if (
        [ 'samplingPlan', 'spotRecord', 'lab' ].includes(
          approveInfo.approveField
        )
      ) {
        // 更新modifyApprovalRecord审批表的数据
        await ctx.service[fileName].updateModifyApprovalRecord(
          res.info,
          res.qlcProjectStatus,
          approveInfo.approveField
        );

        // 如果检测报告已经通过审核，实验室数据无法再填写，其他状态还是可以填写 jhw ++
        await ctx.service[fileName].uploadAmendStatus(
          res.qlcProjectStatus,
          res.info.form_component_values,
          'amend'
        );
      }
      // 如果是<点击完成项目>审批通过 更新归档时间与状态
      if ([ 'finishProject' ].includes(approveInfo.approveField)) {
        await ctx.service[fileName].uploadfinishProjectStatus(
          res.info.form_component_values,
          res.status
        );
      }
      if (approveInfo.approveField === 'samplingPlan' && res.qlcProjectStatus === 2) {
        console.log('生成方案审核记录单');
        await ctx.service[fileName].downSchemeReport(res);
      }
    } else {
      if (res.info.result === 'agree' && (!approveRecord || approveRecords.length - 1 < signIndex)) {
        try {
          await ctx.service[fileName].signPdf({ originatorEmployeeId, statusField: approveInfo.statusField, currentEmployeeId, processInstanceField: approveInfo._idField, processInstanceId: obj.processInstanceId, signIndex });
        } catch (error) {
          console.log(error, node + '：' + projectSN + '签名失败!');
        }
        // 审核通过后，项目审批状态未更新为2，给相应的pdf添加盖章和骑缝章并添加电子签名
        if (res.qlcProjectStatus === 2 && progress?.[approveInfo.statusField]?.status < 2) {
          try {
            await ctx.service[fileName].signPdf({ statusField: approveInfo.statusField, currentEmployeeId, processInstanceField: approveInfo._idField, processInstanceId: obj.processInstanceId, isComplete: true });
            await ctx.service[fileName].createReviewRecord({ _id: projectInfo._id, node: nodes[1] });
          } catch (error) {
            console.log(error, node + '：' + projectSN + '签名失败!');
          }
        }
      }
      await this.updateApproveRecord({ processInstanceId: obj.processInstanceId }, res.info);
      await ctx.service[fileName].updateModifystatus(approveInfo._idField, res, approveInfo.statusField);
    }
  }

  // 创建审核记录
  async createApproveRecord(data) {
    if (!(await this.ctx.model.ApproveRecord.findOne({ processInstanceId: data.processInstanceId }))) {
      await this.ctx.model.ApproveRecord.create(data);
      // console.log(res);
    }
  }
  // 更新审核记录
  async updateApproveRecord(query, setFields) {
    await this.ctx.model.ApproveRecord.updateMany(query, { $set: setFields });
  }
  // 获取审核实例详情
  async getProcessinstance(process_instance_id, AppKey, AppSecret) {
    const { ctx } = this;
    try {
      const qlcProjectStatusObj = {
        RUNNING: 1,
        TERMINATED: 4,
        COMPLETED: 2,
        REFUSE: 3,
        CANCELED: 5,
      };
      const dingTalkBasicInfo = await this.dingTalkApiGettoken(AppKey, AppSecret);
      const res = await ctx.curl('https://oapi.dingtalk.com/topapi/processinstance/get?access_token=' + dingTalkBasicInfo.access_token, {
        data: {
          process_instance_id,
        },
        method: 'POST',
        dataType: 'json',
      });
      if (res.data.errcode === 0) {
        let status = '';
        if (res.data.process_instance.result === 'refuse') {
          status = 'REFUSE';
        } else {
          status = res.data.process_instance.status;
        }
        const process_instance = res.data.process_instance;
        process_instance.processInstanceId = process_instance_id;

        for (let i = 0; i < process_instance.operation_records.length; i++) {
          const item = process_instance.operation_records[i];
          const userInfo = await this.dingTalkGteUserInfoById(item.userid, AppKey, AppSecret);
          if (userInfo.errcode === '0') {
            const serviceEmployee = await ctx.model.ServiceEmployee.findOne({ phoneNum: userInfo.data.result.mobile });
            // if (i === process_instance.operation_records.length - 1) {
            //   currentEmployeeId = serviceEmployee._id;
            // }
            if (serviceEmployee) {
              item.serviceEmployeeId = serviceEmployee._id;
            } else {
              console.log(userInfo.data.result.mobile, item.userid, '获取审批人员信息失败', '审批id', process_instance_id);
            }

          } else {
            ctx.auditLog('获取钉钉用户信息', `审批实例id：${process_instance_id}，userId:${item.userid}，发起合同审批失败，错误信息：${userInfo.errMsg}`, 'error');
          }

        }

        return { qlcProjectStatus: qlcProjectStatusObj[status], status, info: process_instance };
      }
      return { errmsg: res.data };
    } catch (error) {
      console.log(error, '获取审批实例失败');
    }
  }

  // 通过userId获取钉钉用户信息 htt+++
  async dingTalkGteUserInfoById(userid, AppKey, AppSecret) {
    try {
      const access_token = (await this.dingTalkApiGettoken(AppKey, AppSecret)).access_token;
      const dingTalkUserInfoBack = await this.ctx.curl('https://oapi.dingtalk.com/topapi/v2/user/get?access_token=' + access_token, {
        data: {
          userid,
          language: 'zh_CN',
        },
        dataType: 'json',
        method: 'POST',
      });
      if (dingTalkUserInfoBack.data.errcode === 60020) {
        return {
          errcode: '60020',
          errmsg: dingTalkUserInfoBack.data.errmsg,
        };
      } else if (dingTalkUserInfoBack.data.errcode !== 0) {
        return {
          errcode: '100',
          errmsg: dingTalkUserInfoBack.data.errmsg,
        };
      }
      return {
        errcode: '0',
        data: dingTalkUserInfoBack.data,
      };
    } catch (error) {
      console.log(error);
    }
  }

  async getFormDefaultData(form_component_values = [], type) {
    if (!dingApprovalData[type]) {
      throw new Error('没有找到对应的审批类型：' + type);
    }
    let defaultData = dingApprovalData[type].default_form_component_values;
    defaultData = JSON.parse(JSON.stringify(defaultData));
    form_component_values.forEach(item => {
      const index = defaultData.findIndex(item2 => item2.name === item.name);
      if (index !== -1) {
        defaultData[index].value = item.value;
      }
    });
    return defaultData;

  }


}

module.exports = DingTalkWorkFlowService;
