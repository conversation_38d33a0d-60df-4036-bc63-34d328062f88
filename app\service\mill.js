const Service = require('egg').Service;
const { tools } = require('@utils');
const moment = require('moment');
let isSeriousHarm = false;
class MillService extends Service {
  async addMill(params) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    let successCount = 0;
    try {
      let res = {};
      // if (params.coverOld) {
      //   await ctx.model.MillConstruction.deleteMany({ EnterpriseID: params.mills[0].EnterpriseID });
      // }
      if (params.isImport) {
        let employees2 = [];
        let employees = [];
        let harms = {};
        let departs = [];
        let departOne = {};
        let departTwo = {};
        // const departThree = {};
        let station = '';
        for (let i = 0; i < params.mills.length; i++) {
          params.mills[i].EnterpriseID = EnterpriseID;
          for (let j = 0; j < params.mills[i].children.length; j++) {
            departs = [];
            employees2 = [];
            if (params.mills[i].children[j].category === 'stations') {
              harms = await this.matchHarmFactors(params.mills[i].children[j].customizeHarm ? [ ...(params.mills[i].children[j].customizeHarm + '').split(/,|，|、/gi) ] : []);
              params.mills[i].children[j].harmFactors = harms.harmFactors;
              params.mills[i].children[j].customizeHarm = harms.customHamrs;
              // 查询原岗位员工
              employees = await ctx.model.MillConstruction.aggregate([
                { $match: { EnterpriseID } },
                { $unwind: '$children' },
                { $match: { 'children._id': params.mills[i].children[j]._id } },
                { $project: { 'children.children': 1 } },
              ]);
              params.mills[i].children[j].children = [];
              if (employees && employees[0]) {
                params.mills[i].children[j].children = employees[0].children.children;
              } else {
                // 根据厂房车间岗位层级匹配对应层级的部门的人员
                departTwo = await ctx.model.Dingtree.find({ EnterpriseID, name: params.mills[i].name });
                for (let m = 0; m < departTwo.length; m++) {
                  departs.push([ departTwo[m]._id ]);
                }
                station = params.mills[i].children[j].name;
                departs.push(departTwo.map(item => item._id));
                for (let i = 0; i < departs.length; i++) {
                  if (departs[i][0]) {
                    employees = await ctx.model.Employee.aggregate([
                      { $match: { EnterpriseID, station } },
                      { $unwind: '$departs' },
                      { $project: { departs: 1, isArr: { $isArray: '$departs' } } },
                      { $match: { isArr: true, 'departs.0': { $exists: true } } },
                      { $project: { isIn: { $setIsSubset: [ departs[i], '$departs' ] } } },
                      { $match: { isIn: true } },
                      { $group: { _id: '$_id' } },
                    ]);
                    for (let j = 0; j < employees.length; j++) {
                      employees2.push(employees[j]._id);
                    }
                  }
                }
                employees2 = Array.from(new Set(employees2));
                employees2 = employees2.map(item => {
                  return { employees: item };
                });
                params.mills[i].children[j].children = employees2;

              }
            } else {
              for (let k = 0; k < params.mills[i].children[j].children.length; k++) {
                harms = await this.matchHarmFactors(params.mills[i].children[j].children[k].customizeHarm ? [ ...(params.mills[i].children[j].children[k].customizeHarm + '').split(/,|，|、/gi) ] : []);
                params.mills[i].children[j].children[k].harmFactors = harms.harmFactors;
                params.mills[i].children[j].children[k].customizeHarm = harms.customHamrs;
                // 查询原岗位员工
                employees = await ctx.model.MillConstruction.aggregate([
                  { $match: { EnterpriseID: params.mills[i].EnterpriseID } },
                  { $unwind: '$children' },
                  { $unwind: '$children.children' },
                  { $match: { 'children.children._id': params.mills[i].children[j].children[k]._id } },
                  { $project: { 'children.children.children': 1 } },
                ]);
                params.mills[i].children[j].children[k].children = [];
                station = params.mills[i].children[j].children[k].name;
                // departs = await ctx.model.Dingtree.find({ name: { $in: [ params.mills[i].name, params.mills[i].children[j].name, params.mills[i].children[j].children[k].name ] } });
                // employees2 = await ctx.model.Employee.aggregate([
                //   { $match: { EnterpriseID: params.mills[i].EnterpriseID } },
                //   { $unwind: '$departs' },
                //   { $match: { departs: { $all: departs } } },
                // ]);
                if (employees && employees[0]) {
                  params.mills[i].children[j].children[k].children = employees[0].children.children.children;
                } else {
                  // 根据厂房车间岗位层级匹配对应层级的部门的人员
                  departTwo = await ctx.model.Dingtree.find({ EnterpriseID, name: params.mills[i].children[j].name });
                  for (let m = 0; m < departTwo.length; m++) {
                    departOne = await ctx.model.Dingtree.find({ EnterpriseID, _id: departTwo[m].parentid });
                    for (let n = 0; n < departOne.length; n++) {
                      if (departOne[n].name === params.mills[i].name) {
                        departs.push([ departOne[n]._id, departTwo[m]._id ]);
                      }
                    }
                  }
                  for (let i = 0; i < departs.length; i++) {
                    if (departs[i][0]) {
                      employees = await ctx.model.Employee.aggregate([
                        { $match: { EnterpriseID, station } },
                        { $unwind: '$departs' },
                        { $project: { departs: 1, isArr: { $isArray: '$departs' } } },
                        { $match: { isArr: true, 'departs.0': { $exists: true } } },
                        { $project: { isIn: { $setIsSubset: [ departs[i], '$departs' ] } } },
                        { $match: { isIn: true } },
                        { $group: { _id: '$_id' } },
                      ]);
                      for (let j = 0; j < employees.length; j++) {
                        employees2.push(employees[j]._id);
                      }
                    }
                  }
                  employees2 = Array.from(new Set(employees2));
                  employees2 = employees2.map(item => {
                    return { employees: item };
                  });
                  params.mills[i].children[j].children[k].children = employees2;
                }
              }
            }
          }
          if (params.mills[i]._id) { // 存在即修改
            // wzq use findByIdAndUpdate, if error, please use updateOne
            await ctx.model.MillConstruction.findByIdAndUpdate(params.mills[i]._id, { $set: params.mills[i] }, { new: true });
          } else { // 不存在即添加
            res = await new ctx.model.MillConstruction(params.mills[i]).save();
            for (let j = 0; j < res.children.length; j++) {
              if (res.category === 'workspaces') {
                for (let k = 0; k < res.children[j].children.length; k++) {
                  await ctx.model.EmployeeStatusChange.updateOne({ employee: res.children[j].children[k].employees },
                    { $push: { statusChanges: { changType: 2,
                      EnterpriseID,
                      stationsTo: [ res.children[j]._id ],
                      message: '转岗' } } }
                  );
                }
              } else {
                for (let k = 0; k < res.children[j].children.length; k++) {
                  for (let m = 0; m < res.children[j].children[k].children.length; m++) {
                    await ctx.model.EmployeeStatusChange.updateOne({ employee: res.children[j].children[k].children[m].employees },
                      { $push: { statusChanges: { changType: 2,
                        EnterpriseID,
                        stationsTo: [ res.children[j].children[k]._id ],
                        message: '转岗' } } }
                    );
                  }
                }
              }
            }
          }
          successCount++;
        }
        // 更新档案完成度
        this.updateFilesCompleteness();
      } else {
        params.mills[0].EnterpriseID = EnterpriseID;
        await ctx.model.MillConstruction.insertMany(params.mills);
      }
      // 更新adminorg接害人数统计
      await this.getStatistics();
      return successCount;
    } catch (error) {
      console.log(error);
      return successCount;
    }
  }


  // 更新工作场所档案完成度-只要岗位上有人，那完成度就是100%
  async updateFilesCompleteness() {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const stations = await ctx.model.MillConstruction.aggregate([
      { $match: { EnterpriseID } },
      { $unwind: '$children' },
      { $addFields: { stations: { $cond: [{ $eq: [ '$category', 'mill' ] },
        '$children.children', [ '$children' ]] } } },
      { $project: { 'stations.children': 1 } },
      { $match: { 'stations.children.0': { $exists: true } } },
    ]);
    if (stations[0]) { // 更新档案完成度
      ctx.service.filesCompleteness.update({
        millConstruction: { completion: 100 },
      });
    } else {
      ctx.service.filesCompleteness.update({
        millConstruction: { completion: 0 },
      });
    }
  }


  // 获取统计数据
  async getStatistics() {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const data = await ctx.model.MillConstruction.find({ EnterpriseID }).populate('children.children.children.employees', 'status', { EnterpriseID, status: 1 }).populate('children.children.employees', 'status', { EnterpriseID, status: 1 });
    const harmFactors = await this.findAllHarmFactors();
    const statistics = [];
    // 获取严重危害因素人数
    const allCount = {
      all: { value: 0, employee: [], label: '劳动者接害总人数', harmFactors: {} },
      seriousHarm: { value: 0, employee: [], label: '严重危害因素', harmFactors: {} },
      noSeriousHarm: { value: 0, employee: [], label: '一般危害因素', harmFactors: {} },
      chemical: { value: 0, employee: [], label: '化学有害因素', harmFactors: {} },
      dust: { value: 0, employee: [], label: '粉尘', harmFactors: {} },
      noise: { value: 0, employee: [], label: '噪声' },
      physical: { value: 0, employee: [], label: '其他物理因素', harmFactors: {} },
      radiation: { value: 0, employee: [], label: '放射性物质', harmFactors: {} },
      biological: { value: 0, employee: [], label: '生物', harmFactors: {} },
    };
    data.forEach(item => {
      item.countObj = this.getPeopleCount([ item ], harmFactors, {
        all: { value: 0, employee: [], label: '劳动者接害总人数', harmFactors: {} },
        seriousHarm: { value: 0, employee: [], label: '严重危害因素', harmFactors: {} },
        noSeriousHarm: { value: 0, employee: [], label: '一般危害因素', harmFactors: {} },
        chemical: { value: 0, employee: [], label: '化学有害因素', harmFactors: {} },
        dust: { value: 0, employee: [], label: '粉尘', harmFactors: {} },
        noise: { value: 0, employee: [], label: '噪声' },
        physical: { value: 0, employee: [], label: '其他物理因素', harmFactors: {} },
        radiation: { value: 0, employee: [], label: '放射性物质', harmFactors: {} },
        biological: { value: 0, employee: [], label: '生物', harmFactors: {} },
      });
      // 去重，并统计人数
      item.countObj.all.employee = Array.from(new Set(item.countObj.all.employee));
      item.countObj.all.value = item.countObj.all.employee.length;
      item.countObj.seriousHarm.employee = Array.from(new Set(item.countObj.seriousHarm.employee));
      item.countObj.seriousHarm.value = item.countObj.seriousHarm.employee.length;
      item.countObj.noSeriousHarm.employee = Array.from(new Set(item.countObj.noSeriousHarm.employee));
      item.countObj.noSeriousHarm.value = item.countObj.noSeriousHarm.employee.length;
      item.countObj.dust.employee = Array.from(new Set(item.countObj.dust.employee));
      item.countObj.dust.value = item.countObj.dust.employee.length;
      item.countObj.chemical.employee = Array.from(new Set(item.countObj.chemical.employee));
      item.countObj.chemical.value = item.countObj.chemical.employee.length;
      item.countObj.biological.employee = Array.from(new Set(item.countObj.biological.employee));
      item.countObj.biological.value = item.countObj.biological.employee.length;
      item.countObj.radiation.employee = Array.from(new Set(item.countObj.radiation.employee));
      item.countObj.radiation.value = item.countObj.radiation.employee.length;
      item.countObj.physical.employee = Array.from(new Set(item.countObj.physical.employee));
      item.countObj.physical.value = item.countObj.physical.employee.length;
      item.countObj.noise.employee = Array.from(new Set(item.countObj.noise.employee));
      item.countObj.noise.value = item.countObj.noise.employee.length;

      statistics.push({ name: item.name, sort: 'item', count: Object.values(item.countObj) });
      allCount.all.employee = allCount.all.employee.concat(item.countObj.all.employee);
      allCount.dust.employee = allCount.dust.employee.concat(item.countObj.dust.employee);
      for (const key in item.countObj.dust.harmFactors) {
        if (!allCount.dust.harmFactors[key]) allCount.dust.harmFactors[key] = [];
        allCount.dust.harmFactors[key] = allCount.dust.harmFactors[key].concat(item.countObj.dust.harmFactors[key]);
      }
      allCount.chemical.employee = allCount.chemical.employee.concat(item.countObj.chemical.employee);
      for (const key in item.countObj.chemical.harmFactors) {
        if (!allCount.chemical.harmFactors[key]) allCount.chemical.harmFactors[key] = [];
        allCount.chemical.harmFactors[key] = allCount.chemical.harmFactors[key].concat(item.countObj.chemical.harmFactors[key]);
      }
      allCount.biological.employee = allCount.biological.employee.concat(item.countObj.biological.employee);
      for (const key in item.countObj.biological.harmFactors) {
        if (!allCount.biological.harmFactors[key]) allCount.biological.harmFactors[key] = [];
        allCount.biological.harmFactors[key] = allCount.biological.harmFactors[key].concat(item.countObj.biological.harmFactors[key]);
      }
      allCount.radiation.employee = allCount.radiation.employee.concat(item.countObj.radiation.employee);
      for (const key in item.countObj.radiation.harmFactors) {
        if (!allCount.radiation.harmFactors[key]) allCount.radiation.harmFactors[key] = [];
        allCount.radiation.harmFactors[key] = allCount.radiation.harmFactors[key].concat(item.countObj.radiation.harmFactors[key]);
      }
      allCount.physical.employee = allCount.physical.employee.concat(item.countObj.physical.employee);
      for (const key in item.countObj.physical.harmFactors) {
        if (!allCount.physical.harmFactors[key]) allCount.physical.harmFactors[key] = [];
        allCount.physical.harmFactors[key] = allCount.physical.harmFactors[key].concat(item.countObj.physical.harmFactors[key]);
      }
      allCount.noSeriousHarm.employee = allCount.noSeriousHarm.employee.concat(item.countObj.noSeriousHarm.employee);
      allCount.seriousHarm.employee = allCount.seriousHarm.employee.concat(item.countObj.seriousHarm.employee);
      allCount.noise.employee = allCount.noise.employee.concat(item.countObj.noise.employee);
    });

    // 总计去重
    allCount.all.employee = Array.from(new Set(allCount.all.employee));
    allCount.all.value = allCount.all.employee.length;
    allCount.dust.employee = Array.from(new Set(allCount.dust.employee));
    allCount.dust.harmFactors1 = [];
    for (const key in allCount.dust.harmFactors) {
      allCount.dust.harmFactors[key] = Array.from(new Set(allCount.dust.harmFactors[key]));
      allCount.dust.harmFactors[key][0] && (allCount.dust.harmFactors1.push({ label: key, employee: allCount.dust.harmFactors[key], value: allCount.dust.harmFactors[key].length }));
    }
    allCount.dust.harmFactors = allCount.dust.harmFactors1;
    allCount.dust.value = allCount.dust.employee.length;
    allCount.chemical.employee = Array.from(new Set(allCount.chemical.employee));
    allCount.chemical.value = allCount.chemical.employee.length;
    allCount.chemical.harmFactors1 = [];
    for (const key in allCount.chemical.harmFactors) {
      allCount.chemical.harmFactors[key] = Array.from(new Set(allCount.chemical.harmFactors[key]));
      allCount.chemical.harmFactors[key].length > 0 && (allCount.chemical.harmFactors1.push({ label: key, employee: allCount.chemical.harmFactors[key], value: allCount.chemical.harmFactors[key].length }));
    }
    allCount.chemical.harmFactors = allCount.chemical.harmFactors1;
    allCount.biological.employee = Array.from(new Set(allCount.biological.employee));
    allCount.biological.value = allCount.biological.employee.length;
    allCount.biological.harmFactors1 = [];
    for (const key in allCount.biological.harmFactors) {
      allCount.biological.harmFactors[key] = Array.from(new Set(allCount.biological.harmFactors[key]));
      allCount.biological.harmFactors[key].length > 0 && (allCount.biological.harmFactors1.push({ label: key, employee: allCount.biological.harmFactors[key], value: allCount.biological.harmFactors[key].length }));
    }
    allCount.biological.harmFactors = allCount.biological.harmFactors1;

    allCount.radiation.employee = Array.from(new Set(allCount.radiation.employee));
    allCount.radiation.value = allCount.radiation.employee.length;
    allCount.radiation.harmFactors1 = [];
    for (const key in allCount.radiation.harmFactors) {
      allCount.radiation.harmFactors[key] = Array.from(new Set(allCount.radiation.harmFactors[key]));
      allCount.radiation.harmFactors[key][0] && (allCount.radiation.harmFactors1.push({ label: key, employee: allCount.radiation.harmFactors[key], value: allCount.radiation.harmFactors[key].length }));
    }
    allCount.radiation.harmFactors = allCount.radiation.harmFactors1;
    allCount.physical.employee = Array.from(new Set(allCount.physical.employee));
    allCount.physical.value = allCount.physical.employee.length;
    allCount.physical.harmFactors1 = [];
    for (const key in allCount.physical.harmFactors) {
      allCount.physical.harmFactors[key] = Array.from(new Set(allCount.physical.harmFactors[key]));
      allCount.physical.harmFactors[key][0] && (allCount.physical.harmFactors1.push({ label: key, employee: allCount.physical.harmFactors[key], value: allCount.physical.harmFactors[key].length }));
    }
    allCount.physical.harmFactors = allCount.physical.harmFactors1;
    allCount.noSeriousHarm.employee = Array.from(new Set(allCount.noSeriousHarm.employee));
    allCount.noSeriousHarm.value = allCount.noSeriousHarm.employee.length;
    allCount.seriousHarm.employee = Array.from(new Set(allCount.seriousHarm.employee));
    allCount.seriousHarm.value = allCount.seriousHarm.employee.length;
    allCount.noise.employee = Array.from(new Set(allCount.noise.employee));
    allCount.noise.value = allCount.noise.employee.length;
    statistics.push({ name: '总计', sort: 'all', count: Object.values(allCount) });

    // 更新adminorg
    await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { $set: { harmStatistics: statistics } });
  }

  // 判断是否是严重危害因素
  isSeriousHarm(harmFactor, harmFactors) {
    return harmFactors.some(item =>
      item.harmFactors.some(
        item2 => item2.label === harmFactor && item2.serious === '1'
      )
    );
  }

  // 获取接害人数
  getPeopleCount(data, harmFactors, countObj) {
    for (let i = 0; i < data.length; i++) {
      if (data[i].category !== 'stations' && data[i].children) {
        countObj = this.getPeopleCount(data[i].children, harmFactors, countObj);
      } else if (data[i].children) {
        if (data[i].harmFactors.length > 0 || data[i].customizeHarm) {
          data[i].children = data[i].children.filter(item => item.employees);
          countObj.all.employee = countObj.all.employee.concat(data[i].children.map(item => item.employees._id));
          isSeriousHarm = data[i].harmFactors.some(item =>
            this.isSeriousHarm(item[1], harmFactors)
          );
          if (isSeriousHarm) {
            data[i].children = (data[i].children.filter(item => item.employees));
            countObj.seriousHarm.employee = countObj.seriousHarm.employee.concat(data[i].children.map(item => item.employees._id));
          } else {
            data[i].children = data[i].children.filter(item => item.employees);
            countObj.noSeriousHarm.employee = countObj.noSeriousHarm.employee.concat(data[i].children.map(item => item.employees._id));
          }
          let item = {};

          for (let j = 0; j < data[i].harmFactors.length; j++) {
            item = data[i].harmFactors[j];
            data[i].children = data[i].children.filter(item => item.employees);
            if (item[0].indexOf('粉尘') !== -1) {
              if (!countObj.dust.harmFactors[item[1]]) countObj.dust.harmFactors[item[1]] = [];
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.dust.employee.push(data[i].children[k].employees._id);
                countObj.dust.harmFactors[item[1]].push(data[i].children[k].employees._id);
              }
            }
            if (item[0].indexOf('化学') !== -1) {
              if (!countObj.chemical.harmFactors[item[1]]) countObj.chemical.harmFactors[item[1]] = [];
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.chemical.employee.push(data[i].children[k].employees._id);
                countObj.chemical.harmFactors[item[1]].push(data[i].children[k].employees._id);
              }
            }
            if (item[0].indexOf('生物') !== -1) {
              if (!countObj.biological.harmFactors[item[1]]) countObj.biological.harmFactors[item[1]] = [];
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.biological.employee.push(data[i].children[k].employees._id);
                countObj.biological.harmFactors[item[1]].push(data[i].children[k].employees._id);
              }
            }
            if (item[0].indexOf('放射') !== -1) {
              if (!countObj.radiation.harmFactors[item[1]]) countObj.radiation.harmFactors[item[1]] = [];
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.radiation.employee.push(data[i].children[k].employees._id);
                countObj.radiation.harmFactors[item[1]].push(data[i].children[k].employees._id);
              }
            }
            if (item[0].indexOf('物理') !== -1 && item[1].indexOf('噪声') === -1) {
              if (!countObj.physical.harmFactors[item[1]]) countObj.physical.harmFactors[item[1]] = [];
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.physical.employee.push(data[i].children[k].employees._id);
                countObj.physical.harmFactors[item[1]].push(data[i].children[k].employees._id);
              }
            }
            if (item[1].indexOf('噪声') !== -1) {
              for (let k = 0; k < data[i].children.length; k++) {
                countObj.noise.employee.push(data[i].children[k].employees._id);
              }
            }
          }
        }
      }
    }
    return countObj;
  }

  // 根据危害因素名称数组匹配危害因素
  async matchHarmFactors(harmFactors) {
    const res = await this.ctx.model.OccupationalexposureLimits.aggregate([
      { $unwind: '$chineseName' },
      { $match: { chineseName: { $in: harmFactors } } },
    ]);
    const arr = [];// 匹配到的结果
    const matchHarms = [];
    res.forEach(item => {
      matchHarms.push(item.chineseName);
      arr.push([ item.catetory, item.chineseName ]);
    });
    // 差集
    const customHamrs = harmFactors.filter(item => !(new Set(matchHarms)).has(item));
    return { harmFactors: arr, customHamrs: customHamrs.join('、') };
  }

  async addWorkSpace(params) {
    try {
      const res = await this.ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $addToSet: { children: params.workspace } });
      if (res.nModified > 0) {
        return '添加成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async addStation(params) {
    const { ctx } = this;
    try {
      let stationId = '';
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      // let employees = await ctx.model.Employee.find({ EnterpriseID, station: params.station.name });
      const departs = [];
      let employees = [];
      let employees2 = [];
      let departOne = {};
      let departTwo = {};
      // const departThree = {};
      let query = {};
      let setFiled = {};
      params.station.category = 'stations';
      if (params.millId) {
        // 查询层级匹配的部门
        // departThree = await ctx.model.Dingtree.find({ EnterpriseID, name: params.station.name });
        // for (let i = 0; i < departThree.length; i++) {
        departTwo = await ctx.model.Dingtree.find({ EnterpriseID, name: params.workspaceName });
        for (let j = 0; j < departTwo.length; j++) {
          // if (departTwo[j].name === params.workspaceName) {
          departOne = await ctx.model.Dingtree.find({ EnterpriseID, _id: departTwo[j].parentid });
          for (let k = 0; k < departOne.length; k++) {
            if (departOne[k].name === params.millName) {
              departs.push([ departOne[k]._id, departTwo[j]._id ]);
            }
          }
          // }
        }
        // }
        query = { _id: params.millId, 'children._id': params.workspaceId };
        setFiled = 'children.$.children';
      } else {
        // departThree = await ctx.model.Dingtree.find({ EnterpriseID, name: params.station.name });
        // for (let i = 0; i < departThree.length; i++) {
        departTwo = await ctx.model.Dingtree.find({ EnterpriseID, name: params.workspaceName });
        for (let j = 0; j < departTwo.length; j++) {
          // if (departTwo[j].name === params.workspaceName) {
          departs.push([ departTwo[j]._id ]);
          // }
        }
        // }
        query = { _id: params.workspaceId };
        setFiled = 'children';
      }
      for (let i = 0; i < departs.length; i++) {
        employees = await ctx.model.Employee.aggregate([
          { $match: { EnterpriseID, station: params.station.name } },
          { $unwind: '$departs' },
          { $project: { departs: 1, isArr: { $isArray: '$departs' } } },
          { $match: { isArr: true, 'departs.0': { $exists: true } } },
          { $project: { isIn: { $setIsSubset: [ departs[i], '$departs' ] } } },
          { $match: { isIn: true } },
          { $group: { _id: '$_id' } },
        ]);
        for (let j = 0; j < employees.length; j++) {
          employees2.push(employees[j]._id);
        }
      }
      employees2 = Array.from(new Set(employees2));
      const employeeIds = employees2;
      employees2 = employees2.map(item => {
        return { employees: item };
      });
      params.station.children = employees2;
      const updateRes = await ctx.model.MillConstruction.updateOne(query, { $addToSet: { [setFiled]: params.station } });
      let res = {};
      if (params.millId) {
        res = await ctx.model.MillConstruction.aggregate([
          { $match: { _id: params.millId } },
          { $unwind: '$children' },
          { $match: { 'children._id': params.workspaceId } },
          { $unwind: '$children.children' },
          { $match: { 'children.children.name': params.station.name } },
        ]);
        stationId = res[0].children.children._id;
      } else {
        res = await ctx.model.MillConstruction.aggregate([
          { $match: { _id: params.workspaceId } },
          { $unwind: '$children' },
          { $match: { 'children.name': params.station.name } },
        ]);
        stationId = res[0].children._id;
      }
      // 更新工作状态变更
      await ctx.model.EmployeeStatusChange.updateMany({ employee: { $in: employeeIds } }, { $push: { statusChanges: {
        changType: 2,
        EnterpriseID,
        stationsTo: [ stationId ],
        message: '转岗',
      } } });
      if (updateRes.nModified > 0) {
        // 更新档案完成度
        this.updateFilesCompleteness();
        this.getStatistics();
      }

    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async findMillConstruction(params) {
    try {
      // console.log(params.EnterpriseID, 'iddddddddddddddddddddddd');
      const res = await this.ctx.model.MillConstruction.find({ EnterpriseID: params.EnterpriseID }).populate('children.children.children.employees', '', { EnterpriseID: params.EnterpriseID }).populate('children.children.employees', '', { EnterpriseID: params.EnterpriseID });
      return res;
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  // 查询防护用具
  async protectiveEquipment(params) {
    const { ctx } = this;
    try {
      const res = [];
      for (let i = 0; i < params.length - 1; i++) {
        let con = await ctx.model.TouchLimits.findOne({ chineseName: params[i][1] }, { protectiveEquipment: 1, chineseName: 1 });
        let con2 = await ctx.model.DustTouchLimit.findOne({ chineseName: params[i][1] }, { protectiveEquipment: 1, chineseName: 1 });
        con = tools.convertToEditJson(con);
        con2 = tools.convertToEditJson(con2);
        res.push(con ? con.protectiveEquipment : '');
        res.push(con2 ? con2.protectiveEquipment : '');
      }
      const res1 = Array.from(new Set(res));
      return res1;
    } catch (error) {
      console.log(error);
      return 500;
    }
  }
  // 查询员工
  async findEmployees(params) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    // const departThree = [];
    let departTwo = [];
    let departOne = [];
    const departs = [];
    let employees = [];
    let employees2 = [];
    if (params.millName) {
      // 查询层级匹配的部门
      // departThree = await ctx.model.Dingtree.find({ EnterpriseID, name: params.station });
      // for (let i = 0; i < departThree.length; i++) {
      departTwo = await ctx.model.Dingtree.find({ EnterpriseID, name: params.workspaceName });
      for (let j = 0; j < departTwo.length; j++) {
        // if (departTwo[j].name === params.workspaceName) {
        departOne = await ctx.model.Dingtree.find({ EnterpriseID, _id: departTwo[j].parentid });
        for (let k = 0; k < departOne.length; k++) {
          if (departOne[k].name === params.millName) {
            departs.push([ departOne[k]._id ]);
          }
        }
        // }
      }
      // }
    } else {
      // departThree = await ctx.model.Dingtree.find({ EnterpriseID, name: params.station });
      // for (let i = 0; i < departThree.length; i++) {
      departTwo = await ctx.model.Dingtree.find({ EnterpriseID, name: params.workspaceName });
      for (let j = 0; j < departTwo.length; j++) {
        // if (departTwo[j].name === params.workspaceName) {
        departs.push([ departTwo[j]._id ]);
        // }
      }
      // }
    }
    for (let i = 0; i < departs.length; i++) {
      employees = await ctx.model.Employee.aggregate([
        { $match: { EnterpriseID, station: params.station } },
        { $unwind: '$departs' },
        { $project: { departs: 1, isArr: { $isArray: '$departs' } } },
        { $match: { isArr: true, 'departs.0': { $exists: true } } },
        { $project: { isIn: { $setIsSubset: [ departs[i], '$departs' ] } } },
        { $match: { isIn: true } },
        { $group: { _id: '$_id' } },
      ]);
      for (let j = 0; j < employees.length; j++) {
        employees2.push(employees[j]._id);
      }
    }
    employees2 = Array.from(new Set(employees2));
    employees2 = employees2.map(item => {
      return { _id: item };
    });
    if (!params.station) {
      employees2 = await ctx.model.Employee.find({ EnterpriseID, status: 1 }, { name: 1 });
    }
    // if (params.isInit) {
    //   await this.addEmployee({ employeeId: res.map(item => item._id), ...params });
    // }
    return employees2;

  }


  async addEmployee(params) {
    try {
      const { ctx } = this;
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      // 先判断员工是否本来就在该岗位
      let mills = [];
      const employeeIds = [];
      if (params.millId) {
        mills = await ctx.model.MillConstruction.aggregate([
          {
            $match: {
              EnterpriseID,
            },
          },
          {
            $unwind: '$children',
          },
          {
            $unwind: '$children.children',
          },
          {
            $match: {
              'children.children._id': params.stationId,
            },
          },
          {
            $lookup: {
              from: 'employees',
              localField: 'children.children.children.employees',
              foreignField: '_id',
              as: 'employees',
            },
          },
          { $project: { employees: 1 } },
        ]);

      } else {
        mills = await ctx.model.MillConstruction.aggregate([
          {
            $match: {
              EnterpriseID,
            },
          },
          {
            $unwind: '$children',
          },
          {
            $match: {
              'children._id': params.stationId,
            },
          },
          {
            $lookup: {
              from: 'employees',
              localField: 'children.children.employees',
              foreignField: '_id',
              as: 'employees',
            },
          },
          {
            $project: { employees: 1 },
          },
        ]);
      }
      if (mills.length > 0) {
        mills[0].employees.forEach(item => {
          if (item.status === 1) employeeIds.push(item._id);
        });
      }
      // 更新员工工作变更状态
      let statusChange = {};
      const employeeIdsSet = new Set(params.employeeId);
      // 交集
      const diffEmployees = employeeIds.filter(item => employeeIdsSet.has(item));
      // 需要删除的人员
      const deleteEmployeeIds = employeeIds.filter(item => !new Set(diffEmployees).has(item));
      // 需要添加的人员
      const addEmployeeIds = params.employeeId.filter(item => !new Set(diffEmployees).has(item));
      // 从该岗位删除的员工

      statusChange = {
        changType: 2,
        EnterpriseID,
        stationFrom: params.stationId,
        message: '转岗',
      };
      await ctx.model.EmployeeStatusChange.updateMany({ employee: { $in: deleteEmployeeIds } }, { $addToSet: { statusChanges: statusChange } });

      // 岗位新增的员工
      statusChange = {
        changType: 2,
        EnterpriseID,
        stationsTo: [ params.stationId ],
        message: '转岗',
      };
      await ctx.model.EmployeeStatusChange.updateMany({ employee: { $in: addEmployeeIds } }, { $addToSet: { statusChanges: statusChange } });
      // 更新档案完成度
      if (addEmployeeIds[0]) {
        ctx.service.filesCompleteness.update({
          millConstruction: { completion: 100 },
        });
      }
      params.employeeId = params.employeeId.map(item => {
        item = { employees: item };
        return item;
      });
      let res = {};
      const allEmployeeId = params.employeeId;
      allEmployeeId.forEach(item => {
        item.isPass = '0';
        return item;
      });
      if (params.millId) {
        // >>>>>>>jhw
        res = await ctx.model.MillConstruction.updateOne({ _id: params.millId }, {
          $set: {
            'children.$[i].children.$[j].children': allEmployeeId,
          },
        }, {
          arrayFilters: [{
            'i._id': params.workspaceId,
          }, { 'j._id': params.stationId },
          ],
        });
        // >>>>>>>jhw
        // >>>>>>>>>htt
        // res = await ctx.model.MillConstruction.updateOne({ _id: params.millId }, {
        //   $set: {
        //     'children.$[i].children.$[j].children': params.employeeId,
        //   },
        // }, {
        //   arrayFilters: [{
        //     'i._id': params.workspaceId,
        //   }, { 'j._id': params.stationId },
        //   ],
        // });
        // >>>>>>>>>htt
      } else {
        // >>>>>jhw
        res = await ctx.model.MillConstruction.updateOne({ _id: params.workspaceId, 'children._id': params.stationId }, {
          $set: {
            'children.$.children': allEmployeeId,
          },
        });
        // >>>>>>>jhw

        // res = await ctx.model.MillConstruction.updateOne({ _id: params.workspaceId, 'children._id': params.stationId }, {
        //   $set: {
        //     'children.$.children': params.employeeId,
        //   },
        // });
      }
      if (res.nModified > 0) {
        await this.getStatistics();
        return '添加成功';
      }

    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async editMill(params) {
    try {
      const res = await this.ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $set: { name: params.name, status: params.status || '1' } });
      if (res.nModified > 0) {
        if (!params.status) await this.getStatistics();
        return '修改成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async editWorkspace(params) {
    try {
      const res = await this.ctx.model.MillConstruction.updateOne({ _id: params.millId, 'children._id': params.workspaceId }, { $set: { 'children.$.name': params.name, 'children.$.status': params.status || '1' } });
      if (res.nModified > 0) {
        return '修改成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async editStation(params) {
    try {
      const { ctx } = this;
      if (params.harmFactors) {
        const harms = await this.matchHarmFactors(params.customizeHarm ? [ ...(params.customizeHarm + '').split(/,|，|、/gi) ] : []);
        params.harmFactors = params.harmFactors.concat(harms.harmFactors);
        params.customizeHarm = harms.customHamrs;
      }
      // const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      // let employees = await ctx.model.Employee.find({ EnterpriseID, station: params.name });
      // employees = employees.map(item => {
      //   return { employees: item._id };
      // });
      delete params.children;
      // params.children = employees;
      const params2 = {};
      let res = {};
      if (params.millId) {
        for (const key in params) {
          if (key === 'millId' || key === 'workspaceId' || key === 'stationId') {
            console.log('');
          } else {
            params2['children.$[i].children.$[j].' + key] = params[key];
          }

        }

        res = await ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $set: params2 },
          {
            arrayFilters: [
              { 'i._id': params.workspaceId },
              { 'j._id': params.stationId },
            ],
          });
      } else {
        for (const key in params) {
          if (key === 'workspaceId' || key === 'stationId') {
            console.log('');
          } else {
            params2['children.$[i].' + key] = params[key];
          }
        }
        res = await ctx.model.MillConstruction.updateOne({ _id: params.workspaceId }, { $set: params2 },
          {
            arrayFilters: [
              { 'i._id': params.stationId },
            ],
          });
      }
      if (res.nModified > 0) {
        await this.getStatistics();
        return '修改成功';

      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async deleteStation(params) {
    try {
      const { ctx } = this;
      let res = {};
      if (params.millId) {
        res = await ctx.model.MillConstruction.updateOne({ _id: params.millId, 'children._id': params.workspaceId }, { $pull: { 'children.$.children': { _id: params.stationId } } });
      } else {
        res = await ctx.model.MillConstruction.updateOne({ _id: params.workspaceId }, { $pull: { children: { _id: params.stationId } } });
      }
      // 更新档案完成度
      this.updateFilesCompleteness();
      if (res.nModified > 0) {
        return '删除成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async deleteWorkspace(params) {
    try {
      const { ctx } = this;
      if (params.millId) {
        await ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $pull: { children: { _id: params.workspaceId } } });
      } else {
        await ctx.model.MillConstruction.deleteOne({ _id: params.workspaceId });
      }
      // 更新档案完成度
      this.updateFilesCompleteness();
      await this.getStatistics();
      return '删除成功';
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async deleteMill(params) {
    try {
      const { ctx } = this;
      await ctx.model.MillConstruction.deleteMany({ _id: { $in: params.millId } });
      // 更新档案完成度
      this.updateFilesCompleteness();
      await this.getStatistics();
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async deleteEmployee(params, noEmployeeWorkChang = false) {
    let res = {};
    try {
      const { ctx } = this;
      if (params.millId) {
        res = await ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $pull: { 'children.$[i].children.$[j].children': { employees: params.employeeId } } },
          {
            arrayFilters: [
              { 'i._id': params.workspaceId },
              { 'j._id': params.stationId },
            ],
          });
      } else {
        res = await ctx.model.MillConstruction.updateOne({ _id: params.workspaceId }, { $pull: { 'children.$[i].children': { employees: params.employeeId } } },
          {
            arrayFilters: [
              { 'i._id': params.stationId },
            ],
          });
      }
      if (noEmployeeWorkChang) {
        if (res.nModified > 0) {
          return '删除成功';
        }
      }
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
      const statusChange = {
        changType: 2,
        EnterpriseID,
        stationFrom: params.stationId,
        message: '转岗',
      };
      await ctx.model.EmployeeStatusChange.updateOne({ employee: params.employeeId }, { $addToSet: { statusChanges: statusChange } });
      if (res.nModified > 0) {
        // 更新档案完成度
        this.updateFilesCompleteness();
        await this.getStatistics();
        return '删除成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async dropUpdateEmployee(params) {
    try {
      const res = await this.ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $set: { 'children.$[i].children.$[j].children': params.employeeIds } },
        {
          arrayFilters: [
            { 'i._id': params.workspaceId },
            { 'j._id': params.stationId },
          ],
        });
      if (res.nModified > 0) {
        return '更新成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async dropUpdateStation(params) {
    try {
      const res = await this.ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $set: { 'children.$[i].children': params.stations } },
        {
          arrayFilters: [
            { 'i._id': params.workspaceId },
          ],
        });
      if (res.nModified > 0) {
        return '更新成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async dropUpdateWorkspace(params) {
    try {
      const res = await this.ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $set: { children: params.workspaces } });
      if (res.nModified > 0) {
        return '更新成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async dropUpdateMill(params) {
    try {
      const { ctx } = this;
      await ctx.model.MillConstruction.deleteMany({ EnterpriseID: params.EnterpriseID });
      await ctx.model.MillConstruction.insertMany(params.mills);
    } catch (error) {
      console.log(error);
      return 500;
    }
  }
  async copyWorkspace(params) {
    try {
      await this.ctx.model.MillConstruction.updateOne({ _id: params.millId }, { $push: { children: params.workspace } });
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async copyStation(params) {
    try {
      await this.ctx.model.MillConstruction.updateOne({ _id: params.millId, 'children._id': params.workspaceId }, { $push: { 'children.$.children': params.workspace } });
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async copyEmployee(params) {
    try {
      const { ctx } = this;
      let res = {};
      let employeeInfo = {};
      if (params.millId) {
        employeeInfo = await ctx.model.MillConstruction.aggregate([
          { $match: { _id: params.millId } },
          { $unwind: '$children' },
          { $match: { 'children._id': params.workspaceId } },
          { $unwind: '$children.children' },
          { $match: { 'children.children._id': params.stationId } },
          { $match: { 'children.children.children.employees': params.employeeId } },
        ]);
        if (employeeInfo.length > 0) {
          return { errMsg: '该岗位已存在该人员，请勿重复添加' };
        }
        res = await ctx.model.MillConstruction.updateOne({ _id: params.millId }, {
          $addToSet: {
            'children.$[i].children.$[j].children': { employees: params.employeeId },
          },
        }, {
          arrayFilters: [{
            'i._id': params.workspaceId,
          }, { 'j._id': params.stationId },
          ],
        });
      } else {
        employeeInfo = await ctx.model.MillConstruction.aggregate([
          { $match: { _id: params.workspaceId } },
          { $unwind: '$children' },
          { $match: { 'children._id': params.stationId } },
          { $unwind: '$children.children' },
          { $match: { 'children.children.employees': params.employeeId } },
        ]);
        if (employeeInfo.length > 0) {
          return { errMsg: '该岗位已存在该人员，请勿重复添加' };
        }
        res = await ctx.model.MillConstruction.updateOne({ _id: params.workspaceId, 'children._id': params.stationId }, {
          $addToSet: {
            'children.$.children': { employees: params.employeeId },
          },
        });
      }
      if (res.nModified > 0) {
        const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
        // 更新员工工作变更状态
        const statusChange = {
          changType: 2,
          EnterpriseID,
          stationsTo: [ params.stationId ],
          message: '转岗',
        };
        await ctx.model.EmployeeStatusChange.updateOne({ employee: params.employeeId }, { $addToSet: { statusChanges: statusChange } });
        return '添加成功';
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async importHarmFactorData(params) {
    try {
      // 清空集合
      for (let i = 0; i < params.length; i++) {
        await this.ctx.model.HarmFactors.updateOne({ label: params[i].label }, { $addToSet: { harmFactors: { $each: params[i].harmFactors } } });
      }
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async findAllHarmFactors() {
    try {
      const res = await this.ctx.model.OccupationalexposureLimits.aggregate([
        { $unwind: '$chineseName' },
        { $group: { _id: '$catetory', harmFactors: { $push: { label: '$chineseName' } } } },
        { $project: { label: '$_id', harmFactors: 1 } },
      ]);
      return res;
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async moveEmployees(params) {
    try {
      const { ctx } = this;
      let info = {};
      let employeeObjs = [];
      for (let i = 0; i < params.employeeId.length; i++) {
        // info = await ctx.model.MillConstruction.findOne({ $or: [{ 'children.children.employees': params.employeeId[i] }, { 'children.children.children.employees': params.employeeId[i] }] });
        if (params.selectStationIds.length === 3) {
          info = await ctx.model.MillConstruction.aggregate([
            { $match: { _id: params.selectStationIds[0] } },
            { $unwind: '$children' },
            { $match: { 'children._id': params.selectStationIds[1] } },
            { $unwind: '$children.children' },
            { $match: { 'children.children._id': params.selectStationIds[2] } },
            { $match: { 'children.children.children.employees': params.employeeId[i] } },
          ]);
        } else {
          info = await ctx.model.MillConstruction.aggregate([
            { $match: { _id: params.selectStationIds[0] } },
            { $unwind: '$children' },
            { $match: { 'children._id': params.selectStationIds[1] } },
            { $unwind: '$children.children' },
            { $match: { 'children.children.employees': params.employeeId[i] } },
          ]);

          console.log('else=====结果', info);
        }
        if (info.length > 0) {
          return { errMsg: '该岗位已存在该人员，请勿重复添加' };
        }
        employeeObjs.push({ employees: params.employeeId[i] });
      }


      // 先移除
      await this.deleteEmployee(params, true);
      // 后添加
      console.log(params.employeeId);
      employeeObjs = params.employeeId.map(item => {
        item = { employees: item, isPass: '1' }; // ==>jhw
        return item;
      });

      console.log('转岗===============', employeeObjs);

      let res = {};
      if (params.selectStationIds.length === 3) {
        res = await ctx.model.MillConstruction.updateOne({ _id: params.selectStationIds[0] }, { $addToSet: { 'children.$[i].children.$[j].children': { $each: employeeObjs } } },
          {
            arrayFilters: [
              { 'i._id': params.selectStationIds[1] },
              { 'j._id': params.selectStationIds[2] },
            ],
          });

      } else {
        res = await ctx.model.MillConstruction.updateOne({ _id: params.selectStationIds[0] }, { $addToSet: { 'children.$[i].children': { $each: employeeObjs } } },
          {
            arrayFilters: [
              { 'i._id': params.selectStationIds[1] },
            ],
          });
      }
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

      // 更新员工工作变更状态
      const statusChange = {
        changType: 2,
        EnterpriseID,
        stationFrom: params.stationId,
        stationsTo: [ params.selectStationIds[params.selectStationIds.length - 1] ],
        changStationReason: params.changStationReason,
        timestamp: params.timestamp,
        message: '转岗',
      };
      console.log(statusChange, '工作状态');
      await ctx.model.EmployeeStatusChange.updateMany({ employee: { $in: params.employeeId } }, { $addToSet: { statusChanges: statusChange } });


      // 更新员工转岗是否已读 ===> jhw
      for (let index = 0; index < employeeObjs.length; index++) {
        const obj = {};
        const element = employeeObjs[index];
        const people = await ctx.model.Employee.find({ _id: element.employees });
        obj.employeeId = people[0]._id;
        const userInfo = await ctx.model.User.find({ companyId: { $in: [ EnterpriseID ] }, idNo: people[0].IDNum });
        if (userInfo.length > 0) {
          const WorkChanges = await ctx.service.employee.findEmployeeWorkChanges({ employee: people[0]._id });
          obj.beforePosition = WorkChanges[0].statusChanges[WorkChanges[0].statusChanges.length - 1].stationFrom;
          obj.nowPosition = WorkChanges[0].statusChanges[WorkChanges[0].statusChanges.length - 1].stationsTo[0];
          obj.isRead = '1';
          const res = await ctx.model.User.findOneAndUpdate({ _id: userInfo[0]._id }, { $set: { aboutTransfer: obj } }, { new: true });
          console.log(res);
        } else {
          continue;
        }
      }
      if (res.nModified > 0) {
        await this.getStatistics();
        return '删除成功';
      }


    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  async getCheckResult(params) {
    // const stations = await await ctx.model.MillConstruction.aggregate([
    //   {$unwind:}
    // ])
    return await this.getStationsCheckResult(params);
  }

  async findEmployeeById(params) {
    try {
      const { ctx } = this;
      const EnterpriseID = params.EnterpriseID;
      const employeeInfo = (await ctx.service.employee.findAllEmployees(params)).res[0];
      // 查询所在岗位及危害因素
      const stations = await ctx.service.zygrRecord.findEmployeeStation({ EnterpriseID, employeeId: params._id });
      const checkResult = await this.getStationsCheckResult(stations);
      const employeeDefends = await ctx.model.Defendproducts.aggregate([
        { $match: { EnterpriseID } },
        { $unwind: '$formData' },
        { $match: { 'formData.receiveMan': employeeInfo.name } },
      ]);
      const employeeTrainings = await ctx.model.EmployeesTrainingPlan.find({ EnterpriseID, employees: params._id });
      return { employeeInfo, stations, checkResult, employeeDefends, employeeTrainings };
    } catch (error) {
      console.log(error);
    }
  }

  // 获取检测结果
  async getStationsCheckResult(stations) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    let checkResultItem = [];
    let model = '';
    let matchOrOne = {};
    let matchOrTwo = {};
    let matchAnd = {};
    let project = {};
    const checkResult = {};
    let modelName = '';
    let checkProjectFields = {};
    for (let i = 0; i < stations.length; i++) {
      for (let j = 0; j < stations[i].harmFactorsAndSort.length; j++) {
        checkProjectFields = {};
        if (stations[i].harmFactorsAndSort[j][0] === '化学') {
          model = 'chemistryFactors';
          modelName = '化学';
          checkProjectFields[model + '.formData.checkProject'] = stations[i].harmFactorsAndSort[j][1].trim();
        } else if (stations[i].harmFactorsAndSort[j][0] === '粉尘') {
          model = 'dustFactors';
          modelName = '粉尘';
          checkProjectFields[model + '.formData.checkProject'] = { $regex: stations[i].harmFactorsAndSort[j][1].trim() };
        } else if (stations[i].harmFactorsAndSort[j][0] === '生物') {
          model = 'biologicalFactors';
          modelName = '生物';
          checkProjectFields[model + '.formData.checkProject'] = stations[i].harmFactorsAndSort[j][1].trim();
        } else {
          if (stations[i].harmFactorsAndSort[j][1].indexOf('噪声') !== -1) {
            model = 'noiseFactors';
            modelName = '噪声';
          } else if (stations[i].harmFactorsAndSort[j][1].indexOf('高温') !== -1) {
            model = 'heatFactors';
            modelName = '高温';
          } else if (stations[i].harmFactorsAndSort[j][1].indexOf('超高频辐射') !== -1) {
            model = 'ultraHighRadiationFactors';
            modelName = '超高频辐射';
          } else if (stations[i].harmFactorsAndSort[j][1].indexOf('高频电磁场') !== -1) {
            model = 'highFrequencyEleFactors';
            modelName = '高频电磁场';
          } else if (stations[i].harmFactorsAndSort[j][1].indexOf('工频电磁场') !== -1) {
            model = 'powerFrequencyElectric';
            modelName = '工频电场';
          } else if (stations[i].harmFactorsAndSort[j][1].indexOf('激光') !== -1) {
            model = 'laserFactors';
            modelName = '激光辐射';
          } else if (stations[i].harmFactorsAndSort[j][1].indexOf('微波') !== -1) {
            model = 'microwaveFactors';
            modelName = '微博辐射';
          } else if (stations[i].harmFactorsAndSort[j][1].indexOf('紫外线') !== -1) {
            model = 'ultravioletFactors';
            modelName = '紫外线';
          } else if (stations[i].harmFactorsAndSort[j][1].indexOf('振动') !== -1) {
            model = 'handBorneVibrationFactors';
            modelName = '手传振动';
          } else if (stations[i].harmFactorsAndSort[j][1].indexOf('游离二氧化硅') !== -1) {
            model = 'SiO2Factors';
            modelName = '游离二氧化硅';
          }
        }
        if (model) {
          matchOrOne = {};
          matchOrTwo = {};
          matchAnd = {};
          project = {};
          matchOrOne[model + '.formData.station'] = { $regex: stations[i].name.trim() };
          matchOrOne[model + '.formData.workspace'] = { $regex: stations[i].workspace.trim() };
          matchOrTwo[model + '.formData.checkAddress'] = { $regex: stations[i].workspace.indexOf('车间') ? stations[i].workspace.trim() : stations[i].workspace.trim() + '车间' };
          matchAnd[model + '.formData.checkAddress'] = { $regex: '车间' + stations[i].name.trim() };
          project[model] = 1;
          project.jobHealthId = 1;
          project['jobHealth.name'] = 1;
          project['jobHealth.reportTime'] = 1;
          checkResultItem = await ctx.model.CheckAssessment.aggregate([
            { $match: { EnterpriseID, process: { $exists: false } } },
            { $unwind: '$' + model + '.formData' },
            {
              $match: {
                ...checkProjectFields,
                $or: [
                  matchOrOne, { $and: [ matchOrTwo, matchAnd ] },
                ],
              },
            },

            {
              $lookup: {
                from: 'jobhealths',
                localField: 'jobHealthId',
                foreignField: '_id',
                as: 'jobHealth',
              },
            },
            {
              $project: project,
            },
          ]);
          for (let k = 0; k < checkResultItem.length; k++) {
            if (checkResultItem[k] && checkResultItem[k].jobHealth[0] && checkResultItem[k][model].formData) {
              checkResultItem[k][model].formData.checkTime = checkResultItem[k].jobHealth[0].reportTime ? moment(new Date(checkResultItem[k].jobHealth[0].reportTime)).format('YYYY-MM-DD') : '';
              checkResultItem[k][model].formData.checkName = checkResultItem[k].jobHealth[0].name;
              checkResultItem[k][model].formData.checkProject = checkResultItem[k][model].formData.checkProject || modelName;
              checkResultItem[k][model].formData.checkResult = checkResultItem[k][model].formData.checkResult || checkResultItem[k][model].formData.conclusion;
              checkResultItem[k][model].formData.protectiveFacilities = stations[i].protectiveFacilities;
              // checkResultItem[k] = checkResultItem[k][model].formData;
              // checkResultItem[k].protectiveFacilities = stations[i].protectiveFacilities;
              // checkResults.push(checkResultItem[k]);
              if (Object.keys(checkResult).indexOf('' + model) === -1) {
                checkResult[model] = { data: [] };
              }
              checkResult[model].model = model;
              checkResult[model].modelName = modelName;
              checkResult[model].data.push(checkResultItem[k][model].formData);
            }
          }
        }
      }
    }
    return Object.values(checkResult);
  }

  // async getStationsCheckResult(stations) {
  //   console.log('2222222222222222222222222222222');
  //   console.log('岗位======================');
  //   console.log(stations);
  //   const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
  //   let checkResult = {};
  //   let checkResultItem = [];
  //   let model = '';
  //   let modelName = '';
  //   let checkProjectFields = {};
  //   for (let i = 0; i < stations.length; i++) {
  //     for (let j = 0; j < stations[i].harmFactorsAndSort.length; j++) {
  //       checkProjectFields = {};
  //       if (stations[i].harmFactorsAndSort[j][0] === '化学') {
  //         model = 'chemistryFactors';
  //         modelName = '化学';
  //         checkProjectFields[model + '.formData.checkProject'] = stations[i].harmFactorsAndSort[j][1].trim();
  //       } else if (stations[i].harmFactorsAndSort[j][0] === '粉尘') {
  //         model = 'dustFactors';
  //         checkProjectFields[model + '.formData.checkProject'] = stations[i].harmFactorsAndSort[j][1].trim();
  //         modelName = '粉尘';
  //       } else if (stations[i].harmFactorsAndSort[j][0] === '生物') {
  //         modelName = '生物';
  //         model = 'biologicalFactors';
  //         checkProjectFields[model + '.formData.checkProject'] = stations[i].harmFactorsAndSort[j][1].trim();
  //       } else {
  //         if (stations[i].harmFactorsAndSort[j][1].indexOf('噪声') !== -1) {
  //           model = 'noiseFactors';
  //           modelName = '噪声';
  //         } else if (stations[i].harmFactorsAndSort[j][1].indexOf('高温') !== -1) {
  //           modelName = '高温';
  //           model = 'heatFactors';
  //         } else if (stations[i].harmFactorsAndSort[j][1].indexOf('以上未提及的可导致职业病的其他物理因素') !== -1) {
  //           modelName = '超高频辐射';
  //           model = 'ultraHighRadiationFactors';
  //         } else if (stations[i].harmFactorsAndSort[j][1].indexOf('高频电磁场') !== -1) {
  //           modelName = '高频电磁场';
  //           model = 'highFrequencyEleFactors';
  //         } else if (stations[i].harmFactorsAndSort[j][1].indexOf('工频电磁场') !== -1) {
  //           model = 'powerFrequencyElectric';
  //           modelName = '工频电场';
  //         } else if (stations[i].harmFactorsAndSort[j][1].indexOf('激光辐射') !== -1) {
  //           modelName = '激光辐射';
  //           model = 'laserFactors';
  //         } else if (stations[i].harmFactorsAndSort[j][1].indexOf('微波') !== -1) {
  //           modelName = '微波辐射';
  //           model = 'microwaveFactors';
  //         } else if (stations[i].harmFactorsAndSort[j][1].indexOf('紫外线') !== -1) {
  //           modelName = '紫外辐射';
  //           model = 'ultravioletFactors';
  //         } else if (stations[i].harmFactorsAndSort[j][1].indexOf('振动') !== -1) {
  //           modelName = '手传振动';
  //           model = 'handBorneVibrationFactors';
  //         } else if (stations[i].harmFactorsAndSort[j][1].indexOf('游离二氧化硅') !== -1) {
  //           modelName = '游离二氧化硅';
  //           model = 'SiO2Factors';
  //         }

  //       }
  //       const matchOrOne = {};
  //       matchOrOne[model + '.formData.station'] = { $regex: stations[i].name.trim() };
  //       matchOrOne[model + '.formData.workspace'] = { $regex: stations[i].workspace.trim() };
  //       const matchOrTwo = {};
  //       matchOrTwo[model + '.formData.checkAddress'] = { $regex: stations[i].workspace.indexOf('车间') ? stations[i].workspace.trim() : stations[i].workspace.trim() + '车间' };
  //       const matchAnd = {};
  //       matchAnd[model + '.formData.checkAddress'] = { $regex: '车间' + stations[i].name.trim() };
  //       const project = {};
  //       project[model] = 1;
  //       checkResultItem = await ctx.model.CheckAssessment.aggregate([
  //         { $match: { EnterpriseID } },
  //         { $unwind: '$' + model + '.formData' },
  //         {
  //           $match: {
  //             ...checkProjectFields,
  //             $or: [
  //               matchOrOne, { $and: [ matchOrTwo, matchAnd ] },
  //             ],
  //           },
  //         },
  //         {
  //           $project: project,
  //         },
  //       ]);
  //       console.log(JSON.stringify(checkResultItem));
  //       // if(checkResultItem[0]) resultArr.push(checkResultItem[0])
  //       if (checkResultItem[0]) {
  //         if (Object.keys(checkResult).indexOf('' + model) === -1) {
  //           checkResult[model] = { data: [] };
  //         }
  //         checkResult[model].model = model;
  //         checkResult[model].modelName = modelName;
  //         checkResult[model].data.push(checkResultItem[0][model].formData);
  //       }
  //     }
  //   }
  //   if (checkResultItem) { checkResult = Object.values(checkResult); }
  //   return checkResult;
  // }
  async findHealthheckResult(params) {
    const res = await this.ctx.model.Suspect.find({ employeeId: params._id }).sort({ checkDate: -1 });
    return res;
  }
}

module.exports = MillService;
