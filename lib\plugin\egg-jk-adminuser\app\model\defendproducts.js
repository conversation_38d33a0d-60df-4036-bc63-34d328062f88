module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const defendproductsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: { type: String },
    year: { type: String },
    formData: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      studio: { type: String }, // 车间
      pname: { type: String }, // 防护用具名称
      sku: { type: String }, // 型号
      quantity: { type: String }, // 数量
      date: { type: Date }, // 日期
      receiveMan: { type: String }, // 领取人
      acknowledge: {
        required: true,
        type: Boolean,
        default: false,
      },
      sign: { type: String }, // 领取人签字
      createTime: { type: Date, default: Date.now },
    }],
    invoice: [{ // 发票
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { type: String },
      webname: { type: String },
    }],
    certifications: [{ // 三证及其他
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { type: String },
      webname: { type: String },
    }],
    receiveForm: [{ // 领用表
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: { type: String },
      webname: { type: String },
      date: { type: String },
    }],
    oldtime: String,
  });
  return mongoose.model('defendproducts', defendproductsSchema);
};
