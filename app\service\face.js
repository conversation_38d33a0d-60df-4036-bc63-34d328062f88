const Service = require('egg').Service;
// This file is auto-generated, don't edit it
// 依赖的模块可通过下载工程中的模块依赖文件或右上角的获取 SDK 依赖信息查看
const OpenapiClient = require('@alicloud/openapi-client');
const Util = require('@alicloud/tea-util');

const fs = require('fs');
const path = require('path');
// const { config } = require('../public/plugins/ueditor/third-party/zeroclipboard/ZeroClipboard');
class Client {
  /**
   * 使用AK&SK初始化账号Client
   * @param accessKeyId
   * @param accessKeySecret
   * @return Client
   * @throws Exception
   */
  static createClient(
    accessKeyId = this.app.config.facebody.accessKeyId,
    accessKeySecret = this.app.config.facebody.accessKeySecret
  ) {
    // console.log('accessKeyId', accessKeyId, JSON.stringify(OpenapiClient));
    const config = new OpenapiClient.Config({
      // 必填，您的 AccessKey ID
      accessKeyId,
      // 必填，您的 AccessKey Secret
      accessKeySecret,
    });
    // 访问的域名
    config.endpoint = 'facebody.cn-shanghai.aliyuncs.com';
    return new OpenapiClient.default(config);
  }

  /**
   * 使用STS鉴权方式初始化账号Client，推荐此方式。
   * @param accessKeyId
   * @param accessKeySecret
   * @param securityToken
   * @return Client
   * @throws Exception
   */
  static createClientWithSTS(accessKeyId, accessKeySecret, securityToken) {
    const config = new OpenapiClient.Config({
      // 必填，您的 AccessKey ID
      accessKeyId,
      // 必填，您的 AccessKey Secret
      accessKeySecret,
      // 必填，您的 Security Token
      securityToken,
      // 必填，表明使用 STS 方式
      type: 'sts',
    });
    // 访问的域名
    config.endpoint = 'facebody.cn-shanghai.aliyuncs.com';
    return new OpenapiClient.default(config);
  }

  /**
   * API 相关
   * @param path params
   * @return OpenApi.Params
   */
  static createApiInfo() {
    const params = new OpenapiClient.Params({
      // 接口名称
      action: 'CompareFace',
      // 接口版本
      version: '2019-12-30',
      // 接口协议
      protocol: 'HTTPS',
      // 接口 HTTP 方法
      method: 'POST',
      authType: 'AK',
      style: 'RPC',
      // 接口 PATH
      pathname: '/',
      // 接口请求体内容格式
      reqBodyType: 'formData',
      // 接口响应体内容格式
      bodyType: 'json',
    });
    return params;
  }

  static async main(args, EnterpriseID, url1, url2, app) {
    console.log('main函数', EnterpriseID, url1, url2);
    // ctx.auditLog('有吗', app.config.upload_path, 'info');
    // 需要根据图片路径，转成base64编码，阿里云服务器读取不到本地图片
    // let data = fs.readFileSync(path.join(__dirname, `../public/upload/images/${EnterpriseID}/${url1}`));
    let data = fs.readFileSync(
      path.resolve(path.join(app.config.upload_path, EnterpriseID, url1))
    );
    console.log(
      '路径一：',
      path.resolve(path.join(app.config.upload_path, EnterpriseID, url1))
    );
    data = Buffer.from(data).toString('base64');
    // let data2 = fs.readFileSync(path.join(__dirname, `../public/upload/images/${EnterpriseID}/${url2}`));
    let data2 = fs.readFileSync(
      path.resolve(path.join(app.config.upload_path, EnterpriseID, url2))
    );
    console.log(
      '路径二',
      path.resolve(path.join(app.config.upload_path, EnterpriseID, url2))
    );
    data2 = Buffer.from(data2).toString('base64');

    // 请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
    // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378664.html
    const client = Client.createClient(
      process.env.ALIBABA_CLOUD_ACCESS_KEY_ID,
      process.env.ALIBABA_CLOUD_ACCESS_KEY_SECRET
    );
    const params = Client.createApiInfo();
    // body params
    const body = {};
    body.QualityScoreThreshold = null;
    body.ImageType = null;
    // body.ImageURLA = url1;
    // body.ImageURLB = url2;
    body.ImageURLA = null;
    body.ImageURLB = null;
    body.ImageDataA = data;
    body.ImageDataB = data2;
    // runtime options
    const runtime = new Util.RuntimeOptions({});
    const request = new OpenapiClient.OpenApiRequest({
      body,
    });
    // 复制代码运行请自行打印 API 的返回值
    // 返回值为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
    const resp = await client.callApi(params, request, runtime);
    // console.log(resp, '最后判断结果');
    return resp;
  }
}

class FaceService extends Service {

  async test(EnterpriseID, url1, url2) {
    try {
      console.log('图片路径信息：', url1, url2);
      this.ctx.auditLog('图片路径信息：', url1, url2, 'info');
      const { app } = this;
      const res = await Client.main(process.argv.slice(2), EnterpriseID, url1, url2, app);
      const confidence = res.body.Data.Confidence;
      console.log(confidence, '识别结果分数');
      let recognition = false; // 识别结果，true验证通过是同一个人，法拉瑟不是同一个人
      if (Number(confidence) > 75) {
        recognition = true;// 识别成功，是同一个人
      }
      console.log(recognition, 'recognition-----');
      this.ctx.auditLog('recognition', recognition, 'info');
      return recognition;
    } catch (error) {
      console.log(error, '这是什么错误啊face');
    }
  }
}
module.exports = FaceService;
