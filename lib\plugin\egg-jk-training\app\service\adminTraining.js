
const Service = require('egg').Service;


class TestPaperService extends Service {
  // 根据课程类型获取培训
  async getList2(data) {
    const query = {
      auditStatus: '2',
      salesStatus: true,
    };
    if (data.trainingType) {
      query.trainingType = +data.trainingType;
      // if (query.trainingType === 1) query.EnterpriseID = data.EnterpriseID;
      if (query.trainingType === 2) query.completeTime = { $gte: new Date() }; // 截止时间大于当前时间
    }
    if (data.keyWord) query.name = { $regex: new RegExp(data.keyWord, 'i') };
    data.pageCurrent = data.pageCurrent ? +data.pageCurrent : 1;
    data.size = data.size ? +data.size : 10;
    // 课程筛选
    // const query2 = { 'courses.name': { $regex: new RegExp(data.keyWord, 'i') } };
    const query2 = {};
    if (data.classificationId) { // 课程类别
      if (data.classificationId !== 'hot') {
        query2['courses.classification'] = data.classificationId;
      }
    }
    let res = await this.ctx.model.AdminTraining.aggregate([
      { $match: query },
      { $lookup: { from: 'Courses', localField: 'coursesID', foreignField: '_id', as: 'courses' } },
      { $match: query2 },
      // { $sort: { date: -1 } },
    ]);
    // 课程浏览量
    res = JSON.parse(JSON.stringify(res));
    res.forEach(ele => {
      ele.views = 0;
      ele.courses.forEach(course => {
        ele.views += course.views;
      });
    });
    res = res.sort(ele => ele.views); // 按课程浏览量排序
    const startIndex = data.size * (data.pageCurrent - 1);
    let lastData = res.slice(startIndex, startIndex + data.size);
    // 判断如果登陆状态，返回结果支付未
    if (data.userId) {
      const ismylist = await this.ctx.model.PersonalTraining.find({
        userId: data.userId,
      }, { adminTrainingId: 1 });
      let myTrain = '';
      for (const item of ismylist) {
        myTrain += (',' + item.adminTrainingId);
      }
      lastData = lastData.map(data => {
        if (myTrain.includes(data._id)) {
          data.isStudy = true;
        }
        return data;
      });
    }
    return {
      list: lastData,
      pageInfo: {
        pageCurrent: data.pageCurrent,
        size: data.size,
        total: res.length,
      },
    };
  }
  // 获取列表(监管端发的管理员培训或者公开课自主培训)
  async getList(data) {
    const { ctx } = this;
    const userId = ctx.session.user ? ctx.session.user._id : '';
    const query = {
      auditStatus: '2',
      salesStatus: true,
    };
    if (data.trainingType) {
      query.trainingType = +data.trainingType;
      if (query.trainingType === 1) query.EnterpriseID = data.EnterpriseID;
      if (query.trainingType === 2) query.completeTime = { $gte: new Date() }; // 截止时间大于当前时间
    }
    data.pageCurrent = data.pageCurrent ? +data.pageCurrent : 1;
    data.size = data.size ? +data.size : 10;
    if (data.keyWord) query.name = { $regex: new RegExp(data.keyWord, 'i') };
    const res = await this.ctx.model.AdminTraining.find(query)
      .sort({ createdAt: -1 })
      .skip((data.pageCurrent - 1) * data.size)
      .limit(data.size);

    // 查询培训状态
    const newRes = JSON.parse(JSON.stringify(res));
    const len = res.length;
    let query2 = {
      trainingType: data.trainingType,
    }; // 个人培训的查询条件
    if (data.trainingType === 1) {
      query2 = {
        employeesId: data.employeesId,
        EnterpriseID: data.EnterpriseID,
      };
    }
    for (let i = 0; i < len; i++) {
      const personalTrainingDetail = await this.ctx.model.PersonalTraining.findOne({
        ...query2,
        adminTrainingId: res[i]._id,
        userId,
        status: true,
      },
      { completeState: 1 });
      // 0 是未开始  1 是进行中  2 是已完成
      newRes[i].personalTrainingStatus = !personalTrainingDetail ? 0 : personalTrainingDetail.completeState ? 2 : 1;
      newRes[i].personalTrainingId = personalTrainingDetail ? personalTrainingDetail._id : '';
      // 查询培训中首个课程的详情
      if (data.size <= 4 && res[i].coursesID && res[i].coursesID[0]) {
        newRes[i].firstCourseDetail = await this.ctx.model.Courses.findOne(
          { _id: res[i].coursesID[0] },
          { cover: 1, labels: 1, views: 1, likes: 1, commentLength: 1 }
        );
      }
    }
    const pageInfo = await this.getPageInfo('AdminTraining', data.size, data.pageCurrent, query);
    return {
      res: newRes,
      pageInfo,
    };
  }
  async getPageInfo(collection, size, pageCurrent, query = {}) {
    const total = await this.ctx.model[collection].find(query).count();
    const pageInfo = {
      total,
      size,
      pageCurrent,
    };
    return pageInfo;
  }

  async update(data) {
    return await this.ctx.model.AdminTraining.updateOne(
      { _id: data._id },
      data,
      { new: true }
    );
  }

  async findOne(data, option = {}) {
    return await this.ctx.model.AdminTraining.findOne(data, option);
  }

  // async del(_id) {
  //   const { ctx } = this;
  //   return await ctx.model.TestPaper.findByIdAndRemove(_id);
  // }
  async getDetail(data) {
    const detail = await this.ctx.model.AdminTraining.findOne(data);
    // const coursesList = detail ? await this.ctx.model.Courses.find({ _id: { $in: detail.coursesID || [] } }) : [];
    // 获取课程及视频想请
    const coursesList = [];
    if (detail) {
      const updateView = detail.auditStatus === '2' && detail.salesStatus; // 是否更新课程观看数
      for (let i = 0; i < detail.coursesID.length; i++) {
        const courseDetail = await this.ctx.service.course.getOneCourseDetail(detail.coursesID[i], updateView);
        if (courseDetail) coursesList.push(courseDetail);
      }
    }
    return {
      detail,
      coursesList,
    };
  }


  // 监管端 - 获取培训计划列表
  async getPlanList(query, current = 1, pageSize = 10) {
    try {
      return {
        list: await this.ctx.model.AdminTraining.find(query).populate([{
          path: 'EnterpriseID',
          select: 'cname',
        }, {
          path: 'completedEnterprise',
          select: 'cname',
        }, {
          path: 'coursesID',
          select: 'name',
        }]).sort({
          date: -1,
        })
          .skip((Number(current) - 1) * Number(pageSize))
          .limit(Number(pageSize)),
        count: await this.ctx.model.AdminTraining.countDocuments(query),
      };
    } catch (error) {
      console.error(error);
      return -1;
    }
  }

}

module.exports = TestPaperService;
