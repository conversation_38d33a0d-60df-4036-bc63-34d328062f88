// 定时任务 同步福州数据
module.exports = app => {
  class byDataMonitor extends app.Subscription {
    static get schedule() {
      return {
        // 每5秒一次
        cron: app.config.byMonitor.scheduleCron || '*/5 * * * * *',
        disable: (app.config.byMonitor.scheduleJob !== 'true'),
        immediate: true, // 启动服务时就立即执行一次任务
        type: 'worker', // 指定每次只有一个 随机的worker 执行
      };
    } // 定时执行的任务
    async subscribe() {
      try {
        this.ctx.auditLog('BYDATA定时任务开始', 'BYDATA定时任务开始', 'info');
        console.time('BYDATA定时任务3耗时');
        try {
          await this.ctx.service.byData.getEquipmentRealTimeData();
        } catch (error) {
          this.ctx.auditLog('BYDATA噪声', JSON.stringify(error), 'error');
        }
        try {
          await this.ctx.service.byData.getEquipmentRealTimeDataH2SPH3();
        } catch (error) {
          this.ctx.auditLog('BYDATAPH3H2S', JSON.stringify(error), 'error');
        }
        try {
          await this.ctx.service.byData.getPIDeviceRealTimeData();
        } catch (error) {
          this.ctx.auditLog('BYDATAPI', JSON.stringify(error), 'error');
        }
        console.timeEnd('BYDATA定时任务3耗时');
      } catch (error) {
        this.ctx.auditLog('同步北元数据失败', JSON.stringify(error), 'error');
      }
    }
  }
  return byDataMonitor;
};
