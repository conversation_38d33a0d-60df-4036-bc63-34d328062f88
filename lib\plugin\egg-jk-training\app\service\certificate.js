
const Service = require('egg').Service;
// 证书管理
class CertificateService extends Service {

  // 根据传入的条件findOne个人培训记录
  async findOne(data) {
    return await this.ctx.model.Certificate.findOne(data);
  }
  async update(data) {
    return await this.ctx.model.Certificate.update(
      { _id: data._id },
      data,
      { new: true }
    );
  }
  // 获取培训证书的批次和编号
  async getCount(params) {
    if (params.number && params.number.length === 6) {
      params.number = { $regex: new RegExp('^' + params.number) };
    }
    const res = await this.ctx.model.Certificate.find(params, { number: 1 }, { sort: { createdAt: -1 } });
    return res.length ? res[0].number : 0;
    // return await this.ctx.model.Certificate.count(params);
  }
  async create(data) {
    const { ctx, config } = this;
    data.userId = ctx.session.user ? ctx.session.user._id : '';
    // 证书去重
    // console.log(data.personalTrainingId, 'data.personalTrainingIdooooo');
    const personalTrainingId = data.personalTrainingId;
    if (!personalTrainingId) return 'personalTrainingId参数必传';
    const count = await ctx.model.Certificate.count({
      personalTrainingId,
      userId: data.userId,
    });
    const { completeState } = await ctx.model.PersonalTraining.findOne({ _id: personalTrainingId });
    if (!completeState) return '请先完成个人培训';
    if (count) return '请勿重复申请证书';
    if (config.branch === 'hf' && (!data.trainingType || (+data.trainingType === 1))) data.unit = '杭州职卫云科技有限公司';
    if ((data.certificateStatus && +data.certificateStatus === 2) || config.branch === 'ah') {
      const effectiveYear = config.certificateConfig.effectiveYear;
      data.issuanceTime = Date.now();
      data.effectiveTime = new Date(new Date().getTime() + 1000 * 60 * 60 * 24 * 365 * effectiveYear);
      data.img = await ctx.service.certificatePdf.createCertificatePdf(data);
      if (!data.img) {
        return '证书生成失败';
      }
    }
    const res = await new ctx.model.Certificate({
      ...data,
    }).save();
    return res;
  }
  // 我的证书列表
  async myList(data) {
    const { ctx, app } = this;
    const userId = ctx.session.user ? ctx.session.user._id : '';
    if (data.keyWord) data.keyWord = data.keyWord.trim();
    const reg = new RegExp(data.keyWord); // 不区分大小写
    const query = {
      userId,
      $or: [ // 多条件，数组
        { 'trainingDetail.name': { $regex: reg } },
        { 'winner.name': { $regex: reg } },
        { 'winner.companyName': { $regex: reg } },
        { unit: { $regex: reg } },
        { number: { $regex: reg } },
      ],
    };
    if (data.trainingType) query.trainingType = +data.trainingType;
    if (data.certificateStatus) query.certificateStatus = +data.certificateStatus;
    data.pageCurrent = data.pageCurrent ? +data.pageCurrent : 1;
    data.size = data.size ? +data.size : 10;
    const res = await ctx.model.Certificate.find(query)
      .sort({ createdAt: -1 })
      .skip((data.pageCurrent - 1) * data.size)
      .limit(data.size);
      // .populate('EnterpriseID', 'cname')
      // .populate('adminUserId', 'name userName');
    const basaPath = app.config.certificate_http_path;
    const resData = JSON.parse(JSON.stringify(res));
    for (let i = 0; i < resData.length; i++) {
      const newEle = resData[i];
      if (newEle.img && newEle.img.length < 35) {
        newEle.img = `/static${basaPath}/${newEle.img}`;
      }
      if (newEle.trainingType === 3) {
        // 员工培训 获取最后一次的考试成绩
        const testRecord = await ctx.model.TestRecord.find(
          { personalTrainingId: newEle.personalTrainingId },
          { resultStatistics: 1 }
        ).sort({ createdAt: -1 });
        if (testRecord.length) {
          newEle.testRecord = testRecord[0].resultStatistics.actualScore || 0;
        } else {
          newEle.testRecord = 0;
        }
      }
    }
    const pageInfo = await this.getPageInfo('Certificate', data.size, data.pageCurrent, query);
    return {
      list: resData,
      pageInfo,
    };
  }
  // 查询支付结果,判断能否生产证书 传入personalTraining ID
  async checkPayStatus(personalTrainingID) {
    // 主动查询,订单信息
    const personalTrainingData = await this.ctx.model.PersonalTraining.aggregate([
      {
        $match: {
          _id: personalTrainingID,
        },
      }, {
        $lookup: {
          from: 'payInfo',
          localField: 'payInfoID',
          foreignField: '_id',
          as: 'payInfo',
        },
      },
    ]);
    if (personalTrainingData[0].payInfo.length > 0) {
      await this.ctx.service.pay.orderCheck(personalTrainingData[0].payInfo[0]);
      const lastPayInfo = await this.ctx.model.PayInfo.findOne({ _id: personalTrainingData[0].payInfo[0]._id }, { payStatus: 1 });
      return lastPayInfo.payStatus === 1;
    }
    return false;

  }
  async getPageInfo(collection, size, pageCurrent, query = {}) {
    const total = await this.ctx.model[collection].find(query).count();
    const pageInfo = {
      total,
      size,
      pageCurrent,
    };
    return pageInfo;
  }
  // 获取个人订单列表
  async myOrderList(data) {
    const { ctx } = this;
    const userID = ctx.session.user ? ctx.session.user._id : '';
    if (data.keyWord) data.keyWord = data.keyWord.trim();
    const reg = new RegExp(data.keyWord); // 不区分大小写
    const query = {
      userID,
      productCategory: { $in: [ 1, 2 ] },
      $or: [ // 多条件，数组
        { description: { $regex: reg } },
      ],
    };
    data.pageCurrent = data.pageCurrent ? +data.pageCurrent : 1;
    data.size = data.size ? +data.size : 10;
    const res = await ctx.model.PayInfo.find(query)
      .sort({ createdAt: -1 })
      .skip((data.pageCurrent - 1) * data.size)
      .limit(data.size);
    const resData = JSON.parse(JSON.stringify(res));
    console.log('res', res, userID);
    const pageInfo = await this.getPageInfo('PayInfo', data.size, data.pageCurrent, query);
    return {
      list: resData,
      pageInfo,
    };
  }
  async obtainInvoice(data) {
    return await this.ctx.model.PayInfo.findOneAndUpdate(
      { _id: data._id },
      { invoiceStatus: 1, invoice: data.invoice },
      { new: true }
    );
  }
}

module.exports = CertificateService;
