
const testPaperController = require('../controller/manage/testPaper');
const questionBankController = require('../controller/manage/questionBank');

module.exports = (options, app) => {
  return async function testPaperRouter(ctx, next) {
    const pluginConfig = app.config.jk_testPaper;
    if (ctx.request.url.startsWith('/manage/testPaper/')) {
      await app.initPluginRouter(ctx, pluginConfig, testPaperController);
    } else if (ctx.request.url.startsWith('/manage/questionBank/')) {
      await app.initPluginRouter(ctx, pluginConfig, questionBankController);
    }
    await next();

  };

};
