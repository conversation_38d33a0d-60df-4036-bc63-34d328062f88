/**
 * @file 自动同步设备数据定时任务
 * @description 定期获取所有在线设备的实时数据并存储
 */

'use strict';

const Subscription = require('egg').Subscription;

class SyncDevicesDataTask extends Subscription {
  // 配置定时任务执行间隔
  static get schedule() {
    return {
      interval: '1h', // 每5分钟执行一次
      type: 'worker', // 只在一个 worker 上运行
      immediate: true, // 应用启动后立即执行一次
    };
  }

  // 定时任务的处理逻辑
  async subscribe() {
    const { ctx } = this;

    try {
      ctx.auditLog('万华设备数据同步', '开始执行万华设备数据同步任务', 'info');

      // 调用 whDataOnline 服务中的同步方法
      const profilename = [
        'YT_OHSYD',
        'FJ_OHSYD',
        'GD_OHSYD',
        'NB_OHSYD',
        'SC_OHSYD',
      ];
      // 获取当前实时数据
      let syncDeviceCount = 0;
      for (const item of profilename) {
        ctx.auditLog('万华设备数据同步', `开始同步${item}设备数据`, 'info');
        const result = await ctx.service.whDataOnline.getCurrentDataValue(item);
        if (result.success) {
          ctx.auditLog('万华设备数据同步', `同步${item}设备数据成功: ${result.count}`, 'info');
        } else {
          ctx.auditLog('万华设备数据同步', `同步${item}设备数据失败: ${result.message}`, 'error');
          continue; // 跳过当前设备数据同步
        }
        syncDeviceCount += result.count;
      }
      ctx.auditLog('同步设备数据', `成功同步${syncDeviceCount}个设备数据`, 'info');
    } catch (error) {
      ctx.auditLog('万华设备数据同步', `万华设备数据同步任务异常: ${error}`, 'error');
    }
  }
}

module.exports = SyncDevicesDataTask;


module.exports = app => {
  class SyncDevicesDataTask extends app.Subscription {
    // 配置定时任务执行间隔
    static get schedule() {
      return app.config.whDataOnline;
    }
    // 定时任务的处理逻辑
    async subscribe() {
      const { ctx } = this;
      try {
        ctx.auditLog('万华设备数据同步', '开始执行万华设备数据同步任务', 'info');
        // 调用 whDataOnline 服务中的同步方法
        const profilename = [ 'YT_OHSYD',
          // 'FJ_OHSYD', 'GD_OHSYD', 'NB_OHSYD', 'SC_OHSYD'
        ];
        // 获取当前实时数据
        let syncDeviceCount = 0;
        for (const item of profilename) {
          ctx.auditLog('万华设备数据同步', `开始同步${item}设备数据`, 'info');
          const result = await ctx.service.whDataOnline.getCurrentDataValue(item);
          if (result.success) {
            ctx.auditLog('万华设备数据同步', `同步${item}设备数据成功: ${result.count}`, 'info');
          } else {
            ctx.auditLog('万华设备数据同步', `同步${item}设备数据失败: ${result.message}`, 'error');
            continue; // 跳过当前设备数据同步
          }
          syncDeviceCount += result.count;
        }
        ctx.auditLog('同步设备数据', `成功同步${syncDeviceCount}个设备数据`, 'info');
      } catch (error) {
        ctx.auditLog('万华设备数据同步', `万华设备数据同步任务异常: ${error}`, 'error');
      }
    }
  }
  return SyncDevicesDataTask;
};
