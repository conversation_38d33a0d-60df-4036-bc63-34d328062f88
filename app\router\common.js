

module.exports = app => {

  const {
    router,
    controller,
  } = app;

  // 关于token等接口写在此路由和对应api控制器中
  // router.get('/api/address/list', controller.api.systemConfig.addressList);

  // 企业、行政、运营用户头像上传
  // router.post('/api/operateUser/uploadLogo', controller.api.operateUser.uploadLogo);

  // 企业培训记录
  // router.post('/api/training/addPropagate', controller.api.propagate.add);
  // router.post('/api/training/editPropagate', controller.api.propagate.update);
  // router.post('/api/training/deletePropagate', controller.api.propagate.delete);
  // mqtt
  router.get('/common/list', controller.common.common.list); // 开启代理服务器
  // router.get('/mqtt/closeBroker', controller.api.mqtt.onSIGINT);
  // router.get('/mqtt/client', controller.api.mqtt.client);


};
