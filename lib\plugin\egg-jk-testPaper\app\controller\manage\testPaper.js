// 考卷
const testPaperController = {
  async getTopicsRandom(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.testPaper.getTopicsRandom(data);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '试题获取成功',
      status: 200,
    });
  },
  async add(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.testPaper.create(data);
    if (res) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '试卷添加成功',
        status: 200,
      });
    } else {
      ctx.helper.renderFail(ctx, {
        data: res,
        message: '试卷名称已存在',
        status: 400,
      });
    }

  },
  async edit(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.testPaper.update(data);
    if (res && (res.ok === 1)) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '试卷编辑成功',
        status: 200,
      });
    } else {
      ctx.helper.renderFail(ctx, {
        data: res,
        message: '试卷编辑失败',
        status: 400,
      });
    }
  },
  async getDetail(ctx) {
    const res = await ctx.service.testPaper.getById(ctx.query._id);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: 'success',
      status: 200,
    });
  },
  async del(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.testPaper.del(data._id);
    res && ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '删除成功',
      status: 200,
    });
  },
  // 获取试卷列表
  async getList(ctx) {
    const data = ctx.request.body;
    const res = await ctx.service.testPaper.getList(data);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: 'success',
      status: 200,
    });
  },

};

module.exports = testPaperController;
