// const _ = require('lodash');

// global.remote = function(appCtx) {

// this.tags = [ 'remote' ];

// this.parse = function(parser, nodes, lexer) {
// 不知道干什么用的，DoraCms未查到使用
// this.parse = function(parser, nodes) {
//   const tok = parser.nextToken();
//   const args = parser.parseSignature(null, true);
//   parser.advanceAfterBlockEnd(tok.value);
//   return new nodes.CallExtensionAsync(this, 'run', args);
// };

// 原DoraCms前台页面所用
// this.run = async (context, args, callback) => {
//   const key = _.isEmpty(args.key) ? false : args.key;
//   // console.log('---key--', key);
//   try {
//     const api = _.isEmpty(args.api) ? false : args.api;
//     const queryObj = _.isEmpty(args.query) ? false : JSON.parse(args.query);
//     const pageSize = _.isEmpty(args.pageSize) ? false : args.pageSize;
//     const isPaging = _.isEmpty(args.isPaging) ? '1' : args.isPaging;

//     let apiData = [];

//     if (!key || !api) {
//       throw new Error(context.ctx.__('validate_error_params'));
//     }

//     const payload = {};

//     if (pageSize) {
//       payload.pageSize = pageSize;
//     }

//     if (isPaging) {
//       payload.isPaging = isPaging;
//     }

//     if (queryObj) {
//       _.assign(payload, queryObj);
//     }

//     apiData = await appCtx.helper.reqJsonData(api, payload);
//     // console.log(payload, '--apiData--', apiData);
//     context.ctx[key] = apiData;
//     return callback(null, '');
//   } catch (error) {
//     context.ctx[key] = [];
//     return callback(null, '');
//   }

// };
// };
