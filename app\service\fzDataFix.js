'use strict';

const Service = require('egg').Service;

/**
 * @service 福州数据处理修复服务
 */
class FzDataFixService extends Service {

  /**
   * 获取日志列表
   * @param {Object} payload - 查询参数
   * @return {Promise<Object>} 日志列表和总数
   */
  async getLogs(payload = {}) {
    const { pageSize = 20, pageNum = 1, taskType, status, dateRange, keyword } = payload;

    // 构建查询条件
    const matchStage = {};
    if (taskType) matchStage.taskType = taskType;
    if (status) matchStage['processingInfo.status'] = status;
    if (dateRange) {
      const [ startDate, endDate ] = dateRange.split(',');
      matchStage.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    }

    // 添加关键词搜索条件
    if (keyword && keyword.trim()) {
      const keywordRegex = new RegExp(keyword.trim(), 'i'); // 不区分大小写的正则表达式
      matchStage.$or = [
        { taskId: keywordRegex },
        { 'dataSource.fileName': keywordRegex },
        { 'dataSource.filePath': keywordRegex },
        { 'result.message': keywordRegex },
        { 'result.errorInfo.message': keywordRegex },
      ];
    }

    // 计算分页参数
    const skip = (pageNum - 1) * pageSize;
    const limitNum = parseInt(pageSize);

    // 使用聚合查询同时获取数据和总数，提高性能
    const aggregationPipeline = [
      { $match: matchStage },
      { $sort: { createdAt: -1 } },
      {
        $facet: {
          // 获取分页数据
          data: [
            { $skip: skip },
            { $limit: limitNum },
            {
              // 在数据库层面进行字段投影，减少数据传输
              $project: {
                taskId: 1,
                taskType: 1,
                'dataSource.fileName': 1,
                'dataSource.fileType': 1,
                'processingInfo.status': 1,
                'processingInfo.retryCount': 1,
                'processingInfo.duration': 1,
                'processingInfo.startTime': 1,
                'processingInfo.endTime': 1,
                'dataStats.totalRecords': 1,
                'dataStats.processedRecords': 1,
                'dataStats.failedRecords': 1,
                'dataStats.skippedRecords': 1,
                'result.message': 1,
                'replayInfo.canReplay': 1,
                createdAt: 1,
                updatedAt: 1,
                // 在数据库层面计算成功率，避免在应用层计算
                successRate: {
                  $cond: {
                    if: { $eq: [ '$dataStats.totalRecords', 0 ] },
                    then: 0,
                    else: {
                      $multiply: [
                        { $divide: [ '$dataStats.processedRecords', '$dataStats.totalRecords' ] },
                        100,
                      ],
                    },
                  },
                },
                // 在数据库层面计算是否可重试
                isRetryable: {
                  $and: [
                    { $lt: [ '$processingInfo.retryCount', '$processingInfo.maxRetries' ] },
                    { $eq: [ '$processingInfo.status', 'failed' ] },
                    { $eq: [ '$replayInfo.canReplay', true ] },
                  ],
                },
              },
            },
          ],
          // 获取总数
          totalCount: [{ $count: 'count' }],
        },
      },
    ];

    const [ result ] = await this.ctx.model.FzDataProcessLog.aggregate(aggregationPipeline);

    const rawList = result.data || [];
    const total = result.totalCount[0]?.count || 0;

    // 简化数据转换逻辑，减少字段访问次数
    const list = rawList.map(item => {
      const dataSource = item.dataSource || {};
      const processingInfo = item.processingInfo || {};
      const dataStats = item.dataStats || {};
      const resultInfo = item.result || {};

      return {
        taskId: item.taskId,
        taskType: item.taskType,
        fileName: dataSource.fileName || '未知文件',
        fileType: dataSource.fileType || '',
        status: processingInfo.status || 'pending',
        totalRecords: dataStats.totalRecords || 0,
        processedRecords: dataStats.processedRecords || 0,
        failedRecords: dataStats.failedRecords || 0,
        skippedRecords: dataStats.skippedRecords || 0,
        successRate: parseFloat((item.successRate || 0).toFixed(2)),
        retryCount: processingInfo.retryCount || 0,
        duration: processingInfo.duration || 0,
        startTime: processingInfo.startTime,
        endTime: processingInfo.endTime,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        message: resultInfo.message || '',
        isRetryable: Boolean(item.isRetryable),
      };
    });

    return {
      list,
      total,
    };
  }

  /**
   * 获取统计信息
   * @param {Object} payload - 查询参数
   * @return {Promise<Object>} 统计数据
   */
  async getStats(payload = {}) {
    const { taskType, dateRange } = payload;

    // 构建查询条件
    const query = {};
    if (taskType) query.taskType = taskType;
    if (dateRange) {
      const [ startDate, endDate ] = dateRange.split(',');
      query.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    }

    // 聚合统计
    const summary = await this.ctx.model.FzDataProcessLog.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalTasks: { $sum: 1 },
          successTasks: {
            $sum: { $cond: [{ $eq: [ '$processingInfo.status', 'success' ] }, 1, 0 ] },
          },
          failedTasks: {
            $sum: { $cond: [{ $eq: [ '$processingInfo.status', 'failed' ] }, 1, 0 ] },
          },
          skippedTasks: {
            $sum: { $cond: [{ $eq: [ '$processingInfo.status', 'skipped' ] }, 1, 0 ] },
          },
          pendingTasks: {
            $sum: { $cond: [{ $eq: [ '$processingInfo.status', 'pending' ] }, 1, 0 ] },
          },
        },
      },
    ]);

    return {
      summary: summary[ 0 ] || {
        totalTasks: 0,
        successTasks: 0,
        failedTasks: 0,
        skippedTasks: 0,
        pendingTasks: 0,
      },
    };
  }

  /**
   * 获取任务详情
   * @param {Object} payload - 查询参数
   * @return {Promise<Object>} 任务详情
   */
  async getTaskDetails(payload = {}) {
    const { taskId } = payload;

    if (!taskId) {
      throw new Error('taskId is required');
    }

    const task = await this.ctx.model.FzDataProcessLog.findOne({ taskId });

    if (!task) {
      throw new Error('Task not found');
    }

    // 初始化返回数据结构
    const result = {
      // 基本任务信息
      taskInfo: {
        taskId: task.taskId,
        taskType: task.taskType,
        status: task.processingInfo.status,
        startTime: task.processingInfo.startTime,
        endTime: task.processingInfo.endTime,
        duration: task.processingInfo.duration,
        retryCount: task.processingInfo.retryCount,
      },
      // 数据统计
      dataStats: task.dataStats,
      // 结果信息
      result: task.result,
      // 数据源信息
      dataSource: task.dataSource,
      // 详细数据
      decryptedData: null,
      dataAnalysis: null,
      processingResult: null,
    };

    // 从metadata.additionalInfo中获取解析数据
    if (task.metadata && task.metadata.additionalInfo) {
      const additionalInfo = task.metadata.additionalInfo;

      // 解密后的原始数据（如果太大，只返回结构信息）
      if (additionalInfo.decryptedData) {
        const data = additionalInfo.decryptedData;
        if (typeof data === 'object' && data !== null) {
          // 如果数据太大，只返回结构信息
          const dataSize = JSON.stringify(data).length;
          if (dataSize > 100000) { // 超过100KB
            result.decryptedData = {
              _summary: '数据太大，仅显示结构信息',
              _dataType: typeof data,
              _isArray: Array.isArray(data),
              _keys: Array.isArray(data) ? `数组长度: ${data.length}` : Object.keys(data),
              _size: `${(dataSize / 1024).toFixed(2)}KB`,
            };
          } else {
            result.decryptedData = data;
          }
        } else {
          result.decryptedData = data;
        }
      }

      // 数据分析结果
      if (additionalInfo.dataAnalysis) {
        result.dataAnalysis = additionalInfo.dataAnalysis;
      }

      // 处理结果详情
      if (additionalInfo.performance || additionalInfo.serviceResult || additionalInfo.errorDetails) {
        result.processingResult = {
          performance: additionalInfo.performance,
          serviceResult: additionalInfo.serviceResult,
          errorDetails: additionalInfo.errorDetails,
        };
      }
    }

    // 从replayParams中获取原始数据（作为备选）
    if (!result.decryptedData && task.replayInfo && task.replayInfo.replayParams) {
      const replayParams = task.replayInfo.replayParams;
      if (replayParams.data) {
        result.decryptedData = replayParams.data;
      }
    }

    // 为了保持向后兼容性，同时返回简化的顶级字段
    return {
      ...result,
      // 向后兼容的字段
      decryptedData: result.decryptedData || {},
      dataAnalysis: result.dataAnalysis || {},
      processingResult: result.processingResult || {},
    };
  }

  /**
   * 重试失败任务
   * @param {Object} payload - 重试参数
   * @param {Array} payload.taskIds - 指定要重试的任务ID列表（可选）
   * @param {String} payload.taskType - 任务类型过滤（可选）
   * @param {Number} payload.maxRetries - 最大重试次数
   * @param {Number} payload.batchSize - 批次大小
   * @return {Promise<Object>} 重试结果
   */
  async retryFailedTasks(payload = {}) {
    const { taskIds, taskType, maxRetries = 3, batchSize = 10 } = payload;

    let query;
    let failedTasks;

    if (taskIds && taskIds.length > 0) {
      // 如果指定了任务ID，直接查找这些任务
      query = {
        taskId: { $in: taskIds },
        'processingInfo.status': { $in: [ 'failed', 'pending' ] }, // 允许重试失败和待处理的任务
      };
      failedTasks = await this.ctx.model.FzDataProcessLog.find(query);
    } else {
      // 否则按原有逻辑批量查找失败任务
      query = { 'processingInfo.status': 'failed' };
      if (taskType) query.taskType = taskType;

      failedTasks = await this.ctx.model.FzDataProcessLog
        .find(query)
        .limit(batchSize);
    }

    let retryCount = 0;
    let skippedCount = 0;
    const retryResults = [];

    for (const task of failedTasks) {
      const canRetry = task.processingInfo.retryCount < maxRetries;

      if (canRetry) {
        try {
          await this.ctx.model.FzDataProcessLog.updateOne(
            { _id: task._id },
            {
              $inc: { 'processingInfo.retryCount': 1 },
              $set: {
                'processingInfo.status': 'pending', // 重置为待处理状态
                'processingInfo.startTime': null,
                'processingInfo.endTime': null,
                'result.success': false,
                'result.message': '任务已重新排队等待处理',
                updatedAt: new Date(),
              },
            }
          );

          retryResults.push({
            taskId: task.taskId,
            status: 'queued',
            message: '已重新排队',
            retryCount: task.processingInfo.retryCount + 1,
          });

          retryCount++;
        } catch (error) {
          retryResults.push({
            taskId: task.taskId,
            status: 'error',
            message: `重试失败: ${error.message}`,
            retryCount: task.processingInfo.retryCount,
          });
        }
      } else {
        retryResults.push({
          taskId: task.taskId,
          status: 'skipped',
          message: `已达到最大重试次数 (${maxRetries})`,
          retryCount: task.processingInfo.retryCount,
        });
        skippedCount++;
      }
    }

    const totalTasks = failedTasks.length;
    const successMessage = taskIds && taskIds.length > 0
      ? `指定任务重试完成: 成功 ${retryCount} 个，跳过 ${skippedCount} 个，共 ${totalTasks} 个任务`
      : `批量重试完成: 成功 ${retryCount} 个，跳过 ${skippedCount} 个，共 ${totalTasks} 个任务`;

    this.ctx.logger.info('任务重试完成', {
      totalTasks,
      retryCount,
      skippedCount,
      taskIds: taskIds || null,
      taskType: taskType || null,
    });

    return {
      success: true,
      message: successMessage,
      data: {
        totalTasks,
        retryCount,
        skippedCount,
        retryResults,
        isSpecificTasks: !!(taskIds && taskIds.length > 0),
      },
    };
  }

  /**
   * 测试数据清洗
   * @param {String} testType - 测试类型（company/healthCheck/diagnosis）
   * @param {Object} payload - 测试参数
   * @return {Promise<Object>} 测试结果
   */
  async testDataCleaning(testType, payload = {}) {
    const { filePath, index } = payload;

    try {
      // 调用现有的fzData服务进行数据清洗测试
      const result = await this.service.fzData.testDataCleaning(testType, { filePath, index });

      return {
        success: true,
        message: `${testType} 数据清洗测试完成`,
        data: result,
      };
    } catch (error) {
      throw new Error(`数据清洗测试失败: ${error.message}`);
    }
  }

  /**
   * 获取文件摘要
   * @return {Promise<Object>} 文件摘要信息
   */
  async getFiles() {
    try {
      const result = await this.service.fzData.getFileDetails();

      return {
        totalFiles: result.summary?.totalFiles || 0,
        companyFiles: result.summary?.companyFiles || 0,
        healthCheckFiles: result.summary?.healthCheckFiles || 0,
        diagnosisFiles: result.summary?.diagnosisFiles || 0,
      };
    } catch (error) {
      throw new Error(`获取文件摘要失败: ${error.message}`);
    }
  }

  /**
   * 获取文件详细信息
   * @return {Promise<Object>} 分类文件详细信息
   */
  async getFileDetails() {
    try {
      const result = await this.service.fzData.getFileDetails();
      return result;
    } catch (error) {
      throw new Error(`获取文件详细信息失败: ${error.message}`);
    }
  }

  /**
   * 清理缓存
   * @return {Promise<Object>} 清理结果
   */
  async clearCache() {
    try {
      // 清理应用层缓存
      if (this.app.cache && typeof this.app.cache.clear === 'function') {
        this.app.cache.clear();
      }

      return {
        success: true,
        message: '缓存清理成功',
      };
    } catch (error) {
      throw new Error(`清理缓存失败: ${error.message}`);
    }
  }

  /**
   * 从文件创建新任务
   * @param {Object} options - 创建选项
   * @param {String} options.taskType - 任务类型 ('all', 'company', 'healthCheck', 'diagnosis')
   * @param {Boolean} options.clearExisting - 是否清理现有任务
   * @param {Boolean} options.skipExisting - 是否跳过已存在的任务
   * @param {Number} options.maxFiles - 最大文件数限制
   * @param {Boolean} options.recentOnly - 是否只处理最近几天的文件
   * @param {Number} options.recentDays - 最近几天，默认2天
   * @return {Promise<Object>} 创建结果
   */
  async createTasksFromFiles(options = {}) {
    const {
      taskType = 'all',
      clearExisting = false,
      skipExisting = true,
      maxFiles = 0,
      recentOnly = false,
      recentDays = 2,
    } = options;

    try {
      this.ctx.logger.info('开始从文件创建新任务', { options });

      // 如果需要清理现有任务
      if (clearExisting) {
        await this._clearExistingTasks(taskType);
      }

      // 获取需要创建任务的文件列表
      const filesByType = await this._getFilesForTaskCreation(taskType, maxFiles, recentOnly, recentDays);

      if (Object.keys(filesByType).length === 0) {
        return {
          success: true,
          message: '没有找到需要创建任务的文件',
          totalFiles: 0,
          createdTasks: 0,
          skippedTasks: 0,
          tasksByType: {},
        };
      }

      // 创建任务
      const results = await this._createTasksFromFileList(filesByType, skipExisting);

      const summary = this._summarizeTaskCreation(results);

      this.ctx.logger.info('任务创建完成', summary);

      return {
        success: true,
        message: `任务创建完成，共创建 ${summary.totalCreated} 个任务`,
        ...summary,
        details: results,
      };

    } catch (error) {
      this.ctx.logger.error('从文件创建任务失败', { error: error.message, options });
      throw new Error(`创建任务失败: ${error.message}`);
    }
  }

  /**
   * 批量创建指定文件的任务
   * @param {Object} options - 创建选项
   * @param {Array} options.filePaths - 文件路径列表
   * @param {String} options.taskType - 任务类型
   * @param {Boolean} options.skipExisting - 是否跳过已存在的任务
   * @return {Promise<Object>} 创建结果
   */
  async createTasksFromSpecificFiles(options = {}) {
    const {
      filePaths = [],
      taskType,
      skipExisting = true,
    } = options;

    if (!filePaths.length) {
      throw new Error('文件路径列表不能为空');
    }

    if (!taskType || ![ 'company', 'healthCheck', 'diagnosis' ].includes(taskType)) {
      throw new Error('必须指定有效的任务类型');
    }

    try {
      this.ctx.logger.info('开始为指定文件批量创建任务', {
        fileCount: filePaths.length,
        taskType,
        skipExisting,
        batchMode: true,
      });

      const BATCH_SIZE = 1000;
      const allResults = [];
      let totalCreated = 0;
      let totalSkipped = 0;
      let totalErrors = 0;

      // 按批次处理文件
      for (let i = 0; i < filePaths.length; i += BATCH_SIZE) {
        const batch = filePaths.slice(i, i + BATCH_SIZE);
        const batchNumber = Math.floor(i / BATCH_SIZE) + 1;
        const totalBatches = Math.ceil(filePaths.length / BATCH_SIZE);

        this.ctx.logger.info(`处理第 ${batchNumber}/${totalBatches} 批，包含 ${batch.length} 个文件`);

        try {
          const batchResults = await this._createTasksBatch(batch, taskType, skipExisting);
          allResults.push(...batchResults);

          // 统计本批次结果
          const batchCreated = batchResults.filter(r => r.created).length;
          const batchSkipped = batchResults.filter(r => r.skipped).length;
          const batchErrors = batchResults.filter(r => r.error).length;

          totalCreated += batchCreated;
          totalSkipped += batchSkipped;
          totalErrors += batchErrors;

          this.ctx.logger.info(`第 ${batchNumber} 批处理完成: 创建 ${batchCreated}, 跳过 ${batchSkipped}, 失败 ${batchErrors}`);

        } catch (error) {
          this.ctx.logger.error(`第 ${batchNumber} 批处理失败，回退到逐个处理:`, error);

          // 回退到逐个处理
          for (const filePath of batch) {
            try {
              const result = await this._createSingleTaskFromFile(filePath, taskType, skipExisting);
              allResults.push(result);

              if (result.created) {
                totalCreated++;
              } else if (result.skipped) {
                totalSkipped++;
              }
            } catch (singleError) {
              totalErrors++;
              allResults.push({
                filePath,
                created: false,
                skipped: false,
                error: singleError.message,
              });
              this.ctx.logger.error(`单个文件创建任务失败: ${filePath}`, singleError);
            }
          }
        }
      }

      return {
        success: totalErrors === 0,
        message: `批量任务创建完成，成功 ${totalCreated} 个，跳过 ${totalSkipped} 个，失败 ${totalErrors} 个`,
        totalFiles: filePaths.length,
        createdTasks: totalCreated,
        skippedTasks: totalSkipped,
        errorTasks: totalErrors,
        details: allResults,
        batchInfo: {
          batchSize: BATCH_SIZE,
          totalBatches: Math.ceil(filePaths.length / BATCH_SIZE),
        },
      };

    } catch (error) {
      this.ctx.logger.error('批量创建指定文件任务失败', error);
      throw new Error(`创建任务失败: ${error.message}`);
    }
  }

  /**
   * 串行处理数据 - 处理现有待处理任务，通过Redis管理状态
   * @param {String} dataType - 数据类型（company/healthCheck/diagnosis）
   * @param {Object} payload - 处理参数
   * @return {Promise<Object>} 任务信息
   */
  async serialProcessData(dataType, payload = {}) {
    const { filePath, index, maxTasks, continueFromTask } = payload;

    try {
      // 构建查询条件：查找待处理或失败的任务
      const query = {
        'processingInfo.status': { $in: [ 'pending', 'failed' ] },
      };

      // 如果指定了数据类型，添加类型过滤
      if (dataType && dataType !== 'all') {
        query.taskType = dataType;
      }

      // 如果指定了文件路径，添加文件过滤
      if (filePath) {
        // 支持按文件名或文件路径过滤
        query.$or = [
          { 'dataSource.filePath': filePath },
          { 'dataSource.fileName': filePath },
        ];
      }

      // 构建数据库查询
      let dbQuery = this.ctx.model.FzDataProcessLog.find(query).sort({ createdAt: 1 });

      // 如果指定了索引，从该索引开始处理
      if (typeof index === 'number' && index >= 0) {
        dbQuery = dbQuery.skip(index);
      }

      // 如果指定了最大任务数量，则限制处理数量
      if (typeof maxTasks === 'number' && maxTasks > 0) {
        dbQuery = dbQuery.limit(maxTasks);
      }
      // 否则处理所有符合条件的任务（不设置limit）

      const pendingTasks = await dbQuery.exec();

      if (pendingTasks.length === 0) {
        return {
          success: true,
          message: `没有找到需要处理的 ${dataType || '全部'} 数据任务`,
          taskId: null,
          status: 'completed',
          processedCount: 0,
        };
      }

      // 创建Redis任务来管理整个串行处理过程
      // 如果是继续处理，使用原有的任务ID；否则创建新的任务ID
      const batchTaskId = continueFromTask || `batch_${dataType || 'all'}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 直接在Redis中创建批处理任务状态（不使用taskQueue.createTask避免ID冲突）
      const taskData = {
        taskId: batchTaskId,
        taskType: dataType || 'all',
        status: 'pending',
        progress: 0,
        message: `${dataType || '全部'} 数据串行处理已启动`,
        dataType: dataType || 'all',
        taskIds: pendingTasks.map(task => task.taskId),
        totalTasks: pendingTasks.length,
        processedTasks: 0,
        processType: 'serial',
        filePath,
        index,
        maxTasks,
        isContinued: !!continueFromTask,
        originalTaskId: continueFromTask || null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // 直接存储到Redis
      try {
        await this.app.redis.hset(`task:${batchTaskId}`, taskData);
        await this.app.redis.expire(`task:${batchTaskId}`, 24 * 60 * 60); // 24小时过期

        // 将任务加入队列（修复队列统计显示问题）
        await this.app.redis.lpush(`queue:${dataType || 'all'}`, batchTaskId);

        this.ctx.logger.info(`批处理任务已创建到Redis: ${batchTaskId}`, taskData);
        this.ctx.logger.info(`任务已加入队列: queue:${dataType || 'all'}`);
      } catch (redisError) {
        this.ctx.logger.error('创建Redis任务失败', redisError);
        // 如果Redis失败，继续处理但记录错误
      }

      // 异步启动串行处理 - 保存上下文引用
      const self = this;
      const logger = this.ctx.logger;

      setImmediate(async () => {
        try {
          logger.info(`setImmediate 开始执行串行处理: ${batchTaskId}`);
          await self._processTasksSerially(batchTaskId, pendingTasks);
          logger.info(`setImmediate 串行处理完成: ${batchTaskId}`);
        } catch (error) {
          logger.error(`setImmediate 串行处理异常: ${batchTaskId}`, error);

          // 更新批处理任务状态为失败
          try {
            await self._updateBatchTaskStatus(batchTaskId, {
              status: 'failed',
              message: `串行处理启动异常: ${error.message}`,
              endTime: new Date().toISOString(),
              error: error.message,
            });
          } catch (updateError) {
            logger.error(`更新失败状态异常: ${batchTaskId}`, updateError);
          }
        }
      });

      this.ctx.logger.info(`串行处理任务已启动: ${batchTaskId}`, {
        dataType,
        taskCount: pendingTasks.length,
        filePath,
        index,
      });

      return {
        success: true,
        message: continueFromTask
          ? `${dataType || '全部'} 数据串行处理已继续，共 ${pendingTasks.length} 个任务`
          : `${dataType || '全部'} 数据串行处理已启动，共 ${pendingTasks.length} 个任务`,
        taskId: batchTaskId,
        status: 'pending',
        totalTasks: pendingTasks.length,
        isContinued: !!continueFromTask,
      };

    } catch (error) {
      this.ctx.logger.error('启动串行处理失败', {
        error: error.message,
        dataType,
        filePath,
        index,
      });

      throw new Error(`启动串行处理失败: ${error.message}`);
    }
  }

  // /**
  //  * 获取任务详情 - 兼容Redis和数据库两种存储方式
  //  * @param {Object} payload - 查询参数
  //  * @return {Promise<Object>} 任务详情
  //  */
  // async getTaskDetails(payload) {
  //   const { taskId } = payload;

  //   if (!taskId) {
  //     throw new Error('缺少taskId参数');
  //   }

  //   try {
  //     // 首先尝试从Redis获取任务信息
  //     const redisTask = await this.service.taskQueue.getTask(taskId);

  //     if (redisTask) {
  //       // 如果Redis中有任务信息，返回Redis数据
  //       return {
  //         taskId: redisTask.taskId,
  //         taskType: redisTask.taskType,
  //         status: redisTask.status,
  //         progress: parseInt(redisTask.progress) || 0,
  //         message: redisTask.message,
  //         processed: parseInt(redisTask.processed) || 0,
  //         duration: parseInt(redisTask.duration) || 0,
  //         createdAt: redisTask.createdAt,
  //         updatedAt: redisTask.updatedAt,
  //         result: redisTask.result ? JSON.parse(redisTask.result) : null,
  //         error: redisTask.error || null,
  //         payload: redisTask.payload || {},
  //       };
  //     }

  //     // 如果Redis中没有，尝试从数据库获取（兼容旧数据）
  //     const dbTask = await this.ctx.model.FzDataProcessLog.findOne({ taskId });

  //     if (dbTask) {
  //       return {
  //         taskId: dbTask.taskId,
  //         taskType: dbTask.taskType,
  //         status: dbTask.processingInfo?.status || 'unknown',
  //         progress: 100, // 数据库中的任务默认认为已完成
  //         message: dbTask.result?.message || '任务已完成',
  //         processed: dbTask.dataStats?.processedRecords || 0,
  //         duration: dbTask.processingInfo?.duration || 0,
  //         createdAt: dbTask.createdAt,
  //         updatedAt: dbTask.updatedAt,
  //         result: dbTask.result || null,
  //         error: dbTask.result?.error || null,
  //         payload: dbTask.metadata?.additionalInfo || {},
  //       };
  //     }

  //     throw new Error('任务不存在');

  //   } catch (error) {
  //     this.ctx.logger.error(`获取任务详情失败: ${taskId}`, error);
  //     throw new Error(`获取任务详情失败: ${error.message}`);
  //   }
  // }

  /**
   * 处理单个任务
   * @param {String} taskId - 任务ID
   * @return {Promise<Object>} 处理结果
   */
  async processSingleTask(taskId) {
    try {
      // 查找任务
      const task = await this.ctx.model.FzDataProcessLog.findOne({ taskId });
      if (!task) {
        throw new Error(`任务不存在: ${taskId}`);
      }

      // 检查任务状态
      if (task.processingInfo.status !== 'pending' && task.processingInfo.status !== 'failed') {
        throw new Error(`任务状态不允许处理: ${task.processingInfo.status}`);
      }

      this.ctx.logger.info(`开始处理单个任务: ${taskId}`, {
        taskType: task.taskType,
        fileName: task.dataSource.fileName,
        filePath: task.dataSource.filePath,
      });

      // 更新任务状态为处理中
      await this.ctx.model.FzDataProcessLog.updateOne(
        { _id: task._id },
        {
          $set: {
            'processingInfo.status': 'processing',
            'processingInfo.startTime': new Date(),
            updatedAt: new Date(),
          },
        }
      );

      // 根据任务类型调用相应的处理方法
      let result;
      const filePath = task.dataSource.filePath;

      if (!filePath) {
        throw new Error('任务缺少文件路径信息');
      }

      switch (task.taskType) {
        case 'company':
          result = await this.service.fzData.processCompanyData({ filePath });
          break;
        case 'healthCheck':
          result = await this.service.fzData.processHealthCheckData({ filePath });
          break;
        case 'diagnosis':
          result = await this.service.fzData.processDiagnosisData({ filePath });
          break;
        default:
          throw new Error(`不支持的任务类型: ${task.taskType}`);
      }

      // 计算处理耗时
      const startTime = task.processingInfo.startTime || new Date();
      const duration = Date.now() - new Date(startTime).getTime();
      const validDuration = isNaN(duration) ? 0 : Math.max(0, duration);

      // 检查是否是跳过处理的情况
      if (result.skipped || result.skipReason) {
        // 更新任务状态为跳过
        await this.ctx.model.FzDataProcessLog.updateOne(
          { _id: task._id },
          {
            $set: {
              'processingInfo.status': 'skipped',
              'processingInfo.endTime': new Date(),
              'processingInfo.duration': validDuration,
              'result.success': false,
              'result.skipped': true,
              'result.skipReason': result.skipReason || 'unknown',
              'result.message': result.message || '已跳过处理',
              'dataStats.totalRecords': result.totalRecords || result.processed || 0,
              'dataStats.processedRecords': result.processedRecords || result.processed || 0,
              'dataStats.failedRecords': result.failedRecords || 0,
              'dataStats.skippedRecords': result.skippedRecords || 1,
              updatedAt: new Date(),
            },
          }
        );

        this.ctx.logger.info(`单个任务已跳过: ${taskId}`, {
          skipReason: result.skipReason,
          message: result.message,
        });

        return {
          success: true,
          skipped: true,
          taskId,
          message: result.message || '任务已跳过处理',
          skipReason: result.skipReason,
          processed: 0,
          duration: validDuration,
          details: result,
        };
      }
      // 更新任务状态为成功
      await this.ctx.model.FzDataProcessLog.updateOne(
        { _id: task._id },
        {
          $set: {
            'processingInfo.status': 'success',
            'processingInfo.endTime': new Date(),
            'processingInfo.duration': validDuration,
            'result.success': true,
            'result.message': result.message || '处理成功',
            'dataStats.totalRecords': result.totalRecords || result.processed || 0,
            'dataStats.processedRecords': result.processedRecords || result.processed || 0,
            'dataStats.failedRecords': result.failedRecords || 0,
            'dataStats.skippedRecords': result.skippedRecords || 0,
            updatedAt: new Date(),
          },
        }
      );

      this.ctx.logger.info(`单个任务处理成功: ${taskId}`, result);

      return {
        success: true,
        taskId,
        message: result.message || '任务处理成功',
        processed: result.processed || result.totalRecords || 0,
        duration: validDuration,
        details: result,
      };


    } catch (error) {
      this.ctx.logger.error(`单个任务处理失败: ${taskId}`, error);

      // 更新任务状态为失败
      try {
        const task = await this.ctx.model.FzDataProcessLog.findOne({ taskId });
        if (task) {
          const startTime = task.processingInfo.startTime || new Date();
          const duration = Date.now() - new Date(startTime).getTime();
          const validDuration = isNaN(duration) ? 0 : Math.max(0, duration);

          await this.ctx.model.FzDataProcessLog.updateOne(
            { _id: task._id },
            {
              $set: {
                'processingInfo.status': 'failed',
                'processingInfo.endTime': new Date(),
                'processingInfo.duration': validDuration,
                'result.success': false,
                'result.message': error.message,
                'result.errorInfo.message': error.message,
                'result.errorInfo.stack': error.stack,
                updatedAt: new Date(),
              },
              $inc: {
                'processingInfo.retryCount': 1,
              },
            }
          );
        }
      } catch (updateError) {
        this.ctx.logger.error(`更新任务失败状态失败: ${taskId}`, updateError);
      }

      throw new Error(`处理任务失败: ${error.message}`);
    }
  }

  /**
   * 批量处理企业数据
   * @param {Object} options - 处理选项
   * @return {Promise<Object>} 处理结果
   */
  async processAllCompanyData(options = {}) {
    return this._processAllDataByType('company', options);
  }

  /**
   * 批量处理体检数据
   * @param {Object} options - 处理选项
   * @return {Promise<Object>} 处理结果
   */
  async processAllHealthCheckData(options = {}) {
    return this._processAllDataByType('healthCheck', options);
  }

  /**
   * 批量处理诊断数据
   * @param {Object} options - 处理选项
   * @return {Promise<Object>} 处理结果
   */
  async processAllDiagnosisData(options = {}) {
    return this._processAllDataByType('diagnosis', options);
  }

  /**
   * 更新批处理任务状态
   * @param {String} batchTaskId - 批处理任务ID
   * @param {Object} updates - 更新数据
   */
  async _updateBatchTaskStatus(batchTaskId, updates) {
    try {
      const updateData = {
        ...updates,
        updatedAt: new Date().toISOString(),
      };

      await this.app.redis.hset(`task:${batchTaskId}`, updateData);
      this.ctx.logger.info(`批处理任务状态已更新: ${batchTaskId}`, updateData);
    } catch (error) {
      this.ctx.logger.error(`更新批处理任务状态失败: ${batchTaskId}`, error);
    }
  }

  /**
   * 获取Redis任务列表
   * @param {Object} options - 查询选项
   * @return {Promise<Object>} 任务列表和分页信息
   */
  async getRedisTaskList(options = {}) {
    const { page = 1, pageSize = 20, taskType, status, pattern } = options;

    try {
      this.ctx.logger.info('开始获取Redis任务列表', { options });

      // 获取所有任务键
      let searchPattern = 'task:*';
      if (pattern) {
        searchPattern = `task:*${pattern}*`;
      }

      this.ctx.logger.info(`使用搜索模式: ${searchPattern}`);
      const taskKeys = await this.app.redis.keys(searchPattern);
      this.ctx.logger.info(`找到 ${taskKeys.length} 个Redis任务键`, { taskKeys: taskKeys.slice(0, 5) });

      if (taskKeys.length === 0) {
        return {
          tasks: [],
          pagination: {
            page,
            pageSize,
            total: 0,
            totalPages: 0,
          },
          summary: {
            total: 0,
            byType: {},
            byStatus: {},
          },
        };
      }

      // 批量获取任务数据
      const pipeline = this.app.redis.pipeline();
      taskKeys.forEach(key => {
        pipeline.hgetall(key);
      });

      const results = await pipeline.exec();
      const allTasks = [];

      for (let i = 0; i < results.length; i++) {
        const [ err, taskData ] = results[i];
        if (!err && taskData && Object.keys(taskData).length > 0) {
          // 解析任务数据
          const task = {
            ...taskData,
            taskKey: taskKeys[i],
            createdAt: taskData.createdAt ? new Date(taskData.createdAt) : null,
            updatedAt: taskData.updatedAt ? new Date(taskData.updatedAt) : null,
          };

          // 尝试解析JSON字段
          if (taskData.taskIds) {
            try {
              task.taskIds = JSON.parse(taskData.taskIds);
            } catch (e) {
              // 忽略解析错误
            }
          }

          allTasks.push(task);
        }
      }

      // 过滤任务
      let filteredTasks = allTasks;

      if (taskType) {
        filteredTasks = filteredTasks.filter(task =>
          task.taskType === taskType || task.dataType === taskType
        );
      }

      if (status) {
        filteredTasks = filteredTasks.filter(task => task.status === status);
      }

      // 按更新时间排序（最新的在前）
      filteredTasks.sort((a, b) => {
        const timeA = a.updatedAt || a.createdAt || new Date(0);
        const timeB = b.updatedAt || b.createdAt || new Date(0);
        return timeB - timeA;
      });

      // 分页
      const total = filteredTasks.length;
      const totalPages = Math.ceil(total / pageSize);
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedTasks = filteredTasks.slice(startIndex, endIndex);

      // 统计信息
      const summary = this._generateTaskSummary(allTasks);

      return {
        tasks: paginatedTasks,
        pagination: {
          page,
          pageSize,
          total,
          totalPages,
        },
        summary,
      };

    } catch (error) {
      this.ctx.logger.error('获取Redis任务列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取Redis队列统计信息
   * @return {Promise<Object>} 队列统计信息
   */
  async getRedisQueueStats() {
    try {
      const stats = {
        queues: {},
        tasks: {},
        system: {},
      };

      // 获取队列长度
      const queueTypes = [ 'company', 'healthCheck', 'diagnosis', 'batch_process' ];
      for (const queueType of queueTypes) {
        const queueKey = `queue:${queueType}`;
        const length = await this.app.redis.llen(queueKey);
        stats.queues[queueType] = {
          length,
          queueKey,
        };
      }

      // 获取任务统计
      const taskKeys = await this.app.redis.keys('task:*');
      stats.tasks.total = taskKeys.length;

      if (taskKeys.length > 0) {
        // 批量获取任务状态
        const pipeline = this.app.redis.pipeline();
        taskKeys.forEach(key => {
          pipeline.hget(key, 'status');
          pipeline.hget(key, 'taskType');
        });

        const results = await pipeline.exec();
        const statusCount = {};
        const typeCount = {};

        for (let i = 0; i < results.length; i += 2) {
          const [ statusErr, status ] = results[i];
          const [ typeErr, taskType ] = results[i + 1];

          if (!statusErr && status) {
            statusCount[status] = (statusCount[status] || 0) + 1;
          }

          if (!typeErr && taskType) {
            typeCount[taskType] = (typeCount[taskType] || 0) + 1;
          }
        }

        stats.tasks.byStatus = statusCount;
        stats.tasks.byType = typeCount;
      }

      // 获取Redis系统信息
      const info = await this.app.redis.info('memory');
      const memoryInfo = this._parseRedisInfo(info);
      stats.system = {
        memory: memoryInfo,
        timestamp: new Date().toISOString(),
      };

      return stats;

    } catch (error) {
      this.ctx.logger.error('获取Redis队列统计失败:', error);
      throw error;
    }
  }

  /**
   * 清理Redis任务
   * @param {Object} options - 清理选项
   * @return {Promise<Object>} 清理结果
   */
  async cleanupRedisTasks(options = {}) {
    const { taskType, status, olderThan, dryRun = true } = options;

    try {
      // 获取所有任务键
      const taskKeys = await this.app.redis.keys('task:*');
      this.ctx.logger.info(`开始清理Redis任务，找到 ${taskKeys.length} 个任务`);

      if (taskKeys.length === 0) {
        return {
          cleanedCount: 0,
          totalCount: 0,
          cleanedTasks: [],
        };
      }

      // 批量获取任务数据进行过滤
      const pipeline = this.app.redis.pipeline();
      taskKeys.forEach(key => {
        pipeline.hgetall(key);
      });

      const results = await pipeline.exec();
      const tasksToClean = [];

      for (let i = 0; i < results.length; i++) {
        const [ err, taskData ] = results[i];
        if (!err && taskData && Object.keys(taskData).length > 0) {
          const taskKey = taskKeys[i];
          let shouldClean = false;

          // 按任务类型过滤
          if (taskType && taskData.taskType !== taskType && taskData.dataType !== taskType) {
            continue;
          }

          // 按状态过滤
          if (status && taskData.status !== status) {
            continue;
          }

          // 按时间过滤
          if (olderThan) {
            const taskTime = new Date(taskData.updatedAt || taskData.createdAt);
            const cutoffTime = new Date();

            if (olderThan.endsWith('h')) {
              cutoffTime.setHours(cutoffTime.getHours() - parseInt(olderThan));
            } else if (olderThan.endsWith('d')) {
              cutoffTime.setDate(cutoffTime.getDate() - parseInt(olderThan));
            } else if (olderThan.endsWith('m')) {
              cutoffTime.setMinutes(cutoffTime.getMinutes() - parseInt(olderThan));
            }

            if (taskTime > cutoffTime) {
              continue;
            }
          }

          // 默认清理已完成或失败的任务
          if (!status && !olderThan) {
            shouldClean = [ 'success', 'failed', 'cancelled' ].includes(taskData.status);
          } else {
            shouldClean = true;
          }

          if (shouldClean) {
            tasksToClean.push({
              key: taskKey,
              taskId: taskData.taskId,
              taskType: taskData.taskType || taskData.dataType,
              status: taskData.status,
              createdAt: taskData.createdAt,
              updatedAt: taskData.updatedAt,
            });
          }
        }
      }

      let cleanedCount = 0;

      // 执行清理
      if (!dryRun && tasksToClean.length > 0) {
        const deletePipeline = this.app.redis.pipeline();
        tasksToClean.forEach(task => {
          deletePipeline.del(task.key);
        });

        const deleteResults = await deletePipeline.exec();
        cleanedCount = deleteResults.filter(([ err ]) => !err).length;

        this.ctx.logger.info(`Redis任务清理完成，删除了 ${cleanedCount} 个任务`);
      } else {
        cleanedCount = tasksToClean.length;
        this.ctx.logger.info(`Redis任务清理预览，将删除 ${cleanedCount} 个任务`);
      }

      return {
        cleanedCount,
        totalCount: taskKeys.length,
        cleanedTasks: tasksToClean.slice(0, 10), // 只返回前10个作为示例
        dryRun,
      };

    } catch (error) {
      this.ctx.logger.error('清理Redis任务失败:', error);
      throw error;
    }
  }

  /**
   * 生成任务摘要统计
   * @param {Array} tasks - 任务列表
   * @return {Object} 统计摘要
   */
  _generateTaskSummary(tasks) {
    const summary = {
      total: tasks.length,
      byType: {},
      byStatus: {},
    };

    tasks.forEach(task => {
      const taskType = task.taskType || task.dataType || 'unknown';
      const status = task.status || 'unknown';

      summary.byType[taskType] = (summary.byType[taskType] || 0) + 1;
      summary.byStatus[status] = (summary.byStatus[status] || 0) + 1;
    });

    return summary;
  }

  /**
   * 解析Redis INFO命令返回的信息
   * @param {String} info - Redis INFO命令返回的字符串
   * @return {Object} 解析后的信息对象
   */
  _parseRedisInfo(info) {
    const result = {};
    const lines = info.split('\r\n');

    lines.forEach(line => {
      if (line && !line.startsWith('#') && line.includes(':')) {
        const [ key, value ] = line.split(':');
        result[key] = value;
      }
    });

    return result;
  }

  /**
   * 串行处理任务列表
   * @param {String} batchTaskId - 批处理任务ID
   * @param {Array} tasks - 待处理任务列表
   * @return {Promise<void>}
   */
  async _processTasksSerially(batchTaskId, tasks) {
    this.ctx.logger.info(`_processTasksSerially 方法开始执行: ${batchTaskId}`, {
      tasksLength: tasks.length,
      hasCtx: !!this.ctx,
      hasApp: !!this.app,
      hasModel: !!this.ctx?.model,
    });

    let processedCount = 0;
    let successCount = 0;
    let failedCount = 0;
    let skippedCount = 0;

    try {
      this.ctx.logger.info(`准备更新批处理任务状态为处理中: ${batchTaskId}`);

      // 更新批处理任务状态为处理中
      await this._updateBatchTaskStatus(batchTaskId, {
        status: 'processing',
        progress: 0,
        message: `开始串行处理 ${tasks.length} 个任务`,
        startTime: new Date().toISOString(),
      });

      this.ctx.logger.info(`批处理任务状态已更新为处理中: ${batchTaskId}`);

      this.ctx.logger.info(`开始任务循环处理，共 ${tasks.length} 个任务`);

      for (let i = 0; i < tasks.length; i++) {
        const task = tasks[i];

        this.ctx.logger.info(`任务循环 ${i + 1}/${tasks.length}，任务详情:`, {
          taskId: task.taskId,
          taskType: task.taskType,
          hasDataSource: !!task.dataSource,
          filePath: task.dataSource?.filePath,
          status: task.processingInfo?.status,
        });

        try {
          this.ctx.logger.info(`开始处理任务 ${i + 1}/${tasks.length}: ${task.taskId}`);

          // 更新当前任务状态为处理中
          await this.ctx.model.FzDataProcessLog.updateOne(
            { _id: task._id },
            {
              $set: {
                'processingInfo.status': 'processing',
                'processingInfo.startTime': new Date(),
                updatedAt: new Date(),
              },
            }
          );

          // 根据任务类型调用相应的处理方法
          let result;
          const filePath = task.dataSource?.filePath;

          if (!filePath) {
            throw new Error('任务缺少文件路径信息');
          }

          switch (task.taskType) {
            case 'company':
              result = await this.service.fzData.processCompanyData({ filePath });
              break;
            case 'healthCheck':
              result = await this.service.fzData.processHealthCheckData({ filePath });
              break;
            case 'diagnosis':
              result = await this.service.fzData.processDiagnosisData({ filePath });
              break;
            default:
              throw new Error(`不支持的任务类型: ${task.taskType}`);
          }

          // 计算处理耗时，确保是有效数字
          const startTime = task.processingInfo?.startTime || new Date();
          const duration = Date.now() - new Date(startTime).getTime();
          const validDuration = isNaN(duration) ? 0 : Math.max(0, duration);

          // 检查是否是跳过处理的情况
          if (result.skipped || result.skipReason) {
            // 更新任务状态为跳过
            await this.ctx.model.FzDataProcessLog.updateOne(
              { _id: task._id },
              {
                $set: {
                  'processingInfo.status': 'skipped',
                  'processingInfo.endTime': new Date(),
                  'processingInfo.duration': validDuration,
                  'result.success': false,
                  'result.skipped': true,
                  'result.skipReason': result.skipReason || 'unknown',
                  'result.message': result.message || '已跳过处理',
                  'dataStats.totalRecords': result.totalRecords || 0,
                  'dataStats.processedRecords': result.processedRecords || 0,
                  'dataStats.failedRecords': result.failedRecords || 0,
                  'dataStats.skippedRecords': result.skippedRecords || 1,
                  updatedAt: new Date(),
                },
              }
            );

            skippedCount++;
            this.ctx.logger.info(`任务已跳过: ${task.taskId}`, {
              skipReason: result.skipReason,
              message: result.message,
            });
          } else {
            // 更新任务状态为成功
            await this.ctx.model.FzDataProcessLog.updateOne(
              { _id: task._id },
              {
                $set: {
                  'processingInfo.status': 'success',
                  'processingInfo.endTime': new Date(),
                  'processingInfo.duration': validDuration,
                  'result.success': true,
                  'result.message': result.message || '处理成功',
                  'dataStats.totalRecords': result.totalRecords || 0,
                  'dataStats.processedRecords': result.processedRecords || 0,
                  'dataStats.failedRecords': result.failedRecords || 0,
                  'dataStats.skippedRecords': result.skippedRecords || 0,
                  updatedAt: new Date(),
                },
              }
            );

            successCount++;
            this.ctx.logger.info(`任务处理成功: ${task.taskId}`, result);
          }

        } catch (error) {
          // 计算处理耗时，确保是有效数字
          const startTime = task.processingInfo?.startTime || new Date();
          const duration = Date.now() - new Date(startTime).getTime();
          const validDuration = isNaN(duration) ? 0 : Math.max(0, duration);

          // 更新任务状态为失败
          await this.ctx.model.FzDataProcessLog.updateOne(
            { _id: task._id },
            {
              $set: {
                'processingInfo.status': 'failed',
                'processingInfo.endTime': new Date(),
                'processingInfo.duration': validDuration,
                'result.success': false,
                'result.message': error.message,
                'result.errorInfo.message': error.message,
                'result.errorInfo.stack': error.stack,
                updatedAt: new Date(),
              },
              $inc: {
                'processingInfo.retryCount': 1,
              },
            }
          );

          failedCount++;
          this.ctx.logger.error(`任务处理失败: ${task.taskId}`, error);
        }

        processedCount++;

        // 更新批处理进度
        const progress = Math.round((processedCount / tasks.length) * 100);
        await this._updateBatchTaskStatus(batchTaskId, {
          progress,
          message: `已处理 ${processedCount}/${tasks.length} 个任务，成功 ${successCount} 个，跳过 ${skippedCount} 个，失败 ${failedCount} 个`,
          processedTasks: processedCount,
        });
      }

      // 更新批处理任务状态为完成
      await this._updateBatchTaskStatus(batchTaskId, {
        status: 'success',
        progress: 100,
        message: `串行处理完成，共处理 ${processedCount} 个任务，成功 ${successCount} 个，跳过 ${skippedCount} 个，失败 ${failedCount} 个`,
        endTime: new Date().toISOString(),
        processedTasks: processedCount,
        successTasks: successCount,
        skippedTasks: skippedCount,
        failedTasks: failedCount,
      });

      this.ctx.logger.info(`批处理任务完成: ${batchTaskId}`, {
        total: tasks.length,
        processed: processedCount,
        success: successCount,
        skipped: skippedCount,
        failed: failedCount,
      });

    } catch (error) {
      // 更新批处理任务状态为失败
      await this._updateBatchTaskStatus(batchTaskId, {
        status: 'failed',
        message: `串行处理异常: ${error.message}`,
        endTime: new Date().toISOString(),
        processedTasks: processedCount,
        successTasks: successCount,
        skippedTasks: skippedCount,
        failedTasks: failedCount,
      });

      this.ctx.logger.error(`批处理任务异常: ${batchTaskId}`, error);
    }
  }

  /**
   * 根据数据类型获取文件类型
   * @param {String} dataType - 数据类型
   * @return {String} 文件类型
   */
  _getFileTypeByDataType(dataType) {
    const typeMap = {
      company: 'FromcrptDownLoad',
      healthCheck: 'FrombhkDataDownLoad',
      diagnosis: 'FromoccdiscaseDownLoad',
    };
    return typeMap[dataType] || 'unknown';
  }

  /**
   * 更新任务状态
   * @param {String} taskId - 任务ID
   * @param {String} status - 状态
   * @param {String} message - 消息
   */
  async _updateTaskStatus(taskId, status, message) {
    try {
      await this.ctx.model.FzDataProcessLog.updateOne(
        { taskId },
        {
          $set: {
            'processingInfo.status': status,
            'result.message': message,
            updatedAt: new Date(),
          },
        }
      );
    } catch (error) {
      this.ctx.logger.error(`更新任务状态失败: ${taskId}`, error);
    }
  }

  /**
   * 更新任务为成功状态
   * @param {String} taskId - 任务ID
   * @param {Object} result - 处理结果
   * @param {Number} duration - 处理时长
   */
  async _updateTaskSuccess(taskId, result, duration) {
    try {
      const updateData = {
        'processingInfo.status': 'success',
        'processingInfo.endTime': new Date(),
        'processingInfo.duration': duration,
        'dataStats.totalRecords': result.processed || 0,
        'dataStats.processedRecords': result.processed || 0,
        'dataStats.failedRecords': 0,
        'dataStats.skippedRecords': 0,
        'result.success': true,
        'result.message': result.message || '处理完成',
        'result.error': null,
        'metadata.additionalInfo.performance': {
          duration,
          processingSpeed: Math.round((result.processed || 0) / (duration / 1000)),
        },
        'metadata.additionalInfo.serviceResult': result,
        updatedAt: new Date(),
      };

      await this.ctx.model.FzDataProcessLog.updateOne(
        { taskId },
        { $set: updateData }
      );
    } catch (error) {
      this.ctx.logger.error(`更新任务成功状态失败: ${taskId}`, error);
    }
  }

  /**
   * 更新任务为失败状态
   * @param {String} taskId - 任务ID
   * @param {String} errorMessage - 错误消息
   * @param {Number} duration - 处理时长
   */
  async _updateTaskFailure(taskId, errorMessage, duration) {
    try {
      const updateData = {
        'processingInfo.status': 'failed',
        'processingInfo.endTime': new Date(),
        'processingInfo.duration': duration,
        'result.success': false,
        'result.message': `处理失败: ${errorMessage}`,
        'result.error': errorMessage,
        'metadata.additionalInfo.errorDetails': {
          error: errorMessage,
          timestamp: new Date(),
          duration,
        },
        updatedAt: new Date(),
      };

      await this.ctx.model.FzDataProcessLog.updateOne(
        { taskId },
        { $set: updateData }
      );
    } catch (error) {
      this.ctx.logger.error(`更新任务失败状态失败: ${taskId}`, error);
    }
  }

  /**
   * 通用的批量数据处理方法
   * @param {String} taskType - 任务类型
   * @param {Object} options - 处理选项
   * @return {Promise<Object>} 处理结果
   */
  async _processAllDataByType(taskType, options = {}) {
    const {
      useParallel = true,
      concurrency = 3,
      batchSize = 10,
      maxFiles = 0,
      skipOnError = true,
    } = options;

    try {
      this.ctx.logger.info(`开始批量处理 ${taskType} 数据`, { options });

      // 获取文件列表
      const files = await this._getFilesToProcess(taskType, maxFiles);

      if (files.length === 0) {
        return {
          success: true,
          message: `没有找到需要处理的 ${taskType} 数据文件`,
          totalFiles: 0,
          processedFiles: 0,
          failedFiles: 0,
          skippedFiles: 0,
        };
      }

      let results;
      if (useParallel) {
        // 使用并发处理
        results = await this._processFilesInParallel(files, taskType, {
          concurrency,
          batchSize,
          skipOnError,
        });
      } else {
        // 使用串行处理
        results = await this._processFilesSerially(files, taskType, { skipOnError });
      }

      // 处理结果并更新任务状态
      await this._updateTaskStatusFromResults(results, taskType);

      const summary = this._summarizeResults(results);

      this.ctx.logger.info(`批量处理 ${taskType} 数据完成`, summary);

      return {
        success: summary.failedFiles === 0 || skipOnError,
        message: `${taskType} 数据批量处理完成`,
        ...summary,
        results,
      };

    } catch (error) {
      this.ctx.logger.error(`批量处理 ${taskType} 数据失败`, { error: error.message });
      throw new Error(`批量处理 ${taskType} 数据失败: ${error.message}`);
    }
  }

  /**
   * 获取需要处理的文件列表
   * @param {String} taskType - 任务类型
   * @param {Number} maxFiles - 最大文件数
   * @return {Promise<Array>} 文件路径列表
   */
  async _getFilesToProcess(taskType, maxFiles) {
    const fs = require('fs');
    const path = require('path');

    // 根据任务类型确定文件类型映射
    const fileTypeMap = {
      company: 'FromcrptDownLoad',
      healthCheck: 'FrombhkDataDownLoad',
      diagnosis: 'FromoccdiscaseDownLoad',
    };

    const targetFileType = fileTypeMap[taskType];
    if (!targetFileType) {
      throw new Error(`未知的任务类型: ${taskType}`);
    }

    // 从配置中获取数据目录
    const dataDir = this.ctx.app.config.upload_fzDataLog_path;
    if (!fs.existsSync(dataDir)) {
      this.ctx.logger.warn(`数据目录不存在: ${dataDir}`);
      return [];
    }

    // 扫描目录获取文件
    const files = [];
    const scanDir = dir => {
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
          scanDir(fullPath); // 递归扫描子目录
        } else if (item.includes(targetFileType) && item.endsWith('.zip')) {
          files.push(fullPath);
        }
      }
    };

    scanDir(dataDir);

    // 限制文件数量
    if (maxFiles > 0 && files.length > maxFiles) {
      return files.slice(0, maxFiles);
    }

    return files;
  }

  /**
   * 并发处理文件
   * @param {Array} files - 文件列表
   * @param {String} taskType - 任务类型
   * @param {Object} options - 处理选项
   * @return {Promise<Array>} 处理结果
   */
  async _processFilesInParallel(files, taskType, options) {
    const { concurrency, batchSize } = options;

    // 使用动态进程管理器
    const DynamicProcessManager = require('../utils/dynamicProcessManager');
    const manager = new DynamicProcessManager({
      maxProcesses: concurrency,
      batchSize,
      processorScript: require('path').join(__dirname, '../workers/fzDataProcessor.js'),
    });

    const result = await manager.processFiles(files, taskType);
    return result.results || [];
  }

  /**
   * 串行处理文件
   * @param {Array} files - 文件列表
   * @param {String} taskType - 任务类型
   * @param {Object} options - 处理选项
   * @return {Promise<Array>} 处理结果
   */
  async _processFilesSerially(files, taskType, options) {
    const { skipOnError } = options;
    const results = [];

    for (const filePath of files) {
      try {
        const result = await this._processSingleFile(filePath, taskType);
        results.push(result);
      } catch (error) {
        const errorResult = {
          fileName: require('path').basename(filePath),
          filePath,
          status: 'failed',
          success: false,
          message: `处理失败: ${error.message}`,
          error: error.message,
        };

        results.push(errorResult);

        if (!skipOnError) {
          throw error;
        }
      }
    }

    return results;
  }

  /**
   * 处理单个文件
   * @param {String} filePath - 文件路径
   * @param {String} taskType - 任务类型
   * @return {Promise<Object>} 处理结果
   */
  async _processSingleFile(filePath, taskType) {
    // 这里可以直接调用现有的数据处理逻辑
    // 或者使用 Worker 线程处理
    const WorkerPool = require('../utils/workerPool');
    const pool = new WorkerPool({ poolSize: 1 });

    await pool.initialize();

    try {
      const result = await pool.execute({
        filePath,
        taskType,
        fileName: require('path').basename(filePath),
      });

      return result;
    } finally {
      await pool.terminate();
    }
  }

  /**
   * 根据处理结果更新任务状态
   * @param {Array} results - 处理结果列表
   * @param {String} taskType - 任务类型
   * @return {Promise<void>}
   */
  async _updateTaskStatusFromResults(results, taskType) {
    for (const result of results) {
      try {
        // 为每个文件创建或更新任务日志
        const taskId = this._generateTaskId(result.fileName, taskType);

        // 查找或创建任务记录
        let taskLog = await this.ctx.model.FzDataProcessLog.findOne({ taskId });

        if (!taskLog) {
          // 创建新的任务记录
          taskLog = await this._createTaskLog(taskId, result, taskType);
        }

        // 根据处理结果更新状态
        if (result.status === 'success' || result.success === true) {
          await taskLog.markAsSuccess({
            message: result.message || '处理成功',
            dataStats: {
              totalRecords: result.processedCount || 0,
              processedRecords: result.processedCount || 0,
              failedRecords: result.errorCount || 0,
              skippedRecords: result.skippedCount || 0,
            },
            decryptedData: result.decryptedData,
            dataAnalysis: result.dataAnalysis,
            performance: result.performance,
          });
        } else if (result.status === 'skipped' || result.skipped === true) {
          await taskLog.markAsSkipped({
            message: result.message || '跳过处理',
            skipReason: result.skipReason,
            decryptedData: result.decryptedData,
            dataAnalysis: result.dataAnalysis,
          });
        } else {
          await taskLog.markAsFailed(
            new Error(result.error || result.message || '处理失败'),
            true,
            {
              decryptedData: result.decryptedData,
              dataAnalysis: result.dataAnalysis,
              performance: result.performance,
            }
          );
        }
      } catch (error) {
        this.ctx.logger.error('更新任务状态失败', {
          fileName: result.fileName,
          error: error.message,
        });
      }
    }
  }

  /**
   * 生成任务ID
   * @param {String} fileName - 文件名
   * @param {String} taskType - 任务类型
   * @return {String} 任务ID
   */
  _generateTaskId(fileName, taskType) {
    const crypto = require('crypto');
    const timestamp = Date.now();
    const hash = crypto.createHash('md5').update(`${fileName}_${taskType}_${timestamp}`).digest('hex');
    return `${taskType}_${hash.substring(0, 8)}`;
  }

  /**
   * 创建任务日志记录
   * @param {String} taskId - 任务ID
   * @param {Object} result - 处理结果
   * @param {String} taskType - 任务类型
   * @return {Promise<Object>} 任务日志记录
   */
  async _createTaskLog(taskId, result, taskType) {
    const fileTypeMap = {
      company: 'FromcrptDownLoad',
      healthCheck: 'FrombhkDataDownLoad',
      diagnosis: 'FromoccdiscaseDownLoad',
    };

    const taskLog = new this.ctx.model.FzDataProcessLog({
      taskId,
      taskType,
      dataSource: {
        fileType: fileTypeMap[taskType],
        filePath: result.filePath,
        fileName: result.fileName,
        fileSize: result.performance?.fileSize || 0,
      },
      replayInfo: {
        canReplay: true,
        replayParams: {
          data: result.decryptedData,
          options: { taskType },
        },
      },
    });

    await taskLog.markAsProcessing();
    return taskLog;
  }

  /**
   * 汇总处理结果
   * @param {Array} results - 处理结果列表
   * @return {Object} 汇总结果
   */
  _summarizeResults(results) {
    const totalFiles = results.length;
    const processedFiles = results.filter(r => r.status === 'success' || r.success === true).length;
    const failedFiles = results.filter(r => r.status === 'failed' || (r.success === false && !r.skipped)).length;
    const skippedFiles = results.filter(r => r.status === 'skipped' || r.skipped === true).length;

    return {
      totalFiles,
      processedFiles,
      failedFiles,
      skippedFiles,
      successRate: totalFiles > 0 ? ((processedFiles / totalFiles) * 100).toFixed(2) : 0,
    };
  }

  /**
   * 清理现有任务
   * @param {String} taskType - 任务类型
   * @return {Promise<void>}
   */
  async _clearExistingTasks(taskType) {
    const query = {};
    if (taskType && taskType !== 'all') {
      query.taskType = taskType;
    }

    const result = await this.ctx.model.FzDataProcessLog.deleteMany(query);
    this.ctx.logger.info(`清理现有任务完成，删除了 ${result.deletedCount} 个任务`, { taskType });
  }

  /**
   * 获取用于创建任务的文件列表
   * @param {String} taskType - 任务类型
   * @param {Number} maxFiles - 最大文件数
   * @param {Boolean} recentOnly - 是否只处理最近几天的文件
   * @param {Number} recentDays - 最近几天，默认2天
   * @return {Promise<Object>} 按类型分组的文件列表
   */
  async _getFilesForTaskCreation(taskType, maxFiles, recentOnly = false, recentDays = 2) {
    const fs = require('fs');
    const path = require('path');

    const fileTypeMap = {
      company: 'FromcrptDownLoad',
      healthCheck: 'FrombhkDataDownLoad',
      diagnosis: 'FromoccdiscaseDownLoad',
    };

    const dataDir = this.ctx.app.config.upload_fzDataLog_path;
    if (!fs.existsSync(dataDir)) {
      this.ctx.logger.warn(`数据目录不存在: ${dataDir}`);
      return {};
    }

    const filesByType = {};
    const typesToProcess = taskType === 'all' ? Object.keys(fileTypeMap) : [ taskType ];

    for (const type of typesToProcess) {
      const targetFileType = fileTypeMap[type];
      if (!targetFileType) continue;

      const files = [];

      if (recentOnly) {
        // 如果启用最近几天过滤，直接查找特定日期的文件
        const targetDates = [];

        // 确保天数为正整数，最小为1天
        const days = Math.max(1, Math.floor(recentDays || 2));

        // 生成最近几天的日期字符串
        for (let i = 0; i < days; i++) {
          const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          targetDates.push(`${year}${month}${day}`);
        }

        // 直接查找包含这些日期的文件
        const scanDirForDates = dir => {
          try {
            const items = fs.readdirSync(dir);
            for (const item of items) {
              const fullPath = path.join(dir, item);
              const stat = fs.statSync(fullPath);

              if (stat.isDirectory()) {
                scanDirForDates(fullPath);
              } else if (item.includes(targetFileType) && item.endsWith('.zip')) {
                // 检查文件名是否包含目标日期
                const hasTargetDate = targetDates.some(date => item.includes(date));
                if (hasTargetDate) {
                  files.push(fullPath);
                }
              }
            }
          } catch (error) {
            this.ctx.logger.warn(`扫描目录失败: ${dir}`, error);
          }
        };

        scanDirForDates(dataDir);
      } else {
        // 如果不启用过滤，扫描所有文件
        const scanDir = dir => {
          try {
            const items = fs.readdirSync(dir);
            for (const item of items) {
              const fullPath = path.join(dir, item);
              const stat = fs.statSync(fullPath);

              if (stat.isDirectory()) {
                scanDir(fullPath);
              } else if (item.includes(targetFileType) && item.endsWith('.zip')) {
                files.push(fullPath);
              }
            }
          } catch (error) {
            this.ctx.logger.warn(`扫描目录失败: ${dir}`, error);
          }
        };

        scanDir(dataDir);
      }

      // 按文件名中的日期倒序排列（最新的文件在前）
      files.sort((a, b) => {
        const fileNameA = path.basename(a);
        const fileNameB = path.basename(b);
        const dateMatchA = fileNameA.match(/(\d{8})/);
        const dateMatchB = fileNameB.match(/(\d{8})/);

        if (dateMatchA && dateMatchB) {
          return dateMatchB[1].localeCompare(dateMatchA[1]); // 倒序排列
        }

        // 如果无法从文件名提取日期，则使用文件修改时间
        try {
          const statA = fs.statSync(a);
          const statB = fs.statSync(b);
          return statB.mtime - statA.mtime;
        } catch (error) {
          return 0;
        }
      });

      // 限制文件数量
      if (maxFiles > 0 && files.length > maxFiles) {
        filesByType[type] = files.slice(0, maxFiles);
      } else {
        filesByType[type] = files;
      }
    }

    return filesByType;
  }

  /**
   * 从文件列表创建任务 - 批量插入优化版本
   * @param {Object} filesByType - 按类型分组的文件列表
   * @param {Boolean} skipExisting - 是否跳过已存在的任务
   * @return {Promise<Array>} 创建结果列表
   */
  async _createTasksFromFileList(filesByType, skipExisting) {
    const results = [];
    const BATCH_SIZE = 1000; // 批量插入大小

    for (const [ taskType, files ] of Object.entries(filesByType)) {
      this.ctx.logger.info(`开始为 ${taskType} 类型创建任务，共 ${files.length} 个文件，将分批处理`);

      // 按批次处理文件
      for (let i = 0; i < files.length; i += BATCH_SIZE) {
        const batch = files.slice(i, i + BATCH_SIZE);
        const batchNumber = Math.floor(i / BATCH_SIZE) + 1;
        const totalBatches = Math.ceil(files.length / BATCH_SIZE);

        this.ctx.logger.info(`处理 ${taskType} 类型第 ${batchNumber}/${totalBatches} 批，包含 ${batch.length} 个文件`);

        try {
          const batchResult = await this._createTasksBatch(batch, taskType, skipExisting);
          // 为每个结果添加 taskType 字段
          const batchResultWithType = batchResult.map(result => ({
            taskType,
            ...result,
          }));
          results.push(...batchResultWithType);
        } catch (error) {
          this.ctx.logger.error(`批量创建任务失败 - ${taskType} 第 ${batchNumber} 批:`, error);

          // 如果批量插入失败，回退到逐个处理
          this.ctx.logger.info(`回退到逐个处理模式 - ${taskType} 第 ${batchNumber} 批`);
          for (const filePath of batch) {
            try {
              const result = await this._createSingleTaskFromFile(filePath, taskType, skipExisting);
              results.push({
                taskType,
                ...result,
              });
            } catch (singleError) {
              results.push({
                taskType,
                filePath,
                created: false,
                skipped: false,
                error: singleError.message,
              });
              this.ctx.logger.error(`单个任务创建失败: ${filePath}`, singleError);
            }
          }
        }
      }
    }

    return results;
  }

  /**
   * 批量创建任务 - 1000条为一组
   * @param {Array} filePaths - 文件路径数组
   * @param {String} taskType - 任务类型
   * @param {Boolean} skipExisting - 是否跳过已存在的任务
   * @return {Promise<Array>} 创建结果列表
   */
  async _createTasksBatch(filePaths, taskType, skipExisting) {
    const path = require('path');
    const fs = require('fs');
    const results = [];
    const tasksToInsert = [];

    // 文件类型映射
    const fileTypeMap = {
      company: 'FromcrptDownLoad',
      healthCheck: 'FrombhkDataDownLoad',
      diagnosis: 'FromoccdiscaseDownLoad',
    };

    const targetFileType = fileTypeMap[taskType];
    if (!targetFileType) {
      throw new Error(`不支持的任务类型: ${taskType}`);
    }

    // 如果需要跳过已存在的任务，先批量查询现有任务
    let existingTasks = new Set();
    if (skipExisting) {
      const existingTasksQuery = await this.ctx.model.FzDataProcessLog.find({
        taskType,
        'dataSource.filePath': { $in: filePaths },
      }, { 'dataSource.filePath': 1 });

      existingTasks = new Set(existingTasksQuery.map(task => task.dataSource.filePath));
    }

    // 准备批量插入的数据
    for (const filePath of filePaths) {
      const fileName = path.basename(filePath);

      // 检查是否已存在
      if (skipExisting && existingTasks.has(filePath)) {
        results.push({
          filePath,
          fileName,
          created: false,
          skipped: true,
          reason: 'already_exists',
        });
        continue;
      }

      // 检查文件是否存在
      try {
        if (!fs.existsSync(filePath)) {
          results.push({
            filePath,
            fileName,
            created: false,
            skipped: false,
            error: '文件不存在',
          });
          continue;
        }

        const fileStats = fs.statSync(filePath);
        const taskId = this._generateTaskId(fileName, taskType);

        // 准备任务数据
        const taskData = {
          taskId,
          taskType,
          dataSource: {
            fileType: targetFileType,
            filePath,
            fileName,
            fileSize: fileStats.size,
          },
          processingInfo: {
            status: 'pending',
            retryCount: 0,
            maxRetries: 3,
          },
          replayInfo: {
            canReplay: true,
            replayParams: {
              filePath,
              taskType,
            },
          },
          metadata: {
            version: '1.0.0',
            environment: this.ctx.app.config.env,
            processedBy: 'system',
            tags: [ 'auto-created' ],
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        tasksToInsert.push(taskData);
        results.push({
          filePath,
          fileName,
          created: true,
          skipped: false,
          taskId,
          fileSize: fileStats.size,
        });

      } catch (error) {
        results.push({
          filePath,
          fileName,
          created: false,
          skipped: false,
          error: error.message,
        });
      }
    }

    // 执行批量插入
    if (tasksToInsert.length > 0) {
      try {
        await this.ctx.model.FzDataProcessLog.insertMany(tasksToInsert, {
          ordered: false, // 允许部分失败，继续插入其他记录
        });

        this.ctx.logger.info(`批量插入成功: ${tasksToInsert.length} 个任务`, {
          taskType,
          batchSize: tasksToInsert.length,
        });

      } catch (error) {
        this.ctx.logger.error('批量插入失败:', error);

        // 如果是重复键错误，更新结果状态
        if (error.code === 11000) {
          // 处理重复键错误，标记为跳过
          const duplicateResults = results.filter(r => r.created);
          duplicateResults.forEach(result => {
            result.created = false;
            result.skipped = true;
            result.reason = 'duplicate_key';
          });
        } else {
          throw error; // 其他错误重新抛出
        }
      }
    }

    return results;
  }

  /**
   * 为单个文件创建任务
   * @param {String} filePath - 文件路径
   * @param {String} taskType - 任务类型
   * @param {Boolean} skipExisting - 是否跳过已存在的任务
   * @return {Promise<Object>} 创建结果
   */
  async _createSingleTaskFromFile(filePath, taskType, skipExisting) {
    const path = require('path');
    const fileName = path.basename(filePath);

    // 检查是否已存在相同的任务
    if (skipExisting) {
      const existingTask = await this.ctx.model.FzDataProcessLog.findOne({
        taskType,
        'dataSource.filePath': filePath,
      });

      if (existingTask) {
        return {
          filePath,
          fileName,
          created: false,
          skipped: true,
          reason: 'already_exists',
          existingTaskId: existingTask.taskId,
        };
      }
    }

    // 生成新的任务ID
    const taskId = this._generateTaskId(fileName, taskType);

    // 创建任务记录
    const fileTypeMap = {
      company: 'FromcrptDownLoad',
      healthCheck: 'FrombhkDataDownLoad',
      diagnosis: 'FromoccdiscaseDownLoad',
    };

    const fs = require('fs');
    const fileStats = fs.statSync(filePath);

    const taskLog = new this.ctx.model.FzDataProcessLog({
      taskId,
      taskType,
      dataSource: {
        fileType: fileTypeMap[taskType],
        filePath,
        fileName,
        fileSize: fileStats.size,
      },
      processingInfo: {
        status: 'pending',
        retryCount: 0,
        maxRetries: 3,
      },
      replayInfo: {
        canReplay: true,
        replayParams: {
          filePath,
          taskType,
        },
      },
      metadata: {
        version: '1.0.0',
        environment: this.ctx.app.config.env,
        processedBy: 'system',
        tags: [ 'auto-created' ],
      },
    });

    await taskLog.save();

    this.ctx.logger.info(`成功创建任务: ${taskId}`, {
      taskType,
      fileName,
      filePath,
    });

    return {
      filePath,
      fileName,
      created: true,
      skipped: false,
      taskId,
      fileSize: fileStats.size,
    };
  }

  /**
   * 汇总任务创建结果
   * @param {Array} results - 创建结果列表
   * @return {Object} 汇总结果
   */
  _summarizeTaskCreation(results) {
    const totalFiles = results.length;
    const createdTasks = results.filter(r => r.created).length;
    const skippedTasks = results.filter(r => r.skipped).length;
    const errorTasks = results.filter(r => r.error).length;

    const tasksByType = {};
    for (const result of results) {
      // 处理 taskType 为 undefined 或空值的情况
      const taskType = result.taskType || 'unknown';

      if (!tasksByType[taskType]) {
        tasksByType[taskType] = {
          total: 0,
          created: 0,
          skipped: 0,
          errors: 0,
        };
      }
      tasksByType[taskType].total++;
      if (result.created) tasksByType[taskType].created++;
      if (result.skipped) tasksByType[taskType].skipped++;
      if (result.error) tasksByType[taskType].errors++;
    }

    return {
      totalFiles,
      totalCreated: createdTasks,
      totalSkipped: skippedTasks,
      totalErrors: errorTasks,
      tasksByType,
      successRate: totalFiles > 0 ? ((createdTasks / totalFiles) * 100).toFixed(2) : 0,
    };
  }
  /**
   * 获取解密文件列表
   * @param {String} taskType - 任务类型
   * @return {Promise<Array>} 文件列表
   */
  async getDecryptFiles(taskType) {
    const fs = require('fs');
    const path = require('path');

    // 根据任务类型确定文件类型映射
    const fileTypeMap = {
      company: 'FromcrptDownLoad',
      healthCheck: 'FrombhkDataDownLoad',
      diagnosis: 'FromoccdiscaseDownLoad',
    };

    const targetFileType = fileTypeMap[taskType];
    if (!targetFileType) {
      throw new Error(`未知的任务类型: ${taskType}`);
    }

    // 从配置中获取数据目录
    const dataDir = this.ctx.app.config.upload_fzDataLog_path;
    if (!fs.existsSync(dataDir)) {
      this.ctx.logger.warn(`数据目录不存在: ${dataDir}`);
      return [];
    }

    // 扫描目录获取文件
    const files = [];
    const scanDir = dir => {
      try {
        const items = fs.readdirSync(dir);
        for (const item of items) {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);

          if (stat.isDirectory()) {
            scanDir(fullPath); // 递归扫描子目录
          } else if (item.includes(targetFileType) && item.endsWith('.zip')) {
            files.push({
              fileName: item,
              filePath: fullPath,
              fileSize: stat.size,
              modifiedTime: stat.mtime,
            });
          }
        }
      } catch (error) {
        this.ctx.logger.warn(`扫描目录失败: ${dir}`, error);
      }
    };

    scanDir(dataDir);

    // 按修改时间倒序排列
    files.sort((a, b) => new Date(b.modifiedTime) - new Date(a.modifiedTime));

    return files;
  }

  /**
   * 解密ZIP文件
   * @param {String} taskType - 任务类型
   * @param {String} fileName - 文件名
   * @return {Promise<Object>} 解密结果
   */
  async decryptZipFile(taskType, fileName) {
    const fs = require('fs');
    const path = require('path');
    const JSZip = require('jszip');
    const EncryptUtil = require('../utils/encryptUtil');

    try {
      // 获取加密密钥
      const encryptionKey = this.ctx.app.config.fzDataEncryptionKey || 'chiscdc@bhkdownload#$%^';

      // 查找文件
      const dataDir = this.ctx.app.config.upload_fzDataLog_path;
      let filePath = null;

      // 递归查找文件
      const findFile = dir => {
        try {
          const items = fs.readdirSync(dir);
          for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);

            if (stat.isDirectory()) {
              const found = findFile(fullPath);
              if (found) return found;
            } else if (item === fileName) {
              return fullPath;
            }
          }
        } catch (error) {
          this.ctx.logger.warn(`查找文件失败: ${dir}`, error);
        }
        return null;
      };

      filePath = findFile(dataDir);

      if (!filePath || !fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${fileName}`);
      }

      // 读取ZIP文件
      const zipContent = fs.readFileSync(filePath);
      const zip = new JSZip();
      const zipData = await zip.loadAsync(zipContent);
      const dataFile = zipData.files['data.json'];

      if (!dataFile) {
        throw new Error(`ZIP文件中不存在data.json: ${fileName}`);
      }

      // 解密数据
      const encryptedContent = await dataFile.async('string');
      const decryptedContent = EncryptUtil.decrypt(encryptedContent, encryptionKey);
      const jsonData = JSON.parse(decryptedContent);

      // 根据任务类型提取数据
      let extractedData = [];
      const dataType = taskType;

      switch (taskType) {
        case 'company':
          if (jsonData.crptList) {
            extractedData = jsonData.crptList;
          } else if (jsonData.ZoneCompanyList) {
            extractedData = jsonData.ZoneCompanyList;
          }
          break;
        case 'healthCheck':
          if (jsonData.bhkList) {
            extractedData = jsonData.bhkList;
          }
          break;
        case 'diagnosis':
          if (jsonData.occdiscasetList) {
            extractedData = jsonData.occdiscasetList;
          }
          break;
        default:
          throw new Error(`不支持的任务类型: ${taskType}`);
      }

      return {
        fileName,
        dataType,
        totalRecords: extractedData.length,
        decryptTime: new Date().toISOString(),
        data: extractedData,
        rawJson: JSON.stringify(jsonData, null, 2),
      };

    } catch (error) {
      this.ctx.logger.error(`解密ZIP文件失败: ${fileName}`, error);
      throw new Error(`解密ZIP文件失败: ${error.message}`);
    }
  }

  /**
   * 根据任务ID解密ZIP文件
   * @param {String} taskId - 任务ID
   * @return {Promise<Object>} 解密结果
   */
  async decryptTaskZip(taskId) {
    try {
      // 查找任务
      const task = await this.ctx.model.FzDataProcessLog.findOne({ taskId });
      if (!task) {
        throw new Error(`任务不存在: ${taskId}`);
      }

      // 获取文件路径和任务类型
      const filePath = task.dataSource?.filePath;
      const taskType = task.taskType;
      const fileName = task.dataSource?.fileName;

      if (!filePath) {
        throw new Error(`任务缺少文件路径信息: ${taskId}`);
      }

      if (!taskType) {
        throw new Error(`任务缺少类型信息: ${taskId}`);
      }

      this.ctx.logger.info(`开始解密任务ZIP文件: ${taskId}`, {
        taskType,
        fileName,
        filePath,
      });

      // 使用现有的解密逻辑
      const result = await this._decryptZipFromPath(filePath, taskType, fileName);

      // 添加任务信息
      result.taskId = taskId;
      result.taskType = taskType;

      return result;

    } catch (error) {
      this.ctx.logger.error(`根据任务ID解密ZIP文件失败: ${taskId}`, error);
      throw new Error(`解密任务ZIP文件失败: ${error.message}`);
    }
  }

  /**
   * 从指定路径解密ZIP文件（内部方法）
   * @param {String} filePath - 文件路径
   * @param {String} taskType - 任务类型
   * @param {String} fileName - 文件名
   * @return {Promise<Object>} 解密结果
   */
  async _decryptZipFromPath(filePath, taskType, fileName) {
    const fs = require('fs');
    const path = require('path');
    const JSZip = require('jszip');
    const EncryptUtil = require('../utils/encryptUtil');

    try {
      // 获取加密密钥
      const encryptionKey = this.ctx.app.config.fzDataEncryptionKey || 'chiscdc@bhkdownload#$%^';

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
      }

      // 读取ZIP文件
      const zipContent = fs.readFileSync(filePath);
      const zip = new JSZip();
      const zipData = await zip.loadAsync(zipContent);
      const dataFile = zipData.files['data.json'];

      if (!dataFile) {
        throw new Error(`ZIP文件中不存在data.json: ${fileName || filePath}`);
      }

      // 解密数据
      const encryptedContent = await dataFile.async('string');
      const decryptedContent = EncryptUtil.decrypt(encryptedContent, encryptionKey);
      const jsonData = JSON.parse(decryptedContent);

      // 根据任务类型提取数据
      let extractedData = [];
      const dataType = taskType;

      switch (taskType) {
        case 'company':
          if (jsonData.crptList) {
            extractedData = jsonData.crptList;
          } else if (jsonData.ZoneCompanyList) {
            extractedData = jsonData.ZoneCompanyList;
          }
          break;
        case 'healthCheck':
          if (jsonData.bhkList) {
            extractedData = jsonData.bhkList;
          }
          break;
        case 'diagnosis':
          if (jsonData.occdiscasetList) {
            extractedData = jsonData.occdiscasetList;
          }
          break;
        default:
          throw new Error(`不支持的任务类型: ${taskType}`);
      }

      return {
        fileName: fileName || path.basename(filePath),
        filePath,
        dataType,
        totalRecords: extractedData.length,
        decryptTime: new Date().toISOString(),
        data: extractedData,
        rawJson: JSON.stringify(jsonData, null, 2),
      };

    } catch (error) {
      this.ctx.logger.error(`解密ZIP文件失败: ${filePath}`, error);
      throw new Error(`解密ZIP文件失败: ${error.message}`);
    }
  }

  async getALLZipFromApi() {
    console.log('FZDATA定时任务开始');
    console.time('FZDATA定时任务耗时');
    const zoneCodeArray = [
      '**********',
      '3501020000',
      '3501020100',
      '3501020200',
      '3501020300',
      '3501020400',
      '3501020500',
      '3501020600',
      '3501020700',
      '3501020800',
      '3501020900',
      '3501021000',
      '3501030000',
      '3501030100',
      '3501030200',
      '3501030300',
      '3501030500',
      '3501030600',
      '3501030700',
      '3501030800',
      '3501030900',
      '3501031000',
      '3501031100',
      '3501040000',
      '3501040100',
      '3501040200',
      '3501040300',
      '3501040400',
      '3501040500',
      '3501040600',
      '3501040700',
      '3501040800',
      '3501040900',
      '3501041000',
      '3501041100',
      '3501041200',
      '3501041300',
      '3501050000',
      '3501050100',
      '3501050200',
      '3501050300',
      '3501050400',
      '3501110000',
      '3501110100',
      '3501110200',
      '3501110300',
      '3501110400',
      '3501110500',
      '3501110600',
      '3501110700',
      '3501110900',
      '3501111000',
      '3501210000',
      '3501210100',
      '3501210200',
      '3501210300',
      '3501210400',
      '3501210500',
      '3501210600',
      '3501210700',
      '3501210800',
      '3501210900',
      '3501211100',
      '3501211200',
      '3501211300',
      '3501211400',
      '3501211500',
      '3501211700',
      '3501220000',
      '3501220100',
      '3501220200',
      '3501220300',
      '3501220400',
      '3501220500',
      '3501220600',
      '3501220700',
      '3501220800',
      '3501220900',
      '3501221000',
      '3501221100',
      '3501221200',
      '3501221300',
      '3501221400',
      '3501221500',
      '3501221600',
      '3501221700',
      '3501221800',
      '3501221900',
      '3501222000',
      '3501222100',
      '3501222200',
      '3501230000',
      '3501230100',
      '3501230200',
      '3501230300',
      '3501230400',
      '3501230500',
      '3501230600',
      '3501230700',
      '3501230800',
      '3501230900',
      '3501231000',
      '3501231100',
      '3501240000',
      '3501240100',
      '3501240200',
      '3501240300',
      '3501240400',
      '3501240500',
      '3501240600',
      '3501240700',
      '3501240800',
      '3501241000',
      '3501241100',
      '3501241200',
      '3501241300',
      '3501241400',
      '3501241500',
      '3501241600',
      '3501241700',
      '3501250000',
      '3501250100',
      '3501250200',
      '3501250300',
      '3501250400',
      '3501250500',
      '3501250600',
      '3501250700',
      '3501250800',
      '3501250900',
      '3501251000',
      '3501251100',
      '3501251200',
      '3501251300',
      '3501251400',
      '3501251500',
      '3501251600',
      '3501251700',
      '3501251800',
      '3501251900',
      '3501252000',
      '3501252100',
      '3501810000',
      '3501810100',
      '3501810200',
      '3501810300',
      '3501810400',
      '3501810500',
      '3501810600',
      '3501810700',
      '3501810800',
      '3501810900',
      '3501811000',
      '3501811100',
      '3501811200',
      '3501811300',
      '3501811400',
      '3501811500',
      '3501811600',
      '3501811700',
      '3501811800',
      '3501811900',
      '3501812000',
      '3501812100',
      '3501812200',
      '3501812300',
      '3501812400',
      '3501820000',
      '3501820100',
      '3501820200',
      '3501820300',
      '3501820400',
      '3501820500',
      '3501820600',
      '3501820700',
      '3501820800',
      '3501820900',
      '3501821000',
      '3501821100',
      '3501821200',
      '3501821300',
      '3501821400',
      '3501821500',
      '3501821600',
      '3501821700',
      '3501821800',
    ];
    // 1. 首先获取tokenid
    const authObj = {
      unitCode: '35010899',
      regCode: 'a8e32a5d9a724293a71f200c23c0f7fc',
    };
    const { tokenId } = await this.ctx.service.fzData.getVerifyInfo(authObj);
    // const tokenId = 'DC5DDE3FF28F487AB6DCF9F5E0672F5D';

    for (const zoneCode of zoneCodeArray) {
      try {
        const toGetZoneCompanyInfo = {
          tokenId,
          zoneCode,
        };
        // 2. 根据tokenid获取区域企业信息 （如果之前下载过就不会再获得新的了）
        let companyFinish = false;
        do {
          const ZoneCompanyList =
            await this.ctx.service.fzData.getZoneCompanyInfo(
              toGetZoneCompanyInfo
            );
          if (ZoneCompanyList === null) {
            companyFinish = true;
          }
        } while (!companyFinish);
        // const tjType = {
        //   4: '放射人员健康检查',
        //   3: '职业健康检查',
        // };
        // 3. 根据tokenId bhkType  bhkYear zoneCode ifNoDownCrptAndOrg 获取体检数据
        const toGetBhkData = {
          tokenId,
          bhkType: '3,4',
          bhkYear: new Date().getFullYear(),
          zoneCode,
          ifNoDownCrptAndOrg: 1,
        };
        let tjFinish = false;
        do {
          const tjData = await this.ctx.service.fzData.getBhkData(toGetBhkData);
          if (tjData === null) {
            tjFinish = true;
          }
        } while (!tjFinish);
        // 4. 根据tokenid获取诊断信息 occdiscaseDownLoad
        const toGetOccdiscasetData = {
          tokenId,
          zoneCode,
        };
        let occFinish = false;
        do {
          const occData = await this.ctx.service.fzData.getOccdiscasetData(
            toGetOccdiscasetData
          );
          if (occData === null) {
            occFinish = true;
          }
        } while (!occFinish);
      } catch (error) {
        this.ctx.auditLog(
          `${zoneCode}同步福州数据失败`,
          JSON.stringify(error),
          'error'
        );
      }
    }
    // 然后就创建处理任务（默认只处理最近2天的文件）
    await this.createTasksFromFiles({
      recentOnly: true,
      recentDays: 2,
    });
    // 然后启动处理任务
    // startTaskQueue;
    await this.serialProcessData();
    console.timeEnd('FZDATA定时任务耗时');
    console.log('FZDATA定时任务结束');
  }
}

module.exports = FzDataFixService;
