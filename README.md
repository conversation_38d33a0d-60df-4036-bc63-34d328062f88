# oapi

Open API

# Open API 版本 更新日志

## 1.0.914更新日志
- Fix
  - 健康一体机体检报告文件路径错误

## 1.0.914更新日志
- Feat
  - px端增加注册功能

## 1.0.913更新日志
- Feat
  - px端增加刷题功能，新增接口manage/questionBank/getTopic

## 1.0.910更新日志
- Feat
  - 调整焦煤所有体检对接单向查询接口

## 1.0.909更新日志
- 功能修复
  - 【手机端】将装置改为装置/部门工序改为工序/模块

## 1.0.901更新日志
- 功能修复
  - 修复iam对接人员状态

## 1.0.900更新日志
- 功能新增
  - 新增在线监测开关

## 1.0.881更新日志
- 功能修复
  - 修复amp1163接口对接逻辑；修复oa代办单点默认传参

## 1.0.881更新日志
- 功能新增
  - 移动端劳动者权限设置

## 1.0.874更新日志
- 功能新增
  - 新增文件文件上传，删除，下载和获取地址的接口；新增开关控制集团和master文件路径

## 1.0.868更新日志
- bug修复
  - 修复个别人员无法获取防护用品计划

## 1.0.793更新日志
- 功能修复
  - 万华防护用品二级审批
## 1.0.857更新日志
## 1.0.904更新日志

- Fix
  - 修改微信服务号配置信息

## 1.0.894更新日志

- Fix

  - 修复体检对接已接收到的数据丢失问题，增加可视化对接任务处理，使用redis优化数据处理逻辑，增加重放机制

## 1.0.903更新日志
- Feat
  - 惠州培训新增配置文件
## 1.0.893更新日志

- Fix
  - 培训详情接口因超时获取失败
- Feat
  - 调整检测报告上传文件方法oss

## 1.0.866更新日志

- Fix
  - 焦煤检测对接缺陷修复

## 1.0.861更新日志

- 功能新增
  - 增加企业查询接口，修改检测报告集团查询企业方式

## 1.0.858更新日志

- 功能新增
  - 增加滑动验证码人机校验

## 1.0.855更新日志

## 1.0.857更新日志

- 功能调整
  - 检测报告审批调整

## 1.0.849更新日志

- bug修复
  - 转pdf添加返回文件地址

## 1.0.847更新日志

- bug修复
  - 修复转pdf问题

## 1.0.845更新日志

- bug修复
  - 检测报告签字失败

## 1.0.849更新日志
- 功能调整
  调整体检报告对接创建公司逻辑

## 1.0.833更新日志
- 功能新增
  小程序增加查看历年的告知书

## 1.0.833更新日志
- 功能修复
  移动端工作台问题反馈：
  ①性别显示不对: 
    跑库脚本：employees18位的按身份证更新gender，不满足或者没有的设置为空''
  ②年龄显示不出来：如果有18位身份证，则根据身份证计算年龄，否则使用employee中的字段
  ③工龄未显示，反而只显示“工龄”两个字: 
    根据employee的workStart计算工龄，如果没有入职时间则使用employee中的workyears，都没有则不显示
    单位是满了一年的，以年为单位，未满一年的，以月为单位，未满一月的，以天为单位


## 1.0.813_1更新日志
- 功能修复
  陕西、万华体检对接项目编码增加
  200664: { name: '鼓膜', classify: '五官科' },
  11438: { name: '左耳听阈加权值', classify: '电测听' },
  11439: { name: '右耳听阈加权值', classify: '电测听' },
  10983: { name: '感觉异常', classify: '神经科' },
  11580: { name: '四肢肌力', classify: '神经科' },

## 1.0.813更新日志
- 功能修复
  移动端中个人信息里的车间岗位，改为装置工序

## 1.0.805更新日志
- 功能新增
  - 移动端职业史增加两个字段供员工自己输入
## 1.0.797更新日志
- 功能新增
  - 手动维护的时候展示“车间”字样
## 1.0.840更新日志
## 1.0.842更新日志

- 功能调整
  - 北元对接修复公司类型

## 1.0.830更新日志

- 功能调整
  - 中将人信息添加phoneCarrier;
  - redis缓存优化updateGameRecord

## 1.0.825更新日志

- 功能调整
  - 排行榜长度改为可配置
  - 增加个人总积分展示

## 1.0.821更新日志

- 功能调整
  - 福州小游戏我的奖品名称

## 1.0.821更新日志

- 功能调整
  - 福州小游戏需求调整

## 1.0.819更新日志

- 功能修复
  - 修复无章版路径保存问题

## 1.0.723更新日志

- 功能调整
  - 【检测全流程】具备检测能力但是没有通过参数认证的检测报告

## 1.0.810更新日志

- 功能修复
  - 修复万华部门无企业id情况

## 1.0.808更新日志
- 功能新增
  - 新增全部已读功能; 优化已读功能
  
## 1.0.807更新日志

- 功能新增
  - 万华体检项目新增
    14610: { name: '癌胚抗原', classify: '甲胎癌胚' },
    14611: { name: '甲胎蛋白', classify: '甲胎癌胚' },

## 1.0.806更新日志
- 功能修复
  - 对接数据性别默认显示无

## 1.0.796更新日志

- 功能新增
  - 万华增加转岗流程

## 1.0.776更新日志

- 功能修复
  - 修复告知书的弹框没了

## 1.0.788更新日志

- 功能新增
  - 增加通用配置文件config.common.js

## 1.0.785更新日志

- 功能新增
  - 新增iService2Host 环境变量

## 1.0.770更新日志

- 功能修复
  - 修复添加，修改职业史不成功问题

## 1.0.771更新日志

- 功能新增
  - 新增问卷调查

## 1.0.769更新日志

- 功能调整
  - amp增加跳过授权返回

## 1.0.767更新日志

- 功能新增
  - 优化职业健康体检预约流程

## 1.0.764更新日志

- 功能新增
  - 河北大屏接口调用

## 1.0.759更新日志

- 功能新增
  - 万华对接审批流程

## 1.0.762更新日志

- 功能调整
  - 安全帽30个月，安全帽领用流程特殊确认

## 1.0.761更新日志

- 功能修复
  - 培训考试记录判断是否通过考试有点问题
- 功能新增
  - 配置中添加培训模式isTrainingLooseMode（是否开启培训宽松模式），开启后，不再判断培训前的签名和人脸识别

## 1.0.760更新日志

- 功能修复
  - 福州数据接口调用增加延时器，1分钟后继续执行 防止接口被频繁调用

## 1.0.758更新日志

- 功能修复
  - 业务接口调用缺陷 manage/user/getCompanysByPhoneNum
  - 暴力破解 账号密码登录的failCountKey缓存设置为1小时、次数调整为5从次
  - 任意文件上传 类型限制调整

## 1.0.756更新日志

- 功能新增
  - 移动端获取体检趋势图的指标

## 1.0.755更新日志

- 功能调整
  - 调整防护用品申请领用流程

## 1.0.754更新日志

- 配置
  - 增加河北培训项目配置

## 1.0.753更新日志

- 功能新增
  - 增加培训支付节点功能

## 1.0.752更新日志

- 功能新增
  - 配合px端新增考试功能

## 1.0.751更新日志

- 功能修复
  - 劳动者短信登录验证

## 1.0.750更新日志

- 功能新增
  - 增加三个定时任务处理体检对接数据,报告文件

## 1.0.745更新日志

- 功能新增
  - 修复镜像漏洞

## 1.0.740，747更新日志

- 功能修复
  - 北元和万华渗透漏洞修复
    - 文件上传类型限制
    - 越权
    - 短息验证码漏洞
    - 接口未授权
    - 暴力破解
    - 短信轰炸

## 1.0.745更新日志

- 功能新增
  - 新增万华体检预约功能接口

## 1.0.743更新日志

- 功能新增
  - 增加万华amp组织权限范围对接，增加定时任务处理备份数据

## 1.0.741更新日志

- 功能新增
  - 增加定时任务处理mdg数据未处理情况，model唯一值

## 1.0.733更新日志

- 功能修复
  - 取消短id校验，修改hmac计算传参

## 1.0.731更新日志

- 功能修复
  - 处理万华岗位人员对接用户信息缺少导致手机号空值重复问题

## 1.0.730更新日志

- 功能新增
  - 云南config

## 1.0.729更新日志

- 功能新增
  - 个人体检指标趋势后端

## 1.0.727更新日志

- 功能新增
  - 增加域名认证，通过设置authTxt 环境变量 ，格式 文件名:文件内容 支持英文逗号多路径 如  404.txt:404,abcd.txt:abcdContent

## 1.0.724,726更新日志

- 功能新增
  - 增加万华oidc pc和h5回调接收转发

## 1.0.722更新日志

- 功能新增
  - 万华对接增加部门变更信息和入职信息

## 1.0.719更新日志

- 功能新增
  - 万华OIDC

## 1.0.717更新日志

- 功能新增
  - wh定时任务增加默认配置

## 1.0.715更新日志

- 功能新增
  - 体检报告签字增加不同分支背景和签字日期

## 1.0.699更新日志

- 功能新增
  - 万华mdg对接
  - 万华组织对接
  - 万华人员对接
  - 万华岗位（权限）对接
  - 万华岗位人员对接

## 1.0.714更新日志

- 修改配置
  - 增加多平台镜像打包
  - 增加新疆兵团配置文件

## 1.0.712更新日志

- 功能新增
  - 新增数据库敏感数据加密，访问控制完整性（角色权限人员hmac），重要数据存储机密性，重要数据存储完整性（操作日志）
  - 新增openssl，sm4，福州sm4加密，sm3，福州sm3算法hmac计算
  - 密码存储校验改为sm3，福州sm3
  - 可切换当前加密算法
  - 模糊查询加密数据
  - 封装通用参数解密返回明文数据
  - 修改数据库查询封装方法适用于加密数据查询
  - 增加通用插件，适用于其他需要加密的表和字段和查询逻辑

## 1.0.648 更新日志

## 1.0.686 更新日志

## 1.0.708更新日志

- fix
  - 修复北元体检数据计量单位的处理,体检机构处理,增加预警

## 1.0.700更新日志

- fix
  - 劳动者只能查看当年体检报告

## 1.0.705更新日志

- fix
  - 设备检维修调整

## 1.0.704更新日志

- fix
  - 设备检维修调整

## 1.0.704更新日志

- fix
  - 尝试修复北元离岗无转岗信息

## 1.0.703更新日志

- fix
  - 尝试修复企业微信登录

## 1.0.702更新日志

- fix
  - 增加福州体检对接预警

## 1.0.701更新日志

- fix
  - sxcc删除本地体检报告文件

## 1.0.691更新日志

- feat
  - 万华体检对接

## 1.0.697 更新日志

- fix 体检预约缺少generateTimeSlots 方法

## 1.0.686 更新日志

- feat
  - 福州小游戏
  - 福州小游戏第2期：表设计
  - 企业列表模糊查询
  - 授权登录后返回活动信息
  - gameEventInfo接口改为app/ 不鉴权

## 1.0.675 更新日志

## 1.0.683更新日志

- feat
  - 体检预约选择时间段

## 1.0.680 更新日志

- feat
  - by未查到员工直接抛出错误

## 1.0.672 更新日志

- feat
  - 初始化万华配置

## 1.0.673 更新日志

- fix
  - 修复放射方案修改审批不通过

## 1.0.670 更新日志

- fix
  - 修复培训头像

## 1.0.669 更新日志

- fix
  - 配置安徽培训跨域

## 1.0.666 更新日志

- fix
  - 修复跨域问题

## 1.0.663 更新日志

- fix
  - 企业套餐支付
  - config.hm subscriptionVerification

## 1.0.655 更新日志

- feat
  - 调整体检清单功能

## 1.0.661，662 ,664更新日志

- feat
  - 修复在线监测转换分子质量bug

## 1.0.660更新日志

- feat
  - 轮询体检签到和档案结果定时任务
  - 体检预约选择时间段

## 1.0.658 更新日志

- feat
  - 获取指标字典接口，sxcc增加40以上额度，40以下额度

## 1.0.659更新日志

- feat
  - 在线监测设备 分子质量

## 1.0.656 更新日志

- feat
  - 山西焦煤推送体检信息

## 1.0.657 更新日志

- fix
  - 修改sxcc的地址

## 1.0.653 更新日志

- fix
  - 修改iservice2的域名地址

## 1.0.651 更新日志

- feat
  - 增加设备检维修签字功能

## 1.0.650 更新日志

- fix
  - 修改iservice2的域名地址

## 1.0.644 更新日志

- fix
  - 修复小游戏可以无限次抽奖的问题

## 1.0.643 更新日志

- feat
  - fzxyx: 添加gameEnterprise表，用于存储企业信息
  - fzxyx: 修改获取企业列表接口
  - 排行榜积分改为根据做题次数累加的
  - 更改抽奖逻辑，改为一次通关可以抽奖一次

## 1.0.640 更新日志

- fix
  - 小游戏抽奖修复
  - appid

## 1.0.635 更新日志

- Feat
  - 添加新疆哈密的配置文件

## 1.0.634 更新日志

- Feat
  - 福州小游戏：添加获取奖品列表、获取已获奖用户列表、获取试卷和游戏记录信息、更新游戏记录和考题正确率、创建/更新游戏用户信息、根据wxUnionId获取游戏用户信息、获取企业列表、获取积分排行榜接口 xxn
  - 福州小游戏：微信H5静默授权 lcz

## 1.0.631 更新日志

- Feat
  - 安徽培训：考试完成后，根据合格的成绩自动发证书

## 1.0.619 更新日志

- Feat
  - 企业微信增加头像对接

## 1.0.618 更新日志

- Fix
  - 修复人员对接 入职就离职的信息留存

## 1.0.617 更新日志

- Fix
  - 补全personalTraining model之前被误删的字段

## 1.0.616 更新日志

- Feat
  - 修复北元对接接口地址

## 1.0.615 更新日志

- Feat
  - 修复北元对接接口地址

## 1.0.590 更新日志

- Feat
  - 焦煤h5增加企业微信OAuth2授权登录

## 1.0.613更新日志

- Fix
  - 广西培训证书模板修改

## 1.0.612更新日志

- 新增调整
  - 【山西焦煤】【体检】修改相应检查项目查询保存部分逻辑

## 1.0.611更新日志

- Feat
  - 增加开关控制是否同时使用企业id进行查询个人和总的体检报告,修改by上传体检数据逻辑

## 1.0.608更新日志

- Feat
  - 增加北元培训对接
  - BY_BASE_API_URL=[http://************:8310](http://************:8310/)
    BY_BASE_API_URL2=[http://************:8310](http://************:8310/)

## 1.0.606更新日志

- Feat
  - 体检人员现场签到消息接口

## 1.0.605更新日志

- Fix
  - 优化提取预约详情接口

## 1.0.604更新日志

- feat
  - 根据subscriptionVerification添加套餐支付相关逻辑
  - export alipay_privateKey=$(cat private_key.pem)
  - export wxpay_privateKey=$(cat apiclient_key.pem)

## 1.0.603更新日志

- Fix
  - 焦煤小程序体检预约必选和选检项目价格判断

## 1.0.602更新日志

- Fix
  - pxapp 修复封面显示问题

## 1.0.601更新日志

- 增加北元在线监测PI系统的对接

## 1.0.599更新日志

- 找回577版本代码

## 1.0.593 更新日志

- 新增功能
  - pxapp 添加培训班查询

## 1.0.593 更新日志

- 新增功能
  - pxapp 的学习考试和培训详情接口

## 1.0.591 更新日志

- 新增功能
  - pxapp的首页、考试、我的信息页面相关接口和调整

## 1.0.589 更新日志

- Feat
  - 【山西焦煤】【体检】体检医院系统对接

## 1.0.588 更新日志

- Feat
  - 增加劳动者小程序检修维护接口

## 1.0.585 更新日志

- Feat
  - 温州体检对接-上传个案卡文件接口如果查询到多个相同编号的个案卡则报错返回

## 1.0.579 更新日志

- Fix
  - 广西培训证书模板修改

## 1.0.577更新日志

- Feat
  - 增加在线监测对接北元的
  - 优化在线监测数据存储
  - 增加初始化数据库部分实验代码

## 1.0.576 更新日志

- Feat
  - 小程序体检预约

## 1.0.574 更新日志

- Feat
  - 修改gitlab-ci
  - 写一个新的温州上传体检报告方法

## 1.0.572 更新日志

- Feat
  - 山西焦煤员工预约体检 - 提醒功能定时任务

## 1.0.571 更新日志

- Fix
  - 修复体检个案卡文件上传报错（暂时取消队列）
- Feat
  - 上传体检报告文件增加一些日志输出

## 1.0.570 更新日志

- Feat
  - docker基础镜像改为node:18-alpine

## 1.0.569 更新日志

- Feat
  - 温州体检对接中增加projectNumber必传字段
  - 上传总检报告文件增加温州体检对接相关逻辑
  - 体检系统对接：增加普通体检类型

## 1.0.568 更新日志

- Feat
  - 个案卡文件队列尝试采用发布订阅direct模式

## 1.0.567 更新日志

- Fix
  - 修复北元岗位审核获取岗位错误

## 1.0.565 更新日志

- Feat
  - 上传个案卡接口删除原文件

## 1.0.566 更新日志

- Fix
  - 修复启动时对列报错；
  - 在温州体检个案卡信息中增加体检机构名称字段

## 1.0.566 更新日志

- 功能修复
  - 修复人员信息部门假删还展示的问题

## 1.0.566 更新日志

- 功能修复
  - 修复人员信息部门假删还展示的问题

## 1.0.564 更新日志

- Feat
  - 新增上传个人职业健康档案-xml-陕西标准接口，新增上传个人体检报告文件接口

## 1.0.563 更新日志

- Feat
  - 北元增加健康一体机对接数据接口接口

## 1.0.562 更新日志

- 新增功能
  - 体检系统对接：人员信息增加危害因素和体检项目

## 1.0.561 更新日志

- Fix
  - 体检系统对接：上传职业健康档案-创建企业没有企业联系人手机号码时，使用企业的统一社会信用代码创建adminuser以及个案卡中危害因素、年龄数据不对等缺陷的修复。

## 1.0.560 更新日志

- Fix
  - 体检系统对接：修复上传职业健康档案时，因体检项目id重复导致的实际体检人数大于应体检人数的问题

## 1.0.559 更新日志

- 新增功能
  - 体检系统对接：支持多家医院
  - 体检系统对接：修复修复个案卡中体检项目结果和结论重复的问题

## 1.0.558 更新日志

- 功能调整
  - 获取个人体检信息增加总页数返

## 1.0.557 更新日志

- 功能修复
  - 修复修复北元岗位更新bug

## 1.0.556 更新日志

- 功能修复
  - 增加北元双预防接口对接

## 1.0.555 更新日志

- bugfix
  - 修复app防护用品申请失败，优化通知提醒

## 1.0.554 更新日志

- bugfix
  - 修复北元dingtree对接数据更新错误，强设oss代理cookie为空
  - 对外获取个人体检信息接口加上联查体检机构表

## 1.0.553 更新日志

- bugfix
  - 修复北元依赖扫描报错 vm2 degenerator pac-resolver

## 1.0.550 更新日志

- bugfix
  - 修改Dockerfile
  - 修改sharp依赖版本

## 1.0.549 更新日志

- bugfix
  - 福州体检汇总对项目存在相同的体检人员进行判断去重

## 1.0.547 更新日志

- 功能调整
  - 温州体检系统对接 - 预约单详情和列表接口中的企业信息和人员信息按照浙江省职业健康档案中的必传项返回相关信息
  - 对xml传参格式及请求头中的Content-Type在中间件时就进行校验

## 1.0.546 更新日志

- 功能新增

  - 北元对接人员部门变更岗位审核及通知，添加北元审核通知，修改北元通知参数，北元集团顶级部门添加简称,adminorg添加简称

  ## 1.0.544 更新日志
- 新增配置

  - 新增安徽培训的配置文件

## 1.0.543 更新日志

- bugfix
  - 体检系统对接-修复预约单对应的体检项目查询错误的问题

## 1.0.542 更新日志

- 功能调整
  - 体检系统对接-增加个案卡中工龄、医学建议、异常指标等信息

## 1.0.540,541 更新日志

- 功能调整
  - 企业微信 by.html 引入jweixin 改为网页引入

## 1.0.537 更新日志

- 功能调整
  - 北元企业微信小程序统一认证通过 网页拉起小程序注入登录token

## 1.0.536 更新日志

- 功能调整
  - 温州体检系统对接：删除职业健康档案中体检类型等信息跟预约单的匹配校验

## 1.0.535 更新日志

- 新增功能
  - 添加浙江省标准的工种编码表，以替换原来的工种代码json文件
  - 接收体检个案卡信息啥，同时创建企业车间点位信息表

## 1.0.534 更新日志

- 新增功能
  - 添加浙江省标准的体检个人卡及企业信息对接接口2个

## 1.0.532 更新日志

- bug修复
  - 中间件中增加对token格式的校验及返回
  - 体检系统对接-增加职业病危害因素代码数据json格式

## 1.0.531 更新日志

- bug修复
  - 体检系统对接-再添加一些日志输出

## 1.0.530 更新日志

- 功能优化
  - 优化福州创建体检项目的逻辑来处理创建过多体检项目

## 1.0.529 更新日志

- bug修复
  - 体检系统对接-添加一些日志输出

## 1.0.528 更新日志

- 功能优化
  - 优化福州创建体检项目的逻辑来处理创建过多体检项目。调整防护用品领用逻辑。

## 1.0.527 更新日志

- bug修复
  - 体检系统对接-修复上传用人单位信息报错，对企业表中的工作场所以及体检机构信息进行去重处理

## 1.0.526 更新日志

- 功能新增
  - gitlab-ci.yml添加代理服务器

## 1.0.524 更新日志

- 功能新增
  - 新增bodyparser中间件（统一处理xml请求）；处理体检系统对接传过来的xml数据

## 1.0.523,525 更新日志

- 功能新增

  - 新增北元小程序用oidc认证登录模块

## 1.0.522 更新日志

- 功能优化

  - 体检对接api/physical/physicalExamInfo接口优化

## 1.0.521 更新日志

- 功能优化
  - 福州对接禁忌症改为禁忌证

## 1.0.518 更新日志

- 新增功能 xxn
  - 添加消息队列通用方法rabbitmqService
  - 同步mongodb中的预约单到sqlserver中

## 1.0.517 更新日志

- 功能修复
  - jt合并master

## 1.0.516_BY 更新日志

- 功能修复
  - 修复定时任务时间读取去掉\n
  - 修复Dingtrees model名字错误

## 1.0.515_BY 更新日志

- 功能新增
  - 增加全量更新 getByDataInitializationTime为all;
  - 更换入职时间字段为JOINSYSDATE

## 1.0.514_BY 更新日志

- 功能新增
  - 增加北元中控数据自动对接功能

## 1.0.513 更新日志

- 功能优化
  - 增加oss api代理访问

## 1.0.511 更新日志

- 功能优化

  - 小程序pdf签名及引导单适配oss

## 1.0.507 更新日志

- 新增功能  xxn

  - 体检系统对接：新增确时间（短信提醒）、获取预约列表及详情3个接口
  - 体检系统地接：增加职业健康档案接口、用人单位信息接口
  - 新增工种代码表，增加/编辑工种代码接口
  - 预约单详情中增加文件地址的信息

## 1.0.508 更新日志

- 功能优化
  - 小程序个人信息接口无工龄字段

## 1.0.506 更新日志

- 功能优化
  - 添加by小程序静态资源，小程序个人信息接口替换工种字段

## 1.0.505 更新日志

- 功能修复
  - 修复福州体检结果危害因素对应关系

## 1.0.503 更新日志

- 功能调整
  - 修改体检接口返回状态，添加log

## 1.0.500 更新日志

- 功能新增
  - 评价方案审核

## 1.0.499 更新日志

- 功能调整
  - 调整心跳返回数据格式

## 1.0.497 更新日志

- 配置更新
  - 新增卫康职卫云配置文件 wkzwy.cn

## 1.0.495 更新日志

- 配置更新
  - 修复漏洞

## 1.0.494 更新日志

- 功能新增
  - 放射合同评审修改

## 1.0.493 更新日志

- 配置更新
  - 修复漏洞

## 1.0.492 更新日志

- 配置更新
  - 心跳添加状态码

## 1.0.488 更新日志

- 配置更新
  - Fix start sit2 script

## 1.0.487 更新日志

- 配置更新
  - 添加北元by、山西焦煤sxcc及内网jkqy配置文件

## 1.0.486 更新日志

- bug修复
  - 修复福州对接体检报告，编号匹配改为完全匹配

## 1.0.484 更新日志

- bug修复
  - 放射报告审批字段修改

## 1.0.482 更新日志

- 功能新增
  - 放射修改审批

## 1.0.481 更新日志

- 功能新增
  - 增加个人体检信息接口，个案卡数据

## 1.0.480_1 更新日志

- bug修复
  - 数据库链接方式兼容新版mongoose修改

## 1.0.480 更新日志

- bug修复
  - 修复体检接口人员信息合并时，危害因素没有被合并的问题

## 1.0.479 更新日志

- 功能新增
  - 放射报告审核

## 1.0.477, 478 更新日志

- 配置更新
  - sit/sit2测试环境db uri更新

## 1.0.476 更新日志

- 新增配置
  - 新增莲都分支

## 1.0.475 更新日志

- 功能新增
  - 新增违章信息推送

## 1.0.474 更新日志

- 功能新增
  - 新增防护用品图片

## 1.0.473 更新日志

- 功能新增
  - oapi: 追加一人一档里的告知书

## 1.0.472 更新日志

- 功能新增
  - 新增体检医院确认，上传引导单

## 1.0.471 更新日志

- 功能修改
  - 完善对接接口dowhile获取,直到空,增加fz配置文件里fzDataApiHost为可配置

## 1.0.470 更新日志

- 功能新增
  - 新增防护用品流程

## 1.0.468, 469 更新日志

- 配置修改
  - mongodb链接改回在url里写ssl=false

## 1.0.467 更新日志

- 配置修改
  - zyws.cn站点迁移至k8s集群

## 1.0.466 更新日志

- 配置调整
  - 取消alinode性能监控和日志功能

## 1.0.465 更新日志

- 功能调整
  - 取消八骏发起审批

## 1.0.464 更新日志

- 配置修改
  - 杭州站点迁移至k8s集群

## 1.0.463 更新日志

- 功能完善
  - 福州对接 完善体检项目详情

## 1.0.462 更新日志

- 功能新增
  - 新增个案卡管理

## 1.0.461 更新日志

- 功能调整
  - 修复福州企业注册地址，工作场所，体检报告默认上报，企业信息统计

## 1.0.460 更新日志

- 功能调整
  - 启动福州定时任务

## 1.0.459 更新日志

- 功能修复
  - 修复福州对接诊断导入失败

## 1.0.458 更新日志

- 配置修改
  - Dockerfile临时copy fzPhy.xlsx

## 1.0.457 更新日志

- 功能新增
- 福州对接
  - 注意项 branch为hz 时并且 isGetFZData: true,  才能启动定时任务
  - 因为对接接口只会返回一次一样的数据 所以非cn一定不要触发这个定时
  - 本次更新对于多个model的索引进行了修改，防止空数据无法创建的问题
  - importToDb.js 为封装通用方法，可以后使用
  - 反馈接口 未做对接处理， 因不知道调用后果，目前所需参数已经经文件保留，以后可以补充

## 1.0.455 更新日志

- 功能新增
- 劳动者app增加调岗签名生成危害告知书

## 1.0.454 更新日志

- 配置修改
- K8S集群部署日志不再输出到文件，由loki统一采集

## 1.0.453 更新日志

- bug修复
- 修复体检端消息查看状态不变问题

## 1.0.451 更新日志

- 功能新增
- 新增劳动者端增加体检预约功能API

## 1.0.450 更新日志

- 功能新增
- 新增问卷调查

## 1.0.449 更新日志

- 功能新增
- 新增劳动者职业史管理接口

## 1.0.448 更新日志

- bug修复
- 去掉课程id参数添加获取照片方法，用来判断是否再次添加照片

## 1.0.447 更新日志

- 版本对齐
- version 1.0.447

## 1.0.445 更新日志

- bug修复
- 生成证书空课程数据去除

## 1.0.444 更新日志

- 更改计算学时方式

## 1.0.443 更新日志

- 功能调试
- 添加新的header输出

## 1.0.442 更新日志

- 功能调试
- 添加新的的输出

## 1.0.441 更新日志

- 功能调试
- 添加token信息的输出

## 1.0.440 更新日志

- 功能新增
- 增加劳动者app的转岗记录和消息通知功能

## 1.0.438 更新日志

- 功能调整
- 体检接口调整医学建议为非必填

## 1.0.436 更新日志

- 功能调整
  - 八骏直辖市地址处理

## 1.0.435 更新日志

- 功能配置
  - 添加杏花岭的微信小程序认证授权配置

## 1.0.434 更新日志

- 功能更新
  - 放射项目发起审批时获取签名

## 1.0.433 更新日志

- 功能配置
  - 福州备案已通过，去掉端口号5443

## 1.0.432 更新日志

- 功能调整
- 恢复合肥培训的公开课证书模板

## 1.0.431 更新日志

- 功能调整
- 体检对接接口添加IDType证件类型，IDType=1时，校验身份证,其他不做校验

## 1.0.429 更新日志

- 功能调整
- 放射合同评审回调处理
- 放射项目对接八骏审批

## 1.0.428 更新日志

- 功能调整
- 体检对接接口添加IDType证件类型，IDType=passport时，不校验身份证

## 1.0.427 更新日志

- 功能调整
  - 添加档案完成项目的回调处理

## 1.0.425 更新日志

- docs
- 修改杏花岭培训证书模板

## 1.0.426 更新日志

- 功能调整
- 八骏对接:创建放射项目，放射项目状态同步

## 1.0.424 更新日志

- 功能配置
  - 增加杏花岭配置文件

## 1.0.423 更新日志

- 功能调整
  - 钉钉事件回调更新审批记录时添加审批节点员工id
  - 修改审批记录单添加审批员工id

## 1.0.422 更新日志

- 功能配置
  - demo站点url fzzyws.cn

## 1.0.421 更新日志

-功能修复

- 获取签字信息时新增加persontrainingid参数

## 1.0.419 更新日志

- 功能配置
  - 增加demo站配置文件

## 1.0.418 更新日志

-功能修复
 -修改传参

## 1.0.417 更新日志

-功能修复

- 修复更新personTraining表的数据有问题

## 1.0.415 更新日志

- docs
  - 修改合肥公开课培训证书模板

## 1.0.412 更新日志

- 输出
- 添加输出调试

## 1.0.405 更新日志

- docs
  - 修改合肥公开课培训证书模板

## 1.0.404 更新日志

- 功能调整
  - 八骏对接的项目需要发起钉钉合同审批

## 1.0.403 更新日志

- 配置更新
  - 默认输出日志到终端

## 1.0.401,402 更新日志

- 配置
- 增加Api鉴权中间件里的上下文输出

## 1.0.400 更新日志

- bug修复
  - 八骏项目状态更新暂停和终止状态错位修复

## 1.0.399 更新日志

- 配置
- 增加体检接口的输出

## 1.0.398

- 标准物质申领事件订阅更新出入库状态

## 1.0.397 更新日志

- docs
- 修改广西培训证书模板

## 1.0.395 更新日志

- 功能新增
- 三个对外接口增加physical路径

## 1.0.392 更新日志

- 功能新增
- 内网支持网络代理调用外部API
- CI/Docker环境更新为node:18-alpine
- 部分插件版本更新

## 1.0.391 更新日志

- 日志增加 wx
  - 增加alipay日志更详细的输出

## 1.0.390 更新日志

- 配置修改 xjx
  - 福州域名改为fzcdc.org.cn

## 1.0.389 更新日志

- 功能调整 xxn
  - 杭州分支：外部导入体检/检测项目时，增加项目工作场所是否全部为杭州市的校验结果日志输出

## 1.0.388 更新日志

- 新增功能 xxn
  - 杭州分支：外部导入体检/检测项目时，增加项目工作场所是否为杭州市校验结果日志输出
  - heartbeat接口中新增当前版本号的获取和输出

## 1.0.387 更新日志

- 修复 physicalExaminationOrgID 结构改变导致的报错

## 1.0.382 更新日志

- 八骏项目对接
- 联系人处理

## 1.0.380 更新日志

- fix
- 增加支付相关接口日志输出

## 1.0.378 更新日志

- 八骏项目对接
- 修改更新项目信息接口的状态码

## 1.0.377 更新日志

- 八骏项目对接 htt
- 客户信息和项目信息添加省市区地址字段（目前先注释了，等八骏调整好后打开），添加联系人和联系电话格式检验以及必填校验

## 1.0.376 更新日志

- 功能调整
  - 同步企业表和诊断表的新增字段

## 1.0.375 更新日志

- fix
  - 同步trainSiteConfig model
  - 修改trainSiteConfig 文件路径获取方式

## 1.0.374 更新日志

- 八骏项目对接 htt
  - 客户信息以及项目信息重新推送后更新为最新的数据
  - 项目添加分类风险等级
  - 地址有英文括号时正则报错处理

## 1.0.373 更新日志

  --修改登录cookie名称
  --修改.gitlab-ci.yml复用上次build的cache

## 1.0.372 更新日志

- fix
- 培训网站改为oapi从iservice获取版权信息

## 1.0.371 更新日志

- feat
- 同步培训网站设置model
- 更新广西证书模板

## 1.0.370 更新日志

- 八骏接口
- 添加企业联系人以及联系电话校验；isVip取值错误修复；添加更新项目实际金额接口

## 1.0.369 更新日志

- feat
  - 增加培训证书（公开课培训，云课堂用）的防伪二维码，
  - 二维码内容为 ：对应分支px域名+ /certificate/number?=证书编号
  - 注广西备案完成需要修改配置里domainNames.px的值

## 1.0.368 更新日志

- 配置更改 xjx
  - 合肥站点改用集群部署

## 1.0.367 更新日志

-fix

- 修复培训代理中心展示收益计算逻辑问题;payInfo 增加paySuccessTime字段

## 1.0.366 更新日志

- 八骏对接过来的ZY/ZS项目，默认为不需要采样

## 1.0.365 更新日志

-功能修改

- 劳动者app获取最新一次的体检数据

## 1.0.364 更新日志

- feat
  - 增加广西培训证书的公司名字，增加培训网站的代理接口返回有效下级人数

## 1.0.363 更新日志

- 配置更改 xjx
  - 集群部署模式rabbitmq更换为集群内服务

## 1.0.362 更新日志

- 配置更改 xjx
  - 集群数据库名称和日志路径采用环境变量
  - 数据库名称和认证采用参数方式

## 1.0.361 更新日志

 -新增功能
   获取劳动者体检信息和岗位检测信息

- 配置
  - 添加广西站点启动入口

## 1.0.358 更新日志

- 配置
  - 新增广西站点配置, gxoapi.zyws.cn

## 1.0.357 更新日志

- fix
  - 修复支付成功后修改上线记录的支付状态,更换代理退款逻辑

## 1.0.356 更新日志

- fix
  - 修复微信小程序session_key暴露问题

## 1.0.355 更新日志

- fix
  - 修复防止代理关系闭环bug

## 1.0.354 更新日志

- 配置更改
  - 连接集群内数据库

## 1.0.353 更新日志

- 新增功能
  - 培训推广相关功能

## 1.0.352 更新日志

- 新增功能
  - 增加查看体检接口日志

## 1.0.351 更新日志

- 新增功能
  - 新增3个对外接口：根据数据更新时间段查询体检机构信息、个人体检信息以及诊断信息

## 1.0.350 更新日志 htt

- 八骏接口地址处理：删除根据信用代码获取地址逻辑，方法重写

## 1.0.349 更新日志

- 体检接口增加座机的格式规则，防止通不过校验

## 1.0.348 更新日志

- 修复缺陷 🐞
    - 新增/修改 体检项目信息之后, 同步更新企业表healcheckInfo字段中的数据

## 1.0.346 更新日志

- 注释全流程项目表企业规模的枚举

## 1.0.345 更新日志

- 添加完成项目审批配置
- 其他审批进入数据修改审批修复

## 1.0.341 更新日志

- 对接八骏修改
  - 八骏对接项目如果全流程已有该项目那么更新全流程表中没有的数据；合同费用，分包费用等未同步修复

## 1.0.340 更新日志

- 配置更改 xjx
  - 福建静态文件目录采用统一的/opt/public,由容器配置映射至主机/opt/fujian

## 1.0.338 更新日志

- 配置更改 xjx
  - package.json添加k8s的启动入口以支持Kubernetes集群，通过容器环境EGG_SERVER_ENV决定加载哪个配置文件
  - 添加福建配置文件config.fj.js
  - 福建正式域名 fjzyws.cn
