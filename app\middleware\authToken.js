const { siteFunc } = require('@utils');
const _ = require('lodash');
module.exports = (options, app) => {
  const routeWhiteList = [
    'api/heartbeat',
    'api/report/createPhyFz',
    'api/byTest',
    'api/byCallback',
    'api/whcallback',
    'whh5',
    'api/healCheckResult',
    'api/healCheckFile',
    'dingSubscript',
    'api/testEncrypt',
    'api/wxWorkAuthAction',
    'api/validateUrlWxWorkAuthAction',
    'api/checkItems/getIndicator',
    'api/sxccPhysicalAppointment/handleCompletePhysicalExam',
    'api/whData/getPersonData',
    'api/whData/getOrgData',
    'api/whData/getAccountData',
    'api/whData/getPostData',
    'api/whData/getPostAndPersonData',
    'api/getSystemConfig',
  ];
  return async function authToken(ctx, next) {
    const method = ctx.request.method,
      ip = ctx.header['x-real-ip'] || ctx.request.ip;

    ctx.session.api = '';
    let fields = null;
    switch (method) {
      case 'GET':
        fields = ctx.query;
        break;
      case 'POST':
      case 'PUT':
      case 'DELETE':
        fields = ctx.request.body;
        break;
      default:
        fields = null;
        break;
    }
    if ((fields && fields.token) || ctx.header.token) {
      if (!_.isEmpty(routeWhiteList)) {
        const checkWhiteRouter = _.filter(routeWhiteList, item => {
          return ctx.originalUrl.indexOf(item) >= 0;
        });
        if (!_.isEmpty(checkWhiteRouter)) {
          await next();
          return;
        }
      }
      ctx.auditLog('获取到的header', ctx.header, 'info');
      ctx.auditLog('获取到的header中的token', ctx.header.token, 'info');
      ctx.auditLog('自定义的fields字段', fields.token, 'info');

      let orginToken = ctx.header.token || fields.token;
      ctx.auditLog('获取的token信息', orginToken, 'info');
      let token;
      try {
        if (typeof orginToken === 'string') {
          orginToken = orginToken.replace(/\\\"/g, '\"');
          token = JSON.parse(orginToken);
        } else {
          token = orginToken;
        }
      } catch (err) {
        ctx.helper.renderCustom(ctx, {
          status: 400,
          message: '参数token格式错误，无法解析：' + orginToken,
        });
        return;
      }
      const agentId = token.agentId || '',
        appKey = token.appKey || '',
        appSecret = token.appSecret || '',
        systemAppSecret = siteFunc.getAppSecret(ctx, appKey, app.config.salt_sha2_key);
      if (appSecret === systemAppSecret) {
        const apiUser = await ctx.service.apiUser.item(ctx, {
          files: { _id: 1, status: 1 },
          query: { _id: agentId, status: 1 },
        });
        if (apiUser && agentId === apiUser._id) {
          const oAppGroup = await ctx.service.oAppGroup.item(ctx, {
            files: { agentId: 1, appKey: 1, appSecret: 1, ips: 1, enable: 1 },
            query: { agentId, appKey, enable: true },
          });

          if (oAppGroup) {
            const ips = (oAppGroup.ips || '').replace(/，| |-|、|\n/g, ',');
            if (ips !== '') {
              const ipArray = ips.split(',');
              if (!ipArray.includes(ip)) {
                ctx.helper.renderFail(ctx, {
                  message: '对不起！您的IP地址暂无权限，可联系相关管理员咨询',
                });
                return;
              }
            }

            if (appSecret === oAppGroup.appSecret) {
              ctx.session.api = token;
              await next();
              return;
            }
          }
        }
      }
      ctx.helper.renderFail(ctx, {
        message: '对不起！您暂无权限，可联系相关管理员咨询',
      });
    } else {
      ctx.auditLog('token校验错误', 'token为空', 'error');
      if (!_.isEmpty(routeWhiteList)) {
        const checkWhiteRouter = _.filter(routeWhiteList, item => {
          return ctx.originalUrl.indexOf(item) >= 0;
        });
        if (!_.isEmpty(checkWhiteRouter)) {
          await next();
          return;
        }
      }
      ctx.helper.renderFail(ctx, {
        message: '对不起！您暂无权限，可联系相关管理员咨询',
      });
    }
  };
};
