const Service = require('egg').Service;
// const moment = require('moment');
// const _ = require('lodash');
const initData = require('../utils/FuninitData');
class JcGroupService extends Service {
  // 根据角色别名获取角色员工信息
  async findEmployeeByRoleAlias(query) {
    const roleIndex = initData.findIndex(item => item.alias === query.alias);
    if (roleIndex === -1) {
      return {
        errMsg: `暂未找到分组名称为${query.parentGroup}，别名为：${query.alias}的的角色信息`,
      };
    }
    const roleName = initData[roleIndex].name;
    const serviceOrgId = query.serviceOrgId;
    const data = await this.ctx.model.FunDivision.aggregate([
      { $match: { serviceOrgId, ...query } },
      {
        $lookup: {
          from: 'serviceEmployee',
          let: { employeeIds: '$employeeIds' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: [ '$_id', '$$employeeIds' ],
                },
              },
            },
            {
              $project: { _id: 1, name: 1, signPath: 1, phoneNum: 1, employeeIds: 1 },
            }],
          as: 'employees',
        },
      },
    ]);
    if (!data[0]) {
      return {
        errMsg: `分组：${query.parentGroup}，暂未创建'${roleName}'角色，请到'职能与待办-角色分配'中创建角色`,
      };
    }
    if (!data[0].employees.length) {
      return {
        errMsg: `"${data[0].name}"角色暂未分配员工，请到职能与待办中分配`,
      };
    }
    return data[0];
  }
}

module.exports = JcGroupService;
