

const Service = require('egg').Service;
const path = require('path');
const moment = require('moment');
const fs = require('fs');
class JcqlcProjectService extends Service {
  async findSignPath(serviceEmployeeId) {
    const { ctx, app } = this;
    const url = ctx.request.url;
    const params = new URLSearchParams(url.split('?')[1]);
    const serviceOrgId = params.get('serviceOrgId');
    if (!serviceEmployeeId) {
      return { serviceOrgId };
    }
    const employeeInfo = await ctx.model.ServiceEmployee.findOne({ _id: serviceEmployeeId }, { signPath: 1, name: 1 }).lean();
    return {
      signPath: employeeInfo !== null && employeeInfo.signPath ? path.join(app.config.sign_path, serviceOrgId, employeeInfo.signPath) : '',
      name: employeeInfo !== null ? employeeInfo.name : '',
    };
  }
  async getApproveUser(data) {
    const { ctx } = this;
    let auditorName = '';
    let auditor = '';
    let auditorSignDate = '';
    let compilingPersonnelSignDate = '';
    let compilingPersonnelName = '';
    let compilingPersonnel = '';
    let threeApprovel = ''; // 实验室三审
    let threeApprovelName = '';// 三审姓名
    let threeApprovelDate = ''; // 实验室三审日期
    // result = await ctx.service.approveRecord.getReportApprovedInfo(data.reportProcessInstanceId);
    const ApproveRecordData = await this.ctx.model.ApproveRecord.findOne({ processInstanceId: data.reportProcessInstanceId });
    const records = [];
    let item = {};
    const operation_resultObj = {
      AGREE: '同意',
      REFUSE: '拒绝',
    };
    let operation_result = '';
    for (let i = 0; i < ApproveRecordData.operation_records.length; i++) {
      item = ApproveRecordData.operation_records[i];
      // const originatorUser = await ctx.helper.dingTalkGteUserInfoById(item.userid);
      // if (originatorUser.errcode !== '0') {
      // return { errMsg: originatorUser.errmsg };
      // }
      let userName = '';
      if (item.serviceEmployeeId) {
        const employeeInfo = await ctx.model.ServiceEmployee.findOne({ _id: item.serviceEmployeeId }, { name: 1 }).lean();
        userName = employeeInfo.name;
      } else {
        ctx.auditLog('获取钉钉审批实例错误', `审批实例id：${data.reportProcessInstanceId}，userId:${item.userid}，错误信息：未绑定serviceEmployeeId`, 'error');
      }

      if (i === 0) {
        operation_result = '发起申请';
      } else {
        operation_result = operation_resultObj[item.operation_result];
        if (!operation_result && item.operation_type === 'PROCESS_CC') {
          operation_result = '抄送';
        }
      }
      records.push({
        _id: item._id,
        userid: item.userid,
        userName,
        operation_result,
        formatTime: moment(new Date(item.date)).format('YYYY-MM-DD HH:mm'),
        date: item.date,
        serviceEmployeeId: item.serviceEmployeeId || '',
      });
    }
    ApproveRecordData.records = records;
    const first = ApproveRecordData.operation_records && ApproveRecordData.operation_records[1] || {}; // 一审
    const second = ApproveRecordData.operation_records && ApproveRecordData.operation_records[2] || {}; // 二审
    const third = ApproveRecordData.operation_records && ApproveRecordData.operation_records[3] || {}; // 三审
    auditorSignDate = moment(new Date(second.date)).format('YYYY年MM月DD日');
    auditor = await (await this.findSignPath(second.serviceEmployeeId));
    auditorName = auditor.name;
    auditor = auditor.signPath;
    compilingPersonnelSignDate = moment(new Date(first.date)).format('YYYY年MM月DD日');
    // auditorSignDate = moment(new Date(second.date)).format('YYYY年MM月DD日');
    compilingPersonnelName = await (await this.findSignPath(first.serviceEmployeeId)).name;
    compilingPersonnel = await (await this.findSignPath(first.serviceEmployeeId)).signPath;
    // 三审
    threeApprovel = await (await this.findSignPath(third.serviceEmployeeId)).signPath;
    threeApprovelDate = moment(new Date(third.date)).format('YYYY年MM月DD日');
    threeApprovelName = await (await this.findSignPath(third.serviceEmployeeId)).name;
    return {
      auditor,
      auditorSignDate,
      compilingPersonnelSignDate,
      compilingPersonnelName,
      compilingPersonnel,
      auditorName,
      result: ApproveRecordData,
      threeApprovelDate,
      threeApprovel,
      threeApprovelName,
    };
  }
  // 实验室审核记录
  async downloadReport({ _id, projectSN } = {}) {
    const { ctx } = this;
    let data;
    data = await ctx.model.JcqlcProject.aggregate([
      { $match: { _id } },
      {
        $lookup: {
          from: 'serviceEmployee',
          foreignField: '_id',
          localField: 'personsOfCompiling',
          as: 'personsOfCompiling',
        },
      },
    ]);
    data = JSON.parse(JSON.stringify(data[0]));
    const serviceOrgId = data.serviceOrgId;
    const { result, auditor, compilingPersonnelName, compilingPersonnel, compilingPersonnelSignDate, auditorSignDate, threeApprovelDate, threeApprovel } = await this.getApproveUser(data);
    const form_component_values = result.form_component_values || [];
    const Cname = [
      { name: '基础信息(存在问题)', field: 'biuFaq' },
      { name: '基础信息(修改情况)', field: 'biuRevise' },
      { name: '检测依据(存在问题)', field: 'inspectionReferenceFaq' },
      { name: '检测依据(修改情况)', field: 'inspectionReferenceRevise' },
      { name: '现场情况(存在问题)', field: 'siteConditionsFaq' },
      { name: '现场情况(修改情况)', field: 'siteConditionsRevise' },
      { name: '现场采样和检测情况(存在问题)', field: 'sampingDetecteFaq' },
      { name: '现场采样和检测情况(修改情况)', field: 'sampingDetecteRevise' },
      { name: '检测结果与判定(存在问题)', field: 'checkResultFaq' },
      { name: '检测结果与判定(修改情况)', field: 'checkResultRevise' },
      { name: '结论(存在问题)', field: 'conclusionFaq' },
      { name: '结论(修改情况)', field: 'conclusionRevise' },
      { name: '建议(存在问题)', field: 'adviseFaq' },
      { name: '建议(修改情况)', field: 'adviseRevise' },
      { name: '附件(存在问题)', field: 'appendixFaq' },
      { name: '附件(修改情况)', field: 'appendixRevise' },
      { name: '其他(存在问题)', field: 'otherFaq' },
      { name: '其他(修改情况)', field: 'otherRevise' },
      { name: '一审意见', field: 'compilingPersonnelOpinion' },
      { name: '一审修改情况', field: 'compilingPersonnelOpinionRevise' },
      { name: '二审意见', field: 'auditorOpinion' },
      { name: '二审修改情况', field: 'auditorOpinionRevise' },
      { name: '三审意见', field: 'threeApprovelOpinion' },
      { name: '三审修改情况', field: 'threeApprovelOpinionRevise' },
    ];
    const wordData = {
      projectName: '',
      projectSN: '',
      EnterpriseName: '',
      personsOfCompiling: '', // 报告编制人
      biuFaq: '无',
      biuRevise: '无',
      inspectionReferenceFaq: '无',
      inspectionReferenceRevise: '无',
      siteConditionsFaq: '无',
      siteConditionsRevise: '无',
      sampingDetecteFaq: '无',
      sampingDetecteRevise: '无',
      checkResultFaq: '无',
      checkResultRevise: '无',
      conclusionFaq: '无',
      conclusionRevise: '无',
      adviseFaq: '无',
      adviseRevise: '无',
      appendixFaq: '无',
      appendixRevise: '无',
      otherFaq: '无',
      otherRevise: '无',
      // 实验室审核报告单
      compilingPersonnelOpinion: '/',
      compilingPersonnelOpinionRevise: '/',
      auditorOpinion: '/',
      auditorOpinionRevise: '/',
      threeApprovelOpinion: '/',
      threeApprovelOpinionRevise: '/',
    };
    Cname.forEach(item => {
      const data = form_component_values.filter(item2 => {
        return item2.name === item.name;
      });
      if (data[0]) {
        if (data[0].value) {
          if (data[0].value === 'null') {
            wordData[item.field] = '/';
          } else {
            wordData[item.field] = data[0].value;
          }
        } else {
          wordData[item.field] = '/';
        }
      }
    });
    wordData.compilingPersonnelOpinion = wordData.compilingPersonnelOpinion === '/' ? '无' : wordData.compilingPersonnelOpinion;
    wordData.auditorOpinion = wordData.auditorOpinion === '/' ? '无' : wordData.auditorOpinion;
    wordData.threeApprovelOpinion = wordData.threeApprovelOpinion === '/' ? '无' : wordData.threeApprovelOpinion;
    if (data) {
      Object.keys(wordData).forEach(keyName => {
        if (data[keyName] && data[keyName] !== null) {
          wordData[keyName] = data[keyName];
        }
      });

      if (data.progress.reportFinalApproved && data.progress.reportFinalApproved.status === 2) { // 检测报告通过了话
        let time;
        if (data.progress.reportPrint && data.progress.reportPrint.status === 2) {
          time = moment(data.progress.reportPrint.completedTime).format('YYYY年MM月DD日');
        } else {
          time = moment(data.progress.reportFinalApproved.completedTime).format('YYYY年MM月DD日');
        }
        wordData.reportFinalApprovedStatus = time;
        let reviewTeamLeaderName = '';
        if (data.personsOfCompiling[0]) {
          const employee = data.personsOfCompiling[data.personsOfCompiling.length - 1];
          reviewTeamLeaderName = employee.name;
        }
        wordData.reviewTeamLeaderName = reviewTeamLeaderName;
        wordData.auditor = auditor;
      }

      if (data.progress.reportApproved && data.progress.reportApproved.status === 2) { // 实验室审核报告单通过了话
        wordData.reportApprovedTime = moment(data.progress.reportApproved.completedTime).format('YYYY年MM月DD日');
        wordData.compilingPersonnelName = compilingPersonnelName;
        wordData.compilingPersonnel = compilingPersonnel;
        wordData.auditor = auditor;
        wordData.compilingPersonnelSignDate = compilingPersonnelSignDate;
        wordData.auditorSignDate = auditorSignDate;
        wordData.threeApprovelDate = threeApprovelDate;
        wordData.threeApprovel = threeApprovel;
      }

      const sth = (await ctx.model.JcqlcProject.aggregate([
        { $match: projectSN ? { projectSN } : { _id } },
        {
          $project: {
            EnterpriseName: 1,
            labRecordFile: 1,
            completedTime: '$progress.createProject_time.completedTime',
            projectSN: 1,
          },
        },
      ]))[0] || {};

      // const year = new Date(sth.completedTime).getFullYear().toString();
      const year = await ctx.helper.getProjectYear(sth.projectSN);
      const tableBack = await ctx.helper.fillWord(
        ctx,
        '实验室审核报告单',
        wordData, {
          EnterpriseID: serviceOrgId,
          imgSize: [ 100, 100 ],
          adminOrgName: data.downApprovedWord,
          year,
          projectSN: sth.projectSN,
          prefix: 'approved',
        }
      );
      // 删除旧文件 (htt update 修改执行顺序，word生成成功后再删除源文件)
      await ctx.helper.delFile({
        model: ctx.model.JcqlcProject,
        match: projectSN ? { projectSN } : { _id },
        projectField: projectSN ? { fileName: '$labReportRecordUploadFile.staticName' } : { fileName: '$reportRecordUploadFile.staticName' },
        filePath: path.join(
          this.config.report_path,
          serviceOrgId,
          year,
          sth.projectSN
        ),
      });
      // 更新word文件名
      await ctx.model.JcqlcProject.updateOne({ serviceOrgId, projectSN }, {
        $set: {
          labReportRecordUploadFile: {
            staticName: tableBack.staticName,
            url: `/${serviceOrgId}/${year}/${sth.projectSN}/${tableBack.staticName}`,
          },
        },
      });
      console.log('实验室报告审核单labReportRecordUploadFile', tableBack);
      return tableBack;
    }
    return {};
  }

  async getProjectYear(projectSN) {
    const { ctx } = this;
    // 获取项目创建日期，如果是子项目那么获取主项目的创建日期
    const projectSN_array = projectSN.split('-');
    let creatProject = projectSN;
    if (projectSN_array.length === 3) {
      creatProject = projectSN_array[0] + '-' + projectSN_array[1];
    }
    const parentProject = await ctx.model.JcqlcProject.findOne({ projectSN: creatProject }, { 'progress.createProject_time': 1 }).lean();
    let year = '2022';
    if (parentProject && parentProject.progress && parentProject.progress.createProject_time && parentProject.progress.createProject_time.completedTime) {
      year = new Date(parentProject.progress.createProject_time.completedTime).getFullYear() + '';
    }
    return year;
  }
  async getRadiateProjectYear(projectSN, modelName = 'RadiateqlcProject') {
    const { ctx } = this;
    const project = await ctx.model[modelName].findOne({ projectSN }, { createdAt: 1 });
    let year = new Date().getFullYear(); // 默认时间
    if (project && project.createdAt) {
      year = new Date(project.createdAt).getFullYear();
    }
    return year + '';
  }
  // 给pdf添加签名
  async signPdf({ originatorEmployeeId, statusField, processInstanceField, processInstanceId, signIndex, currentEmployeeId, isComplete = false } = {}) {
    const { ctx } = this;
    const { config } = this.app;
    // 获取机构ID
    const projectInfo = await this.ctx.model.JcqlcProject.findOne({ [processInstanceField]: processInstanceId }, { EnterpriseName: 1, reportUploadFile: 1, serviceOrgId: 1, projectSN: 1, wordFileName: 1, noPassWordFileName: 1, labTestReportUploadFile: 1 }).lean();
    if (!projectInfo) {
      console.log(processInstanceId, '审批id未绑定到项目表中');
      return;
    }
    const { serviceOrgId, projectSN, EnterpriseName } = projectInfo;

    // 获取机构签章图片
    const serviceOrgInfo = await this.ctx.model.ServiceOrg.findOne({ _id: serviceOrgId }, { signatures: 1, qualifies: 1 }).lean();
    let signatureUrl = '';
    const signatures = serviceOrgInfo.signatures || [];
    signatures.forEach(item => {
      let url = item.url.replace('/static' + config.enterpriseUpload_http_path, '');
      url = path.join(config.enterpriseUpload_path, url);
      if (item.usage.includes('职业卫生')) {
        signatureUrl = url;
      }
    });
    // 获取资质证书
    let qualifyImg = '';
    if (statusField === 'reportFinalApproved') {
      const qualifieIds = serviceOrgInfo.qualifies || [];
      let qualifies = await this.ctx.model.DetectionMechanism.find({ mechanism_name: /职业卫生/, _id: { $in: qualifieIds } }, { img: 1 });
      qualifies = qualifies[0];
      if (!qualifies.img || qualifies.img === '' || path.extname(qualifies.img) === '') {
        throw new Error('机构缺少资质证书，请先添加!');
      }
      qualifyImg = path.join(config.upload_path, qualifies.img);
    }
    // 钉钉审核和文件信息
    const approvalSignFileIno = {
      reportFinalApproved: { // 报告审批
        filePthStart: 'static',
        filePathField: [ 'reportUploadFile.url' ], // `${app.config.static.prefix}${app.config.report_http_path}/${jcqlcID}/${projectYear}/${projectSN}/${fileName}`
        mergeFiles: [ 'reportUploadFile.url', 'wordFileName.signedUrl', 'noPassWordFileName.signedUrl' ], // 需要合并的文件
        outPathFile: [ 'officialReportUploadFile.stampUrl' ],
        outPathFileName: '正式稿',
        noStampFiled: [ 'officialReportUploadFile.noStampUrl' ],
        // outPathFileName2: 'officialReportUploadFile.name',
        nodeField: 'officialReport',
        addSeamStamp: true,
        page: -1,
        addPage: {
          page: 1,
          imagePath: qualifyImg,
        },
        // mergeFiles: [ 'reportUploadFile.url', 'wordFileName.signedUrl' ],
        userSignConfig: [{ // 签名位置
          text: '编写人',
          offsetX: 370,
          offsetY: 15,
          isSignFromApproval: true, // 签名是否来自于审批
          approvalNodeIndex: 0,
        }, {
          text: '审核人',
          offsetX: 370,
          offsetY: 15,
          isSignFromApproval: true, // 签名是否来自于审批
          approvalNodeIndex: 2,
        }, {
          text: '签发人',
          offsetX: 370,
          offsetY: 15,
          isSignFromApproval: true, // 签名是否来自于审批
          approvalNodeIndex: 3,
        }],
        signConfig: [{ // 签章位置
          pages: [ 0 ],
          y: -20,
          text: '月编',
          x: 'center', // x轴居中
        }, {
          text: '盖章',
          offsetX: -60, // 偏移量
          offsetY: -55, // 偏移量
        }],
        textConfig: [{
          text: '盖章',
          offsetX: 0,
          offsetY: -5,
          value: moment(new Date()).format('YYYY-MM-DD'),
        }],
        signImagePath: signatureUrl,
      },
      reportApproved: { // 检测结果报告单审批
        addSeamStamp: true,
        filePthStart: 'static',
        filePathField: [ 'wordFileName.url', 'noPassWordFileName.url' ],
        filePathField2: 'labTestReportUploadFile.url',
        outPathFile: [ 'wordFileName.signedUrl', 'noPassWordFileName.signedUrl' ],
        signImagePath: process.cwd() + '/app/public/images/放射用章.png',
        // signConfig: {
        //   signPoint: { x: 100, y: 100 },
        // },
        noStampFiled: [ 'wordFileName.noStampUrl', 'noPassWordFileName.noStampUrl' ],
        outPathFileName: '检测结果报告单',
        signConfig: [{ // 签章位置
          text: '盖章',
          // offsetX: -50, // 偏移量
        }, {
          pages: [ 0 ],
          x: 'center',
          y: 130,
        }],
        textConfig: [{
          pages: [ 0 ],
          x: 'center', // x轴居中
          y: 140,
          width: 40,
          value: moment(new Date()).format('YYYY-MM'),
        }, {
          text: '日期：',
          offsetX: 63,
          offsetY: 31,
          value: moment(new Date()).format('YYYY-MM-DD'),
        }],
        userSignConfig: [ // 审核人签名位置
          { text: '编制人：',
            offsetX: 80, // 偏移量 图片的位置放置在text所在位置+偏移量
            offsetY: 17,
            isSignFromApproval: true, // 签名是否来自于审批
            approvalNodeIndex: 0 },
          { text: '审核人：',
            offsetX: 80, // 偏移量 图片的位置放置在text所在位置+偏移量
            offsetY: 17,
            isSignFromApproval: true, // 签名是否来自于审批
            approvalNodeIndex: 1 },
          { text: '批准人：',
            offsetX: 80, // 偏移量 图片的位置放置在text所在位置+偏移量
            offsetY: 17,
            isSignFromApproval: true, // 签名是否来自于审批
            approvalNodeIndex: 2 },
        ],
      },
    };
    const configInfo = approvalSignFileIno[statusField];
    if (!configInfo) return;
    let needSign = false;
    configInfo.userSignConfig.forEach((item, index) => {
      if (item.approvalNodeIndex === signIndex) {
        needSign = true;
        signIndex = index;
      }
    });
    if (!needSign && !isComplete) return; // 没匹配到需要签名
    const jcqlcID = serviceOrgId;
    const projectYear = await this.getProjectYear(projectSN);
    const pathDir = path.resolve(path.join(config.report_path, serviceOrgId, projectYear, projectSN));
    for (let i = 0; i < configInfo.filePathField.length; i++) {
      // 未通过参数认证的文件不需要批准人签名
      if (configInfo.filePathField[i] === 'noPassWordFileName.url' &&
        configInfo.userSignConfig[signIndex]?.text === '批准人：'
      ) continue;
      const filePath2 = configInfo.filePathField2 ? this.getVal(projectInfo, configInfo.filePathField2) : null;
      let filePath = this.getVal(projectInfo, configInfo.filePathField[i]);
      filePath = filePath2 && i === 0 ? filePath2 : filePath;
      // console.log(filePath, '盖章的文件路径？？');
      const staticPath = filePath;
      if (!filePath) continue;
      filePath = filePath.replace('/static' + config.report_http_path, '');
      filePath = path.join(config.report_path, filePath);

      let signConfig = configInfo.signConfig;
      const addSeamStamp = configInfo.addSeamStamp;
      let signImagePath = configInfo.signImagePath;
      let signFileInfo = {};
      if (filePath.indexOf('?') !== -1) {
        const temp = filePath.split('?');
        filePath = temp[0];
      }
      const originFilePath = filePath;
      // 原文件就是pdf文件
      if (path.extname(filePath) === '.pdf') {
        const { name } = path.parse(filePath);
        const signFileName = name + '_sign';
        // const originFilePath = filePath;
        filePath = filePath.replace(name, signFileName);
        if (signIndex === 0) {
          fs.writeFileSync(filePath, fs.readFileSync(originFilePath));
        }
      }
      if (!isComplete) { // 添加每个审批人的签字
        if (path.extname(filePath) === '.docx') {
          if (!fs.existsSync(filePath.replace('docx', 'pdf'))) { // 转换成pdf
            await this.ctx.helper.docx2pdf(staticPath, filePath.replace('docx', 'pdf'));
          }
        }
        filePath = filePath.replace('.docx', '.pdf');
        signConfig = configInfo.userSignConfig[signIndex];
        let employeeInfo = null;
        if (signConfig.signField === 'origin') { // 获取该审批节点签名
          // 获取发起人的签名
          employeeInfo = await this.ctx.model.ServiceEmployee.findOne({ _id: originatorEmployeeId }, { name: 1, signPath: 1 });
        } else {
          employeeInfo = await this.ctx.model.ServiceEmployee.findOne({ _id: currentEmployeeId }, { name: 1, signPath: 1 });
        }
        // 获取签名路径
        // console.log(signConfig, signIndex, employeeInfo.signPath, '签名信息');
        if (employeeInfo.signPath) {
          signImagePath = path.resolve(path.join(config.sign_path, serviceOrgId, employeeInfo.signPath)); // 文件目录;
          console.log(projectSN, '项目编号', signIndex);
          signFileInfo = await this.ctx.helper.drawImageToPdfAndSign(filePath, signImagePath, false, false, signConfig);
        }
      } else { // 审批完成进行盖章和电子签名
        filePath = filePath.replace('.docx', '.pdf');
        signFileInfo = await this.ctx.helper.drawImageToPdfAndSign(filePath, signImagePath, true, addSeamStamp, signConfig, configInfo.textConfig);
        if (configInfo.mergeFiles || configInfo.addPage) {
          const tempArr = [];
          if (configInfo.mergeFiles) {
            for (let j = 0; j < configInfo.mergeFiles.length; j++) {
              let item = configInfo.mergeFiles[j];
              const field = item;
              item = this.getVal(projectInfo, item);
              if (!item) continue;
              item = item.replace('/static' + config.report_http_path, '');
              const splitUrl = item.split(this.ctx.app.config.report_path);
              item = path.join(config.report_path, splitUrl[splitUrl.length - 1]);
              if (field === configInfo.filePathField?.[i] && path.extname(originFilePath) === '.pdf') {
                const { name } = path.parse(item);
                // configInfo.mergeFiles[j] = item.replace(name, name + '_sign');
                tempArr.push(item.replace(name, name + '_sign'));
              } else {
                // configInfo.mergeFiles[j] = item.replace('.docx', '.pdf');
                tempArr.push(item.replace('.docx', '.pdf'));
              }
            }
          }
          const outputFilePath = path.join(pathDir, `${projectSN}_${EnterpriseName}_${configInfo.outPathFileName}.pdf`);
          signFileInfo = await this.ctx.helper.mergeFileAndAddPage(filePath, outputFilePath, tempArr, configInfo.addPage);
        }
      }

      if (signFileInfo) {
        // 更新签名后的文件
        const setFields = {
          [configInfo?.outPathFile?.[i]]: '/' + signFileInfo.filePath,
        };
        if (configInfo?.noStampFiled?.[i] && signFileInfo?.noStampFilePath) {
          setFields[configInfo?.noStampFiled?.[i]] = '/' + signFileInfo?.noStampFilePath;
        }
        if (configInfo?.outPathFileName2?.[i]) {
          setFields[configInfo?.outPathFileName2?.[i]] = `${projectSN}_${EnterpriseName}_${configInfo.outPathFileName}.pdf`;
        }
        if (configInfo.nodeField) {
          setFields[`progress.${configInfo.nodeField}`] = {
            status: 2,
            completedTime: new Date(),
          };
        }
        await this.ctx.model.JcqlcProject.updateOne({ _id: projectInfo._id }, { $set: setFields });
        // 创建盖章记录
        await this.ctx.model.StampRecord.create({
          serviceOrgId,
          modelId: projectInfo._id,
          modelName: 'JcqlcProject',
          stampFile: configInfo.outPathFileName,
          stampFilePath: '/' + signFileInfo.filePath,
          optServiceEmployeeId: currentEmployeeId,
        });
      }
    }
    // 检测报告审核----报告生成---此处只按新的模板生成
    if (isComplete && statusField === 'reportFinalApproved') {
      // 获得签名
      const joinSignPath = p => {
        if (!p) { return ''; }
        return path.join(config.sign_path, jcqlcID, p);
      };
      const approve = await ctx.model.ApproveRecord.findOne({ processInstanceId }).lean();
      for (const record of approve.operation_records) {
        record.dateDesc = moment(record.date).format('YYYY年MM月DD日');
        let employee = await ctx.model.ServiceEmployee.findOne(
          { _id: record.serviceEmployeeId },
          { name: 1, signPath: 1 }
        );
        employee = JSON.parse(JSON.stringify(employee));
        record.employeeName = employee?.name || '未知'; // 空值保护
        record.signPath = path.join(config.sign_path, jcqlcID, employee?.signPath); // 签名路径
      }
      // const opeRecords = await Promise.all(approve.operation_records.map(async i => {
      //   const record = { ...i }; // 创建副本避免修改原对象
      //   record.dateDesc = moment(record.date).format('YYYY年MM月DD日');
      //   return record;
      // }));
      console.log('进来了嘛111');
      const dingForm = await ctx.service.dingTalkWorkFlow.getFormDefaultData(approve.form_component_values, 'JCReportReviewRecord331');
      dingForm.forEach((item, i) => {
        const letter = String.fromCharCode(97 + i); // 97对应字母a
        item.name = `${letter}）${item.name}`;
        if (item.value === '否') {
          item.vDesc = '是☐否√';
        } else if (item.value === '是') {
          item.vDesc = '是√否☐';
        }
      });
      const personsOutProjectLookup = {
        $lookup: {
          from: 'serviceEmployee',
          let: { personIds: { $ifNull: [ '$personsOutProject', []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: [ '$_id', '$$personIds' ],
                },
              },
            },
            {
              $project: {
                name: 1,
                signPath: 1,
              },
            },
          ],
          as: 'personsOutProjectDetails',
        },
      };
      const personChangeLookup = {
        $lookup: {
          from: 'serviceEmployee',
          let: { personIds: { $ifNull: [ '$personChange', []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: [ '$_id', '$$personIds' ],
                },
              },
            },
            {
              $project: {
                name: 1,
                signPath: 1,
              },
            },
          ],
          as: 'personChangeDetails',
        },
      };
      const personSupervisorLookup = {
        $lookup: {
          from: 'serviceEmployee',
          let: { personIds: { $ifNull: [ '$personSupervisor', []] } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: [ '$_id', '$$personIds' ],
                },
              },
            },
            {
              $project: {
                name: 1,
                signPath: 1,
              },
            },
          ],
          as: 'personSupervisorDetails',
        },
      };
      const sth = (await ctx.model.JcqlcProject.aggregate([
        { $match: { _id: projectInfo._id } },
        personsOutProjectLookup,
        personChangeLookup,
        personSupervisorLookup,
        {
          $project: {
            EnterpriseName: 1,
            labRecordFile: 1,
            completedTime: '$progress.createProject_time.completedTime',
            projectSN: 1,
            projectName: 1,
            personsOutProject: '$personsOutProject',
            personsOutProjectDetails: '$personsOutProjectDetails',
            personChange: '$personChange',
            personChangeDetails: '$personChangeDetails',
            personSupervisor: '$personSupervisor',
            personSupervisorDetails: '$personSupervisorDetails',
          },
        },
      ]))[0] || {};
      const personsOutProject = joinSignPath(sth.personsOutProjectDetails?.[0]?.signPath);
      const { EnterpriseID } = await this.ctx.model.ServiceEmployee.findOne({ _id: currentEmployeeId }, { name: 1, signPath: 1, EnterpriseID: 1 });
      const year = await ctx.helper.getProjectYear(sth.projectSN);
      // const personChange = joinSignPath(sth.personChangeDetails?.[0]?.signPath);
      const tableBack = await ctx.helper.fillWord(
        ctx,
        '检测报告审核记录表331',
        {
          dingForm,
          opeRecords: approve.operation_records,
          EnterpriseName: sth.EnterpriseName,
          projectSN: sth.projectSN,
          projectName: sth.projectName,
          personsOutProject,
          // personChange, // 项目负责人字段
          EnterpriseID,
        },
        {
          EnterpriseID: serviceOrgId,
          // imgSize: [ 100, 100 ],
          adminOrgName: '',
          year,
          projectSN: sth.projectSN,
          prefix: 'approved',
          emptyTxt: '',
        }
      );

      // 删除旧文件 (htt update 修改执行顺序，word生成成功后再删除源文件)
      await ctx.helper.delFile({
        model: ctx.model.JcqlcProject,
        match: { _id: projectInfo._id },
        projectField: { fileName: '$reportRecordUploadFile.staticName' },
        filePath: path.join(
          this.config.report_path,
          jcqlcID,
          year,
          sth.projectSN
        ),
      });
      await ctx.model.JcqlcProject.updateOne({ _id: projectInfo._id }, {
        $set: {
          reportRecordUploadFile: {
            staticName: tableBack.staticName,
            url: `/${jcqlcID}/${year}/${sth.projectSN}/${tableBack.staticName}`,
          },
        },
      });
      console.log('检测报告审核记录reportRecordUploadFile', tableBack);
    } else if (isComplete && statusField === 'reportApproved') {
      const projectInfo = await this.ctx.model.JcqlcProject.findOne({ reportProcessInstanceId: processInstanceId }, { projectSN: 1 }).lean();
      // 实验室报告审核记录
      this.downloadReport({ _id: projectInfo._id, projectSN: projectInfo.projectSN });
    }
    // let filePath2;
    // if (configInfo.filePathField2) {
    //   filePath2 = this.getVal(projectInfo, configInfo.filePathField2);
    // }
    // let filePath = this.getVal(projectInfo, configInfo.filePathField);
    // filePath = filePath2 || filePath;
    // // console.log(filePath, '盖章的文件路径？？');
    // const staticPath = filePath;

    // filePath = filePath.replace('/static' + config.report_http_path, '');
    // filePath = path.join(config.report_path, filePath);
    // let signConfig = configInfo.signConfig;
    // const addSeamStamp = configInfo.addSeamStamp;
    // let signImagePath = configInfo.signImagePath;
    // let signFileInfo = {};
    // if (filePath.indexOf('?') !== -1) {
    //   const temp = filePath.split('?');
    //   filePath = temp[0];
    // }
    // const originFilePath = filePath;
    // // 原文件就是pdf文件
    // if (path.extname(filePath) === '.pdf') {
    //   const { name } = path.parse(filePath);
    //   const signFileName = name + '_sign';
    //   const originFilePath = filePath;
    //   filePath = filePath.replace(name, signFileName);
    //   if (!fs.existsSync(filePath)) {
    //     fs.writeFileSync(filePath, fs.readFileSync(originFilePath));
    //   }
    // }
    // if (!isComplete) { // 添加每个审批人的签字
    //   if (path.extname(filePath) === '.docx') {
    //     if (!fs.existsSync(filePath.replace('docx', 'pdf'))) {
    //     // 转换成pdf
    //       await this.ctx.helper.docx2pdf(staticPath, filePath.replace('docx', 'pdf'));
    //     }
    //   }
    //   filePath = filePath.replace('.docx', '.pdf');
    //   signConfig = configInfo.userSignConfig[signIndex];
    //   // 获取该审批节点签名
    //   let employeeInfo = null;
    //   if (signConfig.signField === 'origin') {
    //     // 发起人
    //     // 获取发起人的签名
    //     employeeInfo = await this.ctx.model.ServiceEmployee.findOne({ _id: originatorEmployeeId }, { name: 1, signPath: 1 });
    //   } else {
    //     employeeInfo = await this.ctx.model.ServiceEmployee.findOne({ _id: currentEmployeeId }, { name: 1, signPath: 1 });
    //   }
    //   // 获取签名路径
    //   // console.log(signConfig, signIndex, employeeInfo.signPath, '签名信息');
    //   if (employeeInfo.signPath) {

    //     signImagePath = path.resolve(path.join(config.sign_path, serviceOrgId, employeeInfo.signPath)); // 文件目录;
    //     console.log(projectSN, '项目编号', signIndex);
    //     signFileInfo = await this.ctx.helper.drawImageToPdfAndSign(filePath, signImagePath, false, false, signConfig);
    //   }

    // } else { // 审批完成进行盖章和电子签名
    //   filePath = filePath.replace('.docx', '.pdf');
    //   signFileInfo = await this.ctx.helper.drawImageToPdfAndSign(filePath, signImagePath, true, addSeamStamp, signConfig, configInfo.textConfig);
    //   if (configInfo.mergeFiles || configInfo.addPage) {
    //     if (configInfo.mergeFiles) {
    //       for (let i = 0; i < configInfo.mergeFiles.length; i++) {
    //         let item = configInfo.mergeFiles[i];
    //         const field = item;
    //         item = this.getVal(projectInfo, item);
    //         if (!item) {
    //           return;
    //         }
    //         item = item.replace('/static' + config.report_http_path, '');
    //         item = path.join(config.report_path, item);
    //         // configInfo.mergeFiles[i] = item.replace('.docx', '.pdf');
    //         if (field === configInfo.filePathField && path.extname(originFilePath) === '.pdf') {
    //           const { name } = path.parse(item);
    //           configInfo.mergeFiles[i] = item.replace(name, name + '_sign');
    //         } else {
    //           configInfo.mergeFiles[i] = item.replace('.docx', '.pdf');
    //         }
    //       }
    //     }
    //     const outputFilePath = path.join(pathDir, `${projectSN}_${EnterpriseName}_${configInfo.outPathFileName}.pdf`);
    //     signFileInfo = await this.ctx.helper.mergeFileAndAddPage(filePath, outputFilePath, configInfo.mergeFiles, configInfo.addPage);
    //   }
    // }
    // if (signFileInfo) {
    //   // 更新签名后的文件
    //   const setFields = {
    //     [configInfo.outPathFile]: '/' + signFileInfo.filePath,
    //   };
    //   if (configInfo.noStampFiled && signFileInfo.noStampFilePath) {
    //     setFields[configInfo.noStampFiled] = '/' + signFileInfo.noStampFilePath;
    //   }
    //   if (configInfo.outPathFileName2) {
    //     setFields[configInfo.outPathFileName2] = `${projectSN}_${EnterpriseName}_${configInfo.outPathFileName}.pdf`;
    //   }
    //   if (configInfo.nodeField) {
    //     setFields[`progress.${configInfo.nodeField}`] = {
    //       status: 2,
    //       completedTime: new Date(),
    //     };
    //   }
    //   await this.ctx.model.JcqlcProject.updateOne({ _id: projectInfo._id }, { $set: setFields });
    //   // 创建盖章记录
    //   await this.ctx.model.StampRecord.create({
    //     serviceOrgId,
    //     modelId: projectInfo._id,
    //     modelName: 'JcqlcProject',
    //     stampFile: configInfo.outPathFileName,
    //     stampFilePath: '/' + signFileInfo.filePath,
    //     optServiceEmployeeId: currentEmployeeId,
    //   });
    // }
  }

  getVal(projectInfo, filePathField, filePathField2) {
    const fileField = filePathField2 || filePathField;
    const filePathFileds = fileField.split('.');
    let filePath = projectInfo[filePathFileds[0]];
    if (!filePath) return null;
    let index = 1;
    while (index < filePathFileds.length) {
      if (!filePath[filePathFileds[index]] || filePath[filePathFileds[index]] === '') return null;
      filePath = filePath[filePathFileds[index]];
      index++;
    }
    return filePath;
  }
  async updateOne(query, setFieldObj) {
    await this.ctx.model.JcqlcProject.updateOne(query, { $set: setFieldObj });
  }

  async updateModifyApprovalRecord(dingTalkInfo, qlcProjectStatus, modifyTarget) {
    const { form_component_values } = dingTalkInfo;
    const projectSNField = form_component_values.find(item => item.name === '项目编号');
    if (projectSNField) {
      const projectSN = projectSNField.value;
      const project = await this.ctx.model.JcqlcProject.findOne({ projectSN }, { _id: 1 });
      const filter = {
        projectId: project._id,
        status: 1,
        modifyTarget,
      };

      const reviewer = [];

      if (qlcProjectStatus > 1) {
        const operation_records = dingTalkInfo.operation_records.slice(1, 3);

        for (let i = 1; i < operation_records.length; i++) {
          const item = operation_records[i];
          reviewer.push({
            dingUserId: item.userid,
            serviceEmployeeId: item.serviceEmployeeId,
            type: i === 0 ? 'firstTrial' : 'secondTrial',
          });
        }
      }
      // 更新审批状态 ->
      // 如果是同意
      if (qlcProjectStatus === 2) {
        await this.ctx.model.ModifyApprovalRecord.updateOne(filter, {
          $set: {
            approvalResult: 1,
            approvalResultTime: new Date(),
            reviewer,
          },
        });
        if (modifyTarget === 'spotRecord') {
          // 修改现场记录单的状态
          await this.ctx.model.JcqlcProject.updateOne({ _id: project._id }, {
            $set: {
              'subProjects.0.spotProjectCompletedStatus': 1,
              'progress.spotRecord': {
                status: 1,
              },
            },
          });
        } else if (modifyTarget === 'samplingPlan') {
          // 将方案是否通过修改审核的字段改为true
          await this.ctx.model.JcqlcProject.updateOne({ _id: project._id }, { samplingPlanModifiable: true });
        }
      } else if (qlcProjectStatus === 3) {
        // 如果是拒绝
        await this.ctx.model.ModifyApprovalRecord.updateOne(filter, {
          $set: {
            approvalResult: 2,
            approvalResultTime: new Date(),
            modifyAccomplishTime: new Date(),
            status: 0,
            reviewer,
          },
        });
      }
    }
  }

  // htt 修复eslint author：jhw
  async uploadAmendStatus(qlcProjectStatus, form_component_values, by) {
    const component_values = form_component_values.filter(item => item.name === '项目编号');
    const { value: projectSN } = component_values[0] || {};
    let res;
    if (by === 'report') {
      if (qlcProjectStatus === 2) {
        res = await this.ctx.model.JcqlcProject.updateOne({ projectSN }, { $set: { labModifiable: false } });
      }
    }

    if (by === 'amend') {
      if (qlcProjectStatus === 2) {
        res = await this.ctx.model.JcqlcProject.updateOne(
          { projectSN },
          { $set: { labModifiable: true } }
        );
      } else {
        res = await this.ctx.model.JcqlcProject.updateOne(
          { projectSN },
          { $set: { labModifiable: false, labApprodPattern: 'End' } }
        );
      }
      console.log(res);
    }
  }

  async uploadfinishProjectStatus(form_component_values, approvedResult) {
    let projectSN,
      completedTime;

    form_component_values.forEach(ele => {
      if (ele.name === '项目编号') projectSN = ele.value;
      if (ele.name === '完成日期') completedTime = ele.value;
    });

    if (approvedResult === 'COMPLETED') {
      // DA不需要归档,设置completeStatus状态
      if (projectSN.includes('ZJDPDA')) {
        await this.ctx.model.JcqlcProject.updateOne(
          { projectSN },
          {
            $set: {
              completeStatus: 1,
              completeTime: new Date(),
              'progress.finishProjectApproved.status': 2,
              'progress.finishProjectApproved.completedTime': new Date(),
            },
          });
      } else {
        await this.ctx.model.JcqlcProject.updateOne(
          { projectSN },
          {
            $set: {
              'progress.reportArchive.completedTime': completedTime,
              'progress.reportArchive.status': 2,
            },
          });
      }
    } else if (approvedResult === 'REFUSE') {
      if (projectSN.includes('ZJDPDA')) {
        await this.ctx.model.JcqlcProject.updateOne(
          { projectSN },
          {
            $set: {
              completeStatus: 0,
              completeTime: '',
              'progress.finishProjectApproved.status': 0,
              'progress.finishProjectApproved.completedTime': '',
            },
          });
      }
    }
  }
  async downSchemeReport(res) {
    const { ctx } = this;
    const jcqlcProjectInfo = await ctx.model.JcqlcProject.findOne({ samplingPlanApproveId: res.info.processInstanceId });
    let result = {};
    if ([ '职业病危害预评价', '职卫专篇', '职业病危害现状评价', '职业病危害控制效果评价' ].includes(jcqlcProjectInfo.serviceType)) {
      result = await this.PJDownSchemeReport(res.info, jcqlcProjectInfo);
    } else {
      result = await this.JCDownSchemeReport(res.info, jcqlcProjectInfo);
    }
    console.log(412, result);
  }
  // 下载检测方案审核记录单
  async JCDownSchemeReport(approve, projectInfo) {
    const { ctx, config } = this;
    const serviceOrgId = projectInfo.serviceOrgId;
    const field = {};

    field.companyName = projectInfo.EnterpriseName;
    field.projectSN = projectInfo.projectSN;
    field.serviceType = projectInfo.serviceType;

    approve.operation_records = approve.operation_records.filter(ele => ele.operation_type === 'EXECUTE_TASK_NORMAL');
    field.formDefaultData = await ctx.service.dingTalkWorkFlow.getFormDefaultData(approve.form_component_values, 'JCSamplingPlanApprove331');
    const personInChargeRecord = approve.operation_records[0];// 项目负责人
    const personInCharge = await ctx.model.ServiceEmployee.findOne({ _id: personInChargeRecord.serviceEmployeeId }, { signPath: 1, name: 1 });
    // 获取项目负责人签名/日期
    field.personInChargeDate = moment(personInChargeRecord.date).format('YYYY年MM月DD日');
    // 获取检测人签字/日期
    const detectionManagerRecord = approve.operation_records[1];// 检测技术负责人
    const detectionManager = await ctx.model.ServiceEmployee.findOne({ _id: detectionManagerRecord.serviceEmployeeId }, { signPath: 1, name: 1 });
    // detectionManagerSignPath = detectionManager.signPath
    field.detectionManagerDate = moment(detectionManagerRecord.date).format('YYYY年MM月DD日');
    // 检测技术负责人
    if (!detectionManager) {

      return { essMsgs: [ `暂无职业卫生检测技术负责人${config.samplingPlanApproval ? '' : '，请到"职能与代办-角色分配"中完善'}` ], code: -1 };
    }
    if (detectionManager.signPath) {
      const signPath = path.join(ctx.app.config.sign_path, serviceOrgId, detectionManager.signPath);
      if (!fs.existsSync(signPath)) {
        return { essMsgs: [ `职业卫生检测技术负责人：${detectionManager.name}签名无效，请先上传` ], code: -1 };
      }
      field.detectionManagerSignPath = signPath;


    } else {
      return { essMsgs: [ `职业卫生检测技术负责人：${detectionManager.name}未上传签名，请先上传` ], code: -1 };
    }

    // 负责人
    if (!personInCharge) {
      return { essMsgs: [ `暂无项目经理${config.samplingPlanApproval ? '' : '，请到项目详情中选择'}` ], code: -1 };
    }
    if (personInCharge.signPath) {
      const signPath = path.join(ctx.app.config.sign_path, serviceOrgId, personInCharge.signPath);
      if (!fs.existsSync(signPath)) {
        return { essMsgs: [ `项目经理：${personInCharge.name}签名无效，请先上传` ], code: -1 };
      }
      field.personInChargeSignPath = signPath;


    } else {
      return { essMsgs: [ `项目经理：${personInCharge.name}未上传签名，请先上传` ], code: -1 };
    }

    // 监督员 不确定是否走审批流程
    let supervisor = '';
    if (!projectInfo?.personSupervisor?.length) {
      return { essMsgs: [ '监督人未选择' ], code: -1 };
    }
    supervisor = await ctx.model.ServiceEmployee.findOne({ _id: projectInfo.personSupervisor[0] });
    field.EvaluationManagerDate = field.personInChargeDate;
    if (supervisor.signPath) {
      const signPath = path.join(ctx.app.config.sign_path, serviceOrgId, supervisor.signPath);
      if (!fs.existsSync(signPath)) {
        return { essMsgs: [ `监督人：${supervisor.name}签名无效，请先上传` ], code: -1 };
      }
      field.supervisoreSignPath = signPath;

    } else {
      return { essMsgs: [ `监督员：${supervisor.name}未上传签名，请先上传` ], code: -1 };
    }

    let personChange = projectInfo.personChange;
    // 负责人 不确定是否要走审批流程
    if (!personChange || !personChange.length) {
      return { essMsgs: [ '暂未分配项目负责人，请先分配' ], code: -1 };
    }
    personChange = await ctx.model.ServiceEmployee.findOne({ _id: personChange[0] }, { name: 1, signPath: 1 });
    if (!personChange) {
      return { essMsgs: [ '暂未分配项目负责人，请先分配' ], code: -1 };
    }
    if (personChange.signPath) {
      const signPath = path.join(ctx.app.config.sign_path, serviceOrgId, personChange.signPath);
      if (!fs.existsSync(signPath)) {
        return { essMsgs: [ `项目负责人：${personChange.name}签名无效，请先上传` ], code: -1 };
      }
      field.personChangeSignPath = signPath;


    } else {
      return { essMsgs: [ `项目负责人：${personChange.name}未上传签名，请先上传` ], code: -1 };
    }

    const data = await ctx.helper.fillMergerWord(
      ctx,
      '331采样和测量方案审核记录表',
      field,
      {
        year: await ctx.helper.getRadiateProjectYear(projectInfo.projectSN, 'JcqlcProject'),
        projectSN: projectInfo.projectSN,
        oldWordName: '',
      }
    );
    await ctx.model.JcqlcProject.updateOne({ _id: projectInfo._id }, { $set: { samplingPlanApproveFile: { url: data.path, name: data.staticName } } });
    return data;
  }


  // 下载检测方案审核记录单
  async PJDownSchemeReport(approve, projectInfo) {
    const { ctx, config } = this;
    const serviceOrgId = projectInfo.serviceOrgId;
    const field = {};

    field.companyName = projectInfo.EnterpriseName;
    field.projectSN = projectInfo.projectSN;
    field.serviceType = projectInfo.serviceType;

    approve.operation_records = approve.operation_records.filter(ele => ele.operation_type !== 'START_PROCESS_INSTANCE' && ele.operation_type !== 'PROCESS_CC');
    field.formDefaultData = await ctx.service.dingTalkWorkFlow.getFormDefaultData(approve.form_component_values, 'PJSamplingPlanApprove331');
    // 获取项目负责人签名/日期
    const personInChargeRecord = approve.operation_records[0];// 项目负责人
    field.personInChargeDate = moment(personInChargeRecord.date).format('YYYY年MM月DD日');
    const personInCharge = await ctx.model.ServiceEmployee.findOne({ _id: personInChargeRecord.serviceEmployeeId }, { signPath: 1, name: 1 });

    // 获取检测人签字/日期
    const detectionManagerRecord = approve.operation_records[1];// 检测技术负责人
    const detectionManager = await ctx.model.ServiceEmployee.findOne({ _id: detectionManagerRecord.serviceEmployeeId }, { signPath: 1, name: 1 });
    // detectionManagerSignPath = detectionManager.signPath
    field.detectionManagerDate = moment(detectionManagerRecord.date).format('YYYY年MM月DD日');


    // 获取评价技术负责人签字/日期
    const EvaluationManagerRecord = approve.operation_records[2];// 检测技术负责人
    const EvaluationManager = await ctx.model.ServiceEmployee.findOne({ _id: EvaluationManagerRecord.serviceEmployeeId }, { signPath: 1, name: 1 });
    field.EvaluationManagerDate = moment(EvaluationManagerRecord.date).format('YYYY年MM月DD日');

    // 评价技术负责人
    if (!EvaluationManager) {
      return { essMsgs: [ `暂无职业卫生评价技术负责人${config.samplingPlanApproval ? '' : '，请到"职能与代办-角色分配"中完善'}` ], code: -1 };
    }
    if (EvaluationManager.signPath) {
      const signPath = path.join(ctx.app.config.sign_path, serviceOrgId, EvaluationManager.signPath);
      if (!fs.existsSync(signPath)) {
        return { essMsgs: [ `职业卫生评价技术负责人：${EvaluationManager.name}签名无效，请先上传` ], code: -1 };
      }
      field.EvaluationManagerSignPath = signPath;

    } else {
      return { essMsgs: [ `职业卫生评价技术负责人：${EvaluationManager.name}未上传签名，请先上传` ], code: -1 };
    }

    // 检测技术负责人
    if (!detectionManager) {
      return { essMsgs: [ `暂无职业卫生检测技术负责人${config.samplingPlanApproval ? '' : '，请到"职能与代办-角色分配"中完善'}` ], code: -1 };
    }
    if (detectionManager.signPath) {
      const signPath = path.join(ctx.app.config.sign_path, serviceOrgId, detectionManager.signPath);
      if (!fs.existsSync(signPath)) {
        return { essMsgs: [ `职业卫生检测技术负责人：${detectionManager.name}签名无效，请先上传` ], code: -1 };
      }
      field.detectionManagerSignPath = signPath;


    } else {
      return { essMsgs: [ `职业卫生检测技术负责人：${detectionManager.name}未上传签名，请先上传` ], code: -1 };
    }

    // 项目经理
    if (!personInCharge) {
      return { essMsgs: [ `暂无项目经理${config.samplingPlanApproval ? '' : '，请到项目详情中选择'}` ], code: -1 };
    }
    if (personInCharge.signPath) {
      const signPath = path.join(ctx.app.config.sign_path, serviceOrgId, personInCharge.signPath);
      if (!fs.existsSync(signPath)) {
        return { essMsgs: [ `项目经理：${personInCharge.name}签名无效，请先上传` ], code: -1 };
      }
      field.personInChargeSignPath = signPath;


    } else {
      return { essMsgs: [ `项目经理：${personInCharge.name}未上传签名，请先上传` ], code: -1 };
    }

    // 监督员 不确定是走审批流程
    let supervisor = '';
    if (!projectInfo?.personSupervisor?.length) {
      return { essMsgs: [ '监督人未选择' ], code: -1 };
    }
    supervisor = await ctx.model.ServiceEmployee.findOne({ _id: projectInfo.personSupervisor[0] });
    if (supervisor.signPath) {
      const signPath = path.join(ctx.app.config.sign_path, serviceOrgId, supervisor.signPath);
      if (!fs.existsSync(signPath)) {
        return { essMsgs: [ `监督人：${supervisor.name}签名无效，请先上传` ], code: -1 };
      }
      field.supervisoreSignPath = signPath;


    } else {
      return { essMsgs: [ `监督员：${supervisor.name}未上传签名，请先上传` ], code: -1 };
    }

    let personChange = projectInfo.personChange;
    // 负责人 不确定是否要走审批流程
    if (!personChange || !personChange.length) {
      return { essMsgs: [ '暂未分配项目负责人，请先分配' ], code: -1 };
    }
    personChange = await ctx.model.ServiceEmployee.findOne({ _id: personChange[0] }, { name: 1, signPath: 1 });
    if (!personChange) {
      return { essMsgs: [ '暂未分配项目负责人，请先分配' ], code: -1 };
    }
    if (personChange.signPath) {
      const signPath = path.join(ctx.app.config.sign_path, serviceOrgId, personChange.signPath);
      if (!fs.existsSync(signPath)) {
        return { essMsgs: [ `项目负责人：${personChange.name}签名无效，请先上传` ], code: -1 };
      }
      field.personChangeSignPath = signPath;


    } else {
      return { essMsgs: [ `项目负责人：${personChange.name}未上传签名，请先上传` ], code: -1 };
    }


    const data = await ctx.helper.fillMergerWord(
      ctx,
      '331评价方案审核记录表',
      field,
      {
        year: await ctx.helper.getRadiateProjectYear(projectInfo.projectSN, 'JcqlcProject'),
        projectSN: projectInfo.projectSN,
        oldWordName: '',
      }
    );
    await ctx.model.JcqlcProject.updateOne({ _id: projectInfo._id }, { $set: { samplingPlanApproveFile: { url: data.path, name: data.staticName } } });
    return data;
  }
}

module.exports = JcqlcProjectService;
