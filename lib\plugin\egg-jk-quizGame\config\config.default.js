exports.jk_quizGame = {
  alias: 'quizGame', // 插件目录，必须为英文
  pkgName: 'egg-jk-quizGame', // 插件包名
  enName: 'jk_quizGame', // 插件名
  name: '福州小游戏', // 插件名称
  description: '福州小游戏', // 插件描述
  fontApi: [
    {
      url: 'quizGame/getPrizesList',
      method: 'get',
      controllerName: 'getPrizesList',
      details: '获取奖品列表',
    },
    {
      url: 'quizGame/getMyPrize',
      method: 'get',
      controllerName: 'getMyPrize',
      details: '获得我的奖品',
    },
    {
      url: 'quizGame/drawPrize',
      method: 'post',
      controllerName: 'drawPrize',
      details: '获得中奖结果',
    },
    {
      url: 'quizGame/getChance',
      method: 'get',
      controllerName: 'getChance',
      details: '判断用户有没有抽奖资格',
    },
    {
      url: 'quizGame/winningUsers',
      method: 'get',
      controllerName: 'getWinningUsers',
      details: '获取已获奖用户列表',
    },
    {
      url: 'quizGame/testPaper',
      method: 'get',
      controllerName: 'getPaper',
      details: '获取试卷和游戏记录信息',
    },
    {
      url: 'quizGame/captcha',
      method: 'get',
      controllerName: 'createCaptcha',
      details: '获取验证码',
    },
    {
      url: 'quizGame/gameRecord',
      method: 'put',
      controllerName: 'updateGameRecord',
      details: '更新游戏记录',
    },
    {
      url: 'quizGame/gameUser',
      method: 'post',
      controllerName: 'editGameUser',
      details: '更新游戏用户信息',
    },
    {
      url: 'quizGame/gameUser',
      method: 'get',
      controllerName: 'getGameUser',
      details: '根据wxUnionId获取游戏用户信息',
    },
    {
      url: 'quizGame/EnterpriseList',
      method: 'get',
      controllerName: 'getEnterpriseList',
      details: '获取企业列表',
    },
    {
      url: 'quizGame/stepRanking',
      method: 'get',
      controllerName: 'getStepRanking',
      details: '获取个人积分排行榜',
    },
    {
      url: 'quizGame/EnterpriseStepRanking',
      method: 'get',
      controllerName: 'getEnterpriseStepRanking',
      details: '获取团体总积分排行榜',
    },
    {
      url: 'quizGame/gameEventInfo',
      method: 'get',
      controllerName: 'getGameEventInfo',
      details: '获取活动信息',
    },
    {
      url: 'quizGame/winnerInfo',
      method: 'post',
      controllerName: 'addWinnerInfo',
      details: '抽奖后更新个人信息',
    },
  ],
  adminApi: [],
  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_quizGame = {\n
        enable: true,\n        package: 'egg-jk-quizGame',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
  quizGameRouter:{\n
        match: [ctx => ctx.path.startsWith('/api/quizGame')],\n
    },\n
    `, // 插入到 config.default.js 中的配置

  // 列表查询每页显示的数量，默认为10
  // pageSize: 10,
  multipart: {
    fileSize: '50mb',
    fields: '100',
    // mode: 'stream',
    fileExtensions: [ 'docx', 'doc', 'xlsx', 'mp4', 'pdf', 'avi', 'rar' ],
  },
  // 图片上传路径
  propagateUploadPath: './app/public/upload/propagate',
  security: {
    csrf: {
      ignoreJSON: true,
    },
    // 白名单
    // domainWhiteList: ['*'],
  },
};

