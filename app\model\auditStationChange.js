
/*
 * @Author: 相敏月
 * @Date: 2024-04-03 15:00
 * @LastEditors:
 * @LastEditTime:
 * @Description: 北元对接人员信息部门变更，原部门绑定岗位和现部门绑定岗位审核
 *
 */
module.exports = app => {
  const shortid = require('shortid');
  // 岗位变更审核表
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const auditStationChangeSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      employeeId: {
        type: String,
        ref: 'Employees',
      },
      employeeName: {
        type: String,
      },
      nowEnterpriseID: {
        // 企业id
        type: String,
        ref: 'Adminorg',
      },
      originEnterpriseID: {
        // 企业id
        type: String,
        ref: 'Adminorg',
      },
      unitCode: {
        // 编码
        type: String,
      },
      status: { type: Number, default: 1 }, // 1已通过、0未审核、2未通过
      // 岗位状态 1 离岗 2 转岗 4 新上岗/入职 (为了和工作变更信息changType字段匹配)
      stationStatus: {
        type: Number,
        default: 0,
      },
      // 工作状态变更信息绑定id
      workInfoId: {
        type: String,
      },
      auditTime: {
        type: Date,
      }, // 审核时间
      auditUserId: {
        type: String,
        ref: 'Adminusers',
      }, // 审核人
      originDingtreeIds: {
        type: String,
        ref: 'Dingtrees',
      },
      nowDingtreeIds: {
        type: String,
        ref: 'Dingtrees',
      },
      originStationIds: [
        [
          {
            _id: {
              type: String,
              default: shortid.generate,
            },
            category: {
              type: String, // 类别 mill/workspaces/stations
            },
            specificId: {
              type: String,
            },
          },
        ],
      ],
      nowStationIds: [
        [
          {
            _id: {
              type: String,
              default: shortid.generate,
            },
            category: {
              type: String, // 类别 mill/workspaces/stations
            },
            specificId: {
              type: String,
            },
          },
        ],
      ],
      // 岗位变更文件
      files: {
        // 附件
        originName: String, // 用户上传文件名称
        staticName: String, // 后台处理后存储到public静态资源的文件名
      },
      fixedInfo: {
        nowCname: {
          type: String,
        },
        originCname: {
          type: String,
        },
        nowStationName: {
          type: String,
        },
        nowWorkspaceName: {
          type: String,
        },
        originStationName: {
          type: String,
        },
        originWorkspaceName: {
          type: String,
        },
        nowHarmFactors: {
          type: String,
        },
        originHarmFactors: {
          type: String,
        },
        shouldCheck: {
          type: Boolean,
        },
      },
    },
    { timestamps: true }
  );

  return mongoose.model(
    'auditStationChange',
    auditStationChangeSchema
  );
};
