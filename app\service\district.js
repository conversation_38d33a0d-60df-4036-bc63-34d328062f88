
const Service = require('egg').Service;


// const await = require('await-stream-ready/lib/await');
// const _ = require('lodash');
const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _safeDelete,
} = require('./general');


class DistrictService extends Service {
  async addressList1() {
    console.log('service====');
    const listdata = this.ctx.model.District.find();
    return listdata;

  }

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {

    const listdata = _list(this.ctx.model.District, payload, {
      query,
      searchKeys,
      populate,
      files,
    });
    return listdata;

  }


  async count(params = {}) {
    return _count(this.ctx.model.District, params);
  }

  async create(payload) {
    return _create(this.ctx.model.District, payload);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.District, values, key);
  }

  async safeDelete(res, values) {
    return _safeDelete(res, this.ctx.model.District, values);
  }

  async update(res, _id, payload) {
    return _update(res, this.ctx.model.District, _id, payload);
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.District, params);
  }

  async getByName(name) {
    return await this.ctx.model.District.findOne({ name });
  }

  async getByCode(area_code) {
    return await this.ctx.model.District.findOne({ area_code });
  }

  // 根据area_code获取所有父级地区名称以及经纬度
  // districtRegAdd: [ '北京市', '直辖区', '东城区' ],
  // point: [ '116.416357', '39.928353' ]
  async getParentByCode(area_code) {
    area_code = area_code.padEnd(12, '0');
    const districtRegAdd = []; let point = [];
    let curDistrict = await this.ctx.model.District.findOne({ area_code });
    if (curDistrict) {
      point = [ curDistrict.lng, curDistrict.lat ];
      do {
        districtRegAdd.unshift(curDistrict.name);
        curDistrict = await this.ctx.model.District.findOne({ area_code: curDistrict.parent_code });
      } while (curDistrict);
    }
    return { districtRegAdd, point };
  }

  // 检查单个杭州检测项目工作场所
  async checkArea(workAddress = []) {
    const { ctx, config } = this;
    try {
      if (workAddress && config.branch === 'hz' && workAddress[0]) {
        if (workAddress[0].districts) {
          return workAddress.every(ele => ele.districts.includes('330100000000'));
        }
      }
      return true;
    } catch (err) {
      ctx.auditLog('项目工作区域检查错误', JSON.stringify(err), 'error');
    }
  }

}

module.exports = DistrictService;
