/*
 * @Author: 黄婷婷
 * @Date: 2020-07-06 09:00
 * @LastEditors: 黄婷婷
 * @LastEditTime: 2020-07-06 09:00
 * @Description: 劳动者档案（档案六）
 *
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const zygrRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: String, // 公司id
    employeeId: { type: String, ref: 'Employees' }, // 员工id
    recordId: { type: String, ref: 'Record' }, // 档案id
    recordNum: String, // 员工档案编号
    mergeWord: { // 合并的word
      sort: String, // 是否是系统生成
      fileType: String, // 文件类别
      originName: String, // 用户上传文件名称
      staticName: String, // 后台处理后存储到public静态资源的文件名,
    },
    recordFiles: [ // 档案文件
      {
        name: { type: String }, // 档案项目名称
        isGenerate: Boolean, // 是否是系统生成
        systemGenerate: { // 系统生成的文件
          sort: String, // 分类
          fileType: String, // 文件类别
          originName: String, // 用户上传文件名称
          staticName: String, // 后台处理后存储到public静态资源的文件名,
        },
        clientUpload: [{ // 用户上传的文件
          sort: String, // 分类
          fileType: String, // 文件类别
          originName: String, // 用户上传文件名称
          staticName: String, // 后台处理后存储到public静态资源的文件名
        }],
      },
    ],
  }, { timestamps: true });
  return mongoose.model('zygrRecord', zygrRecordSchema, 'zygrRecord');
};
