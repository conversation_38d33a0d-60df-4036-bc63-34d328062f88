/**
 * 体检机构预约单
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;


  const HealthCheckAppointmentSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: { // 用工单位id
      type: String,
      ref: 'Adminorg',
    },
    EnterpriseCode: { // 用工单位统一社会信用代码
      type: String,
      required: true,
      trim: true,
      maxlength: 18,
      minlength: 18,
      set: v => v.toUpperCase(),
    },
    physicalExamOrgId: { // 体检机构id
      type: String,
      required: true,
      ref: 'PhysicalExamOrg',
    },
    checkType: { // 体检类型 01是上岗前 02是在岗 03是离岗时 04应急 05离岗后 06普通体检
      type: String,
      required: true,
      enum: [ '01', '02', '03', '04', '05', '06' ],
    },
    isReview: { // 是否复查 0否 1是
      type: String,
      default: '0',
      enum: [ '0', '1' ],
    },
    peopleNum: { // 预估/预约 体检人数
      type: Number,
      required: true,
    },
    startTime: { // 开始时间
      type: Date,
      required: true,
    },
    endTime: { // 结束时间
      type: Date,
      required: true,
    },
    healthcheckId: { // 体检项目id
      type: String,
      ref: 'healthcheck',
    },
    employeeIds: [{ // 实际体检人员名单
      type: String,
      ref: 'Employees',
    }],
    status: { //  1 提交申请 2 已确认体检日期 3 已修改体检日期 4 已确认名单(弃用) 5 体检中 6 已完成 7 已取消
      type: Number,
      default: 1,
      enum: [ 1, 2, 3, 5, 6, 7 ],
    },
    files: {
      authorization: String, // 合同委托书
      enterpriseInfo: String, // 用人单位基本信息表以及职业健康检查劳动者信息表
      healthExamRecord: String, // 职业健康检查登记表
    },
  }, { timestamps: true });


  return mongoose.model('HealthCheckAppointment', HealthCheckAppointmentSchema, 'healthCheckAppointment');

};

