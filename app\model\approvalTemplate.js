
module.exports = app => {
  const shortid = require('shortid');
  // 审批模版
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const approvalTemplate = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    isInit: Boolean, // 是否为初始化的模版
    serviceOrgId: String, // 机构id
    dingApprovalCode: String, // 钉钉审批编号
    name: String, // 流程名称
    desc: String, // 描述
    nodes: Array, // 项目节点 ['JcqlcProject','contractReview'] 第一个元素为项目model名，第二个元素为项目节点
    formJson: String, // 表单json
    flowJson: String, // 流程json
    enabled: { // 是否可用
      type: Boolean,
      default: true,
    },
    nodeData: [{ // 节点信息
      // type: String, // 节点类型
      label: String, // 节点名称
      name: String, // 节点类型名称
      role: Array, // 角色
      approvedType: String, // 审批类型 1人工审批 2 自动审批
      approvedUserType: String, // 审批人类型 USER指定成员 SELF
      taskActiontype: String, // 会签(AND)或者或签(OR)
      noApprovedUserType: String, // 1 自动通过 2 转交给管理员
      approvedUsers: Array, // 指定审批人员 serviceEmployeeid approvedUserType为USER时才有这个字段
    }],
  }, { timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' } });

  return mongoose.model('approvalTemplates', approvalTemplate, 'approvalTemplates');
};

