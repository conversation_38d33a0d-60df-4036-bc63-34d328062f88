const Controller = require('egg').Controller;
const mqtt = require('mqtt');
// const _ = require('lodash');
// const { param } = require('../../public/plugins/ueditor/third-party/jquery-1.10.2');

const aedes = require('aedes')();
const server = require('net').createServer(aedes.handle);
let port;
const clientIDs = [];
class PropagateController extends Controller {
  // 搭建 mqtt Broker 开启代理服务
  async broker() {
    const { ip, port } = this.config;
    const client = mqtt.connect(`${ip}:${port}`); // 连接客户端
    console.log(888888888, client);
  }

  // 客户端 连接 暂时没有用到
  async client2(ctx) {
    const clientIp = '*************';
    const allDevices = await ctx.model.Devices.find({}, { model: 1, deviceID: 1 });
    const client = mqtt.connect(`tcp://${clientIp}:${port}`); // 连接客户端
    // const client  = mqtt.connect('mqtt://127.0.0.1',{clientId: clientId, username: 'mimi',password: '123456'})
    client.on('connect', function() {
      for (let i = 0; i < allDevices.length; i++) {
        const clientID = allDevices[i].deviceID; // 不能冲突,设备的唯一码
        if (clientIDs.includes(clientID)) {
          // 订阅下发参数的主题
          // console.log(22222222, `${allDevices[i].model}/${clientID}/report/`);
          client.subscribe(`${allDevices[i].model}/${clientID}/report/`, function(err) {
            if (!err) {
              console.log(666666, `设备 - ${clientID} 订阅成功`);
              // 发布 - 设置仪器时间
              const setTimeMsg = `<N>{
                "Class":"121",
                "Flag":"20211205152432000",
                "DeviceAWAID": "${clientID}",
                "SystemTime":"20211205152432"
              }</N>`;
              // client.publish(`${allDevices[i].model}/${clientID}/excute/`, setTimeMsg);
              client.publish({
                topic: `${allDevices[i].model}/${clientID}/excute/`,
                payload: Buffer.from(setTimeMsg),
              }, (err, res) => {
                console.log(err, res);
              });
            }
          });
        } else {
          console.log(`设备 - ${clientID} 未连接`);
        }
      }
    });

    // 接收订阅的消息
    // const self = this;
    client.on('message', function(topic, message) {
      // message is Buffer
      console.log(7777777, topic, message.toString());
      // self.saveData(topic, message);
      // client.end();
    });
  }

  // 保存噪声数据
  async saveData(message) {
    const res = JSON.parse(message.toString().replace(/<N>/g, '').replace(/<\/N>/g, '')); // message is Buffer
    // if (res.Class === '115') {
    //   console.log('===== 心跳一次 =====');
    // }
    if (res.Class === '001') {
      const { LAFp, LCFp, LZFp, LCSp, LASp, LZSp, Lp } = res;
      this.ctx.model.DevicesData.create({
        deviceType: 1,
        deviceID: res.DeviceAWAID,
        data: { LAFp, LCFp, LZFp, LCSp, LASp, LZSp, Lp },
      });
    }
  }

  // 关闭服务
  async onSIGINT(ctx) {
    console.error('Caught SIGTERM, shutting down.');
    server.close();
    // 关闭客户端
    for (const key in aedes.clients) {
      aedes.clients[key].close();
    }
    // process.exit(0);
    ctx.helper.renderSuccess(ctx, { message: '服务已关闭' });
  }

  // 延时器
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 传感器数据接收(第三方直接发送过来的) post
  async dataReception(ctx) {
    const params = ctx.request.body;
    const { deviceType, deviceID, data, dateTime } = params;
    if (!deviceType || !deviceID || !data || !dateTime) {
      ctx.auditLog('粉尘发过来的参数', `${params}; ${typeof params}`, 'error');
      ctx.helper.renderFail(ctx, {
        message: '数据添加失败，参数不全。',
      });
      return;
    }
    try {
      data.value = (data.value && parseFloat(data.value));
      await ctx.model.DevicesData.create({
        deviceType: 2,
        deviceID,
        data: typeof data === 'string' ? JSON.parse(data) : data,
        dateTime: new Date(dateTime),
      }).then(res => {
        ctx.helper.renderSuccess(ctx, {
          message: '数据接收成功',
          data: res,
        });
      });
    } catch (err) {
      ctx.auditLog('粉尘接收数据报错：', `${typeof params}, ${params}: ${err}`, 'error');
    }
  }
  // 查询接收到的设备数据, net测试用，以后会删除
  async dataReception2(ctx) {
    const params = ctx.request.body;
    const data = await ctx.model.DevicesData.find(params);
    ctx.helper.renderSuccess(ctx, {
      message: 'OK',
      data,
    });
  }

  async issueOrder(ctx) {
    let resMessage = '';
    try {
      console.log('手机端扫码触发issueOrder');
      let resCode = '';
      const { ip, port } = this.config.mqtt;
      const client = mqtt.connect(`${ip}:${port}`); // 连接客户端
      const data = JSON.parse(ctx.request.body.data);
      const id = ctx.request.body.id;
      const quantity_k = ctx.request.body.num.quantity_k;
      const quantity_e = ctx.request.body.num.quantity_e;
      const companyId = ctx.request.body.companyId;
      const m = await ctx.service.ppeSelfLiftCabinet.message(companyId, quantity_k, quantity_e);
      client.subscribe('ZHZN/DuopuOapi', async error => { // 订阅
        if (error) {
          console.log('订阅出错，。。。。。', error, new Date());
          resMessage += '订阅出错，。。。。。;';
        } else {
          console.log('口罩机订阅成功了。。。。。。', new Date());
          resMessage += '订阅成功了;';
        }
      });
      console.log(data, id, quantity_k, quantity_e, m, 'app传过来的信息', new Date());
      const saleId = await ctx.service.ppeSelfLiftCabinet.randomPass(10);
      const messageId = await ctx.service.ppeSelfLiftCabinet.randomPass(7);
      const message = `{"c":22,"f":"DuopuOapi","t":"863488050210916","m":"${m}","s":"${saleId}","mi":${messageId}}`; // 发布的消息
      client.publish('ZHZN/863488050210916', message); // 发布
      console.log('发送的指令', `{"c":22,"f":"DuopuOapi","t":"${id}","m":"${m}","s":"${saleId}","mi":${messageId}}`);
      resCode = `{"c":22,"f":"DuopuOapi","t":"${id}","m":"${m}","s":"${saleId}","mi":${messageId}}`;
      resMessage += `{"c":22,"f":"DuopuOapi","t":"${id}","m":"${m}","s":"${saleId}","mi":${messageId}}`;
      client.on('message', function(topic, message) {
        console.log('口罩机返回信息', message.toString());
      });
      if (!client) {
        console.log('仪器未连接');
        ctx.helper.renderFail(ctx, {
          status: 500,
          message: '没有此机器的数据' + new Date(),
        });
        return;
      }
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        data: resCode,
        message: resMessage,
      });
    } catch (err) {
      console.log(err, '错误');
      ctx.helper.renderFail(ctx, {
        status: 500,
        data: new Error(JSON.stringify(err.stack)).message,
        message: `后端错误${resMessage}`,
      });
    }
  }

  // 噪声客户端 连接  需启动
  async client(ctx) {
    const { ip, port } = this.config.mqtt;
    const client = mqtt.connect(`${ip}:${port}`); // 连接客户端
    // const client  = mqtt.connect('mqtt://127.0.0.1',{clientId: clientId, username: 'mimi',password: '123456'})
    const allDevices = await ctx.model.Devices.find({ deviceType: 1, connectionStatus: true }, { model: 1, deviceID: 1 });
    const clientIDArr = [];
    client.on('connect', function() {
      for (let i = 0; i < allDevices.length; i++) {
        const clientID = allDevices[i].deviceID; // 不能冲突,设备的唯一码
        // 订阅下发参数的主题
        client.subscribe(`${allDevices[i].model}/${clientID}/report/`, function(err) {
          if (!err) {
            clientIDArr.push(clientID);
            ctx.auditLog('mqtt服务', `噪音设备 - ${clientID} 订阅成功`, 'info');
            // console.log(666666, `设备 - ${clientID} 订阅成功`);
          }
        });
      }
    });

    // 接收订阅的消息
    const self = this;
    client.on('message', function(topic, message) { // message is Buffer
      // console.log(7777777, topic, message.toString());
      self.saveData(message);
    });
    await this.sleep(1500);
    ctx.helper.renderSuccess(ctx, {
      status: 200,
      message: '噪音设备订阅成功',
      data: clientIDArr,
    });
  }

}

module.exports = PropagateController;
