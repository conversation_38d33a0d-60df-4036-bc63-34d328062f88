exports.jk_PPESelfLiftCabinet = {
  alias: 'PPESelfLiftCabinet', // 插件目录，必须为英文
  pkgName: 'egg-jk-PPESelfLiftCabinet', // 插件包名
  enName: 'jk_PPESelfLiftCabinet', // 插件名
  name: '防护用品自提柜', // 插件名称
  description: '防护用品自提柜', // 插件描述
  adminApi: [{
    url: 'PPESelfLiftCabinet/issueOrder',
    method: 'get',
    controllerName: 'issueOrder',
    details: '发布命令给相应自提柜',
  }],
  fontApi: [],
  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_PPESelfLiftCabinet = {\n
        enable: true,\n        package: 'egg-jk-PPESelfLiftCabinet',
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
  PPESelfLiftCabinetRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/PPESelfLiftCabinet'), ctx => ctx.path.startsWith('/api/PPESelfLiftCabinet')],\n
    },\n
    `, // 插入到 config.default.js 中的配置

  // 列表查询每页显示的数量，默认为10
  // pageSize: 10,
  multipart: {
    fileSize: '50mb',
    fields: '100',
    // mode: 'stream',
    fileExtensions: [ 'docx', 'doc', 'xlsx', 'mp4', 'pdf', 'avi', 'rar' ],
  },
  // 图片上传路径
  propagateUploadPath: './app/public/upload/PPESelfLiftCabinet',
  security: {
    csrf: {
      ignoreJSON: true,
    },
    // 白名单
    // domainWhiteList: ['*'],
  },
};

