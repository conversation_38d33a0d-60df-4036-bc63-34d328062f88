/*
 * @Author: 系统重构
 * @Date: 2024-01-01
 * @Description: 防护用品模板配置表
 * 用于管理不同分类的防护用品的自定义字段模板
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const protectiveProductTemplateSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },

    // 基础信息
    EnterpriseID: {
      type: String,
      required: true,
      index: true,
    },

    // 关联分类
    categoryId: {
      type: String,
      ref: 'protectionCategory',
      required: true,
      index: true,
    },
    categoryPath: String, // 分类路径
    categoryName: String, // 分类名称

    // 模板信息
    templateName: {
      type: String,
      required: true,
      trim: true,
    }, // 模板名称
    templateDescription: {
      type: String,
      trim: true,
    }, // 模板描述

    // 显示字段配置（对应原来的tableHeader功能）
    displayFields: [{
      fieldKey: {
        type: String,
        required: true,
        trim: true,
      }, // 字段键名
      fieldLabel: {
        type: String,
        required: true,
        trim: true,
      }, // 显示标签
      fieldType: {
        type: String,
        enum: [ 'text', 'number', 'select', 'multiSelect', 'date', 'boolean', 'textarea' ],
        default: 'text',
      }, // 字段类型
      width: {
        type: Number,
        default: 120,
      }, // 列宽
      sortOrder: {
        type: Number,
        default: 0,
      }, // 排序
      isRequired: {
        type: Boolean,
        default: false,
      }, // 是否必填
      isVisible: {
        type: Boolean,
        default: true,
      }, // 是否显示
      isEditable: {
        type: Boolean,
        default: true,
      }, // 是否可编辑
      options: [ String ], // 选项列表（用于select类型）
      defaultValue: Schema.Types.Mixed, // 默认值
      validation: {
        min: Number, // 最小值（数字类型）
        max: Number, // 最大值（数字类型）
        minLength: Number, // 最小长度（文本类型）
        maxLength: Number, // 最大长度（文本类型）
        pattern: String, // 正则表达式
        errorMessage: String, // 验证错误信息
      },
    }],

    // 自定义属性字段配置
    customFields: [{
      fieldKey: {
        type: String,
        required: true,
        trim: true,
      }, // 自定义字段键名
      fieldLabel: {
        type: String,
        required: true,
        trim: true,
      }, // 显示标签
      fieldType: {
        type: String,
        enum: [ 'text', 'number', 'select', 'multiSelect', 'date', 'boolean', 'textarea' ],
        default: 'text',
      }, // 字段类型
      isRequired: {
        type: Boolean,
        default: false,
      }, // 是否必填
      options: [ String ], // 选项列表
      unit: {
        type: String,
        trim: true,
      }, // 单位
      description: {
        type: String,
        trim: true,
      }, // 字段描述
      sortOrder: {
        type: Number,
        default: 0,
      }, // 排序
      validation: {
        min: Number,
        max: Number,
        minLength: Number,
        maxLength: Number,
        pattern: String,
        errorMessage: String,
      },
    }],

    // 模板状态
    isActive: {
      type: Boolean,
      default: true,
    }, // 是否启用
    isDefault: {
      type: Boolean,
      default: false,
    }, // 是否为默认模板

    // 版本控制
    version: {
      type: Number,
      default: 1,
    },

  }, {
    timestamps: true,
    versionKey: '__v',
  });

  // 索引
  protectiveProductTemplateSchema.index({ EnterpriseID: 1, categoryId: 1 });
  protectiveProductTemplateSchema.index({ EnterpriseID: 1, isActive: 1 });
  protectiveProductTemplateSchema.index({ EnterpriseID: 1, isDefault: 1 });

  // 确保每个分类只有一个默认模板
  protectiveProductTemplateSchema.index({
    EnterpriseID: 1,
    categoryId: 1,
    isDefault: 1,
  }, {
    unique: true,
    sparse: true,
    name: 'unique_default_template',
  });


  // 中间件：保存前验证
  protectiveProductTemplateSchema.pre('save', function(next) {
    // 验证字段键名唯一性
    const fieldKeys = new Set();

    // 检查显示字段
    for (const field of this.displayFields) {
      if (fieldKeys.has(field.fieldKey)) {
        return next(new Error(`字段键名 ${field.fieldKey} 重复`));
      }
      fieldKeys.add(field.fieldKey);
    }

    // 检查自定义字段
    for (const field of this.customFields) {
      if (fieldKeys.has(field.fieldKey)) {
        return next(new Error(`字段键名 ${field.fieldKey} 重复`));
      }
      fieldKeys.add(field.fieldKey);
    }

    next();
  });

  // 静态方法：获取分类的默认模板
  protectiveProductTemplateSchema.statics.getDefaultTemplate = function(enterpriseId, categoryId) {
    return this.findOne({
      EnterpriseID: enterpriseId,
      categoryId,
      isDefault: true,
      isActive: true,
    });
  };

  // 静态方法：获取分类的所有模板
  protectiveProductTemplateSchema.statics.getTemplatesByCategory = function(enterpriseId, categoryId) {
    return this.find({
      EnterpriseID: enterpriseId,
      categoryId,
      isActive: true,
    }).sort({ isDefault: -1, templateName: 1 });
  };

  // 实例方法：生成字段配置
  protectiveProductTemplateSchema.methods.generateFieldConfig = function() {
    const config = {
      displayFields: {},
      customFields: {},
      tableHeader: [], // 兼容原有的tableHeader格式
    };

    // 处理显示字段
    this.displayFields.forEach(field => {
      config.displayFields[field.fieldKey] = {
        label: field.fieldLabel,
        type: field.fieldType,
        required: field.isRequired,
        visible: field.isVisible,
        editable: field.isEditable,
        options: field.options,
        validation: field.validation,
      };

      if (field.isVisible) {
        config.tableHeader.push({
          prop: field.fieldKey,
          label: field.fieldLabel,
          width: field.width,
          sortOrder: field.sortOrder,
        });
      }
    });

    // 处理自定义字段
    this.customFields.forEach(field => {
      config.customFields[field.fieldKey] = {
        label: field.fieldLabel,
        type: field.fieldType,
        required: field.isRequired,
        options: field.options,
        unit: field.unit,
        description: field.description,
        validation: field.validation,
      };
    });

    // 按排序顺序排列tableHeader
    config.tableHeader.sort((a, b) => a.sortOrder - b.sortOrder);

    return config;
  };

  return mongoose.model('protectiveProductTemplate', protectiveProductTemplateSchema);
};
