
const Controller = require('egg').Controller;

class CommentController extends Controller {


  async add() {
    const { ctx } = this;
    const { newComment } = ctx.request.body;
    const userId = ctx.session.user._id;
    if (!newComment) {
      ctx.helper.renderFail(ctx, { message: '留言不能为空' });
      return;
    }
    const userComent = await ctx.service.comment.findByUserId(userId);
    newComment.role = 1;
    if (userComent) { // 添加
      ctx.service.comment.update(userComent._id, newComment).then(res => {
        if (res.ok === 1) {
          ctx.helper.renderSuccess(ctx, { message: '留言成功', data: {} });
        } else {
          ctx.helper.renderFail(ctx, { message: '留言失败' });
        }
      });
    } else { // 创建
      ctx.service.comment.create({
        userId,
        comments: [ newComment ],
      }).then(res => {
        ctx.helper.renderSuccess(ctx, { data: res.comments, message: '留言成功' });
      });
    }

  }

  // 获取留言建议
  async get() {
    const { ctx, app } = this;
    const userId = ctx.session.user._id;
    const page = ctx.request.body.page || 1;
    const limit = ctx.request.body.limit || 10;
    const res = await ctx.service.comment.findByUserId(userId, +limit, +page);
    if (res && res.comments) {
      const uploadPath = `http://${ctx.request.host}/static${app.config.upload_http_path}/comment_files/${userId}/`;
      const result = res.comments.map(ele => {
        const newEle = JSON.parse(JSON.stringify(ele));
        if (ele.img) newEle.img = uploadPath + ele.img;
        return newEle;
      });
      ctx.helper.renderSuccess(ctx, {
        data: result.reverse(),
        message: limit === result.length ? '获取留言成功' : '最后一批了',
      });
    } else {
      ctx.helper.renderFail(ctx, { message: '留言获取失败' });
    }

  }

}
module.exports = CommentController;
