const moment = require('moment');
module.exports = app => {
  return {
    schedule: app.config.shaanxiDataInterfaceLog,

    async task(ctx) {
      try {
        const { isAllHandle = false } = app.config;
        ctx.auditLog('陕西数据接口日志处理定时任务开始', '', 'info');

        // 获取当天的开始和结束时间
        const handleDStartTime = moment().startOf('day').toDate();
        const handleEndTime = moment().endOf('day').toDate();

        // 获取所有组织的ID
        const orgInfo = await ctx.model.PhysicalExamOrg.distinct('_id');

        // 创建处理日志的异步任务列表
        const logTasks = orgInfo.map(item => {
          const query = {
            orgId: item,
          };

          // 只在isAllHandle为false时添加时间过滤条件
          if (!isAllHandle) {
            query.handleDStartTime = handleDStartTime;
            query.handleEndTime = handleEndTime;
          }

          // 将异步任务推送到任务数组
          return ctx.service.shaanxiData.handleInterfaceLog(query);
        });

        // 等待所有的异步任务执行完毕
        await Promise.all(logTasks);

        ctx.auditLog('陕西数据接口日志处理定时任务结束', '', 'info');
        ctx.auditLog('陕西数据接口日志处理定时任务处理完成', '', 'info');
        ctx.auditLog('处理体检对接体检总报告各人数问题', '', 'info');
        const healthCheckInfo = await ctx.model.Healthcheck.distinct('_id');
        if (healthCheckInfo.length === 0) return;
        for (const item of healthCheckInfo) {
          await ctx.service.shaanxiData.handleHealthCheckAccount({
            batch: item,
          });
        }
        ctx.auditLog('处理体检对接体检总报告各人数问题完成', '', 'info');
        ctx.auditLog('合并体检报告和体检数据', '', 'info');
        await ctx.service.shaanxiData.mergeHealthCheckData();
        ctx.auditLog('合并体检报告和体检数据完成', '', 'info');
      } catch (error) {
        ctx.auditLog('陕西数据接口日志处理定时任务处理失败', error.message, 'error');
        throw error;
      }
    },
  };
};
