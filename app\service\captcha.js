'use strict';

const Service = require('egg').Service;
const path = require('path');
const fs = require('fs');
const sharp = require('sharp');
const { v4: uuidv4 } = require('uuid');

class CaptchaService extends Service {
  constructor(ctx) {
    super(ctx);
    // 验证容错范围 - 允许的误差像素
    this.errorRange = 5;
    // 记录验证码信息的缓存，生产环境应该使用Redis
    this.captchaStore = new Map();
  }

  /**
   * 生成滑动验证码
   * @return {Object} 验证码信息
   */
  async generateCaptcha() {
    const { app } = this;

    try {
      // 1. 生成唯一的验证码ID
      const captchaId = uuidv4();

      // 2. 随机选择背景图片
      const backgroundImages = await this.getBackgroundImages();
      const randomIndex = Math.floor(Math.random() * backgroundImages.length);
      const selectedImage = backgroundImages[randomIndex];

      // 3. 生成随机滑块位置
      // 假设图片宽度为280px，高度为150px
      // left值范围控制在[100, 200]之间，避免太靠边
      const left = Math.floor(Math.random() * 100) + 100;

      // top值范围控制在[10, 100]之间，避免太靠上或太靠下
      const top = Math.floor(Math.random() * 90) + 10;

      // 4. 构建图片URL (如果是本地存储，这里可能需要完整的URL或相对路径)
      const imageUrl = this.getImageUrl(selectedImage);

      // 5. 在缓存中存储验证信息，用于后续验证
      // 设置5分钟过期时间
      const captchaData = {
        id: captchaId,
        left, // 正确的滑块位置
        top,
        imageUrl,
        verified: false,
        createdAt: Date.now(),
      };

      await app.redis.set(
        `captcha:${captchaId}`,
        JSON.stringify(captchaData),
        'EX',
        300 // 5分钟过期
      );

      // 6. 返回给前端的数据
      return {
        captchaId,
        imageUrl,
        top, // 只返回top值，left值不返回(由用户滑动猜测)
        left,
      };
    } catch (error) {
      this.ctx.logger.error('生成验证码失败:', error);
      throw new Error('生成验证码失败');
    }
  }

  /**
   * 获取背景图片列表
   * @return {Array} 图片列表
   */
  async getBackgroundImages() {
    // 方法1: 从指定目录读取图片文件
    // const imagesDir = path.join(this.app.baseDir, 'app/public/captcha');
    // const files = fs.readdirSync(imagesDir);
    // return files.filter(file => /\.(jpg|jpeg|png)$/i.test(file));

    // 方法2: 使用预设的图片列表
    return [
      'bgBlue.png',
      'bgGreem.png',
      'bgOrange.png',
      'bgPurple.png',
    ];
  }

  /**
   * 获取图片URL
   * @param {String} imageName 图片名称
   * @return {String} 图片URL
   */
  getImageUrl(imageName) {
    // 如果使用CDN或OSS存储
    // return `${this.app.config.cdnHost}/captcha/${imageName}`;

    // 如果使用本地存储
    return `/static/images/${imageName}`;
  }

  /**
   * 验证滑块位置
   * @param {string} captchaId 验证码ID
   * @param {number} moveX 用户滑动的X坐标
   * @return {Object} 验证结果
   */
  async verifyCaptcha(captchaId, moveX) {
    const { app } = this;

    // 从Redis获取验证码信息
    const captchaInfoStr = await app.redis.get(`captcha:${captchaId}`);

    // 验证码不存在或已过期
    if (!captchaInfoStr) {
      return { valid: false, message: '验证码不存在或已过期' };
    }

    const captchaInfo = JSON.parse(captchaInfoStr);

    // 检查验证码是否已被使用
    if (captchaInfo.verified) {
      return { valid: false, message: '验证码已被使用' };
    }

    // 验证滑块位置
    const valid = Math.abs(moveX - captchaInfo.left) <= this.errorRange;
    // 标记验证码已使用
    captchaInfo.verified = true;

    if (valid) {

      // 更新Redis中的验证码信息，保留剩余过期时间
      await app.redis.set(
        `captcha:${captchaId}`,
        JSON.stringify(captchaInfo),
        'EX',
        300 // 保持原来的过期时间
      );

      return { valid: true, message: '验证成功' };
    }

    return { valid: false, message: '验证失败' };
  }

  /**
   * 验证token是否有效
   * @param {string} token 验证token
   * @return {boolean} 是否有效
   */
  async verifyToken(token) {
    try {
      const data = this.app.jwt.verify(token, this.app.config.jwt.secret);
      const captchaInfo = this.captchaStore.get(data.captchaId);

      return captchaInfo && captchaInfo.verified;
    } catch (error) {
      return false;
    }
  }

  /**
   * 生成验证通过的token
   * @param {string} captchaId 验证码ID
   * @return {string} JWT token
   */
  generateToken(captchaId) {
    return this.app.jwt.sign({
      captchaId,
      timestamp: Date.now(),
    }, this.app.config.jwt.secret, { expiresIn: '10m' });
  }

  /**
   * 获取随机背景图片
   * @return {string} 图片路径
   */
  async getRandomBackgroundImage() {
    const bgImagesDir = path.join(this.app.baseDir, 'app/public/captcha/bg');
    const files = fs.readdirSync(bgImagesDir)
      .filter(file => /\.(jpg|jpeg|png)$/i.test(file));

    if (files.length === 0) {
      throw new Error('没有可用的背景图片');
    }

    const randomFile = files[Math.floor(Math.random() * files.length)];
    return path.join(bgImagesDir, randomFile);
  }

  /**
   * 处理背景图片，创建缺口
   * @param {string} imagePath 图片路径
   * @param {number} x 缺口位置X坐标
   * @param {number} y 缺口位置Y坐标
   * @return {string} 处理后的图片路径
   */
  async processBackgroundImage(imagePath, x, y) {
    const outputPath = path.join(this.app.baseDir, 'app/public/captcha/temp', `bg_${Date.now()}.png`);

    // 使用sharp库处理图片，在缺口位置创建一个半透明区域
    await sharp(imagePath)
      .composite([{
        input: {
          create: {
            width: 40,
            height: 40,
            channels: 4,
            background: { r: 100, g: 100, b: 100, alpha: 0.6 },
          },
        },
        left: x,
        top: y,
      }])
      .toFile(outputPath);

    return outputPath;
  }

  /**
   * 处理滑块图片
   * @param {string} imagePath 背景图片路径
   * @param {number} x 缺口位置X坐标
   * @param {number} y 缺口位置Y坐标
   * @return {string} 处理后的滑块图片路径
   */
  async processBlockImage(imagePath, x, y) {
    const outputPath = path.join(this.app.baseDir, 'app/public/captcha/temp', `block_${Date.now()}.png`);

    // 从背景图裁剪出滑块区域
    await sharp(imagePath)
      .extract({ left: x, top: y, width: 40, height: 40 })
      .toFile(outputPath);

    return outputPath;
  }

  /**
   * 将图片转换为Base64字符串
   * @param {string} imagePath 图片路径
   * @return {string} Base64编码的图片URL
   */
  async saveImageAsBase64(imagePath) {
    const buffer = await fs.promises.readFile(imagePath);
    const base64String = buffer.toString('base64');
    const mimeType = path.extname(imagePath).toLowerCase() === '.png' ? 'image/png' : 'image/jpeg';

    return `data:${mimeType};base64,${base64String}`;
  }

  /**
   * 清理临时文件
   * @param {Array<string>} filePaths 文件路径列表
   */
  cleanupTempFiles(filePaths) {
    filePaths.forEach(filePath => {
      try {
        fs.unlinkSync(filePath);
      } catch (error) {
        this.ctx.logger.error('清理临时文件失败：', error);
      }
    });
  }
}

module.exports = CaptchaService;
