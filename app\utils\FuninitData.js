module.exports = [{
  parentGroup: '-1',
  name: 'vip客服部',
  departIds: [],
  assignmentType: 'depart',
  alias: 'VIPCustomerService',
}, {
  parentGroup: '-1',
  name: '文员',
  employeeIds: [],
  assignmentType: 'employee',
  alias: 'clerk',
}, {
  parentGroup: '-1',
  name: '市场',
  departIds: [],
  assignmentType: 'depart',
  alias: 'marketing',
}, {
  parentGroup: '-1',
  name: '市场总监',
  employeeIds: [],
  assignmentType: 'employee',
  alias: 'marketingManager',
}, {
  parentGroup: '-1',
  name: '业务统计',
  departIds: [],
  assignmentType: 'employee',
  alias: 'businessStatistics',
}, {
  parentGroup: '职业卫生',
  name: '职业卫生检测技术负责人',
  alias: 'detectionManager',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '职业卫生',
  name: '职业卫生评价技术负责人',
  alias: 'EvaluationManager',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '职业卫生',
  name: '职业卫生质量控制负责人',
  employeeIds: [],
  assignmentType: 'employee',
  alias: 'qualityControlManager',
}, {
  parentGroup: '职业卫生',
  name: '评价报告授权签发人',
  alias: 'authorizedIssuer',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '职业卫生',
  name: '监督员',
  alias: 'supervisor',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '职业卫生',
  name: '签发人',
  employeeIds: [],
  assignmentType: 'employee',
  alias: 'personsOfIssuer',
}, {
  parentGroup: '职业卫生',
  name: '评审组长',
  employeeIds: [],
  assignmentType: 'employee',
  alias: 'reviewTeamLeader',
}, {
  parentGroup: '职业卫生',
  name: '技术',
  departIds: [],
  assignmentType: 'depart',
  alias: 'technicist',
}, {
  parentGroup: '职业卫生',
  name: '职业卫生室主任',
  employeeIds: [],
  assignmentType: 'employee',
  alias: 'occupationalHealthManager',
}, {
  parentGroup: '职业卫生',
  name: '检测',
  departIds: [],
  assignmentType: 'depart',
  alias: 'detection',
}, {
  parentGroup: '职业卫生',
  name: '评价',
  departIds: [],
  assignmentType: 'depart',
  alias: 'comment',
}, {
  parentGroup: '职业卫生',
  name: '综合',
  departIds: [],
  assignmentType: 'depart',
  alias: 'comprehensiveOfTechnicist',
}, {
  parentGroup: '工业放射卫生',
  name: '监督员',
  alias: 'supervisor',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '医疗放射卫生',
  name: '医疗放射卫生检测技术负责人',
  alias: 'detectionManager',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '医疗放射卫生',
  name: '放射卫生',
  alias: 'inspectionManager',
  departIds: [],
  assignmentType: 'depart',
}, {
  parentGroup: '医疗放射卫生',
  name: '监督员',
  alias: 'supervisor',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '医疗放射卫生',
  name: '归档审核人',
  alias: 'personsOfReviewer',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '医疗放射卫生',
  name: '检测报告批准人',
  alias: 'reportApprover',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '医疗放射卫生',
  name: '批准人',
  alias: 'approver',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '医疗放射卫生',
  name: '评价报告编制',
  alias: 'reportPreparation',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '医疗放射卫生',
  name: '放射合同评审人员',
  alias: 'radiateContractreviewers',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '医疗放射卫生',
  name: '放射合同评审组长',
  alias: 'radiateContractLeader',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '个人剂量管理',
  name: '个人剂量',
  alias: 'inspectionManager',
  departIds: [],
  assignmentType: 'depart',
}, {
  parentGroup: '个人剂量管理',
  name: '归档审核人',
  alias: 'archiveReviewers',
  employeeIds: [],
  assignmentType: 'employee',
},
{
  parentGroup: '个人剂量管理',
  name: '分样人',
  alias: 'sampleDistributor',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '个人剂量管理',
  name: '受理人',
  alias: 'processor',
  employeeIds: [],
  assignmentType: 'employee',
}, {
  parentGroup: '个人剂量管理',
  name: '收样人',
  alias: 'sampleCollector',
  employeeIds: [],
  assignmentType: 'employee',
},
{
  parentGroup: '实验室',
  name: '实验室',
  departIds: [],
  assignmentType: 'depart',
  alias: 'lab',
}, {
  parentGroup: '实验室',
  name: '实验室主管',
  assignmentType: 'employee',
  employeeIds: [],
  alias: 'labManager',
}, {
  parentGroup: '实验室',
  name: '复核人',
  employeeIds: [],
  assignmentType: 'employee',
  alias: 'labReviewers',
}, {
  parentGroup: '实验室',
  name: '实验室(油雾组)',
  departIds: [],
  assignmentType: 'depart',
  alias: 'YW',
}, {
  parentGroup: '实验室',
  name: '实验室(原吸组)',
  departIds: [],
  assignmentType: 'depart',
  alias: 'AAS',
},
{
  parentGroup: '实验室',
  name: '实验室(电化学组)',
  departIds: [],
  assignmentType: 'depart',
  alias: 'DHXF',
},
{
  parentGroup: '实验室',
  name: '实验室(洗脱称重组)',
  departIds: [],
  assignmentType: 'depart',
  alias: 'XTCZ',
},
{
  parentGroup: '实验室',
  name: '实验室(分光组)',
  departIds: [],
  assignmentType: 'depart',
  alias: 'FG',
},
// {
//   parentGroup: '实验室',
//   name: '实验室(电极法)',
//   departIds: [],
//   assignmentType: 'depart',
//   alias: 'ISE',
// },
{
  parentGroup: '实验室',
  name: '实验室(粉尘组)',
  departIds: [],
  assignmentType: 'depart',
  alias: 'Dust',
},
{
  parentGroup: '实验室',
  name: '实验室(气相色谱组)',
  departIds: [],
  assignmentType: 'depart',
  alias: 'GLC',
},
{
  parentGroup: '实验室',
  name: '实验室(气质组)',
  departIds: [],
  assignmentType: 'depart',
  alias: 'GC-MS',
},
{
  parentGroup: '实验室',
  name: '实验室(游离二氧化硅组)',
  departIds: [],
  assignmentType: 'depart',
  alias: 'FSiO2',
},
{
  parentGroup: '实验室',
  name: '离子色谱小组',
  departIds: [],
  assignmentType: 'depart',
  alias: 'IC',
}, {
  parentGroup: '实验室',
  assignmentType: 'depart',
  name: '原子荧光',
  alias: 'AFS',
}, {
  parentGroup: '实验室',
  name: '样品管理员',
  employeeIds: [],
  assignmentType: 'employee',
  alias: 'sampleAdministrator',
}, {
  parentGroup: '实验室',
  name: '标准物质验收人',
  employeeIds: [],
  assignmentType: 'employee',
  alias: 'referenceMaterialAcceptor',
}, {
  parentGroup: '实验室',
  name: '标准物质复核人',
  employeeIds: [],
  assignmentType: 'employee',
  alias: 'referenceMaterialReviewer',
}, {
  parentGroup: '实验室',
  name: '期间核查验收人',
  employeeIds: [],
  assignmentType: 'employee',
  alias: 'periodInspectionAcceptor',
}, {
  parentGroup: '实验室',
  name: '期间核查复核人',
  employeeIds: [],
  assignmentType: 'employee',
  alias: 'periodInspectionReviewer',
}];
