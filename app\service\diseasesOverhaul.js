// 检修维护记录
const Service = require('egg').Service;

class DiseasesOverhaulService extends Service {
  /**
   *  查询检修维护记录
   * @param query
   * @return {Promise<*>}
   */
  async list(query) {
    const { ctx } = this;
    try {
      const pageNum = parseInt(query.pageNum) || 1;
      const pageSize = parseInt(query.size) || 10;
      const skip = (pageNum - 1) * pageSize;
      const userId = ctx.session.user._id;
      const userInfo = await ctx.model.User.findOne({ _id: userId }).select(
        'employeeId'
      );
      if (!userInfo) {
        return {
          list: [],
          pageInfo: { pageSize, pageNum, total: 0 },
        };
      }
      const { employeeId } = userInfo;
      const match = {
        $or: [{ workShopHeader: employeeId }, { checkHeader: employeeId }, { creater: userInfo.employeeId }],
      };
      const diseasesOverhauls = await ctx.model.DiseasesOverhaul.aggregate([
        {
          $match: match,
        },
        {
          $addFields: {
            isEdit: { $eq: [ '$checkHeader', employeeId ] },
          },
        },
        {
          $lookup: {
            from: 'employees',
            localField: 'workShopHeader',
            foreignField: '_id',
            as: 'workShopHeaderOptions',
            pipeline: [
              {
                $project: {
                  _id: 0,
                  // label: '$name'+ ' ' + '$unitCode',
                  label: {
                    $concat: [ '$name', '(', '$unitCode', ')' ],
                  },
                  value: '$_id',
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'employees',
            localField: 'checkHeader',
            foreignField: '_id',
            as: 'checkHeaderOptions',
            pipeline: [
              {
                $project: {
                  _id: 0,
                  // label: '$name'+ ' ' + '$unitCode',
                  label: {
                    $concat: [ '$name', '(', '$unitCode', ')' ],
                  },
                  value: '$_id',
                },
              },
            ],
          },
        },
        {
          $sort: { checkTime: -1 },
        },
        {
          $facet: {
            list: [{ $skip: skip }, { $limit: pageSize }],
            total: [
              {
                $count: 'total',
              },
            ],
          },
        },
        {
          $addFields: {
            'pageInfo.total': { $arrayElemAt: [ '$total.total', 0 ] },
            'pageInfo.pageSize': pageSize,
            'pageInfo.pageNum': pageNum,
          },
        },
        {
          $project: {
            list: 1,
            pageInfo: 1,
          },
        },
      ]);
      if (diseasesOverhauls[0] && diseasesOverhauls[0].list.length > 0) {
        const data = JSON.parse(JSON.stringify(diseasesOverhauls[0]));
        for (let i = 0; i < data.list.length; i++) {
          const item = data.list[i];
          if (item.signImg) {
            item.allPath = await ctx.helper.concatenatePath({
              path: `${this.app.config.upload_http_path}/${item.EnterpriseID}/${item.signImg}`,
            });
          } else {
            item.signImg = '';
            item.allPath = '';
          }
        }
        const workPlace = await this.findWorkShop(employeeId);
        return {
          ...data,
          workPlace,
        };
      }
      return {
        list: [],
        workPlace: [],
        pageInfo: { pageSize, pageNum, total: 0 },
      };

    } catch (err) {
      throw err;
    }
  }
  // 查工作场所么
  async findWorkShop() {
    const ctx = this.ctx;
    try {
      const userId = ctx.session.user._id;
      const userInfo = await ctx.model.User.findOne({ _id: userId }).select(
        'employeeId'
      );
      const { employeeId } = userInfo;
      const employeeInfo = await ctx.model.Employee.findOne({ _id: employeeId })
        .select('EnterpriseID')
        .lean();
      if (!employeeInfo) {
        return [];
      }
      const { EnterpriseID } = employeeInfo;
      return await ctx.model.MillConstruction.find({ EnterpriseID });
    } catch (error) {
      throw error;
    }
  }
  /**
   * 添加检修维护记录
   * @param body
   * @return {Promise<*>}
   */
  async add(body) {
    const { ctx } = this;
    try {
      const userId = ctx.session.user._id;
      const userInfo = await ctx.model.User.findOne({ _id: userId }).select(
        'employeeId'
      );
      const { employeeId } = userInfo;
      const employeeInfo = await ctx.model.Employee.findOne({
        _id: employeeId,
      })
        .select('EnterpriseID')
        .lean();
      if (!employeeInfo) {
        return [];
      }
      const { EnterpriseID } = employeeInfo;
      body.EnterpriseID = EnterpriseID;
      body.creater = employeeId;
      const data = await ctx.model.DiseasesOverhaul.create(body);
      return data;
    } catch (e) {
      ctx.auditLog('添加检修维护记录', `${JSON.stringify(e)}`, 'error');
      return null;
    }
  }
  /**
   * 修改检修维护记录
   * @param body
   * @return {Promise<*>}
   */
  async update(body) {
    const { ctx } = this;
    try {
      if (!body || !body._id) {
        throw new Error('参数错误');
      }
      const data = await ctx.model.DiseasesOverhaul.updateOne(
        {
          _id: body._id,
        },
        {
          $set: body,
        }
      );
      return data;
    } catch (e) {
      ctx.auditLog('修改检修维护记录', `${JSON.stringify(e)}`, 'error');
      throw e;
    }
  }
  /** 删除
   * @param ids.ids
   *  @param ids
   *  @return {Promise<void>}
  */
  async remove({ ids = [] }) {
    const { ctx } = this;
    try {
      const data = await ctx.model.DiseasesOverhaul.deleteMany({
        _id: { $in: ids },
      });
      return data;
    } catch (e) {
      ctx.auditLog('删除检修维护记录', `${JSON.stringify(e)}`, 'error');
      throw e;
    }
  }
  /** 查人
   * @param name
   * @return {Promise<void>}
   */
  async searchPerson(name) {
    const { ctx } = this;
    try {
      if (!name) {
        return [];
      }
      const userId = ctx.session.user._id;
      const userInfo = await ctx.model.User.findOne({ _id: userId }).select(
        'employeeId'
      );
      const { employeeId } = userInfo;
      const employeeInfo = await ctx.model.Employee.findOne({ _id: employeeId })
        .select('EnterpriseID')
        .lean();
      if (!employeeInfo) {
        return [];
      }
      const { EnterpriseID } = employeeInfo;
      const match = {
        enable: true,
        status: 1,
        EnterpriseID,
        $or: [
          { phoneNum: { $regex: name } },
          { name: { $regex: name } },
          { IDNum: { $regex: name } },
          { unitCode: { $regex: name } },
        ],
      };
      const pipeline = [
        {
          $match: match,
        },
        { $sort: { name: 1 } },
        {
          $unwind: { path: '$departs', preserveNullAndEmptyArrays: true },
        },
        {
          $graphLookup: {
            from: 'dingtrees',
            startWith: '$departs',
            connectFromField: 'parentid',
            connectToField: '_id',
            as: 'departsGraph',
            depthField: 'depth',
          },
        },
        {
          $unwind: { path: '$departsGraph', preserveNullAndEmptyArrays: true },
        },
        { $sort: { 'departsGraph.depth': -1 } },
        {
          $group: {
            _id: '$_id',
            root: { $first: '$$ROOT' },
            departsGraph: { $push: '$departsGraph' },
          },
        },
        {
          $addFields: {
            'root.departsName': {
              $reduce: {
                input: {
                  $map: {
                    input: '$departsGraph',
                    as: 'dep',
                    in: {
                      $cond: {
                        if: {
                          $eq: [
                            {
                              $type: '$$dep.shortName',
                            },
                            'missing',
                          ],
                        },
                        then: '$$dep.name',
                        else: '$$dep.shortName',
                      },
                    },
                  },
                },
                initialValue: '',
                in: {
                  $concat: [
                    '$$value',
                    {
                      $cond: {
                        if: { $eq: [ '$$value', '' ] },
                        then: '',
                        else: '-',
                      },
                    },
                    '$$this',
                  ],
                },
              },
            },
          },
        },
        {
          $replaceRoot: { newRoot: '$root' },
        },
        {
          $project: {
            departsGraph: 0,
          },
        },
        { $sort: { name: 1 } },
      ];
      const res = await ctx.model.Employee.aggregate(pipeline)
        .collation({ locale: 'zh@collation=gb2312han' })
        .exec();
      if (res.length > 0) {
        for (let i = 0; i < res.length; i++) {
          const item = res[i];
          item.label = item.name;
          item.id = item._id;
        }
      }
      return res;
    } catch (error) {
      throw error;
    }
  }
}
module.exports = DiseasesOverhaulService;
