// 考试大纲管理
const examSyllabusController = {
  // 创建
  async create(ctx) {
    const data = ctx.request.body;
    try {
      const res = await ctx.service.examSyllabus.create(data);
      ctx.helper.renderSuccess(ctx, {
        message: '创建考试大纲',
        data: res,
      });
    } catch (e) {
      ctx.auditLog('创建考试大纲错误', e, 'error');
      ctx.helper.renderCustom(ctx, {
        message: e.message || '添加失败',
        status: 400,
      });
    }
  },
  // 更新
  async updateOne(ctx) {
    const data = ctx.request.body;
    try {
      const res = await ctx.service.examSyllabus.updateOne(data);
      ctx.helper.renderSuccess(ctx, {
        message: '更新考试大纲',
        data: res,
      });
    } catch (e) {
      ctx.auditLog('更新考试大纲错误', e, 'error');
      ctx.helper.renderCustom(ctx, {
        message: e.message || '更新失败',
        status: 400,
      });
    }
  },
  // 删除
  async deleteOne(ctx) {
    const data = ctx.request.body;
    try {
      const res = await ctx.service.examSyllabus.deleteOne(data);
      ctx.helper.renderSuccess(ctx, {
        message: '删除考试大纲',
        data: res,
      });
    } catch (e) {
      ctx.auditLog('删除考试大纲错误', e, 'error');
      ctx.helper.renderCustom(ctx, {
        message: e.message || '删除失败',
        status: 400,
      });
    }
  },
  // 获取单个详情
  async findOne(ctx) {
    const data = ctx.request.query;
    const res = await ctx.service.examSyllabus.findOne(data);
    if (res) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '数据获取成功',
        status: 200,
      });
    } else {
      ctx.helper.renderCustom(ctx, {
        message: '数据获取失败',
        status: 400,
        data: res,
      });
    }
  },
  // 获取列表
  async list(ctx) {
    const query = ctx.request.query;
    const res = await ctx.service.examSyllabus.getList(query);
    if (res && res.list) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '数据获取成功',
        status: 200,
      });
    } else {
      ctx.helper.renderCustom(ctx, {
        message: '数据获取失败',
        status: 400,
        data: res,
      });
    }
  },
  // 获取考试大纲管理统计数据
  async getStatistics(ctx) {
    const query = ctx.request.query;
    const res = await ctx.service.examSyllabus.statisticsForSingleArea(query);
    if (res) {
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '数据获取成功',
        status: 200,
      });
    } else {
      ctx.helper.renderCustom(ctx, {
        message: '数据获取失败',
        status: 400,
        data: res,
      });
    }
  },
  async getSubRegionsTrainData(ctx) {
    const query = ctx.request.query;
    try {
      const res = await ctx.service.examSyllabus.getSubRegionsTrainData(query);
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '数据获取成功',
      });
    } catch (e) {
      console.log(4444444, e);
      ctx.helper.renderCustom(ctx, {
        message: e.message || '数据获取失败',
        status: 400,
      });
    }
  },
  // 根据考试大纲id获取培训详情（暂时搁置！）
  async getTraningByExamSyllabus(ctx) {
    const query = ctx.request.query;
    try {
      // const res = await ctx.service.examSyllabus.getTraningByExamSyllabus(query._id || query.examSyllabusId, query.trainingStatus);
      ctx.helper.renderSuccess(ctx, {
        data: query,
        message: '数据获取成功',
      });
    } catch (e) {
      console.log(4444444, e);
      ctx.helper.renderCustom(ctx, {
        message: e.message || '数据获取失败',
        status: 400,
      });
    }
  },

};

module.exports = examSyllabusController;
