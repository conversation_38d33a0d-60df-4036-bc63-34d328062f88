const Controller = require('egg').Controller;
/**
 * @Controller DiseasesOverhaulController-检修维护 劳动者小程序端
 */
class DiseasesOverhaulController extends Controller {
  /**
   *  获取检修维护列表
   *  @Summary 获取检修维护列表
   *  @function GET
   *  @Router GET /manage/diseasesOverhaul/list
   *  @Request query string enterpriseID 公司id
   */
  async list() {
    const { ctx } = this;
    // 根据用户id获取employeeid
    console.log(ctx.request.query);
    const data = await ctx.service.diseasesOverhaul.list(ctx.request.query);
    if (data) {
      ctx.helper.renderSuccess(ctx, { data, message: '数据获取成功' });
    } else {
      ctx.helper.renderFail(ctx, { data, message: '数据获取失败' });
    }
  }
  /**
   *  添加检修维护
   *  @Summary 添加检修维护
   *  @Router POST /manage/diseasesOverhaul/add
   */
  async add() {
    const { ctx } = this;
    const data = await ctx.service.diseasesOverhaul.add(ctx.request.body);
    if (data) {
      ctx.helper.renderSuccess(ctx, { data, message: '添加成功' });
    } else {
      ctx.helper.renderFail(ctx, { data, message: '添加失败' });
    }
  }
  /**
   *  修改检修维护
   *  @Summary 修改检修维护
   *  @Router PUT /manage/diseasesOverhaul/update
   */
  async update() {
    const { ctx } = this;
    const data = await ctx.service.diseasesOverhaul.update(ctx.request.body);
    if (data) {
      ctx.helper.renderSuccess(ctx, { data, message: '修改成功' });
    } else {
      ctx.helper.renderFail(ctx, { data, message: '修改失败' });
    }
  }
  /**
   *  删除检修维护
   *  @Summary 删除检修维护
   *  @function DELETE
   *  @Router DELETE /manage/diseasesOverhaul/delete
   *  @Request query string id *query
   */
  async delete() {
    const { ctx } = this;
    const { _id } = ctx.request.body;
    if (!_id) {
      throw new Error('id不能为空');
    }
    const data = await ctx.service.diseasesOverhaul.remove({ ids: [ _id ] });
    if (data) {
      ctx.helper.renderSuccess(ctx, { data, message: '删除成功' });
    } else {
      ctx.helper.renderFail(ctx, { data, message: '删除失败' });
    }
  }
  /**
   *  获取工作场所
   * @Summary 获取工作场所
   * @function GET
   * @Router GET /manage/diseasesOverhaul/getWorkplace
   */
  async getWorkplace() {
    const { ctx } = this;
    try {
      const data = await ctx.service.diseasesOverhaul.findWorkShop();
      ctx.helper.renderSuccess(ctx, { data, message: '获取成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: '获取失败' });
    }
  }
  /**
   *  根据名称查询人列表
   *  @Summary 根据名称查询人列表
   *  @function GET
   *  @Router GET /manage/diseasesOverhaul/searchPerson
   */
  async searchPerson() {
    const { ctx } = this;
    try {
      const { name } = ctx.request.query;
      if (!name) {
        throw new Error('请输入姓名');
      }
      const data = await ctx.service.diseasesOverhaul.searchPerson(name);
      ctx.helper.renderSuccess(ctx, { data, message: '获取成功' });
    } catch (error) {
      ctx.helper.renderFail(ctx, { message: '获取失败' });
    }
  }
}
module.exports = DiseasesOverhaulController;
