/**
 *
 * created by JHw
 * 实验室：标准物质申领记录
 * !!此表从labInfoCopy进化而来，属于其表used_sku字段
 * @param app
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const receiptRecord = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    // 发起审批时更新
    mark: String, // 使用的子标示
    applyUser: String, // 申请人
    apply_time: Date, // 申请时间
    count: String, // 申领数量

    // 领用时更新
    recipient: String, // 领用人
    recipients_time: Date, // 领用时间

    // 审批同意更新
    authorizer: String, // 批准人

    // 审批状态
    applyAcceptanceApproved: {
      status: { // 0 未创建 1 进行中（审批中） 2 已完成（已同意）3 已拒绝  4 被终止
        type: Number,
        default: 0,
      },
      completedTime: Date, // 完成时间
      records: [], // 钉钉审批记录
    },

    applyAcceptanceProcessInstanceId: String, // 审批实例id
    applyAcceptanceApprovedStartTime: Date, // 审批发起时间

    standProductId: String, // 标准物质Id

  });
  return mongoose.model('receiptRecords', receiptRecord, 'receiptRecord');
};
