const parentId = '8TLpmC1mB'; // 福州体检对接的父级ID

const routes = [
  { label: '日志管理', api: 'fzData/logs', comments: '日志管理' },
  { label: '统计数据', api: 'fzData/stats', comments: '统计数据' },
  { label: '任务详情', api: 'fzData/taskDetails', comments: '任务详情' },
  { label: '创建任务', api: 'fzData/createTasks', comments: '创建任务' },
  {
    label: '单任务处理',
    api: 'fzData/processSingleTask',
    comments: '单任务处理',
  },
  {
    label: '继续处理',
    api: 'fzData/continue-processing',
    comments: '继续处理',
  },
  { label: '重试任务', api: 'fzData/retry', comments: '重试任务' },
  {
    label: '公司数据清洗测试',
    api: 'fzData/test/company',
    comments: '公司数据清洗测试',
  },
  {
    label: '健康检查数据清洗测试',
    api: 'fzData/test/healthCheck',
    comments: '健康检查数据清洗测试',
  },
  {
    label: '诊断数据清洗测试',
    api: 'fzData/test/diagnosis',
    comments: '诊断数据清洗测试',
  },
  { label: '文件列表', api: 'fzData/files', comments: '文件列表' },
  { label: '文件详情', api: 'fzData/fileDetails', comments: '文件详情' },
  { label: '清理缓存', api: 'fzData/cache', comments: '清理缓存' },
  {
    label: '串行处理公司',
    api: 'fzData/serial/company',
    comments: '串行处理公司',
  },
  {
    label: '串行处理健康检查',
    api: 'fzData/serial/healthCheck',
    comments: '串行处理健康检查',
  },
  {
    label: '串行处理诊断',
    api: 'fzData/serial/diagnosis',
    comments: '串行处理诊断',
  },
  { label: '进度监控', api: 'fzData/progress', comments: '进度监控' },
  {
    label: '任务进度监控',
    api: 'fzData/progress/:taskId',
    comments: '任务进度监控',
  },
  {
    label: 'Redis任务列表',
    api: 'fzData/redis/tasks',
    comments: 'Redis任务列表',
  },
  {
    label: 'Redis队列统计',
    api: 'fzData/redis/stats',
    comments: 'Redis队列统计',
  },
  {
    label: '清理Redis任务',
    api: 'fzData/redis/cleanup',
    comments: '清理Redis任务',
  },
  {
    label: '手动同步ZIP',
    api: 'fzData/manual/getAllZipFromApi',
    comments: '手动同步ZIP',
  },
  {
    label: '解密文件列表',
    api: 'fzData/decrypt/files',
    comments: '解密文件列表',
  },
  { label: '解密ZIP包', api: 'fzData/decrypt/zip', comments: '解密ZIP包' },
  {
    label: '解密任务ZIP包',
    api: 'fzData/decrypt/task',
    comments: '解密任务ZIP包',
  },
];

async function batchInsertRoutes() {
  for (let i = 0; i < routes.length; i++) {
    const route = routes[i];

    const requestBody = {
      label: route.label,
      type: '1',
      api: route.api,
      icon: '',
      routePath: route.api,
      componentPath: '',
      enable: true,
      parentId,
      sortId: i + 1,
      comments: route.comments,
      parent: { label: '1' },
    };

    try {
      const response = await fetch(
        'http://127.0.0.1:7005/manage/userResource/addOne',
        {
          headers: {
            accept: 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json;charset=UTF-8',
            'sec-ch-ua':
              '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            cookie:
              'locale=zh-cn; __51vcke__JKe7tHEz85t72Pgb=0391c6ca-a5e6-588b-b262-390a4691ab8b; __51vuft__JKe7tHEz85t72Pgb=1747189280790; _ga=GA1.1.12053759.1747189281; admin_zyws_xhl_qy=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiJDSFVIQU9BIiwiRW50ZXJwcmlzZUlEIjoiSFIwMDAwMDAwMSIsImlzVHJpYWxVc2VyIjpmYWxzZSwiaWF0IjoxNzQ4OTY5MzgzLCJleHAiOjE3NTE1NjEzODN9.uPquBwOxT3PYJI0y8cvhug-psnNwrh5uwfgE3HWniro; admin_zyws_xhl_qy.sig=tCjhnW3dF4nKBkL7VRRNaWgSSHvfpiT7KGpfQKwfI9Q; LiveWSBYT41590011=c2c2023eca374304a6c5b2aaa0e935a0; NBYT41590011fistvisitetime=1750385170071; NBYT41590011visitecounts=1; NBYT41590011lastvisitetime=1750385185204; NBYT41590011visitepages=2; __51uvsct__JKe7tHEz85t72Pgb=26; _ga_KSDJWQ1501=GS2.1.s1750752903$o31$g1$t1750752904$j59$l0$h0; admin_zwkj_zyws_tools=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiJUdmJzdVdhWXMiLCJpYXQiOjE3NTA4Mzc1NDMsImV4cCI6MTc1MzQyOTU0M30.73mPJFGAkbKgA7dDDDF4HXYdiUhA2r3Su1u0IgnlO2U; admin_duopu_jkqy_operate=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiJ0TWRhbHNUS2MiLCJpYXQiOjE3NTA5MjQyNDIsImV4cCI6MTc1MzUxNjI0Mn0.7KZvOp7499n3XpuIsPNVBAVYHQh1Ys-Y4B6_vSC6Fag; admin_duopu_jkqy_operate.sig=pajCn0BY5xAchybg_wXAIlUdjEPq4nI32STnNuCq_kw; DUOPU_SESS=GSpQ-p-ENefiPae26FEF36Ss4WK-HLPHeTMuqWAx3Tly6wiwkypy8X4Dzcbpox_a2ck16mCK57hzegIMCAqQ8AnmQJ3mUXWiT-rNsQmMx5vK7MFQ3by6bj_UgUUQctzJYWcDw5aWNjnvwGOpJCdmwCo2wPJnQs-2l1R8w1hVbZ_5D-TmgtdOfjV3iuOJ6f5y546jVx7mouGLcy1gLSrk9bDOhODiXYG0URh6kGfjN_5MYWtU0qs_MfU6KqVIwawGH672YtafREsm5Eq7uBcra_LN_Z7r7v_3wwwzWoEOsLrlL3yP9vWBZZvifLJ4-seGHc4gVwVzOT6_kefcIr6wYVxqBGdWAEyQ4x7otujZ9HAEr0s4qwigu2VEiknRQhfkc4OvuFlRU-dfLTh1ozrxxXIL6SGa0Muzd_BGjZKWfFwMYbT7oHWbG91-HeMfccBc01gyAIuJZ-H2qA3Yzyfn0jwZYrdswtIXu0K2or6pGmhDmLg9QLS2MzDa5k5cJ2zG1eZ2ZhCaBN0asqGf3pjJqkYYy8JHjkECCY9DHzJtPBlJlk0_sOKSnCXvIoNyXn6y_ne4dW_u55V7_9sLysSXdHXosA8iyHuLCaNRbuXrg2kwpJv6G8vPm2mbwIMhMH2qAxSSk_iYX5vq5elSui1NNUrU80sP1dqEzXiJp46sSDryE13gaSHcCTb-UvgzJNOGcbvY-7o_tm0HOm1rps5thbRrBdnjQ8oukEga8kScsN4FlGA8T-RwR7a9AwXD4RrfK90xx-bhxcfNOzVOBM9Ksg==; sidebarStatus=0',
            Referer: 'http://127.0.0.1:7005/admin/userResource',
          },
          body: JSON.stringify(requestBody),
          method: 'POST',
        }
      );

      const result = await response.json();
      console.log(`${route.label} - 导入结果:`, result);

      // 添加延时避免请求过快
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.error(`${route.label} - 导入失败:`, error);
    }
  }
}

// 执行批量导入
batchInsertRoutes();
