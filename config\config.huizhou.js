const path = require('path');
const port = 7009;

module.exports = appInfo => {

  return {
    // 插件路径
    admin_root_path: '/static',
    // 数据库连接
    mongoose: {
      client: {
        url: `${process.env.mdbSrv}/?readPreference=nearest&ssl=false`,
        options: {
          authSource: 'admin',
          dbName: process.env.mdbName,
          user: process.env.mdbUser,
          pass: process.env.mdbPass,
          useNewUrlParser: true,
          useUnifiedTopology: true,

        },
      },
      tools: {
        url: `${process.env.mdbSrv}/?readPreference=nearest&ssl=false`,
        options: {
          authSource: 'admin',
          dbName: process.env.mdbToolsName,
          user: process.env.mdbUser,
          pass: process.env.mdbPass,
          useNewUrlParser: true,
          useUnifiedTopology: true,

        },
      },
    },

    storageType: process.env.storageType
      ? process.env.storageType.replace('\n', '')
      : '',
    oss: {
      endPoint: process.env.endPoint
        ? process.env.endPoint.replace('\n', '')
        : '', //  http://xxx.xx.xx:8084 / https://xxx.xx.xx:8084 {protocol}{hostname}{port}
      accessKeyId: process.env.accessKey
        ? process.env.accessKey.replace('\n', '')
        : '',
      accessKeySecret: process.env.secretKey
        ? process.env.secretKey.replace('\n', '')
        : '',
      region: process.env.region ? process.env.region.replace('\n', '') : '',
      timeout: process.env.ossTimeout
        ? process.env.ossTimeout.replace('\n', '')
        : '',
      buckets: {
        default: {
          name: process.env.bucket ? process.env.bucket.replace('\n', '') : '',
          accessPolicy: process.env.accessPolicy
            ? process.env.accessPolicy.replace('\n', '')
            : '', // 是否是私有的bucket，默认是false
        },
      },
    },

    // 静态目录
    static: {
      prefix: '/static',
      dir: [
        path.join(appInfo.baseDir, 'backstage/dist'),
        '/opt/public/cms',
        '/opt/public/super',
        '/opt/public/jc',
        '/opt/public/operate',
        '/opt/public',
        path.join(appInfo.baseDir, 'app/public'),
      ],
      maxAge: 31536000,
    },
    // 加密解密
    session_secret: 'sxbychem_secret',
    auth_cookie_name: 'zyws_sxbychem_oapi',
    auth_govcookie_name: 'zyws_sxbychem_super',
    auth_jcqlccookie_name: 'zyws_sxbychem_jcqlc',
    encrypt_key: '243d477d-d6bb-4ab1-b0a9-a1b1c9419562',
    cms_encrypt_key: 'duopu_jkqy',
    salt_aes_key: 'duopu_jkqy',
    salt_sha2_key: '7a58917b-d493-4940-87d4-ab2b0fa8a3b7',
    encryptApp_key: '751f621ea5c8f930',
    encryptApp_vi: '2624750004598718',
    enterpriseUserGroup: 'vGEfBpfsv',

    // 存放生成的企业档案的文件系统目录
    enterprise_path: '/opt/public/enterprise',
    // 下载企业档案的http路径
    enterprise_http_path: '/enterprise',
    enterpriseUpload_path: '/opt/public/upload/enterprise',
    // 用户自行上传文件的文件系统目录，代码拼接: upload_path + /EnterpriseID/ + 文件名
    upload_path: '/opt/public/upload/enterprise',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_http_path + /EnterpriseID/ + 文件名
    upload_http_path: '/upload/enterprise',

    // CMS上传文件的文件系统目录
    upload_cms_path: {
      upload_path: '/opt/public/cms',
      static_root_path: 'cms', // 针对云存储可设置
    },
    // 存放培训网站相关文件系统目录，代码拼接：trainSitFile_path + 文件名
    trainSitFile_path: '/opt/public/trainSitFile',
    // 浏览培训网站相关http路径，代码拼接：/static+ trainSitFile_http_path + 文件名
    trainSitFile_http_path: '/trainSitFile',

    // 课程封面目录，代码拼接: upload_courses_path + /课程ID/ + 文件名
    upload_courses_path: '/opt/public/courses',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_courses_http_path + /课程ID/ + 文件名
    upload_courses_http_path: '/courses',

    // 存放生成的培训证书的文件系统目录，代码拼接：certificate_path + 文件名
    certificate_path: '/opt/public/certificate',
    // 下载培训证书的http路径，代码拼接：/static+ certificate_http_path + 文件名
    certificate_http_path: '/certificate',

    // 上传体检报告目录，代码拼接: upload_phy_report_path + /体检机构ID/ + 文件名
    upload_phy_report_path: '/opt/public/upload/tjReport',
    // 上行上传体检报告的http路径， 代码拼接：/static + upload_http_phy_report_path + /体检机构ID/ + 文件名
    upload_http_phy_report_path: '/upload/tjReport',

    // 用于绑定用户表单中各用户类型的ID
    groupID: {
      // 以下是cn环境的！！！
      operateGroupID: 'E1XjEmqA', // 超级管理员，运营用户角色ID
      superGroupID: 'RMwIEyjWK', // 政府用户角色ID
      adminGroupID: 'vGEfBpfsv', // 企业用户角色ID
      serviceGroupID: 'e4Qf2ic6-', // 机构用户角色ID
      userGroupID: 'V7au7L2Rw', // 劳动者用户角色ID
      physicalExamGroupID: 'zHLKFCyXD', // 体检用户角色ID
    },

    // 阿里云视频
    aliVideo: {
      accessKeyId: process.env.aliVideo_accessKeyId,
      secretAccessKey: process.env.aliVideo_secretAccessKey,
      // userId: 'process.env.aliVideo_userId',
    },
    // 人脸识别
    facebody: {
      accessKeyId: process.env.facebody_accessKeyId,
      accessKeySecret: process.env.facebody_accessKeySecret,
    },

    cluster: {
      listen: {
        port,
        hostname: '',
      },
    },
    // 用于获取页脚
    domainNames: {
      px: 'https://zywspx.hzzfy.com',
    },
    // cn环境：文档类别id设置 对应的是表contentCategory的id
    categories: {
      learning: [
        { name: '法律', id: 'E1lagiaw' }, // law
        { name: '法规', id: 'V1U2-a9le' }, // legislation
        { name: '规章', id: '4JknAqTv' }, // regulation
        { name: '规范性文件', id: 'N7c-AjP_' }, // normativeDocs
      ],
      industryNews: {
        // 首页上的
        name: '行业动态',
        id: 'PUc7czJc',
      },
      informationPlatform: {
        name: '健康企业信息平台', // 首页上的
        id: '293sdcDe',
      },
      trainingEducation: {
        // 培训首页上的
        name: '培训教育平台',
        id: '6HiSgZEGi',
      },
      health: [
        { name: '健康达人', id: 'DBw994Lmx' },
        { name: '健康企业', id: '5VWyuEgVZ' },
      ],
    },
    wxAutho: {
      // 用户微信授权
      appid: 'wx1e2839a340f00288',
      secret: 'de6ff31ed989d3730486434286741ee2',
    },
    // 服务地址配置
    server_path: `http://localhost:${port}`,
    server_api: `http://localhost:${port}/api`,
    mqtt: {
      ip: 'tcp://mqtt',
      port: 1883,
    },
    iServiceHost: 'http://iservice.cloudqas.whchem.com',
    rabbitMq: {
      url: `amqp://${process.env.rmqUser}:${process.env.rmqPass}@${process.env.rmqHost}:${process.env.rmqPort}`,
      queues: [
        {
          name: 'shaanxiHealthExamRecordData',
          exchange: 'shaanxi',
          service: 'shaanxiData',
          callback: 'uploadShaanxiHealthExamRecordData',
          routingKey: 'shaanxiHealthExamRecordData',
        },
        {
          name: 'whPersonInfo',
          exchange: 'whohs',
          service: 'whData',
          callback: 'processPersonInfo',
          routingKey: 'whPersonInfo',
        },
        {
          name: 'whAccountData',
          exchange: 'whohs',
          service: 'whData',
          callback: 'processPersonAndDingtree',
          routingKey: 'whAccountData',
        },
        {
          name: 'whOrgData',
          exchange: 'whohs',
          service: 'whData',
          callback: 'processRedisData',
          routingKey: 'whOrgData',
        },
        {
          name: 'whPostInfo',
          exchange: 'whohs',
          service: 'whData',
          callback: 'postInfoReceive',
          routingKey: 'whPostInfo',
        },
        {
          name: 'whPostAndPersonInfo',
          exchange: 'whohs',
          service: 'whData',
          callback: 'postAndPersonInfoReceive',
          routingKey: 'whPostAndPersonInfo',
        },
      ],
      // 其余配置 参考 https://www.npmjs.com/package/amqplib
    },
    coolHost: 'https://cool.zyws.cn/',
    oidc: {
      // 北元oidc认证系统
      keyCloak: {
        api_host: process.env.KEYCLOAK_API_HOST, // keycloak的api地址
        realm_name: process.env.KEYCLOAK_REALM_NAME, // keycloak的realm名称
        client_id: process.env.KEYCLOAK_CLIENT_ID, // keycloak的client_id
        client_secret: process.env.KEYCLOAK_CLIENT_SECRET, // keycloak的client_secret
        redirect_uri: process.env.KEYCLOAK_REDIRECT_URI, // keycloak的登录回调地址
        post_logout_redirect_uri: process.env.KEYCLOAK_POST_LOGOUT_REDIRECT_URI, // keycloak的退出回调地址
        qy_callback_host:
          process.env.KEYCLOAK_QY_HOST, // 企业回调地址
      },
      h5Host: process.env.ZYWS_H5_HOST || '',
    },
    qyWxAuth: {
      // 企业微信授权
      corpid: process.env.qyWxAuth_corpid.replace('\n', ''),
      corpsecret: process.env.qyWxAuth_corpsecret.replace('\n', ''),
      agentid: process.env.qyWxAuth_agentid.replace('\n', ''),
      appid: process.env.qyWxAuth_appid.replace('\n', ''),
    },
    // 微信支付宝回调地址
    wxpay_notify_url: 'https://oapi.sxbychem.com/app/pay/wxNotify',
    wxpay_refund_notify_url:
      'https://oapi.sxbychem.com/app/pay/wxRefundsNotify',
    alipay_notify_url: 'https://oapi.sxbychem.com/app/pay/alipaysNotify',
    isGetByData: false, // 是否启动获取北元数据的定时任务 除了北元其他地方不要启动否则导致数据丢失 true表示启动
    getByDataInitializationTime:
      process.env.getByDataInitializationTime.replace('\n', '') || '', //  格式为2024-03-08, // 北元数据初始化时间
    // 新增配置请放在此行上方，branch放在最后
    // 当前所属站点，用于区分不同环境的配置；默认是master；
    branch: 'huizhou', // 当前所属站点为北元化工集团环境, sxbychem.com
    // 北元通知
    byNotice: {
      // 北元接口地址
      api_host: process.env.BY_NOTICE_BASE_HOST.replace('\n', ''),
      // 北元通知的ak
      ak: process.env.BY_ACCESS_KEY.replace('\n', ''),
      // 北元通知的sk
      sk: process.env.BY_SECRET_KEY.replace('\n', ''),
    },
    byMonitor: {
      piApi: process.env.BY_MONITOR_PI_API.replace('\n', ''),
      isMock: process.env.BY_MONITOR_ISMOCK.replace('\n', ''),
      scheduleJob: process.env.BY_MONITOR_SCHEDULEJOB.replace('\n', ''),
      scheduleCron: process.env.BY_MONITOR_SCHEDULECRON.replace('\n', ''),
    },
    isAllowCreateOrgAndEmployee: false, // 是否允许创建机构和员工
    // 个人体检报告对接是否用企业id和员工身份证号查询
    isUseOrgIdAndIdCardQuery: false,
    // master和集团分支功能区分开关， 0为功能相同不需要区分，1为需要区分
    isGroupBranch: process.env.isGroupBranch || '1',
    // wh请求地址集合
    whRequest: {
      // wh用户岗位推送返回接口
      userPositionPush:
        process.env.WH_USER_POSITION_PUSH ||
        'http://jq1.whchem.com:8080/RESTAdapter/1164/UserPositionReturn',
      host: process.env.WH_HOST || 'http://************',
      qy: process.env.WH_QY || 'https://ohs-qy.cloudqas.whchem.com',
      username: process.env.WH_USER_NAME || 'user_ohs',
      password: process.env.WH_PASSWORD || 'Ohs@2024',
    },
    whDepartData: {
      disable: process.env.whDepartData !== 'true',
      immediate: true,
      interval: '20m',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    whPolicyData: {
      disable: process.env.whPolicyData !== 'true',
      immediate: true,
      interval: '1d',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    whBaseData: {
      disable: process.env.whBaseData !== 'true',
      immediate: true,
      interval: '60m',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    shaanxiDataInterfaceLog: {
      disable: process.env.shaanxiDataInterfaceLog !== 'true',
      immediate: true,
      interval: '1d',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    // model字段值是否唯一
    isUnique: process.env.ENABLE_UNIQUE !== 'false',
  };
};
