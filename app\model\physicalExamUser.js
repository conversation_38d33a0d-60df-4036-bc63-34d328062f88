/**
 * Created By htt
 * 体检端用户对象
 */
const encryptionPlugin = require('../utils/encryptionPlugin');

module.exports = app => {
  const mongoose = app.mongoose;
  const ctx = app.createAnonymousContext();
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const CryptoJS = require('crypto-js');
  const physicalExamGroupID = app.config.groupID.physicalExamGroupID || ''; // 体检用户角色ID
  const { dbEncryption = false } = app.config;
  const PhysicalExamUserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    userName: {
      // 用户名
      type: String,
      default: '',
    },
    name: {
      // 姓名
      type: String,
      default: '',
    },
    nameForStore: {
      // 加密姓名
      type: String,
    },
    nameSplitEncrypted: {
      // 分段加密的姓名
      type: String,
    },
    phoneNum: {
      type: String,
      sparse: true,
    },
    phoneNumForStore: {
      // 用于加密存储的手机号
      type: String,
    },
    phoneNumSplitEncrypted: {
      // 分段加密的手机号
      type: String,
    },
    email: {
      type: String,
      default: '',
    },
    password: {
      type: String,
      set(val) {
        if (!dbEncryption) {
          return val && val.length > 30
            ? val
            : CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
        }
        return val;
      },
    },
    org_id: {
      // 所属体检机构id
      type: String,
      default: '',
      ref: 'PhysicalExamOrg', // 关联体检机构表
    },
    org: {
      // 所属机构名, 后面已经被弃用了，不建议使用
      type: String,
      default: '',
    },
    department_id: {
      // 所属部门id
      type: String,
      default: '',
    },
    department: {
      // 所属部门名称
      type: String,
      default: '',
    },
    group: {
      type: String,
      default: physicalExamGroupID,
      ref: 'AdminGroup',
    },
    countryCode: {
      // 手机号前国家代码
      type: String,
      default: '86',
    },
    date: {
      type: Date,
      default: Date.now,
    }, // 创建/更改时间
    ctime: {
      // 创建/更改时间
      type: Date,
      default: Date.now,
    },
    logo: {
      // 头像
      type: String,
      default: '/static/upload/images/defaultlogo.png',
    },
    state: {
      // 在职状态
      type: Boolean,
      default: true,
    },
    enable: {
      // 是否能用
      type: Boolean,
      default: true,
    },
    comments: String, // 备注
    encryptionAlgorithm: {
      type: String,
    },
    passwordEncryptionAlgorithm: { // 密码加密hmac算法
      type: String,
    },
    // 人员角色权限hmac
    user_role_power_hmac: String,
    // 人员角色权限hmac算法
    user_role_power_hmac_algorithm: {
      type: String,
    },
  });
  const phoneNumField = dbEncryption ? 'phoneNumForStore' : 'phoneNum';

  // PhysicalExamUserSchema.index({ phoneNum: 1 });
  PhysicalExamUserSchema.index(
    { [phoneNumField]: 1 },
    {
      unique: true,
      partialFilterExpression: { [phoneNumField]: { $exists: true } },
    }
  );
  dbEncryption && PhysicalExamUserSchema.plugin(encryptionPlugin, {
    fields: {
      phoneNum: 11,
      name: 3,
    },
    model: 'PhysicalExamUser',
    ctx,
  });

  return mongoose.model('PhysicalExamUser', PhysicalExamUserSchema, 'physicalExamUsers');


};

