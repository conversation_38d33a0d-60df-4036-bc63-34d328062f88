module.exports = app => {
  const { router, controller, config } = app;
  const authTxt = config.authTxt;
  console.log('authTxt', authTxt);
  for (const item of authTxt.split(',')) {
    const [ path, content ] = item.split(':');
    if (path) {
      router.get(`/${path}`, ctx => {
        ctx.body =
          typeof content === 'string' ? content : 'Invalid content';
      });
    }
  }
  // 发送验证码
  router.get('/app/sendVerificationCode', controller.app.user.sendVerificationCode);
  // 获取验证码
  router.get('/app/user/getCaptcha', controller.app.sliderVerify.getCaptcha);
  // 验证验证码
  router.post('/app/user/verifyCaptcha', controller.app.sliderVerify.verifyCaptcha);
  // 用户名密码登录
  router.post('/app/user/doLogin', controller.app.user.loginAction);
  // 手机注册登录
  router.post('/app/user/smDoLogin', controller.app.user.smLoginAction);
  // 微信授权注册登录
  router.post('/app/user/authLoginAction', controller.app.user.authLoginAction);
  // 企业微信授权注册登录
  router.post('/app/user/authLoginQYWx', controller.app.user.authLoginQYWx);
  // 绑定手机号
  router.post('/app/user/bindPhoneNum', controller.app.user.bindPhoneNum);
  // 企业微信绑定手机号
  router.post('/app/user/qyWxBindPhoneNum', controller.app.user.qyWxBindPhoneNum);
  // 支付宝授权注册登录
  router.post('/app/user/alipayAuthLoginAction', controller.app.user.alipayAuthLoginAction);
  // 用户名密码注册
  router.post('/app/user/doReg', controller.app.user.regAction);
  // 微信公众号授权
  router.post('/app/user/wxH5AuthAction', controller.app.user.wxH5AuthAction);
  // 上传留言图片
  router.post('/app/user/uploadCommentImage', controller.app.user.uploadCommentImage);

  // 保存用户签名
  router.post('/app/user/uploadSignImage', controller.app.user.uploadSignImage);
  router.post('/app/user/uploadSignImg', controller.app.user.uploadSignImg);

  // 防护用品领用签名
  router.post('/app/user/ppeSign', controller.app.user.ppeSign);
  // 小程序培训首次进入的时候需要签名
  router.post('/app/user/personSign', controller.app.user.personSign);
  // 小程序培训时调用摄像头进行拍照
  router.post('/app/user/pxVerifyImage', controller.app.user.pxVerifyImage);
  router.post('/app/user/ExmaVerifyImage', controller.app.user.ExmaVerifyImage);
  router.post('/app/user/getSign', controller.app.user.getSign);
  router.post('/app/user/getFacePicture', controller.app.user.getFacePicture);

  // 将转岗的一系列通知关闭
  router.post('/app/user/closeNotify', controller.app.user.closeNotify);
  // 上传投诉建议图片
  router.post('/app/user/uploadComplaintsImage', controller.app.user.uploadComplaintsImage);
  // 查询身份证信息
  router.post('/manage/user/findInfoByIDNum', controller.app.user.findInfoByIDNum);
  // 获取法律法规列表
  router.post('/app/learning/list', controller.app.learning.list);
  // 获取文章类别id
  router.get('/app/learning/categories', controller.app.learning.categories);
  router.get('/', controller.home.index);
  // 只有当branch为'fz'时才启用fzdata路由
  if (config.branch === 'fz') {
    router.get('/fzdata', controller.home.fzDataHtml);
  }
  // 直接返回GFbLsgrBx2.txt文件
  router.get('/GFbLsgrBx2.txt', controller.home.GFbLsgrBx2);
  router.get('/WW_verify_5F27NpogfHZeGJaa.txt', controller.home.WW_verify_5F27NpogfHZeGJaa);
  router.get('/WW_verify_hdQcbJlYi2xY4dH9.txt', controller.home.WW_verify_hdQcbJlYi2xY4dH9);
  // 首页中全局搜索
  router.post('/app/learning/globalSearch', controller.app.learning.globalSearch);
  // 获取公开课列表
  router.post('/app/learning/publicCoursesList', controller.app.learning.publicCoursesList);
  // 用户名密码登录
  router.post('/app/loginAction', controller.app.allUser.loginAction);
  // 手机号验证码登录
  router.post('/app/smLoginAction', controller.app.allUser.smLoginAction);
  // 修改密码
  router.post('/app/allUser/updatePwd', controller.app.allUser.updatePwd);
  // 登录状态校验
  router.get('/app/allUser/loginVerification', controller.app.allUser.loginVerification);
  // app检查更新
  router.post('/app/checkForUpdate', controller.app.allUser.checkForUpdate);
  // 培训-专家入驻 注册
  router.post('/app/user/expertRegAction', controller.app.user.expertRegAction);
  router.get('/app/user/getInfo', controller.app.user.getInfo);

  // 获取PPE示范视频
  router.get('/app/ppeVideo/getPPEVideo', controller.app.ppeVideo.getPPEVideo);

  // 支付
  router.post('/app/pay/nativePay', controller.app.pay.nativePay);
  // 支付宝回调通知
  router.post('/app/pay/alipayNotify', controller.app.pay.alipayNotify);
  // 查询支付宝订单信息
  router.post('/app/pay/alipayCheck', controller.app.pay.alipayCheck);
  // 支付宝关闭订单
  router.post('/app/pay/alipayCloseOrder', controller.app.pay.alipayCloseOrder);
  // 退款
  // router.post('/app/pay/wxRefunds', controller.app.pay.wxRefunds);
  // 支付宝退款
  // router.post('/app/pay/alipayRefunds', controller.app.pay.alipayRefunds);
  // 微信回调
  router.post('/app/pay/wxNotify', controller.app.pay.wxNotify);
  // 微信退款回调
  router.post('/app/pay/wxRefundsNotify', controller.app.pay.wxRefundsNotify);
  // 微信查单
  router.post('/app/pay/wxpayCheck', controller.app.pay.wxpayCheck);
  // 微信关闭订单
  router.post('/app/pay/wxCloseOrder', controller.app.pay.wxCloseOrder);
  // 查询订单
  router.post('/app/pay/orderCheck', controller.app.pay.orderCheck);
  // 微信小程序支付
  router.post('/app/pay/wxSpPay', controller.app.pay.wxSpPay);
  // // 混合退款
  // router.post('/app/pay/refunds', controller.app.pay.refunds);
  // router.post('/app/pay/getTransactionsByOutTradeNo', controller.app.pay.getTransactionsByOutTradeNo);

  // 培训支付
  router.post('/app/trainPay/trainNativePay', controller.app.trainPay.trainNativePay);
  router.post('/app/trainPay/trainGroupPay', controller.app.trainPay.trainGroupPay);


  router.post('/app/face/test', controller.app.face.test);

  // 劳动者app获取场所名称
  router.get('/app/user/getWorkPlace', controller.app.user.getWorkPlace);

  // 劳动者app查询通知
  router.post('/app/user/findNotices', controller.app.user.findNotices);
  // 劳动者app查询转岗通知
  router.post('/app/user/getReorientationInfo', controller.app.user.getReorientationInfo);
  // 劳动者app查询历年转岗通知
  router.post('/app/user/getAllReorientationInfo', controller.app.user.getAllReorientationInfo);
  // 福州小游戏 H5 微信静默授权
  router.post('/app/user/fzxyxAuthAction', controller.app.user.fzxyxAuthAction);
  router.get('/app/quizGame/gameEventInfo', controller.app.user.getGameEventInfo);

  router.get('/app/getHbdpData', controller.app.user.getHbdpData);

  // 发送注册验证码
  router.get('/app/sendVerificationCodeRegister', controller.app.user.sendVerificationCodeRegister);
  // 注册
  router.post('/app/user/register', controller.app.user.register);
};
