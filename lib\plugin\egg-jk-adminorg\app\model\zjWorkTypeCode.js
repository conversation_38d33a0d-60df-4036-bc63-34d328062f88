/**
 * Created by xxn on 2024/4/10.
 * 浙江标准 工种代码表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const ZjWorkTypeCodeSchema = new Schema({
    _id: {
      type: String,
    },
    code: { // 工种代码
      type: String,
      trim: true,
      required: true,
      unique: true,
    },
    name: { // 工种名称
      type: String,
      trim: true,
      required: true,
    },
  });

  return mongoose.model('ZjWorkTypeCode', ZjWorkTypeCodeSchema, 'zjWorkTypeCode');

};
