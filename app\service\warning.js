const Service = require('egg').Service;
// const { siteFunc } = require('@utils');
/* 预警
将根据企业的检测结果和体检结果，生成风险预警等级。
一级预警：新发职业病病例1例及以上。
二级预警：有疑似职业病案例1例及以上，严重危害因素超标一个点位或一个以上点位，其他危害因素超标1倍以上，噪声95dB（A）以上。
三级预警：危害因素检测结果非严重危害因素超标1个点位以上。
四级预警：职业健康检查发现复查禁忌证和复查，噪声超标：85-95dB（A）

一级预警、二级预警：监管部门需要进行现场监督检测。
三级预警、四级预警：发短信要求企业完成现场整改。
*/

class WarningService extends Service {

  // 获取当前用户的名称
  async findCurUserName(physicalExaminationOrgID = null) {
    const { ctx } = this;
    const org = await ctx.model.PhysicalExamOrg.findOne(
      { _id: physicalExaminationOrgID || '' },
      { name: true }
    );
    return org ? org.name : '';
  }

  // 修改预警：内容、等级、工作场所
  async update(_id, warningLever, warningList, workAddress, physicalExaminationOrgID) {
    const { ctx } = this;
    const oldWarning = await ctx.model.Warning.findOne(
      { _id },
      { content: 1, lever: 1, status: 1, workAddress: 1 }
    );
    if (!oldWarning) return '未找到相关预警或者已删除';
    const changeFlag1 = oldWarning.content.toString() === warningList.toString();
    const changeFlag2 = workAddress.toString() === oldWarning.workAddress.toString();
    if (changeFlag1 && changeFlag2) return '预警的内容和地址都没有变化，不用更新';

    // 更新预警
    const updateUserName = await this.findCurUserName(physicalExaminationOrgID);
    let newStatus;
    if (!changeFlag1 && oldWarning.status === 5) { // 已撤销的预警又死灰复燃了
      newStatus = 1;
    } else {
      newStatus = warningList.length === 0 ? 5 : oldWarning.status;
    }
    const newData = {
      content: warningList.length === 0 ? [ `该条预警已被${updateUserName}撤销` ] : warningList,
      lever: warningLever ? +warningLever : oldWarning.lever,
      status: newStatus,
      workAddress,
    };
    if (!changeFlag1) {
      // 预警内容改变了, 需要添加操作记录
      const oldLever = [ '', '一', '二', '三', '四' ][oldWarning.lever];
      newData.$push = {
        process: {
          time: Date.now(),
          thing: `${warningList.length === 0 ? '撤销预警' : '修改预警'}（${updateUserName}）`,
          remark: '修改前：【' + oldLever + '级预警】预警内容：' + oldWarning.content.join('; '),
          content: warningList,
        },
      };
    }

    const res = await ctx.model.Warning.update(
      { _id },
      newData
    );
    if (res && res.ok === 1) {
      ctx.auditLog('预警记录改动', `${updateUserName}修改了id为${_id}的预警`, 'info');
    } else {
      ctx.auditLog('预警记录改动', `${updateUserName}修改id为${_id}的预警失败`, 'warn');
    }
    return res;
  }

  // // 撤销预警
  // async cancel(query) {
  //   const { ctx } = this;
  //   const oldWarning = await ctx.model.Warning.findOne({
  //     ...query,
  //     delete: false,
  //     status: { $ne: 3 }, // 已解除的预警不能修改
  //   }, {
  //     content: true,
  //     lever: true,
  //   });
  //   if (!oldWarning) {
  //     return '未找到相关预警或者已删除';
  //   }
  //   const oldLever = [ '', '一', '二', '三', '四' ][oldWarning.lever];
  //   const updateUserName = await this.findCurUserName(); // 修改的企业的名称
  //   const res = await ctx.model.Warning.update(
  //     { _id: oldWarning._id },
  //     {
  //       content: [ `该条预警已被${updateUserName}撤销` ],
  //       status: 5,
  //       $push: {
  //         process: {
  //           time: Date.now(),
  //           thing: `撤销预警（${updateUserName}）`,
  //           remark: '修改前：【' + oldLever + '级预警】预警内容：' + oldWarning.content.join('; '),
  //           content: [],
  //         },
  //       },
  //     });
  //   if (res && res.ok === 1) {
  //     ctx.auditLog('预警记录改动', `${updateUserName}撤销了id为${oldWarning._id}的预警`);
  //   } else {
  //     ctx.auditLog('预警记录改动', `${updateUserName}修改id为${oldWarning._id}的预警失败`, 'warn');
  //   }
  //   return res;
  // }

  // // 预警短信内容处理
  // async dealWithVerifyMassage(companyDetail, org_id, msgContent) {
  //   const { ctx, app } = this;
  //   if (companyDetail.phoneNum) {
  //     // pc端页面消息提醒
  //     const res = await ctx.service.messageNotification.sendMessage(
  //       '体检机构信息',
  //       msgContent,
  //       [{
  //         readerID: companyDetail._id,
  //         readerGroup: app.config.groupID.adminGroupID,
  //         isRead: 0,
  //       }],
  //       ctx.session.physicalExamUserInfo.EnterpriseID,
  //       ctx.session.physicalExamUserInfo.group
  //     );
  //     console.log('pc端体检机构信息提醒:', res);
  //     // 开始处理手机短信提醒部分
  //     const phoneNum = app.config.admin_root_path === '/static' ? companyDetail.phoneNum : '18868812544'; // 找到企业联系人电话
  //     const org = await ctx.model.PhysicalExamOrg.findOne({ _id: org_id }, { phoneNum: true, name: true });
  //     if (msgContent.length > 20) {
  //       msgContent = msgContent.slice(0, 19);
  //     }
  //     ctx.service.warning.sendVerifyMassage(companyDetail.cname, msgContent, org.name || '', phoneNum, org.phoneNum || '');
  //   } else {
  //     ctx.auditLog('预警短信发送失败', `给名为${companyDetail.cname}的企业发送预警提示短信失败，因为没有企业联系电话未设置。`, 'warn');
  //   }
  // }
  // // 发送预警督促信息
  // async sendVerifyMassage(companyName, content, org, phoneNum, orgPhone) {
  //   const { ctx } = this;
  //   const MessageConfig = this.app.config.aliMessage || '';
  //   try {
  //     // console.log(999999, MessageConfig.accessKeyId, companyName, content, org, phoneNum, orgPhone);
  //     const TemplateParam = {
  //       cname: companyName,
  //       content,
  //       sname: org,
  //       phone: orgPhone,
  //     };
  //     const params = {
  //       RegionId: 'cn-hangzhou',
  //       TemplateCode: 'SMS_205890689',
  //       TemplateParam: JSON.stringify(TemplateParam),
  //       PhoneNumbers: phoneNum,
  //       SignName: '职业健康信息化平台',
  //       accessKeyId: MessageConfig.accessKeyId,
  //     };
  //     // 发送短消息
  //     const alires = await siteFunc.sendTellMessagesByPhoneNum(MessageConfig, params);
  //     console.log('阿里短信返回', alires);
  //     return true;
  //   } catch (err) {
  //     ctx.helper.renderFail(ctx, {
  //       message: err,
  //     });
  //   }
  // }

  // // 获取某条预警的预警状态和是否可以编辑
  // async getModifiedStatus(jobHealthId) {
  //   if (!jobHealthId) return '原数据id未传';
  //   const { ctx } = this;
  //   const warningDetail = await ctx.model.Warning.findOne(
  //     { jobHealthId, delete: false },
  //     { modifiedStatus: 1 }
  //   );
  //   // console.log(111111, jobHealthId, warningDetail);
  //   if (warningDetail && [ 1, 2, 4 ].includes(warningDetail.modifiedStatus)) {
  //     return warningDetail.modifiedStatus; // 不可修改
  //   }
  //   return 0; // 表示可以修改
  // }
  // // 修改预警的修改状态 2表示提交修改预警和项目数据的申请
  // async applyModify(jobHealthId = '', modifiedStatus = 2, reason = '') {
  //   if (!jobHealthId) return '原数据id必须传';
  //   const { ctx } = this;
  //   const warningDetail = await ctx.model.Warning.findOne(
  //     { jobHealthId, delete: false },
  //     { modifiedStatus: 1 }
  //   );
  //   if (!warningDetail || !warningDetail._id) return `未找到jobHealthId为${jobHealthId}的相关预警`;
  //   if (warningDetail.modifiedStatus === 2) return '请勿重复提交修改申请';
  //   if (warningDetail.modifiedStatus === 4) return '您的修改申请已被驳回，详见预警';
  //   const org = await ctx.model.PhysicalExamOrg.findOne(
  //     { _id: ctx.session.physicalExamUserInfo.EnterpriseID },
  //     { name: true }
  //   );
  //   const updateUserName = org ? org.name : '';
  //   await ctx.model.Warning.updateOne(
  //     { _id: warningDetail._id },
  //     {
  //       modifiedStatus,
  //       $push: {
  //         process: {
  //           time: Date.now(),
  //           thing: '申请修改上报数据',
  //           remark: `${updateUserName}申请修改上报数据（理由：${reason}）`,
  //         },
  //       },
  //     },
  //     {
  //       upsert: true,
  //       new: true,
  //     }
  //   );
  //   return '修改申请已提交';
  // }

}

module.exports = WarningService;
