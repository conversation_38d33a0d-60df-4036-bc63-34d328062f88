'use strict';
/**
 * DES加密解密工具类
 * iwangxi
 */
const CryptoJS = require('crypto-js');
const fs = require('fs');
const path = require('path');
const JSZip = require('jszip');
const axios = require('axios');
const FormData = require('form-data');
const moment = require('moment');
const mkdirp = require('mkdirp');
// const apiHost = 'http://*************:8002/BhkDataService';
class EncryptUtil {
  static encrypt(source, key) {
    try {
      return this.encryptByDes(this.pkcs5Pad(source), this.pkcs5Pad(key));
    } catch (e) {
      console.error(e);
      return '';
    }
  }
  static encryptByKey(source, key) {
    try {
      return this.encryptByDes(this.pkcs5Pad(source), this.pkcs5Pad(key));
    } catch (e) {
      console.error(e);
      return null;
    }
  }
  static decrypt(source, key) {
    try {
      return this.decryptByDes(source, this.pkcs5Pad(key)).trim().replace(/\0/g, '');
    } catch (e) {
      return '';
    }
  }
  static decryptByKey(source, key) {
    try {
      return this.decryptByDes(source, this.pkcs5Pad(key)).trim();
    } catch (e) {
      console.error(e);
      return null;
    }
  }
  static encryptByDes(source, key) {
    const keyHex = CryptoJS.enc.Utf8.parse(key);
    const encrypted = CryptoJS.DES.encrypt(source, keyHex, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });
    return encrypted.ciphertext.toString();
  }
  static decryptByDes(ciphertext, key) {
    const keyHex = CryptoJS.enc.Utf8.parse(key);
    const decrypted = CryptoJS.DES.decrypt(
      {
        ciphertext: CryptoJS.enc.Hex.parse(ciphertext),
      },
      keyHex,
      {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
      }
    );
    return decrypted.toString(CryptoJS.enc.Utf8);
  }
  static pkcs5Pad(source) {
    const blockSize = 8; // DES块大小为8字节
    const paddingChar = '\u0000'; // 使用ASCII码为0的字符进行填充
    const padLength = blockSize - (source.length % blockSize);
    let padded = source;
    for (let i = 0; i < padLength; i++) {
      padded += paddingChar;
    }
    return padded;
  }
  static async postDataAndProcessResponse(ctx, apiUrl, data, key) {
    try {
      // 添加延时器，1分钟后继续执行 防止接口被频繁调用
      await new Promise(resolve => setTimeout(resolve, 60 * 1000));
      const apiHost = ctx.app.config.fzDataApiHost;
      const url = `${apiHost}${apiUrl}`; // 拼接url
      const clearText = JSON.stringify(data);
      ctx.auditLog('接口地址和传参明文：' + url, clearText, 'info');
      const encryptText = EncryptUtil.encrypt(clearText, key);
      ctx.auditLog('传参密文：', encryptText, 'info');
      const zip = new JSZip();
      zip.file('data.json', encryptText);
      const zipContent = await zip.generateAsync({
        type: 'nodebuffer',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 9,
        },
      });
      // 拼接从 config.upload_fzDataLog_path 读取的路径 判断是否存在
      // await mkdirp(path.resolve(app.config.report_template_path));
      const DATE = new Date();
      const filesName = moment(DATE).format('YYYYMMDD'); // 文件夹名称
      const configFilePath = path.resolve(
        path.join(ctx.app.config.upload_fzDataLog_path, `/${filesName}`)
      );
      await mkdirp(configFilePath);
      fs.writeFileSync(
        path.join(
          configFilePath,
          `${moment(DATE).format('HHmmss')}To${apiUrl.replace('/', '')}.zip`
        ),
        zipContent
      );
      const buffer = Buffer.from(zipContent);
      const formData = new FormData();
      formData.append('1', buffer, 'data.zip');
      const config = {
        method: 'post',
        url,
        headers: {
          Accept: '*/*',
          Host: '*************:8002',
          Connection: 'keep-alive',
          'Content-Type': formData.getHeaders()['content-type'],
          ...formData.getHeaders(),
        },
        responseType: 'arraybuffer',
        data: formData,
      };
      const response = await axios(config);
      const zipResponse = await JSZip.loadAsync(response.data);
      // 将获取到zip保存到本地
      fs.writeFileSync(
        path.join(
          configFilePath,
          `${moment(DATE).format('HHmmss')}From${apiUrl.replace('/', '')}.zip`
        ),
        response.data
      );
      const decryptedData = await zipResponse.file('data.json').async('string');
      const decryptText = EncryptUtil.decrypt(decryptedData, key);
      ctx.auditLog('接口返回解密后：', decryptText, 'info');
      const resStr = decryptText.replace(/\0/g, '');
      const resObj = JSON.parse(resStr);
      return resObj;
    } catch (error) {
      console.error('Error:', error);
    }
  }

}

// const dataToPost = {
//   unitCode: '35010899',
//   regCode: 'a8e32a5d9a724293a71f200c23c0f7fc',
// };

// EncryptUtil.postDataAndProcessResponse(dataToPost, 'chiscdc@bhkdownload#$%^');
// 导出
module.exports = EncryptUtil;
