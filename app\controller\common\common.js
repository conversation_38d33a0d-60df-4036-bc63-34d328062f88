const Controller = require('egg').Controller;
const { promisify } = require('util');
const sleep = promisify(setTimeout);
class CommonController extends Controller {

  async list() {
    const { ctx } = this;
    try {
      console.log('33333333333');
      await sleep(5000);
      // const apiUserList = await ctx.service.apiUser.find({ isPaging: '0', pagSize: 10 });
      console.log(ctx.model.ApiUser);
      ctx.helper.renderSuccess(ctx, {
        data: '',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

}
module.exports = CommonController;
