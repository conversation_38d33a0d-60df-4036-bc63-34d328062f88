const crypto = require('crypto');
// 对查询参数进行排序
function sortQuery(jsonObj) {
  if (jsonObj == null) return '';
  const lowerCasekeyObj = {};
  const lowerCasekeyArr = [];
  let lowerKey = '';
  for (const key in jsonObj) {
    lowerKey = key.toLowerCase();
    lowerCasekeyArr.push(lowerKey);
    lowerCasekeyObj[lowerKey] = jsonObj[key];
  }
  let res = '';
  lowerCasekeyArr.sort();
  for (const i in lowerCasekeyArr) {
    const key = lowerCasekeyArr[i];
    res += lowerCasekeyArr[i] + '=' + lowerCasekeyObj[key] + '&';
  }
  return res.substring(0, res.length - 1);
}

// 自定义请求头排序
function sortCustomHeader(headerJson) {
  if (headerJson == null) return '';
  const lowerCasekeyObj = {};
  const lowerCasekeyArr = [];
  let lowerKey = '';
  for (const key in headerJson) {
    if (key.startsWith('X-MC-')) {
      lowerKey = key.toLowerCase();
      lowerCasekeyArr.push(lowerKey);
      lowerCasekeyObj[lowerKey] = headerJson[key];
    }
  }
  let res = '';
  lowerCasekeyArr.sort();
  for (const i in lowerCasekeyArr) {
    const key = lowerCasekeyArr[i];
    res += lowerCasekeyArr[i] + ':' + lowerCasekeyObj[key] + ';';
  }
  return res.substring(0, res.length - 1);
}

// json转查询串
function json2query(jsonObj) {
  let res = '';
  for (const key in jsonObj) {
    res += key + '=' + jsonObj[key] + '&';
  }
  return res.substring(0, res.length - 1);
}
function buildSignString(uri, methodName, queryJson, headerJson) {
  let signStr = '';
  // HTTP Schema
  signStr = signStr + methodName + '\n';
  // HTTP URI
  signStr = signStr + uri + '\n';
  // HTTP ContentType
  signStr = signStr + headerJson['Content-Type'] + '\n';
  // CanonicalQueryString
  signStr = `${signStr}${sortQuery(queryJson)}\n`;
  // CanonicalCustomHeaders
  signStr = signStr + sortCustomHeader(headerJson);

  return signStr;
}

// // 使用token方式签名
// function signHeaderWithToken(uri, methodName, queryJson, headerJson) {
//   const signStr = buildSignString(uri, methodName, queryJson, headerJson);
//   console.log('签名源==========Start===========');
//   console.log(signStr);
//   console.log('签名源==========End===========');
//   const signature = crypto
//     .createHmac('sha256', APP_SECRET)
//     .update(signStr, 'utf8')
//     .digest('hex');
//   const final_signature = 'Sign ' + APP_ID + '-' + signature;
//   console.log('签名结果：', final_signature);
//   headerJson.Authorization = final_signature;
// }
// 使用aksk方式签名
function signHeaderWithAkSk(uri, methodName, queryJson, headerJson, ak, sk) {
  const signStr = buildSignString(uri, methodName, queryJson, headerJson) + '\n';
  console.log('签名源==========Start===========');
  console.log(signStr);
  console.log('签名源==========End===========');
  const signature = crypto
    .createHmac('sha256', sk)
    .update(signStr, 'utf8')
    .digest('hex');
  const final_signature = 'Sign ' + ak + '-' + signature;
  console.log('签名结果：', final_signature);
  headerJson.Authorization = final_signature;
}
// 将方法导出
module.exports = {
  // signHeaderWithToken,
  signHeaderWithAkSk,
  json2query,
};
