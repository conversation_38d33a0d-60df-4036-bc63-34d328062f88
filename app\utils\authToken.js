const jwt = require('jsonwebtoken');


const token = {

  async checkToken(token, encrypt_key, ctx) {
    // return new Promise((resolve, reject) => {
    // 首先检查token是否在黑名单中
    const isBlacklisted = await ctx.helper.getCache(`token_blacklist_${token}`);
    if (isBlacklisted) {
      return false;
    }
    return new Promise(resolve => {
      jwt.verify(token, encrypt_key, function(err, decoded) {
        if (err) {
          console.log('/utils/authToken.js的错误：');
          console.log(err);
          resolve(false);
        } else {
          console.log('---decoded---', decoded);
          resolve(decoded);
        }
      });
    });
  },

};
module.exports = token;
