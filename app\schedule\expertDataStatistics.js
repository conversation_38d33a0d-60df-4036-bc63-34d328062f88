
// 培训后台主页 数据统计
module.exports = () => {
  return {
    schedule: {
      cron: '0 0 8 */1 * ?', // 每天8点执行一次
      disable: false,
      immediate: false, // 启动服务时就立即执行一次任务
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },

    // 定时执行的任务
    async task(ctx) {
      try {
        const experts = await ctx.model.User.find({ role: 'expert', state: '1' }, { followers: 1 });
        for (let i = 0; i < experts.length; i++) {
          const expert = experts[i];
          const userID = expert._id;
          const fans = expert.followers.length;
          let views = 0,
            likes = 0;
          const courses = await ctx.model.Courses.find({ source: 'user', authorID: userID }, { likes: 1, views: 1 });
          for (let j = 0; j < courses.length; j++) {
            views += courses[j].views;
            likes += courses[j].likes;
          }
          await ctx.model.ExpertDataStatistics.create({
            userID,
            views,
            likes,
            fans,
          });
        }
      } catch (error) {
        ctx.auditLog('专家培训数据统计定时器报错', JSON.stringify(error), 'error');
      }
    },
  };
};
