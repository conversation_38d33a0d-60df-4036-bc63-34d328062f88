module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 考试大纲
  const ExamSyllabusSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 考试大纲名称
      type: String,
      required: true,
    },
    certificateType: { // 证书类型: 1企业负责人初次培训、2企业负责人继续教育、3职业健康管理人员初次培训、4职业健康管理人员继续教育、5劳动者上岗前培训、6劳动者在岗培训
      type: Number,
      required: true,
      enum: [ 1, 2, 3, 4, 5, 6 ],
    },
    passingPercentage: { // 通过百分比，单位%
      type: Number,
      required: true,
      min: [ 0, 'passingPercentage 不能小于 0' ],
      max: [ 100, 'passingPercentage 不能大于 100' ],
    },
    examDuration: { // 考试时长, 单位分钟
      type: Number,
      required: true,
      min: [ 1, 'examDuration 不能小于 1 分钟' ],
    },
    outlineList: [{ // 大纲内容列表
      _id: {
        type: String,
        default: shortid.generate,
      },
      outline: { // 大纲id
        type: [ String ],
        validator: {
          validator: v => v.length === 2,
          message: 'outline 必须包含一个一级大纲 id 和一个二级大纲 id',
        },
      },
      rules: [{
        _id: {
          type: String,
          default: shortid.generate,
        },
        topicType: { // 题目类型 1：单选 2：多选 3：判断 4：填空 5：问答（4，5暂时用不上）
          type: Number,
          required: true,
          enum: [ 1, 2, 3 ],
        },
        quantity: { // 数量
          type: Number,
          default: 0,
        },
        topicScore: { // 题目分数
          type: Number,
          default: 0,
          min: [ 1, 'topicScore 不能小于 1 分' ],
          max: [ 100, 'topicScore 不能大于 100 分' ],
        },
      }],
    }],
    creator: { // 创建人id
      type: String,
      ref: 'SuperUser',
      required: true,
    },
    source: { // 来源
      type: String,
      enum: [ 'SuperUser' ], // SuperUser: 监管端
      default: 'SuperUser',
    },
    status: { // 状态
      type: Number,
      enum: [ 0, 1 ], // 1: 启用 0: 禁用
      default: 1,
    },
  }, { timestamps: true });


  return mongoose.model('ExamSyllabus', ExamSyllabusSchema, 'examSyllabus');
};
