module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const medicalExamCyclesSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      harmFactorId: {
        type: String,
        ref: 'OccupationalexposureLimits',
        required: true,
      },
      // 作业场所等级
      // workplaceLevel: {
      //   type: String,
      //   ref: 'WorkplaceLevels',
      //   required: true,
      // },
      cycleDuration: {
        type: Number, // 数值部分
        required: true,
      },
      cycleUnit: {
        type: String, // 'days', 'weeks', 'months', 'years'
        required: true,
      },
      endCondition: {
        type: String, // 'fixed_date', 'repeat', 'limited_times'
        required: true,
      },
      endDate: {
        type: Date,
      },
      // 生效时间
      effectiveTime: {
        type: Date,
        required: true,
      },
      repeatTimes: {
        type: Number, // 使用时的次数限制
      },
    },
    { timestamps: true }
  );
  return mongoose.model('medicalExamCycle', medicalExamCyclesSchema);
};

