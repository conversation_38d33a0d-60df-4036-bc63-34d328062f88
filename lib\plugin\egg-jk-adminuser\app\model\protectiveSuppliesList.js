/*
 * @Author: 林卉桐
 * @Date: 8 26th, 2021 2:22
 * @LastEditors: 汪周强
 * @LastEditTime: 2023-10-19 15:05:19
 * @Description: 各企业的防护用品清单
 *
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const protectiveSuppliesListSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: { type: String },
    list: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        name: String, // 表格名称
        tableHeader: Array, // 表头设置
        data: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          materialCode: String, // 物料编码
          product: String, // 产品名称
          productSpec: String, // 产品规格
          style: String, // 样式
          // classification: String, // 分类
          modelNumber: String, // 型号
          protectionType: String, // 防护类型
          APF: String, // APF
          useMethod: String, // 使用方式
          accessoryInfo: String, // 可配滤盒数
          screenThickness: String, // 面屏厚度
          // partsName: String, // 配件型号
          function: String, // 防护用途
          vender: String, // 厂家
          characteristic: String, // 特点
          industryEnvironment: String, // 使用行业或环境
          harmFactors: Array, // 危害因素
          surplus: {
            type: Number,
            default: 0,
          }, // 库存
          NRR: String, // NRR（dB）
          SNR: String, // SNR（dB）
          packing: String, // 包装
          picture: String, // 图片文件
        }],
      },
    ],

  });
  return mongoose.model('protectiveSuppliesList', protectiveSuppliesListSchema);
};
