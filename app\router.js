module.exports = app => {
  require('./router/api')(app);
  require('./router/app')(app);
  require('./router/jcqlc')(app);
  require('./router/manage')(app);
  require('./router/govApp')(app);
  require('./router/govManage')(app);
  require('./router/qlcManage')(app);
  require('./router/dpd')(app);
  // require('./router/common')(app);
  // 钉钉事件订阅
  const { router, controller } = app;
  router.post('/dingSubscript', controller.home.dingSubscript);
};
