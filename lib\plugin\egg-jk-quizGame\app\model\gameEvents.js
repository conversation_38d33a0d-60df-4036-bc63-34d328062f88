
// 临安小游戏 活动设置
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const GameEventSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    // 主办方
    organizer: {
      type: String,
      trim: true,
    },
    name: { // 活动名称
      type: String,
      required: true,
      index: true,
    },
    startTime: { // 活动开始时间
      type: Date,
      required: true,
      Set: v => new Date(v),
    },
    endTime: { // 活动结束时间
      type: Date,
      required: true,
      Set: v => new Date(v),
    },
    requiredQuestionBank: { // 必选题库id
      type: String,
      ref: 'GameQuestionBank',
    },
    optionalQuestionBank: [ // 可选题库
      {
        _id: { // 可选题库id
          type: String,
          ref: 'GameQuestionBank',
        },
        num: { // 数量
          type: Number,
          min: 1,
          Set: v => Number(v),
        },
      },
    ],
    totalTopicNum: { // 总题数, >= 必选题库题数
      type: Number,
      min: 1,
      Set: v => Number(v),
    },
    getPrizeChanceNum: { // 获得抽奖机会的题数 <= 总题数
      type: Number,
      min: 1,
      Set: v => Number(v),
    },
    dailyAnswerNum: { // 每天答题次数, 没有即为无限制
      type: Number,
      Set: v => Number(v),
    },
    everyTimeWrongNum: { // 每次允许错题数, 没有即为无限制
      type: Number,
      Set: v => Number(v),
    },
    answerTimeLimit: { // 答题时间限制，单位秒, 没有即为无限制
      type: Number,
      Set: v => Number(v),
    },
    topicScore: { // 题目积分设置（按难、中、易3种类型）
      easy: {
        type: Number,
        default: 1,
        Set: v => Number(v),
      },
      medium: {
        type: Number,
        default: 1,
        Set: v => Number(v),
      },
      hard: {
        type: Number,
        default: 1,
        Set: v => Number(v),
      },
    },
    gameRule: { // 游戏规则描述 文本编辑器生成的html
      type: String,
      default: '',
    },
    prizeRule: { // 抽奖规则描述 文本编辑器生成的html
      type: String,
      default: '',
    },
    needWinnerInfo: { // 是否需要填写中奖者信息
      type: Boolean,
      default: false,
    },
    winnerInfo: [{ // 中奖者需要填写的信息
      type: String,
      enum: [ 'EnterpriseName', 'name', 'phoneNum', 'phoneCarrier', 'address', 'idCard' ],
    }],
    rankListNum: { // 排行榜显示人数
      type: Number,
      default: 30,
      set: v => Number(v),
    },
    enable: { // 是否可用，逻辑删除用的
      type: Boolean,
      default: true,
    },
    creator: { // 创建者
      type: String,
      default: '',
    },
    // 是否开启人机验证
    isCaptcha: {
      type: Boolean,
      default: false,
    },
  }, { timestamps: true });


  return mongoose.model('GameEvents', GameEventSchema, 'gameEvents');
};
