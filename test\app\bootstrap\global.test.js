const { app, mock, assert } = require('egg-mock/bootstrap');
const shortid = require('shortid');

before(() => {
  // 等待 app 启动成功，才能执行测试用例
  return app.ready();
});
afterEach(mock.restore);

describe('test/app/bootstrap/global.test.js', () => {
  it('global.checkCurrentId', function* () {
    const resultNull = global.checkCurrentId();
    const resultErr = global.checkCurrentId(123);
    const resultTrue = global.checkCurrentId(`${shortid.generate()},${shortid.generate()}`);
    const resultFalse = global.checkCurrentId(',');
    assert(resultTrue && !resultFalse && !resultNull && !resultErr);
  });

  it('global.getStrLength', function* () {
    const result = global.getStrLength('柳儿 is Girl!');
    assert(result);
  });

  it('global.getDateStr', function* () {
    const result = global.getDateStr(3);
    assert(result);
  });

  it('global.getAuthUserFields', function* () {
    const result1 = global.getAuthUserFields('login');
    const result2 = global.getAuthUserFields('base');
    const result3 = global.getAuthUserFields('session');
    assert(result1 && result2 && result3);
  });

  it('global.getContentListFields', function* () {
    const result = global.getContentListFields();
    const result1 = global.getContentListFields('normal');
    const result2 = global.getContentListFields('simple');
    const result3 = global.getContentListFields('stage1');
    assert(result && result1 && result2 && result3);
  });
});
