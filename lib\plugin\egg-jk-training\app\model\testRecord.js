module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 培训的考试记录
  const testRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    personalTrainingId: { // 个人培训id
      type: String,
      ref: 'PersonalTraining',
    },
    courseId: { // 课程id, 没有的话就表示是大考
      type: String,
      ref: 'PersonalTraining',
    },
    testResult: Object, // 考试结果(包含了topicId以及答题结果)
    resultStatistics: Object, // 考试结果统计
  }, {
    timestamps: true,
  });

  return mongoose.model('TestRecord', testRecordSchema, 'testRecord');
};
