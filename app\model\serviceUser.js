/**
 * 机构端用户对象
 */
const encryptionPlugin = require('../utils/encryptionPlugin');
module.exports = app => {
  const mongoose = app.mongoose;
  const ctx = app.createAnonymousContext();
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const CryptoJS = require('crypto-js');
  const serviceGroupID = app.config.groupID.serviceGroupID || ''; // 机构用户角色ID

  require('./adminGroup');
  const { dbEncryption = false } = app.config;

  const ServiceUserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    userName: { // 用户名
      type: String,
      default: '',
    },
    name: { // 姓名
      type: String,
      default: '',
    },
    nameForStore: {
      // 加密姓名
      type: String,
    },
    nameSplitEncrypted: {
      // 分段加密的姓名
      type: String,
    },
    phoneNum: {
      type: String,
      index: true, // 普通索引
      require: true,
    },
    phoneNumForStore: {
      // 用于加密存储的手机号
      type: String,
    },
    phoneNumSplitEncrypted: {
      // 分段加密的手机号
      type: String,
    },
    email: {
      type: String,
      default: '',
    },
    password: {
      type: String,
      set(val) {
        if (!dbEncryption) {
          return CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
        }
        return val;
      },
    },
    org_id: { // 所属机构id
      type: String,
      default: '',
      ref: 'ServiceOrg', // 关联机构表
    },
    org: { // 所属机构名
      type: String,
      default: '',
    },
    department_id: { // 所属部门id
      type: String,
      default: '',
    },
    department: { // 所属部门名称
      type: String,
      default: '',
    },
    isQLC: { // 是否机构VIP(全流程)用户
      type: Boolean,
      default: false,
    },
    group: {
      type: String,
      default: serviceGroupID,
      ref: 'AdminGroup',
    },
    countryCode: { // 手机号前国家代码
      type: String,
      default: '86',
    },
    date: {
      type: Date,
      default: Date.now,
    }, // 创建/更改时间
    ctime: { // 创建/更改时间
      type: Date,
      default: Date.now,
    },
    logo: { // 头像
      type: String,
      default: '/static/upload/images/defaultlogo.png',
    },
    state: { // 在职状态
      type: Boolean,
      default: true,
    },
    enable: { // 是否能用
      type: Boolean,
      default: true,
    },
    comments: String, // 备注
    // 人员角色权限hmac
    user_role_power_hmac: String,
    passwordEncryptionAlgorithm: { // 密码加密hmac算法
      type: String,
    },
    // 人员角色权限hmac算法
    user_role_power_hmac_algorithm: {
      type: String,
    },
    encryptionAlgorithm: { // 加密算法
      type: String,
    },
  });
  dbEncryption && ServiceUserSchema.plugin(encryptionPlugin, {
    fields: {
      name: 3,
      phoneNum: 11,
    },
    model: 'ServiceUser',
    ctx,
  });

  return mongoose.model('ServiceUser', ServiceUserSchema, 'serviceUsers');
};
