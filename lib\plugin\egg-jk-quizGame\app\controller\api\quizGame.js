// 临安小游戏 - 答题游戏
const moment = require('moment');
const svgCaptcha = require('svg-captcha');
const QuizGameController = {
  // 获取活动信息 xxn 2024-10-30
  // @param {String} gameEventId 活动id
  async getGameEventInfo(ctx) {
    try {
      const gameEventId = ctx.query.gameEventId;
      if (!gameEventId) throw new Error('参数错误，gameEventId不能为空');
      const gameEvent = await ctx.service.gameEvents.getOne({ _id: gameEventId });
      if (!gameEvent) throw new Error('未找到活动信息, gameEventId:' + gameEventId);
      ctx.helper.renderSuccess(ctx, {
        data: {
          _id: gameEvent._id,
          name: gameEvent.name,
          startTime: moment(gameEvent.startTime).format('YYYY-MM-DD HH:mm'),
          endTime: moment(gameEvent.endTime).format('YYYY-MM-DD HH:mm'),
          status: gameEvent.status, // 活动状态 0: 未开始 1: 进行中 2: 已结束
          gameRule: gameEvent.gameRule, // 游戏规则
          prizeRule: gameEvent.prizeRule, // 抽奖规则
          needWinnerInfo: gameEvent.needWinnerInfo, // 是否需要获奖者信息
          winnerInfo: gameEvent.winnerInfo, // 获奖者需填写的信息
          organizer: gameEvent.organizer || '', // 主办方
          isCaptcha: gameEvent.isCaptcha || false, // 是否开启人机验证
        },
        message: 'success',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 根据wxUnionId获取游戏用户信息, 没有就创建一个！！ xxn 2024-9-4
  // @param {String} wxUnionId 微信unionId
  // @param {String} gameEventId 活动id
  async getGameUser(ctx) {
    try {
      const { wxUnionId, gameEventId } = ctx.query;
      const data = await ctx.service.gameUser.getGameUser(gameEventId, wxUnionId);
      ctx.helper.renderSuccess(ctx, {
        data,
        message: 'success',
      });
    } catch (err) {
      ctx.auditLog('获取游戏用户信息错误', err.message, 'error');
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 更新 游戏用户信息 xxn 2024-9-4
  // @param {String} _id 用户id
  // @param {String} name 姓名
  // @param {String} phoneNum 手机号
  // @param {String} Enterprise 单位id
  // @param {String} gender 性别 enum: [ 'male', 'female' ]
  async editGameUser(ctx) {
    try {
      const payload = ctx.request.body;
      const data = await ctx.service.gameUser.editGameUser(payload);
      ctx.helper.renderSuccess(ctx, {
        data,
        message: 'success',
      });
    } catch (err) {
      ctx.auditLog('更新游戏用户信息错误', err.message, 'error');
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 抽奖 - 返回中奖结果
  // @param {String} gameRecordId 游戏记录id
  async drawPrize(ctx) {
    const { gameRecordId } = ctx.request.body; // gamerecordId
    try {
      const prize = await ctx.service.prizes.drawPrize(gameRecordId);
      ctx.helper.renderSuccess(ctx, {
        data: prize,
        message: 'success',
        status: 200,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 判断用户有没有抽奖资格
  // @param {String} userId 用户id
  async getChance(ctx) {
    try {
      const userId = ctx.query.userId || ctx.query.id; // 用户id
      const chance = await ctx.service.prizes.getChance(userId);
      ctx.helper.renderSuccess(ctx, {
        data: chance,
        message: 'success',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获得我的奖品
  // @param {String} userId 用户id
  async getMyPrize(ctx) {
    const { userId } = ctx.query;
    try {
      const urlImg = await ctx.service.prizes.getMyPrize(userId);
      ctx.helper.renderSuccess(ctx, {
        message: 'success',
        status: 200,
        data: urlImg,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取奖品列表 xxn 2024-9-4
  // @param {String} gameEventId 活动id
  async getPrizesList(ctx) {
    const gameEventId = ctx.query.gameEventId;
    if (!gameEventId) {
      return ctx.helper.renderFail(ctx, {
        message: '参数错误，gameEventId不能为空',
      });
    }
    const data = await ctx.service.prizes.getList(gameEventId);
    ctx.helper.renderSuccess(ctx, {
      data,
      message: 'success',
      status: 200,
    });
  },

  /*
   * 生成算数验证码
   * @param {String} userId 用户id
   * @return {Object} { captcha, result }
   */
  async createCaptcha(ctx, app) {
    // 生成算数验证码
    const userId = ctx.query.userId;
    const captcha = svgCaptcha.createMathExpr({
      mathMin: 1,
      mathMax: 20,
      mathOperator: '+-',
      width: 120,
      height: 40,
      fontSize: 48,
      noise: 2,
      color: true,
      background: '#fff',
    });

    const cacheKey = `captcha:${userId}`;
    await app.redis.set(cacheKey, captcha.text, 'EX', 300);

    // 返回图片和答案
    const res = {
      captcha: captcha.data, // svg图片内容
      // result: captcha.text, // 正确答案（生产环境不要返回给前端）
    };
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '验证码生成成功',
    });
  },

  // 获取试卷和游戏记录 xxn 2024-9-4
  // @param {String} userId 用户id
  // @param {String} captcha? 用户输入的验证码
  async getPaper(ctx, app) {
    try {
      const { userId, captcha } = ctx.query;
      if (!userId) throw new Error('未获取到用户信息');

      // 1. 缓存用户信息
      const cacheKeyUser = `gameUser:${userId}`;
      let userInfo = await app.redis.get(cacheKeyUser);
      if (!userInfo) {
        userInfo = await ctx.service.gameUser.getOne({ _id: userId });
        if (!userInfo) throw new Error('未找到用户信息, userId:' + userId);
        await app.redis.set(cacheKeyUser, JSON.stringify(userInfo), 'EX', 300); // 缓存 300 秒
      } else {
        userInfo = JSON.parse(userInfo);
      }
      if (userInfo.status === 0) {
        throw new Error('您的账户已被锁定，无法参与活动，如有疑问请联系系统管理员。');
      }

      const { gameEventId, Enterprise } = userInfo;
      if (!gameEventId) throw new Error('未找到用户的gameEventId, userId:' + userId);
      if (!Enterprise) throw new Error('未找到用户的企业信息，请先完善用户信息。');

      // 2. 缓存活动信息
      const cacheKeyGameEvent = `gameEvent:${gameEventId}`;
      let gameEvent = await app.redis.get(cacheKeyGameEvent);
      if (!gameEvent) {
        gameEvent = await ctx.service.gameEvents.getOne({ _id: gameEventId });
        if (!gameEvent) throw new Error('未找到用户的活动信息, gameEventId:' + gameEventId);
        await app.redis.set(cacheKeyGameEvent, JSON.stringify(gameEvent), 'EX', 300); // 缓存 300 秒
      } else {
        gameEvent = JSON.parse(gameEvent);
      }

      // 3. 检查活动状态
      if (gameEvent.status === '0') {
        const endTime = moment(gameEvent.endTime).format('YYYY-MM-DD HH:mm');
        throw new Error('活动未开始, 请于' + endTime + '开始游戏');
      } else if (gameEvent.status === '2') {
        throw new Error('活动已结束, 请关注下次活动');
      }

      // 校验验证码
      if (gameEvent.isCaptcha) {
        if (!captcha) throw new Error('验证码不能为空');

        const cacheKey = `captcha:${userId}`;
        const result = await app.redis.get(cacheKey);
        if (!result) throw new Error('验证码已过期，请重新获取。');
        if (result !== captcha) throw new Error('验证码错误，请重新输入。');
        await app.redis.del(cacheKey);
      }

      // 4. 检查当天答题次数
      let todayGameRecordCount = 0;
      if (gameEvent.dailyAnswerNum) {
        todayGameRecordCount = await ctx.service.gameRecords.getTodayGameRecordCount(userId);
        if (todayGameRecordCount >= gameEvent.dailyAnswerNum) {
          throw new Error('今天的答题次数已用完, 请明天再来。');
        }
      }

      // 5. 获取试卷
      const paper = await ctx.service.gameRecords.getTestPaper(
        gameEvent.requiredQuestionBank,
        gameEvent.optionalQuestionBank,
        gameEvent.totalTopicNum
      );

      // 6. 创建游戏记录
      let gameRecord;
      const topicIds = paper.map(item => item._id);
      if (topicIds && topicIds.length) {
        gameRecord = await ctx.service.gameRecords.createGameRecord({
          gameEventId,
          Enterprise,
          userId,
          testPaper: topicIds,
        });
      }

      // 7. 返回数据
      ctx.helper.renderSuccess(ctx, {
        data: {
          paper, // 试卷(题目列表)
          gameRecordId: gameRecord ? gameRecord._id : '', // 当前的游戏记录id
          todayGameRecordCount: todayGameRecordCount + 1, // 当天已有的游戏记录数量(包括这次)
          gameEventInfo: { // 活动信息
            name: gameEvent.name,
            getPrizeChanceNum: gameEvent.getPrizeChanceNum, // 获得抽奖机会的题数
            everyTimeWrongNum: gameEvent.everyTimeWrongNum, // 每次允许错题数
            answerTimeLimit: gameEvent.answerTimeLimit, // 答题时间限制
            gameRule: gameEvent.gameRule, // 游戏规则描述
          },
        },
        message: 'success',
      });
    } catch (err) {
      ctx.auditLog('获取试卷和游戏记录错误', err.message, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 400,
        message: err.message,
      });
    }
  },

  // 更新游戏记录 xxn 2024-9-4
  // @param {String} gameRecordId 游戏记录id
  // @param {String} topicId 题目id
  // @param {Array} answers 用户答案列表，格式：['A', 'B']
  async updateGameRecord(ctx) {
    try {
      const payload = ctx.request.body;
      const { gameRecordId, topicId, answers } = payload;
      if (!gameRecordId || !topicId || !answers) throw new Error('参数不全，gameRecordId, topicId, answers不能为空.');
      if (!Array.isArray(answers)) throw new Error('参数错误，answers必须是数组格式.');
      const gameRecord = await ctx.service.gameRecords.updateGameRecord2(gameRecordId, topicId, answers);
      ctx.helper.renderSuccess(ctx, {
        data: gameRecord,
        message: 'success',
      });
    } catch (err) {
      ctx.auditLog('更新游戏记录错误', err.message, 'error');
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取当前个人总积分排行榜 xxn 2024-9-4
  // @param {String} gameEventId 活动id
  // @param {String} userId 用户id
  async getStepRanking(ctx) {
    try {
      const { gameEventId, userId } = ctx.query;
      const res = await ctx.service.gameRecords.getStepRanking(gameEventId);
      const data = res.map(item => {
        let name = item.name;
        if (item.name.length === 2) {
          name = item.name.slice(0, 1) + '*';
        } else if (item.name.length > 2) {
          name = item.name.slice(0, 1) + '*'.repeat(item.name.length - 2) + item.name.slice(-1);
        }
        return { name, curScore: item.curScore };
      });
      const myScore = await ctx.service.gameRecords.getMyTotalScore(gameEventId, userId); // 获取当前用户的总积分
      ctx.helper.renderSuccess(ctx, {
        data: {
          myScore,
          list: data,
        },
        message: 'success',
      });
    } catch (err) {
      ctx.auditLog('获取积分排行榜错误', err.message, 'error');
      console.log(44444, err);
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取当前企业总积分排行榜 xxn 2024-9-4
  // @param {String} gameEventId 活动id
  async getEnterpriseStepRanking(ctx) {
    try {
      const gameEventId = ctx.query.gameEventId;
      const res = await ctx.service.gameRecords.getEnterpriseStepRanking(gameEventId);
      const data = res.map(item => {
        return { name: item.cname, curScore: item.curScore };
      });
      ctx.helper.renderSuccess(ctx, {
        data,
        message: 'success',
      });
    } catch (err) {
      ctx.auditLog('获取团体积分排行榜错误', err.message, 'error');
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取已获奖用户列表 xxn 2024-9-4
  // @param {String} gameEventId 活动id
  // @param {String} limit = 30 榜单长度
  async getWinningUsers(ctx) {
    try {
      const limit = +ctx.query.limit || 30;
      const gameEventId = ctx.query.gameEventId;
      const data = await ctx.service.gameUser.getWinningUsers(gameEventId, limit);
      ctx.helper.renderSuccess(ctx, {
        data,
        message: 'success',
      });
    } catch (err) {
      ctx.auditLog('获取已获奖用户列表错误', err.message, 'error');
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },

  // 获取活动企业列表
  // @param {String} gameEventId 活动id
  async getEnterpriseList(ctx) {
    const gameEventId = ctx.query.gameEventId;
    const keyword = ctx.query.keyword;
    if (!gameEventId) {
      return ctx.helper.renderFail(ctx, {
        message: '参数错误，gameEventId不能为空',
      });
    }
    const query = { gameEventId, enable: true };
    if (keyword) {
      const regex = new RegExp(keyword.replace(/\\/g, '\\\\'), 'i');
      query.cname = { $regex: regex };
    }
    const list = await ctx.service.gameEnterprise.getEnterpriseList(query);
    const data = list.map(item => ({ cname: item.cname, _id: item._id }));
    // 如果data中存在cname为"非报名企业"的项，则将其放在数组的第一位。
    const nonRegisteredEnterprise = data.findIndex(item => item.cname.includes('非报名企业'));
    if (nonRegisteredEnterprise !== -1) {
      const nonRegisteredItem = data.splice(nonRegisteredEnterprise, 1)[0];
      data.unshift(nonRegisteredItem);
    }
    ctx.helper.renderSuccess(ctx, {
      data,
      message: 'success',
    });
  },

  // 抽奖后更新收奖人信息
  // @param {String} userId 用户id 必传参
  // @param {String} EnterpriseName 活动id
  // @param {String} name 姓名
  // @param {String} phoneNum 手机号
  // @param {String} address 地址
  // @param {String} idType 证件类型 enum: [ '身份证', '护照', '港澳通行证', '台湾通行证', '军官证', '其他' ]
  // @param {String} idCard 证件号码
  async addWinnerInfo(ctx) {
    try {
      const payload = ctx.request.body;
      if (!payload.userId) throw new Error('参数不全，userId不能为空');
      const data = await ctx.service.gameUser.addWinnerInfo(payload);
      ctx.helper.renderSuccess(ctx, {
        data,
        message: '个人信息添加成功',
      });
    } catch (err) {
      console.log(44444, err);
      ctx.auditLog('添加获奖者信息错误', err.message, 'error');
      ctx.helper.renderFail(ctx, {
        message: err.message,
      });
    }
  },
};

module.exports = QuizGameController;
