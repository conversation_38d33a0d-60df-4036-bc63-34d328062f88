module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const devicesSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      EnterpriseID: {
        // 企业ID
        type: String,
        // require: true,
      },
      name: {
        // 设备名称
        type: String,
        // require: true,
      },
      deviceType: {
        // 检测的危害因素类别
        type: String,
        ref: 'DeviceType',
      },
      detect: [
        {
          // 检测的具体危害因素
          type: String, // occupationalexposurelimits
        },
      ],
      exceededVal: Number, // 超标值，比如噪声 85，粉尘：8
      unit: String, // 单位，比如噪声是dB(A)
      model: {
        // 设备型号
        type: String,
        // require: true,
      },
      deviceID: {
        // 序列号, 设备的唯一码
        type: String,
        // require: true,
        // unique: true,
      },
      factory: {
        // 制造厂商
        type: String,
      },
      deviceNum: {
        // 出厂编号/机号
        type: String,
      },
      effectiveDate: {
        // 有效期
        type: Date,
      },
      calibrationDate: {
        // 最近的校准日期
        type: Date,
      },
      connectionStatus: {
        // 电源连接状态/是否开启，没有开启的将不统计它的数据
        type: Boolean,
        default: false,
      },
      millConstruction: [
        {
          // 监测的厂房车间岗位
          type: String,
          ref: 'MillConstruction',
        },
      ],
      minuteData: [
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          dateTime: Date,
          value: Number,
        },
      ],
      hourData: [
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          dateTime: Date,
          value: Number,
        },
      ],
      dayData: [
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          dateTime: Date,
          value: Number,
        },
      ],
      monthData: [
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          dateTime: String, // eg: '2021-1' 表示2021年2月
          value: Number,
        },
      ],
      description: {
        // 设备描述
        type: String,
      },
      status: {
        // 暂时用不到，先备着
        type: Boolean,
        default: true,
      },
    },
    { timestamps: true }
  );

  return mongoose.model('Devices', devicesSchema, 'devices');
};
