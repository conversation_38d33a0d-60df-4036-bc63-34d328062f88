/*
 * @Author: tangzg <EMAIL>
 * @Date: 2025-03-11 15:08:08
 * @LastEditors: tangzg <EMAIL>
 * @LastEditTime: 2025-03-11 15:33:32
 * @FilePath: \jcqlc\lib\plugin\egg-jk-funDivision\app\model\archivalList.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 机构端-service 归档文件列表配置
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const archivalList = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    serviceOrgId: { type: String }, // 机构id
    projectType: String, // 项目类型（除other外为归档生成依据，other为模版文件配置内容） 职业卫生-zyws 放射卫生-radiate 个人剂量-personalDose 工业放射-industrialRay 质控文件管理-segmentFile 其他-other
    qualityControlFiles: [{ // 质控文件列表
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileIndex: Number, // 文件序号
      alias: String, // 别名 文件名称对应真实文件名 不可修改
      departIds: Array, // 负责的部门
      fileName: String, // 文件名称
      businessType: String, // 业务类型
      // QCnumber: String, // 质控编号
      effectiveDate: Date, // 生效时间
      generate: String, // 生成地址

      mergerDemand: {
        type: Boolean,
        default: false,
      }, // 是否多个文件合并
      segment: [
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          segmentAlias: String, // 分段文件别称
          segmentFileName: String, // 文件名称
          segmentKey: String, // 分段关键字
          segmentName: String, // 分段内容 -> 一个文件可能有几个段
          QCnumber: String, // 质控编号
          QCversion: String, // 质控版本（第几次修改,空为不显示第几次修改）
          effectiveDate: Date, // 生效时间
        },
      ],
    }],
  }, { timestamps: true });

  return mongoose.model('archivalList', archivalList, 'archivalLists');

};
