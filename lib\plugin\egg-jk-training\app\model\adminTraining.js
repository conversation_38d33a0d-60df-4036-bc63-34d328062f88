module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 管理员培训以及公开的培训
  const AdminTrainingSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 名称
      type: String,
      require: true,
    },

    Introduction: { // 简介
      type: String,
      default: '',
    },

    trainingType: {
      type: Number,
      default: 2, // 1管理员培训 2自主培训
    }, // 培训类型


    authorID: { // 运营端id, 运营端创建的则需要
      type: String,
      ref: 'OperateUser',
      default: '',
    }, // 创建人的ID， 监管端创建的管理员培训不需要这个

    superID: { // 监管id, 监管端创建的则需要
      type: String,
      ref: 'SuperUser',
      default: '', // 运营端创建自主培训则不需要
    },
    userID: { // 专家发布的培训
      type: String,
      ref: 'User',
    },


    // =================================企业状态
    EnterpriseID: [{ // 企业id，监管端创建的培训则默认有，自主培训则每个企业第一个人加入就把企业ID放进去
      type: String,
      ref: 'Adminorg',
    }], // 运营端的培训：判断是否参与过，用企业ID＋培训计划ID查进度表，有记录就参与过，没有就往里插入企业ID
    completedEnterprise: [{
      type: String,
      ref: 'Adminorg',
    }], // 完成的企业，监管端创建的计划则需组长＋管理员完成，运营创建的自主培训则一个人完成即可
    incompleteEnterprise: [{
      type: String,
      ref: 'Adminorg',
    }], // 未完成但有参与记录的企业


    coursesID: [{
      type: String,
      ref: 'Courses',
    }], // 必修课程

    electives: [{
      type: String,
      ref: 'Courses',
    }], // 选修课

    // ==============================================学时
    requiredCoursesHours: {
      type: Number,
      default: 0,
      set: val => Number(val || 0),
    }, // 必修课学时
    electivesCoursesHours: {
      type: Number,
      default: 0,
    }, // 选修课学时最低条件


    // ==============================================时间
    date: { // 创建时间，也是开始时间
      type: Date,
      default: Date.now,
    },

    updateTime: [{ // 更新时间，每更新一次记录一次时间
      type: Date,
    }],

    completeTime: {
      type: Date,
    }, // 计划完成时间


    // =====================================考试信息      ===========
    needExam: {
      type: Boolean,
      default: false,
    }, // 是否需要考试

    allowTestOnly: {
      type: Boolean,
      default: false,
    }, // 是否允许直接参与考试，needExam为false时，这个毫无意义

    examinationType: {
      type: Number,
      default: 2,
    },
    /** 考试类型，needExam为false时 或者allowTestOnly为true，这个毫无意义
     *  1 每个课程结束考试
     *  2 整个培训结束才考试
     *  3 课程结束和培训结束都要考试（考死学生）
     *  4 每节视频结束穿插考试（以后再说）
     */

    examination: { // 考试信息
      singleChoice: {
        num: {
          type: Number,
          default: 0,
        },
        scores: {
          type: Number,
          default: 5,
        },
      },
      multipleChoice: {
        num: {
          type: Number,
          default: 0,
        },
        scores: {
          type: Number,
          default: 5,
        },
      },
      Judgment: {
        num: {
          type: Number,
          default: 0,
        },
        scores: {
          type: Number,
          default: 5,
        },
      }, // 判断
      fillBlank: {
        num: {
          type: Number,
          default: 0,
        },
        scores: {
          type: Number,
          default: 5,
        },
      }, // 填空
      essayQuestion: {
        num: {
          type: Number,
          default: 0,
        },
        scores: {
          type: Number,
          default: 5,
        },
      }, // 综合大题
      passingGrate: {
        type: Number,
        default: 60,
      }, // 及格分占比
      limitTime: {
        type: Number,
        default: 30,
      }, // 考试时长，分钟
      timesLimit: {
        type: Number,
        default: 3,
      }, // 考试次数
    },


    // =======================================视频间隔断点========================
    breakpoint: {
      type: Number,
      default: 0,
    },
    /** 视频断点，每隔一段时间暂停视频，防止刷视频人跑了，单位 分钟
     *  0 不设断点
     *  1 随机 默认范围（5 - 10）分钟
     *  2 设置指定间隔breakpointInterval
     */
    breakpointInterval: {
      type: Number,
      default: 5,
    }, // 时间间隔 单位分钟，breakpoint为0或1时无效

    breakpointRange: {
      botton: {
        type: Number,
        default: 5,
      },
      top: {
        type: Number,
        default: 10,
      },
    }, // 随机间隔范围 单位分钟，breakpoint为0或2时无效
    certificateReview: { // 证书是否需要审核
      type: Boolean,
      default: true,
    },
    auditStatus: { // 审核状态
      type: String,
      default: '0',
      emun: [ '0', '1', '2', '3' ], // 0没上传视频,暂存 1待审核 2审核通过 3审核不通过
    },
    auditFailedReason: String, // 审核不通过的原因
    salesStatus: { // 实时上下架状态
      type: Boolean,
      default: false, // 默认下架
    },
    salesStatusRecord: [{ // 上下架记录
      salesStatus: { // 上下架状态
        type: Boolean,
        default: true, // 默认下架
      },
      operator: { // 操作人
        _id: String,
        name: String,
      },
      reason: { // 操作缘由
        type: String,
      },
      operatingPlatform: { // 操作平台
        type: String,
        default: 'px',
        emun: [ 'px', 'operate' ],
      },
      time: {
        type: Date,
        default: Date.now,
      },
    }],
    price: { // 价格
      type: Number,
      default: 0,
      set: val => Number(val),
    },
    // 付费途径 1 学习前，2取证前
    paymentMethod: {
      type: Number,
      default: 2,
    },
  }, {
    timestamps: true,
  });


  return mongoose.model('AdminTraining', AdminTrainingSchema, 'adminTraining');
};
