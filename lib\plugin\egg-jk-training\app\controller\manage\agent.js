// 培训推广代理相关
const AgentController = {
  // #region 代理分享记录表相关  agentShareRecord
  /* 创建分享记录
   * @api {post} /agent/createShareRecord
   * @apiDescription 创建分享记录
   * @apiParam {obj} {
   *  shareBy:string, //分享者id 当前登录user id
   *  shareTrain:[{
   *   adminTrainingId:string //分享培训id
   *  }],
   * }
   */
  async createShareRecord(ctx) {
    const data = ctx.request.body;
    data.shareBy = ctx.session.user ? ctx.session.user._id : '';
    if (!data.shareBy) {
      ctx.helper.renderFail(ctx, {
        message: '参数错误',
        status: 400,
      });
      return;
    }
    const res = await ctx.service.agent.createShareRecord(data);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '创建成功',
      status: 200,
    });
  },
  /* 获取分享记录--用于推荐界面
   * @api {get} /agent/getShareDetail
   * @apiDescription 用于推荐界面获取分享课程
   * @apiParam {obj}{
   *   shareRecordId:string //分享记录id
   * }
   */
  async getShareDetail(ctx) {

    const { shareRecordId, pageCurrent, size } = ctx.query;
    const userID = ctx.session.user ? ctx.session.user._id : '';
    console.log('userID', userID);
    if (!shareRecordId) {
      ctx.helper.renderFail(ctx, {
        message: '参数错误',
        status: 400,
      });
      return;
    }
    const res = await ctx.service.agent.getShareDetail({ shareRecordId, pageCurrent: +pageCurrent, size: +size, userID });
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '获取成功',
      status: 200,
    });
  },
  /* 获取分享记录列表
   * @api {get} /agent/getShareRecordList
   * @apiParam {obj} {
   *  pageCurrent:number, //当前页
   *  size:number, //每页条数
   * }
   */
  async getShareRecordList(ctx) {
    const params = ctx.query;
    params.pageCurrent = +params.pageCurrent || 1;
    params.size = +params.size || 10;
    params.userID = ctx.session.user ? ctx.session.user._id : '';
    const res = await ctx.service.agent.getShareRecordList(params);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '获取成功',
      status: 200,
    });
  },
  // #endregion


  // #region 代理关系表相关 agentRelation
  /* 查询有无上级
   * @api {get} /agent/getParent
   * @apiDescription 用于判断是否有上级 进行代理关系添加
   */
  async getParent(ctx) {
    const userID = ctx.session.user ? ctx.session.user._id : '';
    const res = await ctx.service.agent.getParent({ userID });
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '获取成功',
      status: 200,
    });
  },
  /* 代理中心获取信息
   * @api {get} /agent/getAgentInfo
   * @apiDescription 代理中心获取信息接口
   */
  async getAgentInfo(ctx) {
    // 获取get的传参
    const params = ctx.query;
    params.userID = ctx.session.user ? ctx.session.user._id : '';
    params.page = +params.page || 1;
    params.limit = +params.limit || 10;
    const res = await ctx.service.agent.getAgentInfo(params);
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: '获取成功',
      status: 200,
    });
  },
  /* 添加代理关系
   * @api {post} /agent/addAgentRelation
   * @apiDescription 添加代理关系
   * @apiParam {obj} {
   *     userID:string, 当前登录id
   *     parentUserID:string, 父级id
   *     source:string, 来源
   * }
   */
  async addAgentRelation(ctx) {
    const data = ctx.request.body;
    data.userID = ctx.session.user ? ctx.session.user._id : '';
    if (!data.userID || !data.parentUserID) {
      console.log('参数', data);
      ctx.helper.renderFail(ctx, {
        message: '参数错误',
        status: 400,
      });
      return;
    }
    const res = await ctx.service.agent.addAgentRelation(data);
    if (res.code === 200) {
      ctx.helper.renderSuccess(ctx, {
        data: {},
        message: '添加成功',
        status: 200,
      });
    } else {
      ctx.helper.renderFail(ctx, {
        message: res.message,
        status: res.code,
      });
    }
  },
  /* 获取代理排行
   * @api {get} /agent/getRanking
   * @apiDescription 获取代理排行，当月的下级代理数量和当月收益
   */
  async getRanking(ctx) {
    try {
      const res = await ctx.service.agent.getRanking({});
      ctx.helper.renderSuccess(ctx, {
        data: res,
        message: '获取成功',
        status: 200,
      });
    } catch (error) {
      console.log('error', error);
    }

  },
  // 测试分配收益接口
  // async test(ctx) {
  //   const data = ctx.request.body;
  //   const res = await ctx.service.agent.triggerRefund(data.out_trade_no);
  //   ctx.helper.renderSuccess(ctx, {
  //     data: res,
  //     message: '获取成功',
  //     status: 200,
  //   });
  // },
  // #endregion

  // #region 培训网站配置相关 trainSiteConfig
  /* 获取培训网站配置
    * @api {get} /agent/getTrainSiteConfig
    * @apiDescription 获取培训网站配置,增加从iservice获取底部版权
    * @apiParam {obj} {}
    */
  async getTrainSiteConfig(ctx) {
    console.log('getTrainSiteConfig');
    const res = await ctx.service.agent.getTrainSiteConfig();
    const result = JSON.parse(JSON.stringify(res || ''));
    if (result) {
      const needFile = [ 'miniLogo', 'longLogo', 'siteHelpFile', 'officialAccount', 'wxMiniProgram' ];
      for (const key in result) {
        if (needFile.includes(key) && result[key]) {
          result[key] = '/static' + ctx.app.config.trainSitFile_http_path + '/' + result[key];
        } else if (key === 'linkList') {
          for (let i = 0; i < result[key].length; i++) {
            if (result[key][i].type === 'tel') {
              for (let j = 0; j < result[key][i].content.length; j++) {
                result[key][i].content[j].codeImg = '/static' + ctx.app.config.trainSitFile_http_path + '/' + result[key][i].content[j].codeImg;
              }
            }
          }
        }
      }
    }
    ctx.helper.renderSuccess(ctx, {
      data: result,
      message: '获取成功',
      status: 200,
    });
  },
  // #endregion
};

module.exports = AgentController;
