
const quizGameController = require('../controller/manage/quizGame');
const quizGameController2 = require('../controller/api/quizGame');

module.exports = (options, app) => {
  return async function quizGameRouter(ctx, next) {
    const pluginConfig = app.config.jk_quizGame;
    if (ctx.request.url.startsWith('/api/quizGame/')) {
      await app.initPluginRouter(ctx, pluginConfig, quizGameController, quizGameController2);
    }
    await next();
  };
};
