'use strict';

const Controller = require('egg').Controller;

/**
 * @controller ProcessMonitor
 * @description 进程监控控制器 - 提供实时进程状态和任务处理状态监控
 */
class ProcessMonitorController extends Controller {

  /**
   * 获取当前系统进程状态
   * @memberof ProcessMonitorController
   * @function GET
   * @route /api/process-monitor/status
   */
  async getProcessStatus() {
    const { ctx } = this;

    try {
      // 获取系统状态信息
      const systemStatus = await ctx.service.fzDataFix.getSystemStatus();

      // 获取并发状态信息
      const concurrencyStatus = await ctx.service.fzDataFix.getConcurrencyStatus();

      // 获取任务队列状态
      const queueStatus = await ctx.service.fzDataFix.getTaskQueueStatus();

      ctx.helper.renderCustom(ctx, {
        success: true,
        message: '进程状态获取成功',
        data: {
          system: systemStatus,
          concurrency: concurrencyStatus,
          queue: queueStatus,
          timestamp: new Date(),
        },
      });

    } catch (error) {
      ctx.logger.error('获取进程状态失败:', error);
      ctx.helper.renderCustom(ctx, {
        success: false,
        message: `获取进程状态失败: ${error.message}`,
        error: error.message,
      }, 500);
    }
  }

  /**
   * 获取动态进程监控信息
   * @memberof ProcessMonitorController
   * @function GET
   * @route /api/process-monitor/dynamic-processes
   */
  async getDynamicProcesses() {
    const { ctx } = this;

    try {
      // 这里需要从全局状态中获取动态进程信息
      // 我们需要扩展DynamicProcessManager来支持状态查询
      const processInfo = global.dynamicProcessManager
        ? global.dynamicProcessManager.getStatus()
        : { processes: [], totalProcesses: 0, activeProcesses: 0 };

      ctx.helper.renderCustom(ctx, {
        success: true,
        message: '动态进程信息获取成功',
        data: {
          ...processInfo,
          mainProcessPid: process.pid,
          timestamp: new Date(),
        },
      });

    } catch (error) {
      ctx.logger.error('获取动态进程信息失败:', error);
      ctx.helper.renderCustom(ctx, {
        success: false,
        message: `获取动态进程信息失败: ${error.message}`,
        error: error.message,
      }, 500);
    }
  }

  /**
   * 获取任务处理实时状态
   * @memberof ProcessMonitorController
   * @function GET
   * @route /api/process-monitor/task-status
   */
  async getTaskStatus() {
    const { ctx } = this;

    try {
      // 获取当前正在处理的任务状态
      const taskStatus = await ctx.service.fzDataFix.getTaskQueueStatus({
        includeProgress: true,
        includeWorkerStats: true,
      });

      // 获取处理日志统计
      const logStats = await ctx.service.fzDataFix.getProcessLogStats({
        timeRange: '1h', // 最近1小时
        groupBy: 'taskType',
      });

      ctx.helper.renderCustom(ctx, {
        success: true,
        message: '任务状态获取成功',
        data: {
          current: taskStatus,
          recent: logStats,
          timestamp: new Date(),
        },
      });

    } catch (error) {
      ctx.logger.error('获取任务状态失败:', error);
      ctx.helper.renderCustom(ctx, {
        success: false,
        message: `获取任务状态失败: ${error.message}`,
        error: error.message,
      }, 500);
    }
  }

  /**
   * 启动任务队列处理（用于测试）
   * @memberof ProcessMonitorController
   * @function POST
   * @route /api/process-monitor/start-task
   */
  async startTask() {
    const { ctx } = this;
    const { taskType = 'company', batchSize = 10, concurrency = 3 } = ctx.request.body;

    try {
      ctx.logger.info('通过监控接口启动任务处理', { taskType, batchSize, concurrency });

      // 根据任务类型调用相应的处理方法
      let result;
      switch (taskType) {
        case 'company':
          result = await ctx.service.fzDataFix.processAllCompanyData({
            batchSize,
            concurrency,
            useParallel: true,
          });
          break;
        case 'healthCheck':
          result = await ctx.service.fzDataFix.processAllHealthCheckData({
            batchSize,
            concurrency,
            useParallel: true,
          });
          break;
        case 'diagnosis':
          result = await ctx.service.fzDataFix.processAllDiagnosisData({
            batchSize,
            concurrency,
            useParallel: true,
          });
          break;
        default:
          throw new Error(`不支持的任务类型: ${taskType}`);
      }

      ctx.helper.renderCustom(ctx, {
        success: true,
        message: '任务启动成功',
        data: result,
      });

    } catch (error) {
      ctx.logger.error('启动任务失败:', error);
      ctx.helper.renderCustom(ctx, {
        success: false,
        message: `启动任务失败: ${error.message}`,
        error: error.message,
      }, 500);
    }
  }

  /**
   * 获取监控页面HTML
   */
  async getDashboard() {
    const { ctx } = this;
    try {
      // 读取仪表盘HTML文件
      const fs = require('fs');
      const path = require('path');
      const dashboardPath = path.join(__dirname, '../../view/process-monitor-dashboard.html');
      if (fs.existsSync(dashboardPath)) {
        const html = fs.readFileSync(dashboardPath, 'utf8');
        ctx.type = 'text/html';
        ctx.body = html;
      } else {
        // 如果没有专门的仪表盘文件，返回简单的HTML
        ctx.type = 'text/html';
        ctx.body = this.getSimpleDashboardHTML();
      }
    } catch (error) {
      ctx.logger.error('获取监控仪表盘失败:', error);
      ctx.body = {
        success: false,
        message: '获取监控仪表盘失败: ' + error.message,
      };
    }
  }

  /**
   * 获取完整的进程系统状态（用于前端监控页面）
   */
  async getProcessSystemStatus() {
    const { ctx } = this;
    try {
      const systemStatus = {
        mainProcessPid: process.pid,
        activeProcesses: 0,
        maxProcesses: 0,
        taskTimeout: 0,
      };

      const stats = {
        totalCreated: 0,
        totalCompleted: 0,
        averageTime: 0,
      };

      let dynamicProcesses = [];

      // 获取动态进程管理器状态
      if (global.dynamicProcessManager) {
        const manager = global.dynamicProcessManager;

        // 获取系统状态
        systemStatus.activeProcesses = manager.activeProcesses ? manager.activeProcesses.size : 0;
        systemStatus.maxProcesses = manager.maxProcesses || 0;
        systemStatus.taskTimeout = manager.taskTimeout || 0;

        // 获取活跃进程列表
        if (manager.activeProcesses) {
          dynamicProcesses = Array.from(manager.activeProcesses.values()).map(process => ({
            processId: process.id,
            pid: process.process ? process.process.pid : 'N/A',
            filesCount: process.filesToProcess ? process.filesToProcess.length : 0,
            runningTime: Date.now() - process.startTime,
            status: process.status || 'processing',
            taskType: process.taskType || 'unknown',
            progress: process.progress || 0,
          }));
        }

        // 获取统计信息
        if (manager.stats) {
          stats.totalCreated = manager.stats.totalCreated || 0;
          stats.totalCompleted = manager.stats.totalCompleted || 0;
          stats.averageTime = manager.stats.averageTime || 0;
        }
      }

      ctx.body = {
        code: 200,
        message: '获取进程系统状态成功',
        data: {
          systemStatus,
          stats,
          dynamicProcesses,
          lastUpdate: new Date().toLocaleString('zh-CN'),
        },
      };

    } catch (error) {
      ctx.logger.error('获取进程系统状态失败:', error);
      ctx.body = {
        code: 500,
        message: '获取进程系统状态失败: ' + error.message,
      };
    }
  }

  /**
   * 测试动态进程功能
   */
  async testDynamicProcess() {
    const { ctx } = this;
    try {
      // 获取福州数据服务
      const fzDataService = ctx.service.fzDataFix;

      // 创建测试任务
      const testTasks = Array.from({ length: 10 }, (_, index) => ({
        taskId: `test_${Date.now()}_${index}`,
        taskType: 'healthCheck',
        dataSource: {
          filePath: `/test/path/file_${index}.dat`,
        },
      }));

      ctx.logger.info(`开始动态进程测试，创建 ${testTasks.length} 个测试任务`);

      // 使用动态进程处理测试任务
      const result = await fzDataService.processTasksWithDynamicProcesses(testTasks, {
        concurrency: 3,
        skipOnError: true,
        processId: `test_${Date.now()}`,
      });

      ctx.body = {
        code: 200,
        message: '动态进程测试完成',
        data: {
          totalTasks: testTasks.length,
          results: result,
          testTime: new Date().toISOString(),
        },
      };

    } catch (error) {
      ctx.logger.error('动态进程测试失败:', error);
      ctx.body = {
        code: 500,
        message: '动态进程测试失败: ' + error.message,
      };
    }
  }
}

module.exports = ProcessMonitorController;
