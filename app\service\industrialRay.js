const Service = require('egg').Service;
const path = require('path');
const moment = require('moment');
const fs = require('fs');

class industrialRay extends Service {
  getVal(projectInfo, filePathField) {
    const filePathFileds = filePathField.split('.');
    let filePath = projectInfo[filePathFileds[0]];
    let index = 1;
    while (index < filePathFileds.length) {
      filePath = filePath[filePathFileds[index]];
      index++;
    }
    return filePath;
  }
  // 给pdf添加签名
  async signPdf({
    originatorEmployeeId,
    statusField,
    processInstanceField,
    processInstanceId,
    signIndex,
    currentEmployeeId,
    isComplete = false,
  } = {}) {
    const { config } = this.app;
    const projectInfo = await this.ctx.model.IndustrialRayProject.findOne(
      { [processInstanceField]: processInstanceId },
      {
        EnterpriseName: 1,
        createdAt: 1,
        serviceOrgId: 1,
        projectSN: 1,
        reportFile: 1,
        testReportFile: 1,
      }
    ).lean();
    if (!projectInfo) {
      console.log(processInstanceId, '审批id未绑定到项目表中');
      return;
    }
    const { serviceOrgId, createdAt, projectSN, EnterpriseName } = projectInfo;
    // 钉钉审核和文件信息
    const approvalSignFileIno = {
      // 方案审批对不上节点，先不签字了2025.5.29
      // samplingSchemeReview: {
      //   filePthStart: 'static',
      //   filePathField: [ 'samplingSchemeFileName' ],
      //   outPathFile: [ '' ],
      //   outPathFileInfo: [ '' ],
      //   outPathFileName: '方案审核',
      //   userSignConfig: [
      //     { text: '编制人：',
      //       offsetX: 70, // 偏移量 图片的位置放置在text所在位置+偏移量
      //       offsetY: -8,
      //       isSignFromApproval: true, // 签名是否来自于审批
      //       approvalNodeIndex: 0 },
      //     { text: '审核人：',
      //       offsetX: 70, // 偏移量 图片的位置放置在text所在位置+偏移量
      //       offsetY: -8,
      //       isSignFromApproval: true, // 签名是否来自于审批
      //       approvalNodeIndex: 1 },
      //     { text: '批准人：',
      //       offsetX: 70, // 偏移量 图片的位置放置在text所在位置+偏移量
      //       offsetY: -8,
      //       isSignFromApproval: true, // 签名是否来自于审批
      //       approvalNodeIndex: 2 }],
      //   signConfig: [],
      //   signImagePath: process.cwd() + '/app/public/images/放射用章.png',
      // },
      reportApproved: {
        filePthStart: 'static',
        filePathField: [ 'reportFile.url', 'testReportFile.url' ], // `${app.config.static.prefix}${app.config.report_http_path}/${jcqlcID}/${projectYear}/${projectSN}/${fileName}`
        outPathFile: [ 'periodicalReportUploadFileName.url', 'testReportUploadFileName.url' ],
        outPathFileName2: [ 'periodicalReportUploadFileName.name', 'testReportUploadFileName.name' ],
        nodeField: 'officialReportUpload',
        outPathFileName: [ '定期报告正式稿', '检测报告正式稿' ],
        userSignConfig: [
          {
            text: '编制人',
            offsetX: 70, // 偏移量 图片的位置放置在text所在位置+偏移量
            offsetY: 15,
            isSignFromApproval: true, // 签名是否来自于审批
            approvalNodeIndex: 0,
          },
          {
            text: '审核人',
            offsetX: 70, // 偏移量 图片的位置放置在text所在位置+偏移量
            offsetY: 15,
            isSignFromApproval: true, // 签名是否来自于审批
            approvalNodeIndex: 2,
          },
          {
            text: '签发人',
            offsetX: 70, // 偏移量 图片的位置放置在text所在位置+偏移量
            offsetY: 15,
            isSignFromApproval: true, // 签名是否来自于审批
            approvalNodeIndex: 3,
          },
        ],
        signConfig: [{ // 签章位置
          pages: [ 0 ],
          y: -20,
          text: '月编',
          x: 'center', // x轴居中
        }, {
          text: '盖章',
          offsetX: -60, // 偏移量
          offsetY: -55, // 偏移量
        }],
        textConfig: [
          // {
          //   pages: [ 0 ],
          //   y: 100,
          //   x: 'center', // x轴居中
          //   offsetY: 0, // 偏移量
          //   width: 50,
          //   value: moment(new Date()).format('YYYY-MM'),
          // }
        ],
        signImagePath: process.cwd() + '/app/public/images/放射用章.png',
      },
    };
    const configInfo = approvalSignFileIno[statusField];
    if (!configInfo || configInfo.length === 0) return;
    let needSign = false;
    configInfo.userSignConfig.forEach((item, index) => {
      if (item.approvalNodeIndex === signIndex) {
        needSign = true;
        signIndex = index;
      }
    });
    if (!needSign && !isComplete) return; // 没匹配到需要签名
    let signFileInfo = {};
    const projectYear = new Date(createdAt).getFullYear() + '';
    const pathDir = path.resolve(path.join(config.report_path, serviceOrgId, projectYear, projectSN));
    for (let i = 0; i < configInfo.filePathField.length; i++) {
      let filePath = this.getVal(projectInfo, configInfo.filePathField[i]);
      const staticPath = filePath;
      filePath = filePath.replace('/static' + config.report_http_path, '');
      filePath = path.join(config.report_path, filePath);
      console.log(filePath, 'filePathfilePath');

      let signConfig = configInfo.signConfig;
      const addSeamStamp = configInfo.addSeamStamp || false;
      let signImagePath = configInfo.signImagePath;
      if (filePath.indexOf('?') !== -1) {
        const temp = filePath.split('?');
        filePath = temp[0];
      }
      const originFilePath = filePath;
      // 原文件就是pdf文件
      if (path.extname(filePath) === '.pdf') {
        const { name } = path.parse(filePath);
        const signFileName = name + '_sign';
        // const originFilePath = filePath;
        filePath = filePath.replace(name, signFileName);
        if (signIndex === 0) {
          fs.writeFileSync(filePath, fs.readFileSync(originFilePath));
        }
      }
      if (!isComplete) {
        // 添加每个审批人的签字
        if (path.extname(filePath) === '.docx') {
          if (!fs.existsSync(filePath.replace('docx', 'pdf'))) { // 转换成pdf
            await this.ctx.helper.docx2pdf(staticPath, filePath.replace('docx', 'pdf'));
          }
        }
        filePath = filePath.replace('.docx', '.pdf');
        signConfig = JSON.parse(JSON.stringify(configInfo.userSignConfig[signIndex]));
        if (statusField === 'reportApproved' && i === 0) { signConfig.offsetX = 370; }
        let employeeInfo = null;
        if (signConfig.signField === 'origin') {
          // 获取发起人的签名
          employeeInfo = await this.ctx.model.ServiceEmployee.findOne(
            { _id: originatorEmployeeId }, { name: 1, signPath: 1 }
          );
        } else {
          employeeInfo = await this.ctx.model.ServiceEmployee.findOne(
            { _id: currentEmployeeId }, { name: 1, signPath: 1 }
          );
        }
        // 获取签名路径
        // console.log(signConfig, signIndex, employeeInfo.signPath, '签名信息');
        if (employeeInfo.signPath) {
          signImagePath = path.resolve(path.join(config.sign_path, serviceOrgId, employeeInfo.signPath)); // 文件目录;
          console.log(projectSN, '项目编号', i, signIndex, signConfig);
          signFileInfo = await this.ctx.helper.drawImageToPdfAndSign(
            filePath,
            signImagePath,
            false,
            false,
            signConfig
          );
        }
      } else { // 审批完成进行盖章和电子签名
        filePath = filePath.replace('.docx', '.pdf');
        signFileInfo = await this.ctx.helper.drawImageToPdfAndSign(
          filePath,
          signImagePath,
          true,
          addSeamStamp,
          signConfig,
          configInfo.textConfig
        );
        if (configInfo.mergeFiles || configInfo.addPage) {
          const tempArr = [];
          if (configInfo.mergeFiles) {
            for (let j = 0; j < configInfo.mergeFiles.length; j++) {
              let item = configInfo.mergeFiles[j];
              const field = item;
              item = this.getVal(projectInfo, item);
              if (!item) continue;
              item = item.replace('/static' + config.report_http_path, '');
              const splitUrl = item.split(this.ctx.app.config.report_path);
              item = path.join(
                config.report_path,
                splitUrl[splitUrl.length - 1]
              );
              if (
                field === configInfo.filePathField?.[i] &&
                path.extname(originFilePath) === '.pdf'
              ) {
                const { name } = path.parse(item);
                // configInfo.mergeFiles[j] = item.replace(name, name + '_sign');
                tempArr.push(item.replace(name, name + '_sign'));
              } else {
                // configInfo.mergeFiles[j] = item.replace('.docx', '.pdf');
                tempArr.push(item.replace('.docx', '.pdf'));
              }
            }
          }
          const outputFilePath = path.join(
            pathDir,
            `${projectSN}_${EnterpriseName}_${configInfo.outPathFileName?.[i]}.pdf`
          );
          signFileInfo = await this.ctx.helper.mergeFileAndAddPage(
            filePath,
            outputFilePath,
            tempArr,
            configInfo.addPage
          );
        }
      }

      if (signFileInfo) {
        // 更新签名后的文件
        const setFields = {
          [configInfo?.outPathFile?.[i]]: '/' + signFileInfo.filePath,
        };
        if (configInfo?.noStampFiled?.[i] && signFileInfo?.noStampFilePath) {
          setFields[configInfo?.noStampFiled?.[i]] = '/' + signFileInfo?.noStampFilePath;
        }
        if (configInfo?.outPathFileName2?.[i]) {
          setFields[
            configInfo?.outPathFileName2?.[i]
          ] = `${projectSN}_${EnterpriseName}_${configInfo.outPathFileName?.[i]}.pdf`;
        }
        if (configInfo.nodeField) {
          setFields[`progress.${configInfo.nodeField}`] = {
            status: 2,
            completedTime: new Date(),
          };
        }
        await this.ctx.model.IndustrialRayProject.updateOne(
          { _id: projectInfo._id },
          { $set: setFields }
        );
        // 创建盖章记录
        await this.ctx.model.StampRecord.create({
          serviceOrgId,
          modelId: projectInfo._id,
          modelName: 'IndustrialRayProject',
          stampFile: configInfo.outPathFileName?.[i],
          stampFilePath: '/' + signFileInfo.filePath,
          optServiceEmployeeId: currentEmployeeId,
        });
      }
    }
  }
  // 生成审批记录
  async createReviewRecord(params) {
    const { ctx } = this;
    const { _id, node } = params;
    const baseInfo = await ctx.model.IndustrialRayProject.findOne({ _id }).lean();
    const { serviceOrgId, projectSN } = baseInfo;
    const serviceOrg = await this.ctx.model.ServiceOrg.findOne({ _id: serviceOrgId }).lean();
    const nodeKeyMap = {
      officialDraftReview: {
        instanceId: 'officialDraftReportInstanceId',
        alias: 'auditOfficialRecordsFile',
        fileName: '工业放射报告审核记录表331',
      },
    };
    if (!nodeKeyMap[node]) return;
    const instanceId = nodeKeyMap[node].instanceId;
    const fileName = nodeKeyMap[node].fileName;

    const form_values = await ctx.model.ApproveRecord.findOne(
      { processInstanceId: baseInfo[instanceId] },
      { form_component_values: 1, operation_records: 1, status: 1 }
    ).lean();
    const { operation_records } = form_values;
    const opeRecords = [];
    for (let i = 0; i < operation_records.length; i++) {
      const record = operation_records[i];
      const employee = await ctx.model.ServiceEmployee.findOne(
        { _id: record.serviceEmployeeId },
        { name: 1, signPath: 1 }
      ).lean();
      console.log(employee, 'employeeemployeeemployee');
      const temp = {
        employeeName: employee?.name || '/',
        signPath: path.join(
          this.config.sign_path,
          serviceOrgId,
          employee?.signPath
        ),
        dateDesc: moment(record.date).format('YYYY年MM月DD日'),
      };
      opeRecords.push(temp);
    }
    const data = {
      EnterpriseName: baseInfo.EnterpriseName,
      projectName: baseInfo.projectName,
      projectSN,
      opeRecords,
      orgName: serviceOrg?.name,
    };
    const year = baseInfo.createdAt.getFullYear() + '';
    const tableBack = await ctx.helper.fillWord(ctx, fileName, data, {
      EnterpriseID: serviceOrgId,
      adminOrgName: '',
      year,
      projectSN,
      prefix: 'approved',
      emptyTxt: '',
    });
    console.log(tableBack, 'tableBacktableBack11');
    // 删除旧文件 (htt update 修改执行顺序，word生成成功后再删除源文件)
    await ctx.helper.delFile({
      model: ctx.model.IndustrialRayProject,
      match: { _id: baseInfo._id },
      projectField: { fileName: '$reportRecordUploadFile.staticName' },
      filePath: path.join(
        this.config.report_path,
        serviceOrgId,
        year,
        projectSN
      ),
    });
    await ctx.model.IndustrialRayProject.updateOne(
      { _id: baseInfo._id },
      {
        $set: {
          reportRecordUploadFile: {
            staticName: tableBack.staticName,
            url: `/${serviceOrgId}/${year}/${projectSN}/${tableBack.staticName}`,
          },
        },
      }
    );
    console.log('检测报告审核记录reportRecordUploadFile', tableBack);
  }
  // 更新修改审批状态
  async updateModifystatus(condition, applyInfo, statusField) {
    const { ctx } = this;
    const info = applyInfo.info;
    const field = 'progress.' + statusField;
    if (applyInfo.qlcProjectStatus > 1) {
      // 更新状态
      await ctx.model.IndustrialRayProject.updateOne(
        { [condition]: info.processInstanceId },
        {
          $set: {
            [field]: {
              status: applyInfo.qlcProjectStatus,
              completedTime: new Date(),
            },
          },
        }
      );
    }
  }
}

module.exports = industrialRay;
