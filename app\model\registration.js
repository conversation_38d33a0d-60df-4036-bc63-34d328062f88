// 培训班报名记录 xxn 2024-07-16
module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const RegistrationSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    pxOrgId: { // 培训机构id
      type: String,
      ref: 'PxOrg',
      required: true,
    },
    trainingClassId: { // 培训班id
      type: String,
      ref: 'TrainingClass',
      required: true,
    },
    EnterpriseID: { // 企业id
      type: String,
      ref: 'Adminorg',
    },
    creator: { // 创建人id,也可以作为企业联系人
      type: String,
      ref: 'AdminUser',
      required: true,
    },
    employees: [{ // 培训报名名单
      type: String,
    }],
    price: { // 培训总费用
      type: Number,
      default: 0,
      set: val => Number(val),
    },
    status: { // 状态
      type: Number,
      enum: [ 0, 1 ], // 0: 待确认 1: 已确认
      default: 0,
    },
    deleted: { // 是否删除
      type: Boolean,
      default: false,
    },
  }, { timestamps: true });


  return mongoose.model('Registration', RegistrationSchema, 'registration');
};
