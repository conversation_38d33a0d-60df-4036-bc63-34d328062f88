const Controller = require('egg').Controller;
class HealthMachineController extends Controller {
  // 全量对接测试接口
  async healCheckResult(ctx) {
    try {
      const res = await ctx.service.healthMachineCheck.healCheckResult(
        ctx.request.body
      );
      ctx.auditLog('北元一体机对接结果', JSON.stringify(res), 'info');
      ctx.body = {
        code: '1',
        msg: '上传成功',
      };
      ctx.status = 200;
    } catch (error) {
      ctx.body = {
        code: '0',
        msg: '上传失败',
      };
      ctx.status = 200;
      console.log(error);
    }
  }
  async healCheckFile(ctx) {
    try {
      const res = await ctx.service.healthMachineCheck.healCheckFile(ctx);
      if (!res) {
        throw 'healCheckFile service error';
      }
      ctx.auditLog('北元一体机对接结果', JSON.stringify(res), 'info');
      ctx.body = {
        code: '1',
        msg: '上传成功',
      };
      ctx.status = 200;
    } catch (error) {
      ctx.body = {
        code: '0',
        msg: '上传失败',
      };
      ctx.status = 200;
      console.log(error);
    }
  }
}
module.exports = HealthMachineController;
