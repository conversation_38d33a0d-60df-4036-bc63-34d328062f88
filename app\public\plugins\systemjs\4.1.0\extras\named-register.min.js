!function(){const t=System.constructor.prototype,e=System.constructor,r=function(){e.call(this),this.registerRegistry=Object.create(null)};r.prototype=t,System=new r;const s=t.register;t.register=function(t,e,r){return"string"!=typeof t?s.apply(this,arguments):(this.registerRegistry[t]=[e,r],s.call(this,[],function(){return{}}))};const i=t.resolve;t.resolve=function(t,e){return"/"===t[0]||"."===t[0]&&("/"===t[1]||"."===t[1]&&"/"===t[2])?i.call(this,t,e):t in this.registerRegistry?t:i.call(this,t,e)};const n=t.instantiate;t.instantiate=function(t,e){return this.registerRegistry[t]||n.call(this,t,e)}}();