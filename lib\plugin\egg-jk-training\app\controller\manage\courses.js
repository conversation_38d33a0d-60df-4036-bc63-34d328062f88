const shortid = require('shortid');


// 视频播放
const CoursesController = {
  async getCourseAllContent(ctx) {
    try {
      const {
        _id,
      } = ctx.query;
      const course = await ctx.service.courses.getCourseOne(_id);
      const contentList = [];
      course.sort.sort(function(a, b) {
        return a.sequence - b.sequence;
      });
      // console.log(course)
      for (let index = 0; index < course.sort.length; index++) {
        const element = course[course.sort[index].contentType].find(function(item) {
          return item._id === course.sort[index].ID;
        });
        let Video;
        switch (course.sort[index].contentType) {
          case 'videoInfos':
            Video = await ctx.helper.request_alivod('GetVideoInfo', {
              VideoId: element.VideoId,
            }, {});
            contentList.push({
              contentType: course.sort[index].contentType,
              _id: course.sort[index]._id,
              ID: course.sort[index].ID,
              VideoId: element.VideoId,
              name: Video.Video.Title,
              cover: Video.Video.CoverURL,
              Description: Video.Video.Description,
            });
            break;
          case 'documents':
            contentList.push({
              contentType: course.sort[index].contentType,
              _id: course.sort[index]._id,
              ID: course.sort[index].ID,
              htmlContent: element.htmlContent,
              name: element.name,
              cover: element.cover,
              Description: element.Description,
            });
            break;
          default:
            break;
        }
      }
      ctx.helper.renderSuccess(ctx, {
        data: {
          contentList,
          course,
          message: 'OK',
        },
      });
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }

  },
  // 获取视频播放凭证
  async getVideoPlayAuth(ctx) {
    const {
      VideoId,
    } = ctx.query;
    try {
      const response = await ctx.helper.request_alivod('GetVideoPlayAuth', {
        VideoId,
      }, {});
      ctx.helper.renderSuccess(ctx, {
        data: {
          PlayAuth: response.PlayAuth,
          message: 'OK',
        },
      });

    } catch (response) {
      console.log('responseresponse', response);
      console.error('ErrorCode = ' + response.code);
      console.error('ErrorMessage = ' + response.Message);
      console.error('RequestId = ' + response.RequestId);
      ctx.helper.renderFail(ctx, {
        message: JSON.stringify(response),
      });
    }
  },

  // 获取个人进度
  async getPersonalTraining(ctx) {
    const {
      personalTrainingID,
    } = ctx.query;
    try {
      if (!personalTrainingID) {
        ctx.helper.renderFail(ctx, {
          message: '参数错误',
        });
      }
      const personalTraining = await ctx.service.courses.getPersonalTrainingByID({
        _id: personalTrainingID,
      });
      ctx.helper.renderSuccess(ctx, {
        data: {
          personalTraining,
          message: 'OK',
        },
      });

    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  async updateCourseProgress(ctx) {
    try {
      const {
        personalTrainingID,
        courseId,
        videoProgress,
      } = ctx.request.body;
      let duration = ctx.request.body.duration;
      const chapterPosition = ctx.request.body.chapterPosition || ctx.request.body.lastChapterID;
      if (!courseId) {
        ctx.helper.renderFail(ctx, {
          message: '课程id为空',
        });
        return;
      }
      if (!personalTrainingID) {
        ctx.helper.renderFail(ctx, {
          message: '记录id为空',
        });
        return;
      }
      if (videoProgress === undefined) {
        ctx.helper.renderFail(ctx, {
          message: '时间为空',
        });
        return;
      }
      const personalTraining = await ctx.service.courses.getPersonalTrainingByID({
        _id: personalTrainingID,
      });
      if (!personalTraining) {
        ctx.helper.renderFail(ctx, {
          message: '没找到对应记录',
        });
        return;
      }

      if (!personalTraining.courses) {
        personalTraining.courses = [];
      }

      let selectCourse = false;
      // 有相关课程记录，那就找到对应的课程记录
      for (let i = 0; i < personalTraining.courses.length; i++) {
        const element = personalTraining.courses[i];
        if (element.coursesId === courseId) {
          selectCourse = true;
          // 找到了
          if (!element.coursePlayProgress) {
            // 如果没有章节记录，那就创建章节记录
            element.coursePlayProgress = [];
          }
          // 找到对应的章节，然后不可描述
          let mark = false;
          for (let j = 0; j < element.coursePlayProgress.length; j++) {
            if (element.coursePlayProgress[j].chapterID === chapterPosition) {
              if (!duration) duration = element.coursePlayProgress[j].duration;
              // 记录总时长
              if (element.coursePlayProgress[j].totalTime !== undefined) {
                const temp = videoProgress - element.coursePlayProgress[j].videoProgress;
                if (temp > 4 && temp < 7) {
                  element.coursePlayProgress[j].totalTime += temp;
                }
                // 不符合条件，是拖动进度条，这次不记录
              } else {
                // 时长字段都没有
                element.coursePlayProgress[j].totalTime = 0;
              }
              if (element.coursePlayProgress[j].duration === undefined) element.coursePlayProgress[j].duration = duration;
              element.coursePlayProgress[j].videoProgress = videoProgress;
              mark = true;
              break;
            }
          }
          // 如果没找到，
          if (!mark) {
            element.coursePlayProgress.push({
              _id: shortid.generate(),
              chapterID: chapterPosition,
              videoProgress: videoProgress || 0,
              totalTime: videoProgress || 0,
              duration,
            });
          }
          // 修改章节位置
          // if循环减少赋值概率，提高速度(*^▽^*)，毫秒必争
          if (element.chapterPosition !== chapterPosition) element.chapterPosition = chapterPosition;
          break;
        }
      }
      if (!selectCourse) {
        personalTraining.courses.push({
          coursesId: courseId,
          _id: shortid.generate(),
          coursePlayProgress: [{
            _id: shortid.generate(),
            chapterID: chapterPosition,
            videoProgress: videoProgress || 0,
            totalTime: videoProgress || 0,
            duration,
          }],
          chapterPosition,
        });
      }
      await ctx.service.courses.updatePersonalTraining({
        _id: personalTrainingID,
      }, {
        $set: {
          courses: personalTraining.courses,
        },
      });
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
          data: {
            newCourses: personalTraining.courses,
            queryParams: {
              personalTrainingID,
              courseId,
              videoProgress,
              chapterPosition,
              duration,
            },
          },
        },
      });

    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },

  // 暂时不考虑运营端删除课程或者删除章节以及监管端删除课程删除的情况
  // 否则逻辑会变得复杂，出现死记录，该培训就永远完不成
  // 此处只更新课程状态和章节状态，课程完成时间以及培训完成时间不管
  async updateCompleteState(ctx) {
    try {
      const {
        personalTrainingID,
      } = ctx.request.body;
      let personalTraining = await ctx.model.PersonalTraining.findOne({
        _id: personalTrainingID,
      });
      if (!personalTraining) {
        ctx.helper.renderFail(ctx, {
          message: '没有对应记录',
        });
        return;
      }
      if (personalTraining.completeState) { // 培训完了，不用更新了
        ctx.helper.renderSuccess(ctx, {
          data: {
            message: 'OK',
          },
        });
        return;
      }
      personalTraining = JSON.parse(JSON.stringify(personalTraining));
      for (let i = 0; i < personalTraining.courses.length; i++) {
        // 遍历课程所有的记录
        const courseProgress = personalTraining.courses[i];
        if (!courseProgress.completeState) {
          let mark = false; // 标记是否所有章节看完，有未完成的就变为true
          for (let j = 0; j < courseProgress.coursePlayProgress.length; j++) {
            const coursePlayProgress = courseProgress.coursePlayProgress[j];
            if (!coursePlayProgress.completeState) {
              const temp = Number(coursePlayProgress.totalTime) / Number(coursePlayProgress.duration);
              if (temp > 0.75) coursePlayProgress.completeState = true;
              else mark = true; // 没完成，变true
            }
          }
          // 所有都完成，mark还是false
          if (!mark && courseProgress.coursePlayProgress.length) courseProgress.completeState = true;
        }
      }
      await ctx.service.courses.updatePersonalTraining({
        _id: personalTrainingID,
      }, {
        $set: {
          courses: personalTraining.courses,
        },
      });
      ctx.helper.renderSuccess(ctx, {
        data: {
          message: 'OK',
        },
      });
    } catch (error) {
      console.error(error);
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  },
};

module.exports = CoursesController;
