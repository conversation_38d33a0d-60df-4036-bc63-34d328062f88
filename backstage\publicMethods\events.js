import Cookies from 'js-cookie';


function _changeSideBar(that) {
  const sidebarStatus = Cookies.get('sidebarStatus') || '1';
  that.sidebarOpened = sidebarStatus === '1';
  if (that.sidebar) {
    that.sidebar.opened = that.sidebarOpened;
  }
}


export function initEvent(that) {

  const rootVm = that.$root;

  setTimeout(() => {
    _changeSideBar(that);
  }, 500);

  if (rootVm && rootVm.eventBus) {
    // 左侧菜单切换
    // rootVm.eventBus.$on('toggleSideBar', message => {
    //   setTimeout(() => {
    //     _changeSideBar(that);
    //   }, 500);
    // });
    rootVm.eventBus.$on('toggleSideBar', () => {
      setTimeout(() => {
        _changeSideBar(that);
      }, 500);
    });
    // 屏幕大小切换
    rootVm.eventBus.$on('toggleDevice', message => {
      that.device = message;
    });
  }

  // 修改移动端标记
  const {
    body,
  } = document;
  const WIDTH = 992;
  const rect = body.getBoundingClientRect();
  that.device = (rect.width - 1 < WIDTH ? 'mobile' : 'desktop');
}
