const Service = require('egg').Service;
const { EncryptUtil } = require('@utils');
const fs = require('fs');
const path = require('path');
const moment = require('moment');
const mkdirp = require('mkdirp');
// 根api域名
const encryptionKey = 'chiscdc@bhkdownload#$%^';
const apis = {
  getVerifyInfo: {
    url: '/authTokenId',
    method: 'post',
    name: '获取验证信息',
  },
  getZoneCompanyInfo: {
    url: '/crptDownLoad',
    method: 'post',
    name: '获取区域企业信息',
  },
  getBhkData: {
    url: '/bhkDataDownLoad',
    method: 'post',
    name: '获取体检数据',
  },
  getOccdiscasetData: {
    url: '/occdiscaseDownLoad',
    method: 'post',
    name: '获取诊断信息',
  },
  postFeedBackInfo: {
    url: '/feedBackInfo',
    method: 'post',
    name: '反馈信息',
  },
};
/** 福州数据对接Service
 * @class FzDataService
 * @augments {Service}
 * @description 有调用接口，清洗数据，数据转换的方法 和一些逻辑处理
 */
class FzDataService extends Service {
  // #region // 对接接口资源
  /** 接口 authTokenId  获取验证信息
   * @param {Object} params - 参数
   * @param {String} params.unitCode - 单位代码
   * @param {String} params.regCode - 注册码
   * @return {Object} - 返回验证信息
   */
  async getVerifyInfo(params) {
    const { ctx } = this;
    try {
      console.log('进来了', EncryptUtil);
      const { unitCode, regCode } = params;
      const data = { unitCode, regCode };
      const res = await EncryptUtil.postDataAndProcessResponse(
        ctx,
        apis.getVerifyInfo.url,
        data,
        encryptionKey
      );
      console.log('res', res);
      ctx.auditLog('fzData获取验证信息', res, 'info');
      return res;
    } catch (error) {
      ctx.auditLog('获取验证信息', `${error}`, 'error');
    }
  }
  /** 接口 crptDownLoad 获取区域企业信息
   * @param {Object} params - 参数
   * @param {String} params.tokenId - 验证信息
   * @param {String} params.zoneCode - 区域编码
   * @return {Array} - 返回企业信息 crptList
   * @description 传参 tokenId zoneCode
   */
  async getZoneCompanyInfo(params) {
    const { ctx } = this;
    try {
      const { tokenId, zoneCode } = params;
      const data = { tokenId, zoneCode };
      const res = await EncryptUtil.postDataAndProcessResponse(
        ctx,
        apis.getZoneCompanyInfo.url,
        data,
        encryptionKey
      );
      // 如果result.type 不是 00 则数据传输有误
      if (res.type !== '00') {
        throw new Error('数据传输有误' + res.mess);
      }
      const { crptList } = res;
      ctx.auditLog('fzData获取区域企业信息', crptList, 'info');
      return crptList;
    } catch (error) {
      ctx.auditLog('获取区域企业信息', `${error}`, 'error');
      return null;
    }
  }
  /** 接口 bhkDataDownLoad 获取体检数据
   * @param {Object} params - 参数
   * @param {String} params.tokenId - 验证信息
   * @param {String} params.bhkType - 体检类型 3 职业健康检查 4 放射人员健康检查
   * @param {String} params.bhkYear - 体检年份 2021
   * @param {String} params.zoneCode - 企业所在地区编码  多个地区则以逗号隔开
   * @param {String} params.ifNoDownCrptAndOrg - 下载企业数据 1 不需要
   * @return {Array} - 返回体检数据
   */
  async getBhkData(params) {
    const { ctx } = this;
    try {
      const { tokenId, bhkType, bhkYear, zoneCode, ifNoDownCrptAndOrg } =
        params;
      const data = { tokenId, bhkType, bhkYear, zoneCode, ifNoDownCrptAndOrg };
      const res = await EncryptUtil.postDataAndProcessResponse(
        ctx,
        apis.getBhkData.url,
        data,
        encryptionKey
      );
      // 如果result.type 不是 00 则数据传输有误
      if (res.type !== '00') {
        throw new Error('数据传输有误' + res.mess);
      }
      const { bhkList } = res;
      ctx.auditLog('fzData获取体检数据', bhkList, 'info');
      return bhkList;
    } catch (error) {
      ctx.auditLog('获取体检数据', `${error}`, 'error');
      return null;
    }
  }
  /** 获取诊断信息
   * @param {Object} params - 参数
   * @param {String} params.tokenId - 验证信息
   * @param {String} params.zoneCode - 区域编码
   * @param {String} params.isDis - 是否职业病 0：否，1：是，不填全部
   * @return {Array} - 返回诊断信息
   */
  async getOccdiscasetData(params) {
    const { ctx } = this;
    try {
      const { tokenId, zoneCode } = params;
      const data = { tokenId, zoneCode };
      const res = await EncryptUtil.postDataAndProcessResponse(
        ctx,
        apis.getOccdiscasetData.url,
        data,
        encryptionKey
      );
      // 如果result.type 不是 00 则数据传输有误
      if (res.type !== '00') {
        throw new Error('数据传输有误' + res.mess);
      }
      const { occdiscasetList } = res;
      ctx.auditLog('fzData获取诊断数据', occdiscasetList, 'info');
      return occdiscasetList;
    } catch (error) {
      ctx.auditLog('获取诊断数据', `${error}`, 'error');
      return null;
    }
  }
  /** 反馈信息
   * @param {Object} params - 参数
   * @param {String} params.tokenId - 验证信息
   * @param {Number} params.dataType - 数据类型 1 企业数据 2 体检数据 4 诊断数据
   * @param {Number} params.dataSate - 数据状态 1 成功 2 删除
   * @param {Array.Object} params.uuidList - 数据uuid集合
   * @param {String} params.uuidList.uuid - 数据uuid
   * @param {Number} params.uuidList.type - 数据类型 1 企业数据 2 体检数据 4 诊断数据
   * @param {String} params.uuidList.errorMsg - 错误信息
   */
  async postFeedBackInfo(params) {
    const { ctx } = this;
    try {
      const { tokenId, dataType, uuidList } = params;
      const data = { tokenId, dataType, dataSate: 1, uuidList };
      const res = await EncryptUtil.postDataAndProcessResponse(
        ctx,
        apis.postFeedBackInfo.url,
        data,
        encryptionKey
      );
      // 如果result.type 不是 00 则数据传输有误
      if (res.type !== '00') {
        throw new Error('数据传输有误' + res.mess);
      }
      ctx.auditLog('fzData反馈信息', res, 'info');
      return res;
    } catch (error) {
      ctx.auditLog('反馈信息', `${error}`, 'error');
      return null;
    }
  }
  /**  从zip包中获取数据 接口之前下载过的zip包记录
   * @param fileName - 文件名
   * @return {Array} - 返回数据 json 解析后的
   */
  async getOldDataFromZip(fileName) {
    // 读取 ctx.app.config.upload_fzDataLog_path 下的文件夹
    const { ctx } = this;
    try {
      const { upload_fzDataLog_path } = ctx.app.config;
      const fs = require('fs');
      const path = require('path');

      let filePath;

      // 检查 fileName 是否已经是完整路径
      if (path.isAbsolute(fileName)) {
        filePath = fileName;
      } else {
        // 检查 fileName 是否已经包含 .zip 扩展名
        const finalFileName = fileName.endsWith('.zip') ? fileName : fileName + '.zip';
        filePath = path.resolve(upload_fzDataLog_path, 'base', finalFileName);
      }

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
      }

      const zipContent = fs.readFileSync(filePath);
      const JSZip = require('jszip');
      const zip = new JSZip();
      const zipData = await zip.loadAsync(zipContent);
      const data = zipData.files['data.json'];

      if (!data) {
        throw new Error(`zip文件中不存在data.json: ${filePath}`);
      }

      const content = await data.async('string');
      const decryptContent = EncryptUtil.decrypt(content, encryptionKey);
      const jsonContent = JSON.parse(decryptContent);
      return jsonContent;
    } catch (error) {
      ctx.auditLog('从zip包中获取数据', `${error}`, 'error');
      return null;
    }
  }
  /**
   * 将log 写入到 ctx.app.config.upload_fzDataLog_path 下的日期文件夹下
   * @param log
   * @param name
   */
  async writeLogToZipFolder(log, name = 'Info') {
    const { ctx } = this;
    try {
      const DATE = new Date();
      const filesName = moment(DATE).format('YYYYMMDD'); // 文件夹名称
      const configFilePath = path.resolve(
        path.join(ctx.app.config.upload_fzDataLog_path, `/${filesName}`)
      );
      await mkdirp(configFilePath);
      fs.writeFileSync(
        path.join(
          configFilePath,
          `${moment(DATE).format('HHmmss')}Create${name}.json`
        ),
        JSON.stringify(log)
      );
    } catch (error) {
      ctx.auditLog('将log 写入到 ctx.app.config.upload_fzDataLog_path 下的日期文件夹下', `${error}`, 'error');
    }
  }
  // #endregion
  /** 清洗更新企业数据 传的是crplist
   * @param params
   * @return {Promise<void>}
   */
  async clearOurCompanyData(params) {
    const { ctx } = this;
    try {
      const successLog = [];
      const errorLog = [];
      // 以下用于开发环境初始化使用的，非本地开发勿启用
      // await ctx.model.Adminorg.deleteMany({}).exec();
      // await ctx.model.AdminUser.deleteMany({}).exec();
      // await ctx.model.Employee.deleteMany({}).exec();
      // await ctx.model.User.deleteMany({}).exec();
      // await ctx.model.Healthcheck.deleteMany({}).exec();
      // await ctx.model.Suspect.deleteMany({}).exec();
      for (const item of params) {
        try {
          // 处理行业分类
          const indusTypeArray = ctx.service.importToDb.handleIndustryField(
            this.getIndustryCategory(item.indusTypeCode)
          );
          const districts = this.getAddress(item.zoneCode);
          const dist = {
            districts,
            address: item.address,
            point: [],
          };
          let toCreateCompanyInfo = {
            cname: item.crptName || '',
            code: item.institutionCode || '',
            // zoneCode 所属地区编码
            indusTypeCode: indusTypeArray || '', // 行业分类编码
            economyCode: item.economyCode, // 经济类型编码
            companyScale: this.getScale(item.crptSizeCode), // 企业规模
            regAdd: item.address || '',
            // phoneNum: item.linkPhone1 || item.linkPhone2 || '',
            phoneNum: item.phone || '',
            corp: item.corporateDeputy || '',
            area: item.workAera || '',
            money: item.registerFund || '',
            contract: item.corporateDeputy || '',
            regType: item.economyCode || '',
            workAddress: [ dist ] || [],
            districtRegAdd: districts || [],
            leadIn: 4,
            isactive: 1,
          };
          toCreateCompanyInfo = this.filterEmptyFields(toCreateCompanyInfo);
          // 根据企业组织机构代码获取企业信息
          let inOurCompany =
            await ctx.service.importToDb.getOneCompanyBySearchKey({
              searchKey: item.institutionCode,
            });
          // params

          if (!inOurCompany) {
            // 整理企业数据 adminusers 可登录的账号
            let adminUser = {
              group: ctx.app.config.groupID.adminGroupID,
              userName: item.phone || '',
              name: item.corporateDeputy || '',
              phoneNum: item.phone || '',
              password: this.getSixStr(item.institutionCode),
              enable: true,
            };
            // 如果没有手机号码和姓名则不传，防止索引报错 !Important 删掉为空的
            adminUser = this.filterEmptyFields(adminUser);
            const adminUserCreated = await ctx.model.AdminUser.create(
              adminUser
            );
            toCreateCompanyInfo.adminUserId = adminUserCreated._id;
            inOurCompany = await ctx.model.Adminorg.create(toCreateCompanyInfo);
            // flag1

            // 补充创建employee
            // let employeeInfo = {
            //   name: item.corporateDeputy || '', // 姓名
            //   phoneNum: item.phone || '', // 联系电话
            //   EnterpriseID: inOurCompany._id, // 企业id
            // };
            // employeeInfo = this.filterEmptyFields(employeeInfo);
            // const employeeCreated = await ctx.service.importToDb.createEmployee(
            //   employeeInfo
            // );
            // // 需要更新adminuser 里面的targetEditor 为employeeId
            // const updateAdminUserRes =
            //   await ctx.model.AdminUser.findOneAndUpdate(
            //     { _id: adminUserCreated._id },
            //     {
            //       targetEditor: employeeCreated._id,
            //       employees: [ employeeCreated._id ],
            //     }
            //   );
            const log = {
              EnterpriseID: inOurCompany._id,
              adminUserID: adminUserCreated._id,
              // res: updateAdminUserRes,
            };
            this.ctx.auditLog('不存在已创建', `${JSON.stringify(log)}`, 'info');
            successLog.push({
              uuid: item.uuid,
              type: 0,
              errorMsg: '',
            });
            continue;
          }
          // 如果存在企业信息 则更新企业信息
          const updateAdminorgRes = await ctx.model.Adminorg.findOneAndUpdate(
            { _id: inOurCompany._id },
            {
              $set: toCreateCompanyInfo,
            }
          );
          if (inOurCompany.cname !== toCreateCompanyInfo.cname) {
            // 如果企业名称不同则添加到nameUsedBefore
            console.log('企业名称不同则添加到nameUsedBefore');
            await ctx.model.Adminorg.findOneAndUpdate(
              { _id: inOurCompany._id },
              {
                $push: {
                  nameUsedBefore: {
                    name: inOurCompany.cname,
                    note: '报告导入',
                  },
                },
              }
            );
          }
          ctx.auditLog(
            '存在已更新',
            `${JSON.stringify(updateAdminorgRes)}`,
            'info'
          );
        } catch (error) {
          errorLog.push({
            uuid: item.uuid,
            type: 1,
            errorMsg: '创建企业数据时出错，可能必传参数丢失',
          });
          ctx.auditLog('清洗更新企业数据出错啦', `${error}`, 'error');
        }
      }
      ctx.auditLog(
        '清洗更新企业数据成功整体数据',
        `${JSON.stringify(successLog)}`,
        'info'
      );
      if (errorLog.length > 0) {
        ctx.auditLog(
          '清洗更新企业数据失败整体数据',
          `${JSON.stringify(errorLog)}`,
          'error'
        );
      }
      const merageLog = successLog.concat(errorLog);
      await this.writeLogToZipFolder(merageLog, 'Company');
      return true;
    } catch (error) {
      ctx.auditLog('清洗更新企业数据', `${error}`, 'error');
    }
  }
  /** 更新企业数据
   *
   * @param params
   */
  async fixOurCompanyData() {
    const { ctx } = this;
    try {
      let jsonDatas = [];
      // 读取 ctx.app.config.upload_fzDataLog_path 20231017下的文件，输出文件目录 ,
      const { upload_fzDataLog_path } = ctx.app.config;
      const ZoneCompanyListResultObj =
        await this.ctx.service.fzData.getOldDataFromZip('baseOccdiscasetData');
      const { bhkList } = ZoneCompanyListResultObj;
      jsonDatas = jsonDatas.concat(bhkList);
      const filePath = path.resolve(upload_fzDataLog_path, '20231017');
      const files = fs.readdirSync(filePath);
      // 过滤出来包含FromcrptDownLoad的文件
      const filterFiles = files.filter(item =>
        item.includes('FromoccdiscaseDownLoad')
      );
      for (const fileCrp of filterFiles) {
        const filePath = path.resolve(
          upload_fzDataLog_path,
          '20231017',
          fileCrp
        );
        const zipContent = fs.readFileSync(filePath);
        const JSZip = require('jszip');
        const zip = new JSZip();
        const zipData = await zip.loadAsync(zipContent);
        const data = zipData.files['data.json'];
        const content = await data.async('string');
        const decryptContent = EncryptUtil.decrypt(content, encryptionKey);
        const jsonContent = JSON.parse(decryptContent);
        if (jsonContent.type === '00') {
          // console.log('jsonContent', jsonContent);
          jsonDatas = jsonDatas.concat(jsonContent.occdiscasetList);
        }
      }
      // jsonDatas 为数组 【{原始数据哦 QAQ}】
      // 将jsonDatas 转换成json文件 写入到根目录
      fs.writeFileSync(
        path.join(ctx.app.config.baseDir, 'occList.json'),
        JSON.stringify(jsonDatas)
      );
    } catch (error) {
      ctx.auditLog('更新企业数据', `${error}`, 'error');
    }
  }

  /** 取str后6位 用于组织机构代码取后6位
   * @param {String} str - 企业组织机构代码 91350200MA31X6JX7A
   * @return {String} - 返回后6位  X6JX7A
   */
  getSixStr(str) {
    return str.slice(-6);
  }

  // 清洗体检报告数据
  async clearHealthyCheckData(params) {
    const { ctx } = this;
    try {
      // const { bhkList } = await this.getOldDataFromZip('baseTjData');
      console.log('体检HealtheCheck清洗');
      const successLog = [];
      const errorLog = [];
      for (const item of params) {
        try {
          // 1. 根据企业组织机构代码获取企业信息
          let inOurCompany =
            await ctx.service.importToDb.getOneCompanyBySearchKey({
              searchKey: item.institutionCode,
            });
          if (!inOurCompany) {
            // 创建企业
            console.log('创建企业', item.institutionCode);
            const adminUser = {
              group: ctx.app.config.groupID.adminGroupID,
              password: this.getSixStr(item.institutionCode) || '',
              enable: true,
            };
            const adminUserCreated = await ctx.model.AdminUser.create(
              adminUser
            );
            let toCreateCompanyInfo = {
              cname: item.crptName || '',
              code: item.institutionCode || '',
              adminUserId: adminUserCreated._id,
              leadIn: 4,
              isactive: 1,
            };
            toCreateCompanyInfo = this.filterEmptyFields(toCreateCompanyInfo);
            inOurCompany = await ctx.service.importToDb.createAdminorg(
              toCreateCompanyInfo
            );
          }
          // 2. 查询体检机构信息
          let inOurPhysicalExamOrg =
            await ctx.service.importToDb.getOnePhysicalExamOrgBySearchKey({
              searchKey: item.bhkOrganCode,
            });
          if (!inOurPhysicalExamOrg) {
            // 创建体检机构
            console.log('创建体检机构', item.bhkOrganCode);
            inOurPhysicalExamOrg = {};
            // inOurPhysicalExamOrg._id = '3tPAj8PdjpD';
            const physicalExamUser = {
              password: item.bhkOrganCode.slice(-6),
            };
            const reg = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;
            let physicalExamOrg = {
              name: '未知',
              shortName: '未知',
              organization: reg.test(item.bhkOrganCode)
                ? item.bhkOrganCode
                : '',
              unitCode: reg.test(item.bhkOrganCode) ? '' : item.bhkOrganCode,
            };
            physicalExamOrg = this.filterEmptyFields(physicalExamOrg);
            // 没有的话创建
            const params = {
              physicalExamUser,
              physicalExamOrg,
            };
            try {
              const toCreatedRes =
                await ctx.service.importToDb.createPhysicalExamOrgFlow(params);
              inOurPhysicalExamOrg._id = toCreatedRes.physicalExamOrg;
            } catch (error) {
              ctx.auditLog('创建体检机构失败', `${error}`, 'error');
            }
          }
          // 3. 查询是否存在体检报告
          console.log('体检类型为', item.onguardState);
          let haveHealthCheckData =
            // await ctx.service.importToDb.getOneHealthCheckBySearchKey({
            //   searchKey: item.uuid,
            // });
            await ctx.service.importToDb.getOneHealthCheckBySearchType({
              physicalExaminationOrgID: inOurPhysicalExamOrg._id,
              EnterpriseID: inOurCompany._id,
              checkDate: new Date(item.bhkDate),
              checkType: this.getOnguardState(+item.onguardState),
            });
          if (!haveHealthCheckData) {
            const districts = this.getAddress(item.zoneCode);
            const dist = {
              districts,
              address: '',
              point: [],
            };
            // 创建体检报告
            const healCheckData = {
              physicalExaminationOrgID: inOurPhysicalExamOrg._id, // 体检机构id
              enterpriseName: item.crptName, // 企业名称
              EnterpriseID: inOurCompany._id, // 企业id
              enterpriseContactsName: inOurCompany.contract, // 企业联系人姓名
              enterpriseContactsPhonNumber: inOurCompany.phoneNum, // 企业联系人电话
              workAddress: inOurCompany.workAddress && inOurCompany.workAddress
                .length > 0 ? inOurCompany.workAddress : [ dist ], // 工作场所
              year: '' + new Date(item.bhkDate).getFullYear(), // 年份
              projectNumber: item.bhkCode, // 项目编号
              checkDate: new Date(item.bhkDate), // 项目开始时间
              checkEndDate: new Date(item.bhkDate), // 项目结束时间
              // checkPlace: '', // 体检机构地点
              approvalDate: new Date(item.rptPrintDate), // 批准日期
              // applyTime: '', // 申请时间
              checkType: this.getOnguardState(+item.onguardState), // 检查类型
              shouldCheckNum: 0, // 应检人数
              actuallNum: 0, // 实检人数
              normal: 0, // 正常人数
              re_examination: 0, // 复检人数
              suspected: 0, // 疑似职业病人数
              forbid: 0, // 禁忌证人数
              otherDisease: 0, // 其他疾病人数
              // comment: '', // 评价
              recheck: false,
              organization: inOurPhysicalExamOrg.name || '',
              reportStatus: true,
              applyTime: new Date(item.createDate),
            };
            // 创建体检报告
            haveHealthCheckData =
              await ctx.service.importToDb.createHealthCheck(healCheckData);
          }
          // 4. 查询是否存在员工  假设身份证号必有 文档是 人员类型为1 必填
          let inOurEmployee =
            await ctx.service.importToDb.getOneEmployeeBySearchKey({
              searchKey: item.idc,
            });
          if (!inOurEmployee || item.psnType === 3) {
            // 创建员工 需要user 和 employee
            let employeeInfo = {
              name: item.personName || '', // 姓名
              phoneNum: item.lnkTel || '', // 联系电话
              age: item.age || '', // 年龄
              IDNum: item.idc || '', // 身份证号
              gender: this.getGender(item.sex), // 性别
              marriage: item.isxmrd || '', // 婚姻状况
              workYears: item.wrkLnt || '', // 工龄年
              EnterpriseID: inOurCompany._id, // 企业id
              workType: item.workName || '', // 工种
              // departs:[item.dpt],//部门
              status: this.getOnguardState(+item.onguardState) === '2' ? 0 : 1, // 在职状态[编码需要转义]
            };
            let userInfo = {
              name: item.personName || '', // 姓名
              idNo: item.idc || '', // 身份证号
              phoneNum: item.lnkTel || '', // 联系电话
              companyId: [ inOurCompany._id ], // 所在企业
              companyStatus: 2, // 企业审核通过
            };
            // 过滤一下employeeInfo 删除里面为空的字段
            employeeInfo = this.filterEmptyFields(employeeInfo);
            userInfo = this.filterEmptyFields(userInfo);
            // 创建员工 employee 和user
            const createEmployeeAndUserRes =
              await ctx.service.importToDb.createEmployeeAndUserWorkFlows({
                employee: employeeInfo,
                user: userInfo,
              });
            inOurEmployee = createEmployeeAndUserRes.employee;
          }
          // #region // 数据格式转换 体检危害因素 主检判断主检结论 症状信息 疑似职业病 职业禁忌证
          const processedBadrsnList = []; // 体检危害因素
          if (item.badRsnList) {
            for (const badRsnItem of item.badRsnList) {
              processedBadrsnList.push({
                harmFactor: this.getHarmFactors(badRsnItem.badRsnCode), // 有害因素
                examConclusion: this.getConclusion(
                  +badRsnItem.examConclusionCode
                ), // 体检结论
                suspectedOccupationalDisease:
                  this.getSuspectedOccupationalDisease(badRsnItem.yszybCode),
                occupationalContraindications:
                  this.getOccupationalContraindication(badRsnItem.zyjjzCode), // 职业禁忌证
                otherDisease: badRsnItem.qtjbName, // 其他疾病
              });
            }
          }
          const processedMhkRstList = []; // 主检判断主检结论
          if (item.mhkRstList) {
            for (const mhkRsItem of item.mhkRstList) {
              mhkRsItem.examConclusion = this.getConclusion(
                mhkRsItem.bhkRstCode
              );
              processedMhkRstList.push(mhkRsItem);
            }
          }
          const processedSymptomList = []; // 症状信息
          if (item.symptomList) {
            for (const symptomItem of item.symptomList) {
              symptomItem.symptom = this.getSymptom(symptomItem.symptomCode);
              processedSymptomList.push(symptomItem);
            }
          }
          const processedSupoccdiseList = []; // 疑似职业病
          if (item.supoccdiseList) {
            for (const supoccdiseItem of item.supoccdiseList) {
              processedSupoccdiseList.push({
                badrsn: this.getHarmFactors(supoccdiseItem.badrsn), // 有害因素
                occdiseCode: this.getSuspectedOccupationalDisease(
                  supoccdiseItem.occdiseCode
                ), // 疑似职业病
              });
            }
          }
          const processedBhkSubList = []; // 体检详细项目
          if (item.bhkSubList) {
            for (const bhkSubItem of item.bhkSubList) {
              const subNew = {
                ...bhkSubItem,
                ...this.getBhklist(bhkSubItem.itmcod),
              };
              processedBhkSubList.push(subNew);
            }
          }
          const processedContraindList = []; // 职业禁忌证
          if (item.contraindList) {
            for (const contraindItem of item.contraindList) {
              processedContraindList.push({
                badrsn: this.getHarmFactors(contraindItem.badrsn), // 有害因素
                contraind: this.getOccupationalContraindication(
                  contraindItem.contraindCode
                ), // 职业禁忌证
              });
            }
          }
          // #endregion
          const suspectInfo = {
            unitCode: item.uuid,
            batch: haveHealthCheckData._id,
            name: item.personName, // 姓名
            age: item.age, // 年龄
            gender: this.getGender(item.sex), // 性别
            workType: item.workName, // 工种
            harmFactors: this.getHarmFactors(item.badRsn), // 危害因素
            illness: item.mhkAdv, // 建议
            recheck: this.getIsReview(item.ifRhk), // 是否复查
            checkDate: new Date(item.bhkDate), // 体检日期
            IDCard: item.idc || '', // 身份证号
            EnterpriseID: inOurCompany._id, // 企业id
            workYears: item.wrkLnt, // 工龄
            abnormalIndex: item.bhkRst, // 异常指标
            opinion: item.mhkAdv, // 主检建议
            CwithO: this.getFinalConclusion(processedMhkRstList), // 体检结论
            employeeId: inOurEmployee._id, // 员工id
            checkType: this.getOnguardState(+item.onguardState), // 体检类型
            otherHarmFactors: item.otherBadrsn, // 其他危害因素
            riskFactorsOfPhysicalExaminations: processedBadrsnList, // 体检危害因素
            bhkSubList: processedBhkSubList, // 体检结果
            mhkRstList: processedMhkRstList, // 主检判断主检结论
            exmdDataList: item.exmdDataList, // 一般问诊项目
            symptomList: processedSymptomList, // 症状信息
            bhkAnamnesisList: item.bhkAnamnesisList, // 既往病史
            supoccdiseList: processedSupoccdiseList, // 疑似职业病
            contraindList: processedContraindList, // 职业禁忌证
          };

          // 5. 查询该项目中是否存在该员工，如果存在的话，就跳过或更新
          // 先尝试精确匹配：批次 + 员工ID + 体检类型
          let existEmployeeOfProject = await ctx.service.importToDb.getSuspectByQuery({
            batch: suspectInfo.batch,
            employeeId: suspectInfo.employeeId,
            checkType: suspectInfo.checkType,
          });
          // 如果还没找到，尝试按unitCode（福州数据的唯一标识）查询
          if (!existEmployeeOfProject && suspectInfo.unitCode) {
            existEmployeeOfProject = await ctx.service.importToDb.getSuspectByQuery({
              unitCode: suspectInfo.unitCode,
              batch: suspectInfo.batch,
            });
          }
          // 如果精确匹配没找到，尝试按姓名 + 身份证号 + 体检日期 + 企业ID查询
          if (!existEmployeeOfProject && suspectInfo.name && suspectInfo.IDCard) {
            existEmployeeOfProject = await ctx.service.importToDb.getSuspectByQuery({
              name: suspectInfo.name,
              IDCard: suspectInfo.IDCard,
              checkDate: suspectInfo.checkDate,
              EnterpriseID: suspectInfo.EnterpriseID,
              checkType: suspectInfo.checkType,
            });
          }


          // 如果找到重复记录，跳过并记录日志
          if (existEmployeeOfProject) {
            ctx.auditLog('体检数据清洗', `发现重复suspect记录，跳过创建: ${item.uuid}, 已存在记录ID: ${existEmployeeOfProject._id}`, 'info');
            // 更新
            await ctx.service.importToDb.updateSuspect(existEmployeeOfProject._id, suspectInfo);
            successLog.push({
              uuid: item.uuid,
              type: 1, // 表示跳过的记录
              errorMsg: '记录已存在，跳过创建',
            });
            continue;
          }

          await ctx.service.importToDb.createSuspect(
            suspectInfo
          );
          // 自增实检人数
          const $inc = { actuallNum: 1 };
          $inc[
            this.getConclusionField(
              this.getFinalConclusion(processedMhkRstList)
            )
          ] = 1;
          await ctx.model.Healthcheck.findOneAndUpdate(
            { _id: haveHealthCheckData._id },
            { $inc }
          );
          const processedEmhistoryList = []; // 职业史
          if (item.emhistoryList) {
            for (const emhistoryItem of item.emhistoryList) {
              processedEmhistoryList.push({
                employeeId: inOurEmployee._id,
                entryTime: emhistoryItem.stastpDate
                  ? emhistoryItem.stastpDate.split('~')[0]
                  : '', // 入职时间
                leaveTime: emhistoryItem.stastpDate
                  ? emhistoryItem.stastpDate.split('~')[1]
                  : '', // 离职时间
                workUnit: emhistoryItem.unitName,
                workshop: emhistoryItem.department,
                workType: emhistoryItem.workType,
                prfraysrt: emhistoryItem.prfraysrt, // 接触有害因素
                prfwrklod: emhistoryItem.prfwrklod, // (放射)每日工作时数或工作量
                prfshnvlu: emhistoryItem.prfshnvlu, //	(放射)职业史累积受照剂量
                prfexcshn: emhistoryItem.prfexcshn, // (放射)职业史过量照射史
                prfraysrt2: emhistoryItem.prfraysrt2, //	(放射)职业照射种类
                prfraysrtcods: emhistoryItem.prfraysrtcods, // (放射)职业照射种类代码
                fsszl: emhistoryItem.fsszl, //	(放射)放射线种类
                defendStep: emhistoryItem.defendStep, //	(非放射)防护措施
                chkdat: emhistoryItem.chkdat, // 检查日期
                chkdoc: emhistoryItem.chkdoc, //	检查医生
              });
              // 职业病史整理
              await ctx.service.importToDb.createOccupationalHistory(
                processedEmhistoryList,
                'many'
              );
              // res.push({
              //   haveHealthCheckData,
              //   createSuspectRes,
              //   createOccupationalHistoryRes,
              //   incRes,
              // });
            }
          }
          // 体检报告数据处理 企业表里更新
          await ctx.service.healthcheck.dealWithHealcheckInfo(
            haveHealthCheckData._id
          );
          // 这个地方 以后如果要加预警的话在这里加；
          try {
            await ctx.service.report.updateHealthCheck(
              haveHealthCheckData._id
            );
          } catch (error) {
            ctx.auditLog('体检预警判断', `${error}`, 'error');
          }
          successLog.push({
            uuid: item.uuid,
            type: 0,
            errorMsg: '',
          });
        } catch (error) {
          errorLog.push({
            uuid: item.uuid,
            type: 2,
            errorMsg: '创建体检报告数据时出错，可能必传参数丢失',
          });
          ctx.auditLog('清洗体检报告数据出错啦', `${error}`, 'error');
        }
      }
      ctx.auditLog(
        '清洗体检报告数据成功整体数据',
        `${JSON.stringify(successLog)}`,
        'info'
      );
      if (errorLog.length > 0) {
        ctx.auditLog(
          '清洗体检报告数据失败整体数据',
          `${JSON.stringify(errorLog)}`,
          'error'
        );
      }
      const merageLog = successLog.concat(errorLog);
      await this.writeLogToZipFolder(merageLog, 'HealthyCheck');
    } catch (error) {
      ctx.auditLog('清洗体检报告数据', `${error}`, 'error');
    }
  }

  /**
   * 处理企业数据 - 用于串行处理
   * @param {Object} options - 处理选项
   * @param {String} options.filePath - 文件路径（可选）
   * @param {Number} options.index - 文件索引（可选）
   * @return {Promise<Object>} 处理结果
   */
  async processCompanyData(options = {}) {
    const { ctx } = this;
    try {
      const { filePath, index } = options;

      // 如果指定了文件路径，直接处理该文件
      if (filePath) {
        ctx.logger.info(`开始处理企业数据文件: ${filePath}`);

        let jsonData;
        try {
          jsonData = await this.getOldDataFromZip(filePath);
        } catch (error) {
          ctx.logger.error(`读取企业数据文件失败: ${filePath}`, error);
          return {
            success: false,
            skipped: true,
            skipReason: 'file_read_failed',
            message: `无法读取文件数据: ${error.message}`,
            processed: 0,
            duration: 0,
            error: error.message,
          };
        }

        if (!jsonData) {
          return {
            success: false,
            skipped: true,
            skipReason: 'file_empty',
            message: `文件数据为空: ${filePath}`,
            processed: 0,
            duration: 0,
          };
        }

        // 记录文件中的数据结构
        ctx.logger.info('企业数据文件结构:', {
          keys: Object.keys(jsonData),
          hasCrptList: !!jsonData.crptList,
          hasZoneCompanyList: !!jsonData.ZoneCompanyList,
          crptListLength: jsonData.crptList ? jsonData.crptList.length : 0,
          zoneCompanyListLength: jsonData.ZoneCompanyList ? jsonData.ZoneCompanyList.length : 0,
        });

        // 检查特殊响应格式 - 参考 fzDataProcessor.js
        if (jsonData && typeof jsonData === 'object') {
          // 检查无数据响应
          if (jsonData.mess === '未查询到相关数据！' && jsonData.type === '03') {
            ctx.logger.info('企业数据类型为03，未查询到相关数据');
            return {
              success: true,
              skipped: true,
              skipReason: 'no_data',
              message: '未查询到相关数据',
              processed: 0,
              duration: 0,
              details: { message: '未查询到相关数据', type: jsonData.type },
            };
          }

          // 检查Token无效响应
          if (jsonData.mess === '认证TokenId无效，请重新获取TokenId！' && jsonData.type === '02') {
            ctx.logger.info('企业数据TokenId无效');
            return {
              success: true,
              skipped: true,
              skipReason: 'invalid_token',
              message: '认证TokenId无效，请重新获取TokenId',
              processed: 0,
              duration: 0,
              details: { message: '认证TokenId无效', type: jsonData.type },
            };
          }

          // 检查企业数据列表
          if (jsonData.crptList || jsonData.ZoneCompanyList) {
            const companyList = jsonData.crptList || jsonData.ZoneCompanyList;

            if (Array.isArray(companyList) && companyList.length > 0) {
              try {
                const result = await this.clearOurCompanyData(companyList);
                return {
                  success: true,
                  message: `企业数据处理完成，处理了 ${companyList.length} 条记录`,
                  processed: companyList.length,
                  duration: 0,
                  details: result,
                };
              } catch (error) {
                ctx.logger.error(`企业数据处理失败: ${filePath}`, error);
                return {
                  success: false,
                  skipped: false,
                  message: `企业数据处理失败: ${error.message}`,
                  processed: 0,
                  duration: 0,
                  error: error.message,
                };
              }
            } else {
              return {
                success: true,
                skipped: true,
                skipReason: 'empty_data',
                message: '企业数据列表为空',
                processed: 0,
                duration: 0,
                details: { message: '企业数据列表为空', listLength: companyList ? companyList.length : 0 },
              };
            }
          }
        }

        // 如果没有找到预期的数据结构，跳过处理
        return {
          success: true,
          skipped: true,
          skipReason: 'invalid_format',
          message: `文件中没有找到企业数据。文件包含的字段: ${Object.keys(jsonData).join(', ')}`,
          processed: 0,
          duration: 0,
          details: { availableKeys: Object.keys(jsonData) },
        };

      }

      // 如果没有指定文件路径，处理所有企业数据文件
      const upload_fzDataLog_path = ctx.app.config.upload_fzDataLog_path;
      const fs = require('fs');

      if (!fs.existsSync(upload_fzDataLog_path)) {
        throw new Error(`数据目录不存在: ${upload_fzDataLog_path}`);
      }

      // 获取所有企业数据文件
      const files = this._getFilesByType(upload_fzDataLog_path, 'FromcrptDownLoad');

      if (files.length === 0) {
        return {
          success: true,
          message: '没有找到企业数据文件',
          processed: 0,
          duration: 0,
        };
      }

      // 如果指定了索引，只处理该文件
      if (typeof index === 'number' && index >= 0 && index < files.length) {
        const targetFile = files[index];
        const jsonData = await this.getOldDataFromZip(targetFile);
        if (jsonData && (jsonData.crptList || jsonData.ZoneCompanyList)) {
          const companyList = jsonData.crptList || jsonData.ZoneCompanyList;
          const result = await this.clearOurCompanyData(companyList);
          return {
            success: true,
            message: `企业数据处理完成，处理了 ${companyList.length} 条记录`,
            processed: companyList.length,
            duration: 0,
            details: result,
          };
        }
        throw new Error('文件中没有找到企业数据');

      }

      // 处理所有文件
      let totalProcessed = 0;
      const startTime = Date.now();

      for (const file of files) {
        try {
          const jsonData = await this.getOldDataFromZip(file);
          if (jsonData && (jsonData.crptList || jsonData.ZoneCompanyList)) {
            const companyList = jsonData.crptList || jsonData.ZoneCompanyList;
            await this.clearOurCompanyData(companyList);
            totalProcessed += companyList.length;
          }
        } catch (error) {
          ctx.logger.error(`处理企业数据文件失败: ${file}`, error);
        }
      }

      const duration = Date.now() - startTime;

      return {
        success: true,
        message: `企业数据批量处理完成，共处理 ${files.length} 个文件，${totalProcessed} 条记录`,
        processed: totalProcessed,
        duration,
      };

    } catch (error) {
      ctx.logger.error('处理企业数据失败', error);
      return {
        success: false,
        message: `处理企业数据失败: ${error.message}`,
        processed: 0,
        duration: 0,
        error: error.message,
      };
    }
  }

  /**
   * 处理体检数据 - 用于串行处理
   * @param {Object} options - 处理选项
   * @param {String} options.filePath - 文件路径（可选）
   * @param {Number} options.index - 文件索引（可选）
   * @return {Promise<Object>} 处理结果
   */
  async processHealthCheckData(options = {}) {
    const { ctx } = this;
    try {
      const { filePath, index } = options;

      // 如果指定了文件路径，直接处理该文件
      if (filePath) {
        ctx.logger.info(`开始处理体检数据文件: ${filePath}`);

        let jsonData;
        try {
          jsonData = await this.getOldDataFromZip(filePath);
        } catch (error) {
          ctx.logger.error(`读取体检数据文件失败: ${filePath}`, error);
          return {
            success: false,
            skipped: true,
            skipReason: 'file_read_failed',
            message: `无法读取文件数据: ${error.message}`,
            processed: 0,
            duration: 0,
            error: error.message,
          };
        }

        if (!jsonData) {
          return {
            success: false,
            skipped: true,
            skipReason: 'file_empty',
            message: `文件数据为空: ${filePath}`,
            processed: 0,
            duration: 0,
          };
        }

        // 记录文件中的数据结构
        ctx.logger.info('体检数据文件结构:', {
          keys: Object.keys(jsonData),
          hasBhkList: !!jsonData.bhkList,
          bhkListLength: jsonData.bhkList ? jsonData.bhkList.length : 0,
          type: jsonData.type,
          mess: jsonData.mess,
        });

        // 检查特殊响应格式 - 参考 fzDataProcessor.js
        if (jsonData && typeof jsonData === 'object') {
          // 检查无数据响应
          if (jsonData.type === '03') {
            ctx.logger.info('体检数据类型为03，未查询到相关数据');
            return {
              success: true,
              skipped: true,
              skipReason: 'no_data',
              message: '未查询到相关数据',
              processed: 0,
              duration: 0,
              details: { message: '未查询到相关数据', type: jsonData.type },
            };
          }

          // 检查Token无效响应
          if (jsonData.mess === '认证TokenId无效，请重新获取TokenId！' && jsonData.type === '02') {
            ctx.logger.info('体检数据TokenId无效');
            return {
              success: true,
              skipped: true,
              skipReason: 'invalid_token',
              message: '认证TokenId无效，请重新获取TokenId',
              processed: 0,
              duration: 0,
              details: { message: '认证TokenId无效', type: jsonData.type },
            };
          }

          // 检查体检数据列表
          if (jsonData.bhkList) {
            if (Array.isArray(jsonData.bhkList) && jsonData.bhkList.length > 0) {
              try {
                const result = await this.clearHealthyCheckData(jsonData.bhkList);
                return {
                  success: true,
                  message: `体检数据处理完成，处理了 ${jsonData.bhkList.length} 条记录`,
                  processed: jsonData.bhkList.length,
                  duration: 0,
                  details: result,
                };
              } catch (error) {
                ctx.logger.error(`体检数据处理失败: ${filePath}`, error);
                return {
                  success: false,
                  skipped: false,
                  message: `体检数据处理失败: ${error.message}`,
                  processed: 0,
                  duration: 0,
                  error: error.message,
                };
              }
            } else {
              return {
                success: true,
                skipped: true,
                skipReason: 'empty_data',
                message: '体检数据列表为空',
                processed: 0,
                duration: 0,
                details: { message: '体检数据列表为空', listLength: jsonData.bhkList ? jsonData.bhkList.length : 0 },
              };
            }
          }
        }

        // 如果没有找到预期的数据结构，跳过处理
        return {
          success: true,
          skipped: true,
          skipReason: 'invalid_format',
          message: `文件中没有找到体检数据。文件包含的字段: ${Object.keys(jsonData).join(', ')}`,
          processed: 0,
          duration: 0,
          details: { availableKeys: Object.keys(jsonData) },
        };

      }

      // 如果没有指定文件路径，处理所有体检数据文件
      const upload_fzDataLog_path = ctx.app.config.upload_fzDataLog_path;
      const fs = require('fs');

      if (!fs.existsSync(upload_fzDataLog_path)) {
        throw new Error(`数据目录不存在: ${upload_fzDataLog_path}`);
      }

      // 获取所有体检数据文件
      const files = this._getFilesByType(upload_fzDataLog_path, 'FrombhkDataDownLoad');

      if (files.length === 0) {
        return {
          success: true,
          message: '没有找到体检数据文件',
          processed: 0,
          duration: 0,
        };
      }

      // 如果指定了索引，只处理该文件
      if (typeof index === 'number' && index >= 0 && index < files.length) {
        const targetFile = files[index];
        const jsonData = await this.getOldDataFromZip(targetFile);
        if (jsonData && jsonData.bhkList) {
          const result = await this.clearHealthyCheckData(jsonData.bhkList);
          return {
            success: true,
            message: `体检数据处理完成，处理了 ${jsonData.bhkList.length} 条记录`,
            processed: jsonData.bhkList.length,
            duration: 0,
            details: result,
          };
        }
        throw new Error('文件中没有找到体检数据');

      }

      // 处理所有文件
      let totalProcessed = 0;
      const startTime = Date.now();

      for (const file of files) {
        try {
          const jsonData = await this.getOldDataFromZip(file);
          if (jsonData && jsonData.bhkList) {
            await this.clearHealthyCheckData(jsonData.bhkList);
            totalProcessed += jsonData.bhkList.length;
          }
        } catch (error) {
          ctx.logger.error(`处理体检数据文件失败: ${file}`, error);
        }
      }

      const duration = Date.now() - startTime;

      return {
        success: true,
        message: `体检数据批量处理完成，共处理 ${files.length} 个文件，${totalProcessed} 条记录`,
        processed: totalProcessed,
        duration,
      };

    } catch (error) {
      ctx.logger.error('处理体检数据失败', error);
      return {
        success: false,
        message: `处理体检数据失败: ${error.message}`,
        processed: 0,
        duration: 0,
        error: error.message,
      };
    }
  }

  /**
   * 处理诊断数据 - 用于串行处理
   * @param {Object} options - 处理选项
   * @param {String} options.filePath - 文件路径（可选）
   * @param {Number} options.index - 文件索引（可选）
   * @return {Promise<Object>} 处理结果
   */
  async processDiagnosisData(options = {}) {
    const { ctx } = this;
    try {
      const { filePath, index } = options;

      // 如果指定了文件路径，直接处理该文件
      if (filePath) {
        ctx.logger.info(`开始处理诊断数据文件: ${filePath}`);

        let jsonData;
        try {
          jsonData = await this.getOldDataFromZip(filePath);
        } catch (error) {
          ctx.logger.error(`读取诊断数据文件失败: ${filePath}`, error);
          return {
            success: false,
            skipped: true,
            skipReason: 'file_read_failed',
            message: `无法读取文件数据: ${error.message}`,
            processed: 0,
            duration: 0,
            error: error.message,
          };
        }

        if (!jsonData) {
          return {
            success: false,
            skipped: true,
            skipReason: 'file_empty',
            message: `文件数据为空: ${filePath}`,
            processed: 0,
            duration: 0,
          };
        }

        // 记录文件中的数据结构
        ctx.logger.info('诊断数据文件结构:', {
          keys: Object.keys(jsonData),
          hasOccdiscasetList: !!jsonData.occdiscasetList,
          occdiscasetListLength: jsonData.occdiscasetList ? jsonData.occdiscasetList.length : 0,
          type: jsonData.type,
          mess: jsonData.mess,
        });

        // 检查特殊响应格式 - 参考 fzDataProcessor.js
        if (jsonData && typeof jsonData === 'object') {
          // 检查无数据响应
          if (jsonData.mess === '未查询到相关数据！' && jsonData.type === '03') {
            ctx.logger.info('诊断数据类型为03，未查询到相关数据');
            return {
              success: true,
              skipped: true,
              skipReason: 'no_data',
              message: '未查询到相关数据',
              processed: 0,
              duration: 0,
              details: { message: '未查询到相关数据', type: jsonData.type },
            };
          }

          // 检查Token无效响应
          if (jsonData.mess === '认证TokenId无效，请重新获取TokenId！' && jsonData.type === '02') {
            ctx.logger.info('诊断数据TokenId无效');
            return {
              success: true,
              skipped: true,
              skipReason: 'invalid_token',
              message: '认证TokenId无效，请重新获取TokenId',
              processed: 0,
              duration: 0,
              details: { message: '认证TokenId无效', type: jsonData.type },
            };
          }

          // 检查诊断数据type，除非为"00"，其他都按跳过处理
          if (jsonData.type && jsonData.type !== '00') {
            ctx.logger.info(`诊断数据type不为"00"，已跳过处理，实际类型: ${jsonData.type}`);
            return {
              success: true,
              skipped: true,
              skipReason: 'type_not_00',
              message: `type不为00的诊断数据已跳过，实际类型: ${jsonData.type}`,
              processed: 0,
              duration: 0,
              details: { message: 'type不为00的诊断数据已跳过', type: jsonData.type, actualType: jsonData.type },
            };
          }

          // 检查诊断数据列表
          if (jsonData.occdiscasetList) {
            if (Array.isArray(jsonData.occdiscasetList) && jsonData.occdiscasetList.length > 0) {
              try {
                const result = await this.createDiagnosis(jsonData.occdiscasetList);
                return {
                  success: true,
                  message: `诊断数据处理完成，处理了 ${jsonData.occdiscasetList.length} 条记录`,
                  processed: jsonData.occdiscasetList.length,
                  duration: 0,
                  details: result,
                };
              } catch (error) {
                ctx.logger.error(`诊断数据处理失败: ${filePath}`, error);
                return {
                  success: false,
                  skipped: false,
                  message: `诊断数据处理失败: ${error.message}`,
                  processed: 0,
                  duration: 0,
                  error: error.message,
                };
              }
            } else {
              return {
                success: true,
                skipped: true,
                skipReason: 'empty_data',
                message: '诊断数据列表为空',
                processed: 0,
                duration: 0,
                details: { message: '诊断数据列表为空', listLength: jsonData.occdiscasetList ? jsonData.occdiscasetList.length : 0 },
              };
            }
          }
        }

        // 如果没有找到预期的数据结构，跳过处理
        return {
          success: true,
          skipped: true,
          skipReason: 'invalid_format',
          message: `文件中没有找到诊断数据。文件包含的字段: ${Object.keys(jsonData).join(', ')}`,
          processed: 0,
          duration: 0,
          details: { availableKeys: Object.keys(jsonData) },
        };

      }

      // 如果没有指定文件路径，处理所有诊断数据文件
      const upload_fzDataLog_path = ctx.app.config.upload_fzDataLog_path;
      if (!fs.existsSync(upload_fzDataLog_path)) {
        throw new Error(`数据目录不存在: ${upload_fzDataLog_path}`);
      }

      // 获取所有诊断数据文件
      const files = this._getFilesByType(upload_fzDataLog_path, 'FromoccdiscaseDownLoad');

      if (files.length === 0) {
        return {
          success: true,
          message: '没有找到诊断数据文件',
          processed: 0,
          duration: 0,
        };
      }

      // 如果指定了索引，只处理该文件
      if (typeof index === 'number' && index >= 0 && index < files.length) {
        const targetFile = files[index];
        const jsonData = await this.getOldDataFromZip(targetFile);
        if (jsonData && jsonData.occdiscasetList) {
          const result = await this.createDiagnosis(jsonData.occdiscasetList);
          return {
            success: true,
            message: `诊断数据处理完成，处理了 ${jsonData.occdiscasetList.length} 条记录`,
            processed: jsonData.occdiscasetList.length,
            duration: 0,
            details: result,
          };
        }
        throw new Error('文件中没有找到诊断数据');

      }

      // 处理所有文件
      let totalProcessed = 0;
      const startTime = Date.now();

      for (const file of files) {
        try {
          const jsonData = await this.getOldDataFromZip(file);
          if (jsonData && jsonData.occdiscasetList) {
            await this.createDiagnosis(jsonData.occdiscasetList);
            totalProcessed += jsonData.occdiscasetList.length;
          }
        } catch (error) {
          ctx.logger.error(`处理诊断数据文件失败: ${file}`, error);
        }
      }

      const duration = Date.now() - startTime;

      return {
        success: true,
        message: `诊断数据批量处理完成，共处理 ${files.length} 个文件，${totalProcessed} 条记录`,
        processed: totalProcessed,
        duration,
      };

    } catch (error) {
      ctx.logger.error('处理诊断数据失败', error);
      return {
        success: false,
        message: `处理诊断数据失败: ${error.message}`,
        processed: 0,
        duration: 0,
        error: error.message,
      };
    }
  }

  /**
   * 创建诊断数据
   * @param {Object} data 诊断数据
   *
   * @param params
   */
  async createDiagnosis(params) {
    const { ctx } = this;
    try {
      // const { occdiscasetList } = await this.getOldDataFromZip(
      //   'baseOccdiscasetData'
      // );
      // console.log('诊断清洗', occdiscasetList);
      const successLog = [];
      const errorLog = [];
      for (const item of params) {
        try {
          const districts = this.getAddress(item.zoneCode);
          const dist = {
            districts,
            address: item.address,
            point: [],
          };
          // #region 1. 根据企业组织机构代码获取企业信息
          let inOurCompany =
            await ctx.service.importToDb.getOneCompanyBySearchKey({
              searchKey: item.institutionCode || item.crptName,
            });
          if (!inOurCompany) {
            // 创建企业
            console.log('创建企业', item.institutionCode);
            const adminUser = {
              group: ctx.app.config.groupID.adminGroupID,
              password: this.getSixStr(item.institutionCode) || '',
              enable: true,
            };
            const adminUserCreated = await ctx.model.AdminUser.create(
              adminUser
            );
            let toCreateCompanyInfo = {
              cname: item.crptName || '',
              code: item.institutionCode || '',
              adminUserId: adminUserCreated._id,
              workAddress: [ dist ] || [],
              districtRegAdd: districts || [],
              leadIn: 4,
              isactive: 1,
            };
            toCreateCompanyInfo = this.filterEmptyFields(toCreateCompanyInfo);
            inOurCompany = await ctx.service.importToDb.createAdminorg(
              toCreateCompanyInfo
            );
          }
          // #endregion
          // #region 2. 查询是否存在员工  假设身份证号必有 文档是 人员类型为1 必填
          let inOurEmployee =
            await ctx.service.importToDb.getOneEmployeeBySearchKey({
              searchKey: item.idc,
            });
          if (!inOurEmployee) {
            // 创建员工 需要user 和 employee
            console.log('创建员工', item.idc);
            let employeeInfo = {
              name: item.personnelName || '', // 姓名
              phoneNum: item.linktel || '', // 联系电话
              age: item.age, // 年龄
              IDNum: item.idc, // 身份证号
              gender: item.sex === '1' ? '0' : '1', // 性别
              EnterpriseID: inOurCompany._id, // 企业id
              workType: item.workType, // 工种
              // departs: [ item.deptName ], // 部门 太难了 还要建部门 不写了
              // status: this.getOnguardState(+item.onguardState) === '2' ? 0 : 1, // 在职状态[编码需要转义]
            };
            let userInfo = {
              name: item.personName || '', // 姓名
              idNo: item.idc || '', // 身份证号
              phoneNum: item.lnkTel || '', // 联系电话
              companyId: [ inOurCompany._id ], // 所在企业
              companyStatus: 2, // 企业审核通过
            };
            // 过滤一下employeeInfo 删除里面为空的字段
            employeeInfo = this.filterEmptyFields(employeeInfo);
            userInfo = this.filterEmptyFields(userInfo);
            // 创建员工 employee 和user
            const createEmployeeAndUserRes =
              await ctx.service.importToDb.createEmployeeAndUserWorkFlows({
                employee: employeeInfo,
                user: userInfo,
              });
            inOurEmployee = createEmployeeAndUserRes.employee;
          }
          // #endregion
          // #region 3. 查询是否存在体检机构信息
          let inOurPhysicalExamOrg =
            await ctx.service.importToDb.getOnePhysicalExamOrgBySearchKey({
              searchKey: item.creditCode,
            });
          if (!inOurPhysicalExamOrg) {
            // 创建体检机构
            console.log('创建体检机构', item.creditCode);
            inOurPhysicalExamOrg = {}; // 防止null 报错
            // inOurPhysicalExamOrg._id = '3tPAj8PdjpD';
            const physicalExamUser = {
              password: item.creditCode.slice(-6),
            };
            const reg = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;
            let physicalExamOrg = {
              name: item.unitname || '',
              shortName: item.unitname || '',
              organization: reg.test(item.creditCode) ? item.creditCode : '',
              unitCode: reg.test(item.creditCode) ? '' : item.creditCode,
            };
            physicalExamOrg = this.filterEmptyFields(physicalExamOrg);
            // 没有的话创建
            const params = {
              physicalExamUser,
              physicalExamOrg,
            };
            try {
              const toCreatedRes =
                await ctx.service.importToDb.createPhysicalExamOrgFlow(params);
              inOurPhysicalExamOrg._id = toCreatedRes.physicalExamOrg;
            } catch (error) {
              ctx.auditLog('创建体检机构失败', `${error}`, 'error');
            }
          }
          // #endregion
          let odiseaseInfo = {
            name: item.personnelName || '', // 姓名
            IDNum: item.idc || '', // 身份证号
            sex: item.sex === '1' ? '0' : '1', // 性别
            phoneNum: item.linktel || '', // 联系电话
            decideDate: new Date(item.tlDate) || '', // 诊断日期
            cname: inOurCompany.cname || '', // 所在单位
            EnterpriseID: inOurCompany._id, // 企业id
            enterpriseContactsPhonNumber: inOurCompany.phoneNum || '', // 企业联系电话
            enterpriseContactsName: inOurCompany.contract || '', // 企业联系人
            year: new Date(item.tlDate).getFullYear(), // 年份
            employeeId: inOurEmployee._id || '', // 员工id
            diseaseName: this.getOccupationalDisease(item.occdiseCode) || '', // 职业病名称
            hospital: inOurPhysicalExamOrg.name || item.unitname || '', // 诊断机构
            physicalExaminationOrgID: inOurPhysicalExamOrg._id || '', // 体检机构id
            workType: item.workType || '', // 工种
            isOccupatDisease: item.isDis || '', // 是否是职业病
            workAddress:
              inOurCompany.workAddress && inOurCompany.workAddress.length > 0
                ? inOurCompany.workAddress
                : [ dist ], // 工作场所
            applyTime: item.isDis === '1' ? new Date(item.tlDate) : '',
            applyStatus: item.isDis === '1' ? 1 : 0,
          };
          odiseaseInfo = this.filterEmptyFields(odiseaseInfo);
          // 判断更新
          const existingDiagnosis =
            await ctx.model.OccupationalDisease.findOne({
              IDNum: odiseaseInfo.IDNum,
              decideDate: odiseaseInfo.decideDate,
            });
          if (existingDiagnosis) {
            // 如果存在，更新诊断信息
            await ctx.model.OccupationalDisease.updateOne(
              { _id: existingDiagnosis._id },
              { $set: odiseaseInfo }
            );
            successLog.push({
              uuid: existingDiagnosis.rid,
              type: 1,
              errorMsg: '更新诊断数据成功',
            });

          } else {
            // 如果不存在，创建新的诊断信息
            await ctx.service.importToDb.createDiagnosis(odiseaseInfo);
            successLog.push({
              uuid: item.rid,
              type: 0,
              errorMsg: '',
            });
          }
        } catch (error) {
          errorLog.push({
            uuid: item.rid,
            type: 1,
            errorMsg: '创建诊断数据时出错，可能必传参数丢失',
          });
          ctx.auditLog('创建诊断数据出错啦', `${item}`, 'error');
        }
      }
      ctx.auditLog(
        '创建诊断数据成功整体数据',
        `${JSON.stringify(successLog)}`,
        'info'
      );
      if (errorLog.length > 0) {
        ctx.auditLog(
          '创建诊断数据失败整体数据',
          `${JSON.stringify(errorLog)}`,
          'error'
        );
      }
      const merageLog = successLog.concat(errorLog);
      await this.writeLogToZipFolder(merageLog, 'Diagnosis');
      return merageLog;
    } catch (error) {
      ctx.auditLog('创建诊断数据', `${error}`, 'error');
    }
  }

  /**
   * 获取文件详细信息
   * @return {Promise<Object>} 分类文件详细信息
   */
  async getFileDetails() {
    const { ctx } = this;
    try {
      const upload_fzDataLog_path = ctx.app.config.upload_fzDataLog_path;
      const fs = require('fs');

      if (!fs.existsSync(upload_fzDataLog_path)) {
        return {
          summary: {
            totalFiles: 0,
            companyFiles: 0,
            healthCheckFiles: 0,
            diagnosisFiles: 0,
          },
          files: {
            company: [],
            healthCheck: [],
            diagnosis: [],
          },
        };
      }

      // 获取各类型文件
      const companyFiles = this._getFilesByType(upload_fzDataLog_path, 'FromcrptDownLoad');
      const healthCheckFiles = this._getFilesByType(upload_fzDataLog_path, 'FrombhkDataDownLoad');
      const diagnosisFiles = this._getFilesByType(upload_fzDataLog_path, 'FromoccdiscaseDownLoad');

      // 处理文件信息，添加索引
      const processFiles = files => {
        return files.map((filePath, index) => {
          const fileName = require('path').basename(filePath);
          return {
            fileName,
            filePath,
            index,
          };
        });
      };

      const result = {
        summary: {
          totalFiles: companyFiles.length + healthCheckFiles.length + diagnosisFiles.length,
          companyFiles: companyFiles.length,
          healthCheckFiles: healthCheckFiles.length,
          diagnosisFiles: diagnosisFiles.length,
        },
        files: {
          company: processFiles(companyFiles),
          healthCheck: processFiles(healthCheckFiles),
          diagnosis: processFiles(diagnosisFiles),
        },
      };

      return result;

    } catch (error) {
      ctx.logger.error('获取文件详细信息失败', error);
      throw new Error(`获取文件详细信息失败: ${error.message}`);
    }
  }

  /**
   * 测试数据清洗
   * @param {String} testType - 测试类型（company/healthCheck/diagnosis）
   * @param {Object} options - 测试选项
   * @param {String} options.filePath - 文件路径（可选）
   * @param {Number} options.index - 文件索引（可选）
   * @return {Promise<Object>} 测试结果
   */
  async testDataCleaning(testType, options = {}) {
    const { ctx } = this;
    try {
      const { filePath, index } = options;

      // 根据测试类型调用相应的处理方法
      let result;
      switch (testType) {
        case 'company':
          result = await this.processCompanyData({ filePath, index });
          break;
        case 'healthCheck':
          result = await this.processHealthCheckData({ filePath, index });
          break;
        case 'diagnosis':
          result = await this.processDiagnosisData({ filePath, index });
          break;
        default:
          throw new Error(`不支持的测试类型: ${testType}`);
      }

      return {
        success: true,
        message: `${testType} 数据清洗测试完成`,
        testType,
        ...result,
      };

    } catch (error) {
      ctx.logger.error(`数据清洗测试失败: ${testType}`, error);
      return {
        success: false,
        message: `数据清洗测试失败: ${error.message}`,
        testType,
        error: error.message,
      };
    }
  }

  /**
   * 根据文件类型获取文件列表
   * @param {String} dirPath - 目录路径
   * @param {String} fileType - 文件类型标识
   * @return {Array} 文件路径列表
   */
  _getFilesByType(dirPath, fileType) {
    const fs = require('fs');
    const path = require('path');
    const files = [];

    const scanDir = dir => {
      try {
        const items = fs.readdirSync(dir);
        for (const item of items) {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);

          if (stat.isDirectory()) {
            scanDir(fullPath); // 递归扫描子目录
          } else if (item.includes(fileType) && item.endsWith('.zip')) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        this.ctx.logger.error(`扫描目录失败: ${dir}`, error);
      }
    };

    scanDir(dirPath);
    return files;
  }

  // #region // 数据格式转换
  /** 转换体检类型  3.7
   * @param {String} code - 体检类型编码 1001
   * @return {String} - 返回体检类型 0上岗前 1在岗 2离岗 3复查 4应急
   */
  getOnguardState(code) {
    const codeObj = {
      1001: '0', // 上岗前
      1002: '1', // 在岗
      1003: '2', // 离岗
      1004: '3', // 复查
      1005: '4', // 应急
    };
    return codeObj[code] || '';
  }
  /** 处理男女格式
   * @param {String} value - 男女值 男 女
   * @return {String} - 返回男0女1
   */
  getGender(value) {
    const codeObj = {
      男: '0',
      女: '1',
    };
    return codeObj[value];
  }

  /**  处理危害因素 3.8
   * @param {String} value - 危害因素值 190902
   * @return {String} - 返回危害因素  有机锡
   */
  getHarmFactors(value) {
    // value转换为数字
    value = +value;
    const codeObj = {
      11: '粉尘',
      110001: '矽尘',
      110002: '煤尘（游离 SiO2 含量＜10%）',
      110003: '石棉（石棉含量＞10%）粉尘纤维',
      110005: '棉尘',
      110007: '电焊烟尘',
      110008: '硬质合金粉尘',
      110010: '沸石粉尘',
      110011: '滑石粉尘（游离 SiO2 含量＜10%）',
      110012: '石墨粉尘',
      110013: '炭黑粉尘',
      110014: '水泥粉尘（游离 SiO2 含量<10 %）',
      110015: '云母粉尘',
      110016: '陶土粉尘',
      110017: '铝尘（铝金属、铝合金粉尘氧化铝粉尘）',
      110018: '铸造粉尘',
      110100: '白云石粉尘',
      110101: '玻璃钢粉尘',
      110102: '茶尘',
      110103: '沉淀 SiO2（白炭黑）',
      110104: '大理石粉尘 （碳酸钙）',
      110105: '二氧化钛粉尘',
      110106: '酚醛树酯粉尘',
      110107: '谷物粉尘（游离 SiO2 含量＜10%）',
      110108: '硅灰石粉尘',
      110109: '硅藻土粉尘（游离 SiO2 含量＜10%）',
      110110: '活性炭粉尘',
      110111: '聚丙烯粉尘',
      110112: '聚丙烯腈纤维粉尘',
      110113: '聚氯乙烯粉尘',
      110114: '聚乙烯粉尘',
      110115: '麻尘（亚麻、黄麻、芒麻）',
      110116: '木粉尘（硬）',
      110117: '凝聚 SiO2 粉尘',
      110118: '膨润土粉尘',
      110119: '皮毛粉尘',
      110120: '人造矿物纤维绝热棉粉尘（玻璃棉、矿渣棉、岩棉）',
      110121: '桑蚕丝尘',
      110122: '砂轮磨尘',
      110123: '石膏粉尘（硫酸钙）',
      110124: '石灰石粉尘',
      110125: '碳化硅粉尘',
      110126: '碳纤维粉尘',
      110127: '稀土粉尘（游离 SiO2 含量<10 %）',
      110128: '洗衣粉混合尘',
      110129: '烟草尘',
      110130: '萤石混合性粉尘',
      110131: '珍珠岩粉尘',
      110132: '蛭石粉尘',
      110133: '重晶石粉尘（硫酸钡）',
      110134: '工业酶混合尘',
      110135: '过氯酸铵粉尘',
      110136: '锑及其化合物粉尘',
      110137: '铁及其化合物粉尘',
      110140: '锡及其化合物粉尘',
      110999: '其他粉尘',
      12: '化学有害因素',
      120001: '四乙基铅（按 Pb 计）',
      120002: '汞-有机汞化合物（按 Hg 计）',
      120003: '锰及其无机化合物（按 MnO2 计）',
      120004: '铍及其化合物（按 Be 计）',
      120005: '镉及其化合物（按 Cd 计）',
      120006: '铬及其化合物',
      120007: '氧化锌',
      120008: '砷',
      120009: '砷化氢（胂）',
      120010: '黄磷',
      120011: '磷化氢',
      120012: '钡及其可溶性化合物（按 Ba 计）',
      120013: '钒及其化合物（按 V 计）',
      120014: '有机锡',
      120015: '铊及其可溶性化合物（按 Tl 计）',
      120016: '羰基镍（按 Ni 计）',
      120017: '氟及其化合物（不含氟化氢）（按 F 计）',
      120018: '苯',
      120019: '二硫化碳',
      120020: '四氯化碳',
      120021: '甲醇',
      120022: '汽油、溶剂汽油',
      120023: '溴甲烷',
      120024: '1,2-二氯乙烷',
      120025: '正己烷',
      120026: '苯的氨基与硝基化合物（不含三硝基甲苯）',
      120027: '三硝基甲苯',
      120028: '联苯胺（4,4’-二氨基联苯）',
      120029: '氯，氯气',
      120030: '二氧化硫',
      120031: '氮氧化物（一氧化氮和二氧化氮）',
      120032: '氨',
      120033: '光气（碳酰氯）',
      120034: '甲醛',
      120035: '一甲胺',
      120036: '一氧化碳',
      120037: '硫化氢',
      120038: '氯乙烯',
      120039: '三氯乙烯',
      120040: '氯丙烯',
      120041: 'β-氯丁二烯（氯丁二烯）',
      120042: '有机氟聚合物单体及其热裂解物',
      120043: '二异氰酸甲苯酯',
      120044: '二甲基甲酰胺',
      120045: '氰及其腈类化合物',
      120046: '酚',
      120047: '五氯酚及其钠盐',
      120048: '氯甲醚',
      120049: '丙烯酰胺',
      120050: '偏二甲基肼',
      120051: '硫酸二甲酯',
      120052: '有机磷',
      120053: '氨基甲酸酯类',
      120054: '拟除虫菊酯',
      120057: '焦炉逸散物（按苯溶物计）',
      120058: '铅及其无机化合物（按 Pb 计，不包括四乙基铅）',
      120059: '砷及其无机化合物（按 As 计）',
      120060: '三氧化铬、铬酸盐、重铬酸盐（按 Cr 计）',
      120061: '煤焦油',
      120062: 'β萘胺',
      120200: '安妥（α-萘硫脲）',
      120201: '2-氨基吡啶',
      120202: '氨基磺酸铵',
      120203: '奥克托今（环四次甲基四硝胺）',
      120204: '巴豆醛（丁烯醛）',
      120205: '百草枯（1,1-二甲基-4,4-联吡啶鎓盐二氯化物）',
      120206: '百菌清',
      120207: '倍硫磷',
      120208: '苯基醚（二苯醚）',
      120209: '苯醌',
      120210: '苯硫磷',
      120211: '苯乙烯',
      120212: '吡啶',
      120213: '苄基氯',
      120214: '丙酸',
      120215: '丙烯醇',
      120217: '丙烯菊酯',
      120218: '丙烯醛',
      120219: '丙烯酸',
      120220: '丙烯酸甲酯',
      120221: '丙烯酸正丁酯',
      120222: '草甘膦',
      120223: '草酸',
      120224: '重氮甲烷',
      120225: '抽余油（60 ℃~220 ℃）',
      120226: '臭氧',
      120227: '二氯二苯基三氯乙烷（滴滴涕，DDT）',
      120228: 'O,O-二甲基-（2,2,2-三氯-1 羟基乙基）磷酸酯（敌百虫）',
      120229: 'N-3,4-二氯苯基-N`,N`-二甲基脲（敌草隆）',
      120230: 'o,o-二甲基-S-（甲基氨基甲酰甲基）二硫代磷酸酯（乐果）',
      120231: '2,4-二氯苯氧基乙酸（2,4-滴）',
      120232: '碲及其化合物（不含碲化氢）（按 Te 计）',
      120233: '碲化铋（按 Bi2Te3 计）',
      120234: '碘',
      120235: '碘仿',
      120236: '叠氮酸蒸气',
      120237: '叠氮化钠',
      120238: '丁醇',
      120239: '1,3-丁二烯',
      120240: '2-丁氧基乙醇',
      120241: '丁醛',
      120242: '丁酮',
      120243: '丁烯',
      120244: '毒死蜱',
      120245: '对苯二胺',
      120246: '对苯二甲酸',
      120247: '对二氯苯（二氯苯）',
      120248: '对硫磷',
      120249: '对特丁基甲苯',
      120250: '对硝基苯胺',
      120251: '对硝基氯苯',
      120252: '多次甲基多苯基多异氰酸酯',
      120253: '二苯胺',
      120254: '二苯基甲烷二异氰酸酯',
      120255: '二丙二醇甲醚（2-甲氧基甲乙氧基丙醇）',
      120256: '二丙酮醇',
      120257: '2-N-二丁氨基乙醇',
      120258: '二噁烷',
      120259: '二噁英类化合物',
      120260: '二氟氯甲烷',
      120261: '二甲胺',
      120262: '二甲苯（全部异构体）',
      120263: 'N,N-二甲基苯胺',
      120264: '1,3-二甲基丁基乙酸酯（仲乙酸己酯、乙酸仲己酯）',
      120265: '二甲基二氯硅烷',
      120266: '3,3-二甲基联苯胺',
      120267: '二甲基亚砜',
      120268: '二甲基乙酰胺',
      120269: '二甲氧基甲烷',
      120270: '二聚环戊二烯',
      120271: '1,1-二氯-1-硝基乙烷',
      120272: '1,3-二氯丙醇',
      120273: '1,2-二氯丙烷',
      120274: '1,3-二氯丙烯',
      120275: '二氯二氟甲烷',
      120276: '二氯甲烷',
      120277: '二氯乙炔',
      120278: '1,2-二氯乙烯（全部异构体）',
      120279: '二硼烷（乙硼烷）',
      120280: '二缩水甘油醚',
      120281: '二硝基苯（全部异构体）',
      120282: '二硝基甲苯',
      120283: '4,6-二硝基邻甲酚',
      120284: '2,4-二硝基氯苯',
      120285: '二氧化氯',
      120286: '二氧化碳',
      120287: '二氧化锡（按 Sn 计）',
      120288: '2-二乙氨基乙醇',
      120289: '二乙烯三胺（二乙撑三胺）',
      120290: '二乙基甲酮',
      120291: '二乙烯基苯',
      120292: '二异丁基甲酮',
      120293: '甲苯 -2,4- 二异氰酸酯（TDI）',
      120294: '二月桂酸二丁基锡',
      120295: '呋喃',
      120296: '氟化氢（按 F 计）',
      120297: '锆及其化合物（按 Zr 计）',
      120298: '汞-金属汞（蒸气）',
      120299: '钴及其化合物（按 Co 计）',
      120300: '癸硼烷',
      120301: '过氧化苯甲酰',
      120302: '过氧化甲乙酮',
      120303: '过氧化氢',
      120304: '环己胺',
      120305: '环己醇',
      120306: '环己酮',
      120307: '环己烷',
      120308: '环三次甲基三硝胺（黑索今）',
      120309: '环氧丙烷',
      120310: '环氧氯丙烷',
      120311: '邻-茴香胺，对-茴香胺',
      120312: '己二醇',
      120313: '1,6-己二异氰酸酯（六亚甲基二异氰酸酯（HDI））',
      120314: '己内酰胺',
      120315: '2-己酮（甲基正丁基甲酮）',
      120316: '甲拌磷',
      120317: '甲苯',
      120318: 'N-甲苯胺，O-甲苯胺',
      120319: '甲酚（全部异构体）',
      120320: '甲基丙烯腈',
      120321: '甲基丙烯酸',
      120322: '甲基丙烯酸甲酯（异丁烯酸甲酯）',
      120323: '甲基丙烯酸缩水甘油酯',
      120324: '甲基肼',
      120325: '甲基内吸磷',
      120326: '18-甲基炔诺酮（炔诺孕酮）',
      120327: '甲基叔丁基醚',
      120328: '甲硫醇',
      120329: '甲酸',
      120330: '甲乙酮（2-丁酮）',
      120331: '2-甲氧基乙醇（甲氧基乙醇）',
      120332: '2-甲氧基乙基乙酸酯',
      120333: '甲氧氯',
      120334: '间苯二酚',
      120335: '肼',
      120336: '久效磷',
      120337: '糠醇',
      120338: '糠醛',
      120339: '考的松',
      120340: '苦味酸（2,4,6-三硝基苯酚）',
      120341: '联苯',
      120342: '邻苯二甲酸二丁酯',
      120343: '邻苯二甲酸酐（PA）',
      120344: '邻二氯苯',
      120345: '邻氯苯乙烯',
      120346: '邻氯苄叉丙二腈',
      120347: '邻仲丁基苯酚',
      120348: '磷胺',
      120349: '磷酸',
      120350: '磷酸二丁基苯酯',
      120351: '硫酸钡（按 Ba 计）',
      120352: '硫酸及三氧化硫',
      120353: '硫酰氟',
      120354: '六氟丙酮',
      120355: '六氟丙烯',
      120356: '六氟化硫',
      120357: '六六六（六氯环已烷）',
      120358: 'γ-六六六（γ-六氯环己烷）',
      120359: '六氯丁二烯',
      120360: '六氯环戊二烯',
      120361: '六氯萘',
      120362: '六氯乙烷',
      120363: '氯苯',
      120364: '氯丙酮',
      120365: '氯化铵烟',
      120366: '氯化汞（升汞）',
      120367: '氯化苦（三氯硝基甲烷）',
      120368: '氯化氢及盐酸',
      120369: '氯化锌烟',
      120370: '氯甲烷',
      120371: '氯联苯（54 %氯）',
      120372: '氯萘',
      120373: '氯乙醇',
      120374: '氯乙醛',
      120375: 'α-氯乙酰苯',
      120376: '氯乙酰氯',
      120377: '马拉硫磷',
      120378: '马来酸酐',
      120379: '吗啉',
      120380: '煤焦油沥青挥发物（按苯溶物计）',
      120381: '钼及其化合物（按 Mo 计）',
      120382: '内吸磷',
      120383: '萘',
      120384: '萘酚',
      120385: '萘烷',
      120386: '尿素',
      120387: '镍及其无机化合物(按 Ni 计) ',
      120388: '氢化锂',
      120389: '氢醌（对苯二酚）',
      120390: '氢氧化钾',
      120391: '氢氧化钠',
      120392: '氢氧化铯',
      120393: '氰氨化钙',
      120394: '氰戊菊酯',
      120395: '全氟异丁烯',
      120396: '壬烷',
      120397: '乳酸正丁酯',
      120398: '三氟化氯',
      120399: '三氟化硼',
      120400: '三氟甲基次氟化物',
      120401: '三甲苯磷酸酯（全部异构体）',
      120402: '三甲基氯化锡',
      120403: '1,2,3-三氯丙烷',
      120404: '三氯化磷',
      120405: '三氯甲烷（氯仿）',
      120406: '三氯硫磷',
      120407: '三氯氢硅',
      120408: '三氯氧磷',
      120409: '三氯乙醛',
      120410: '1,1,1-三氯乙烷',
      120411: '三溴甲烷',
      120412: '三乙基氯化锡',
      120413: '杀螟松',
      120414: '杀鼠灵（3-（1-丙酮基苄基）-4-羟基香豆素；华法林）',
      120415: '石蜡烟',
      120416: '十溴联苯醚',
      120417: '石油沥青烟(按苯溶物计)',
      120418: '双（巯基乙酸）二辛基锡',
      120419: '双酚 A ',
      120420: '双硫醒',
      120421: '双氯甲醚',
      120422: '四氯乙烯',
      120423: '四氢呋喃',
      120424: '四氢化硅',
      120425: '四氢化锗',
      120426: '四溴化碳',
      120427: '松节油',
      120428: '钽及其氧化物（按 Ta 计）',
      120429: '碳酸钠（纯碱）',
      120430: '羰基氟',
      120431: '锑及其化合物（按 Sb 计）',
      120432: '铜及其化合物（按 Cu 计）',
      120433: '钨及其不溶性化合物（按 W 计）',
      120434: '五氟（一）氯乙烷',
      120435: '五硫化二磷',
      120436: '五羰基铁（按 Fe 计）',
      120437: '五氧化二磷',
      120438: '戊醇',
      120439: '戊烷（全部异构体）',
      120440: '硒化氢（按 Se 计）',
      120441: '硒及其化合物（按 Se 计）（不包括六氟化硒、硒化氢）',
      120442: '纤维素',
      120443: '硝化甘油',
      120444: '硝基苯',
      120445: '1-硝基丙烷（硝基丙烷）',
      120446: '2-硝基丙烷',
      120447: '硝基甲苯（全部异构体）',
      120448: '硝基甲烷',
      120449: '硝基乙烷',
      120450: '辛烷',
      120451: '溴',
      120452: '溴化氢（氢溴酸）',
      120453: '溴丙烷（1-溴丙烷；2-溴丙烷）',
      120454: '溴氰菊酯',
      120455: '溴鼠灵',
      120456: '氧化钙',
      120457: '氧化镁烟',
      120458: '氧乐果',
      120459: '液化石油气',
      120460: '乙胺',
      120461: '乙苯',
      120462: '乙醇胺（氨基乙醇）',
      120463: '乙二胺（乙烯二胺，EDA）',
      120464: '乙二醇',
      120465: '乙二醇二硝酸酯',
      120466: '乙酐（乙酸酐）',
      120467: 'N-乙基吗啉',
      120468: '乙基戊基甲酮',
      120469: '乙腈',
      120470: '乙硫醇',
      120471: '乙醚',
      120472: '乙醛',
      120473: '乙酸',
      120474: '乙酸丙酯',
      120475: '乙酸丁酯',
      120476: '乙酸甲酯',
      120477: '乙酸戊酯（全部异构体）',
      120478: '乙酸乙烯酯',
      120479: '乙酸乙酯',
      120480: '乙烯酮',
      120481: '乙酰甲胺磷',
      120482: '乙酰水杨酸（阿司匹林）',
      120483: '2-乙氧基乙醇',
      120484: '2-乙氧基乙基乙酸酯',
      120485: '钇及其化合物（按 Y 计）',
      120486: '异丙胺',
      120487: '异丙醇',
      120488: 'N-异丙基苯胺',
      120489: '异稻瘟净',
      120490: '3，5，5-三甲基-2-环己烯-1-酮（异佛尔酮）',
      120491: '异佛尔酮二异氰酸酯',
      120492: '异氰酸甲酯',
      120493: '异亚丙基丙酮',
      120494: '铟及其化合物（按 In 计）',
      120495: '茚',
      120496: '莠去津',
      120497: '正丙醇',
      120498: '正丁胺',
      120499: '正丁醇',
      120500: '正丁基硫醇',
      120501: '正丁基缩水甘油醚',
      120502: '正丁醛',
      120503: '正庚烷',
      120505: '萘二异氰酸酯（NDI）',
      120506: 'N,N-二甲基-3-氨基苯酚',
      120507: '1,1-二氯乙烯',
      120508: '甲烷',
      120509: '正香草酸（高香草酸）',
      120510: '酚醛树脂',
      120511: '二溴氯丙烷',
      120512: '多氯联苯',
      120513: '1,3-二氯丙烷',
      120514: '二硫化硒',
      120515: '三氯乙酸',
      120516: '氯酸钾',
      120517: '-3,4 二氯苯基丙酰胺（敌稗）',
      120518: '丙酮醛（甲基乙二醛）',
      120519: '双丙酮醇',
      120520: '钽及其化合物',
      120521: '吖啶',
      120522: '环戊酮',
      120523: '铀及其化合物',
      120524: '钼酸',
      120525: '卤化水杨酰苯胺（Ν-水杨酰苯胺）',
      120526: '邻苯二甲酸二甲酯',
      120527: '氯化苄烷胺（洁尔灭）',
      120528: '二硝基苯酚',
      120529: '三氧化钼',
      120530: '多氯萘',
      120531: '氯酸钠',
      120532: '钾盐镁矾',
      120533: '多溴联苯',
      120534: '柴油',
      120535: '木馏油（焦油）',
      120536: '锂及其化合物',
      120537: '亚硝酸乙酯',
      120538: '甲酸乙酯',
      120539: '环氧树脂',
      120540: '乙炔',
      120541: '五氟氯乙烷',
      120542: '三氯一氟甲烷',
      120543: '对氨基酚',
      120544: '二乙烯二胺（哌嗪）',
      120545: '乙酸苄酯',
      120546: '多氯苯',
      120547: '亚硫酸钠',
      120548: '四氯乙烷',
      120549: '氢氧化铵',
      120550: '铂化物',
      120551: '巯基乙酸',
      120552: '聚氯乙烯热解物',
      120553: '1,2,4-苯三酸酐（TMA）',
      120554: '围涎树碱',
      120555: '对溴苯胺',
      120556: '钼酸铵',
      120557: '氟乙酰胺',
      120558: '二苯胍',
      120559: '烯丙胺',
      120560: '铜及其化合物',
      120561: '丙醇',
      120562: '铝酸钠',
      120563: '乙基另戊基甲酮（5-甲基-3-庚酮）',
      120564: '丙烯基芥子油',
      120565: '氯磺酸',
      120566: '苯乙醇',
      120567: '二甲苯酚',
      120568: '氯乙烷',
      120569: '二异丙胺基氯乙烷',
      120570: '二氯乙醚',
      120571: '脲醛树脂',
      120572: '蒽醌及其染料',
      120573: '氯乙基胺',
      120574: '氯甲酸三氯甲酯（双光气）',
      120575: '三聚氰胺甲醛树脂',
      120576: '2-氯苯基羟胺',
      120577: '氟乙酸钠',
      120578: '过硫酸盐（过硫酸钾、过硫酸钠、过硫酸铵等）',
      120579: '磷酸三邻甲苯酯',
      120580: '二氯酚',
      120581: '钼酸钠',
      120582: '碳酸铵',
      120583: '氧化银',
      120584: '乙基硫代磺酸乙酯',
      120585: '二氯化砜（磺酰氯）',
      120586: '多次甲基多苯基异氰酸酯',
      120587: '硝基萘',
      120588: '三氟甲基次氟酸酯',
      120589: '蒽',
      120590: '苯肼',
      120591: '四氯化硅',
      120592: '碳酸钙',
      120593: '1,2,3-苯三酚（焦棓酚）',
      120594: '氯甲酸甲酯',
      120595: '三乙烯四胺（三乙撑四胺）',
      120596: '乙酸异丙酯',
      120597: '羟基香茅醛',
      120598: '硝基萘胺',
      120599: '邻茴香胺',
      120600: '4-氯苯基羟胺',
      120601: '杀虫脒',
      120602: '1,6-己二胺',
      120603: '苯基羟胺（苯胲）',
      120604: '苄基溴（溴甲苯）',
      120605: '1,3-二氯-2-丙醇',
      120606: '溴乙烷',
      120607: '甲基氨基酚',
      120608: '四氯化钛',
      120609: '苯绕蒽酮',
      120610: '甲酸丁酯',
      120611: '羟基乙酸',
      120612: '三甲基己二酸',
      120613: '硼烷',
      120614: '三氯化硼',
      120615: '甲酸甲酯',
      120616: '对苯二甲酸二甲酯',
      120617: '丙烷',
      120618: '4,6-二硝基邻苯甲酚',
      120619: '多氯酚',
      120620: '双-(二甲基硫代氨基甲酰基)二硫化物（秋兰姆、福美双）',
      120621: '3-氯苯基羟胺',
      120622: '异丙醇胺（1-氨基-2-二丙醇）',
      120623: '2-溴乙氧基苯',
      120624: '溴苯',
      120625: '磷化锌',
      120626: '磷化铝',
      120627: '二苯亚甲基二异氰酸酯（MDI）',
      120628: '四氯苯二酸酐（TCPA）',
      120630: '氯乙酸',
      120631: '环氧乙烷',
      120632: '碘甲烷',
      120633: '丙酮',
      120634: '苯胺',
      120900: 'N-3,4 二氯苯基丙酰胺（敌稗）',
      120901: '氢氟酸',
      120902: '硝酸',
      120903: '烷酸',
      120904: '氰化氢',
      120999: '其他化学有害因素',
      13: '物理有害因素',
      130001: '噪声',
      130002: '振动',
      130003: '高温',
      130004: '高气压',
      130006: '微波',
      130009: '紫外辐射（紫外线）',
      130300: '超高频电磁场（超高频辐射）',
      130301: '高频电磁场（高频辐射）',
      130302: '工频电磁场（工频辐射）',
      130303: '低气压',
      130304: '高原低氧',
      130305: '红外线',
      130306: '激光',
      130307: '低温',
      130999: '其他物理有害因素',
      14: '生物因素',
      140001: '布鲁菌属',
      140002: '炭疽杆菌',
      140400: '白僵蚕孢子',
      140401: '枯草杆菌蛋白酶',
      140402: '工业酶',
      140403: '森林脑炎病毒',
      140404: '艾滋病病毒',
      140405: '伯氏疏螺旋体',
      140999: '其他生物有害因素',
      15: '其他职业病危害因素',
      150001: '电工作业',
      150002: '高处作业',
      150003: '压力容器作业',
      150004: '结核病防治工作',
      150005: '肝炎病防治工作',
      150006: '职业机动车驾驶作业',
      150007: '视屏作业',
      150008: '高原作业',
      150009: '航空作业',
      150999: '其他特殊作业',
      16: '放射物质类',
      160002: 'X射线',
      160501: 'α射线',
      160502: 'β射线',
      160503: 'γ射线',
      160504: '中子',
      160506: '铀及其化合物',
      160507: '氡及其短寿命子体',
      160999: '其他',
      19: '其他因素',
      190900: '井下不良作业条件',
      190901: '金属烟',
      190902: '刮研作业',
    };
    return codeObj[value] || '';
  }
  /** 处理是否复查
   * @param value
   * @return {string}
   */
  getIsReview(value) {
    // value转换为数字
    value = +value;
    const codeObj = {
      0: '否',
      1: '是',
    };
    return codeObj[value] || '';
  }
  /**
   * 处理体检结论 3.9
   * @param {number} value - The value to be processed.
   * @return {string} - 目前未见异常  复查 疑似职业病 禁忌证 其他疾病或异常
   */
  getConclusion(value) {
    // value转换为数字
    value = +value;
    const codeObj = {
      12001: '目前未见异常',
      12002: '复查',
      12003: '疑似职业病',
      12004: '禁忌证',
      12005: '其他疾病或异常',
    };
    return codeObj[value] || '';
  }
  /** 处理体检结论 最终
   * @param List
   */
  getFinalConclusion(List) {
    const codeObj = {
      目前未见异常: 0,
      复查: 2,
      疑似职业病: 5,
      禁忌证: 3,
      其他疾病或异常: 1,
    };
    let res = '目前未见异常';
    // 根据list 中每项当中的examConclusion字段，获取对应的code processedMhkRstList
    for (const item of List) {
      if (codeObj[item.examConclusion] > codeObj[res]) {
        res = item.examConclusion;
      }
    }
    return res;
  }
  /** 处理体检结论对应的字段
   * @param value
   * @return {string}
   */
  getConclusionField(value) {
    // actuallNum: 0, // 实检人数
    //         normal: 0, // 正常人数
    //         re_examination: 0, // 复检人数
    //         suspected: 0, // 疑似职业病人数
    //         forbid: 0, // 禁忌证人数
    //         otherDisease: 0, // 其他疾病人数
    const codeObj = {
      目前未见异常: 'normal',
      复查: 're_examination',
      疑似职业病: 'suspected',
      禁忌证: 'forbid',
      其他疾病或异常: 'otherDisease',
    };
    return codeObj[value] || 'normal';
  }
  /** 处理疑似职业病内容 3.13
   * @param value 编码
   * @return {string}
   */
  getSuspectedOccupationalDisease(value) {
    // value转换为数字
    value = +value;
    const codeObj = {
      10009: '职业性慢性铅中毒',
      10013: '职业性急性四乙基铅中毒',
      10016: '职业性急性砷化氢中毒',
      10024: '职业性慢性磷中毒',
      10025: '职业性急性磷中毒',
      10026: '职业性黄磷皮肤灼伤',
      10029: '职业性慢性汞中毒',
      10034: '职业性急性磷化氢中毒',
      10037: '职业性慢性锰中毒',
      10038: '职业性急性钡中毒',
      10040: '职业性急性钒中毒',
      10045: '职业性慢性铊中毒',
      10046: '职业性急性铊中毒',
      10047: '职业性急性羰基镍中毒',
      10050: '工业性氟病',
      10053: '职业性慢性苯中毒',
      10054: '职业性苯所致白血病',
      10055: '职业性急性苯中毒',
      10063: '职业性慢性二硫化碳中毒',
      10064: '职业性中毒性肝病',
      10065: '职业性急性四氯化碳中毒',
      10066: '职业性急性甲醇中毒',
      10069: '职业性溶剂汽油中毒（慢性）',
      10070: '汽油致职业性皮肤病',
      10072: '职业性急性溴甲烷中毒',
      10074: '职业性慢性正己烷中毒',
      10077: '职业性急性苯的氨基或硝基化合物中毒',
      10079: '职业性慢性三硝基甲苯中毒',
      10080: '职业性三硝基甲苯致白内障',
      10084: '联苯胺所致膀胱癌',
      10085: '职业性接触性皮炎',
      10086: '职业性急性氯气中毒',
      10089: '职业性急性氮氧化物中毒',
      10091: '职业性急性氨气中毒',
      10092: '职业性慢性镉中毒',
      10093: '职业性急性镉中毒',
      10094: '金属烟热',
      10095: '职业性铬鼻病',
      10096: '职业性铬溃疡',
      10097: '职业性铬所致皮炎',
      10098: '职业性铬酸盐制造业工人肺癌',
      10099: '职业性慢性砷中毒',
      10100: '职业性砷所致肺癌、皮肤癌',
      10101: '职业性慢性丙烯酰胺中毒',
      10102: '职业性急性偏二甲基肼中毒',
      10103: '职业性急性光气中毒',
      10104: '职业性急性甲醛中毒',
      10105: '职业性急性一甲胺中毒',
      10106: '职业性急性一氧化碳中毒',
      10109: '职业性急性硫化氢中毒',
      10111: '职业性慢性氯乙烯中毒',
      10112: '氯乙烯所致肝血管肉瘤',
      10113: '职业性急性硫酸二甲酯中毒',
      10114: '职业性急性氯乙烯中毒',
      10115: '职业性急性有机磷杀虫剂中毒',
      10116: '职业性急性三氯乙烯中毒',
      10117: '职业性急性氨基甲酸酯杀虫剂中毒',
      10119: '职业性急性拟除虫菊酯中毒',
      10120: '职业性慢性氯丙烯中毒',
      10121: '职业性哮喘',
      10122: '职业性慢性氯丁二烯中毒',
      10123: '职业性急性氯丁二烯中毒',
      10124: '职业性急性有机氟中毒',
      10125: '职业性急性二甲基甲酰胺中毒',
      10126: '职业性急性氰化物中毒',
      10127: '职业性急性腈类化合物中毒',
      10129: '职业性急性酚中毒',
      10130: '职业性酚皮肤灼伤',
      10132: '职业性急性五氯酚中毒',
      10133: '职业性氯甲醚所致肺癌',
      10137: '矽肺',
      10138: '煤工尘肺',
      10140: '石棉肺',
      10141: '石棉所致肺癌、间皮瘤',
      10142: '石墨尘肺',
      10143: '炭黑尘肺',
      10144: '滑石尘肺',
      10145: '水泥尘肺',
      10146: '云母尘肺',
      10147: '陶工尘肺',
      10148: '铝尘肺',
      10149: '电焊工尘肺',
      10150: '铸工尘肺',
      10151: '棉尘病',
      10159: '职业性手臂振动病',
      10164: '职业性中暑',
      10190: '减压性骨坏死',
      10196: '职业性电光性皮炎',
      10197: '职业性白内障',
      10228: '职业性慢性高原病',
      10229: '职业性航空病',
      10230: '职业性急性汞中毒',
      10232: '职业性急性1，2-二氯乙烷中毒',
      10233: '职业性化学性眼灼伤',
      10234: '职业性牙酸蚀病',
      10235: '职业性皮肤灼伤',
      10237: '职业性布氏杆菌病',
      10238: '职业性炭疽',
      10239: '职业性急性二氧化硫中毒',
      10302: '职业性慢性铍病',
      10303: '职业性铍接触性皮炎',
      10304: '职业性铍溃疡',
      10305: '职业性急性铍病',
      10306: '职业性急性三烷基锡中毒',
      10307: '职业性慢性中毒性肝病',
      10308: '职业性慢性溶剂汽油中毒',
      10309: '职业性急性溶剂汽油中毒',
      10310: '腕管综合征',
      10311: '颈肩腕综合征',
      10312: '肺结核',
      10313: '慢性肝病',
      10314: '职业性急性电光性眼炎（紫外线角膜结膜炎）',
      10315: '职业性急性电光性皮炎',
      10316: '职业性刺激性化学物致慢性阻塞性肺疾病',
      10317: '职业性化学性眼部灼伤',
      10318: '职业性化学性皮肤灼伤',
      10319: '甲醛致职业性皮肤病',
      10320: '职业性三氯乙烯药疹样皮炎',
      10321: '职业性急性化学物中毒性呼吸系统疾病',
      10322: '职业性焦炉逸散物所致肺癌',
      10323: '焦炉逸散物所致职业性皮肤病',
      10324: '职业性急性变应性肺泡炎',
      10325: '职业性噪声聋',
      10326: '职业性爆震聋',
      10327: '急性电光性眼炎(紫外线角膜、结膜炎)',
      10328: '电光性皮炎',
      10329: '职业性炭疸',
      10330: '中枢神经系统器质性疾病',
      10331: '急性高原病',
      10332: '甲醛所致职业性哮喘',
      10333: '急性减压病',
      10366: '职业性金属及其化合物粉尘肺沉着病',
      10367: '职业性硬金属肺病',
      10368: '职业性耳鼻喉口腔疾病',
      10369: '尘肺',
    };
    return codeObj[value] || '';
  }
  /** 处理禁忌证内容 3.14
   * @param value 编码
   * @return {string} 禁忌证内容
   */
  getOccupationalContraindication(value) {
    // value转换为数字
    value = +value;
    const codeObj = {
      10007: '卟啉病',
      10008: '多发性周围神经病',
      10009: '贫血',
      10012: '中枢神经系统器质性疾病',
      10017: '牙本质病变（不包括龋齿）',
      10018: '下颌骨疾病',
      10019: '慢性肝炎',
      10020: '慢性肾炎',
      10022: '慢性肾脏疾病',
      10025: '慢性阻塞性肺病',
      10026: '慢性间质性肺病',
      10028: '钾代谢障碍',
      10031: '支气管哮喘',
      10034: '地方性氟病',
      10035: '骨关节疾病',
      10037: '活动性肺结核',
      10039: '视网膜病变',
      10040: '过敏性皮肤病',
      10041: '神经系统器质性疾病',
      10042: '血清葡萄糖-6-磷酸脱氢酶缺乏症',
      10045: '尿脱落细胞检查巴氏分级国际标准Ⅳ级及以上',
      10055: '器质性心脏病',
      10056: '类风湿关节炎',
      10057: '全血胆碱酯酶活性明显低于正常者',
      10058: '致喘物过敏和支气管哮喘',
      10060: '伴肺功能损害的心血管系统疾病',
      10063: '活动性肺结核病',
      10064: '伴肺功能损害的疾病',
      10070: '雷诺病',
      10097: '加压试验不合格或氧敏感试验阳性者',
      10099: '活动性角膜疾病',
      10100: '白内障',
      10101: '面、手背和前臂等暴露部位严重的皮肤病',
      10102: '白化病',
      10103: '癫痫',
      10105: '红绿色盲',
      10107: '四肢关节运动功能障碍',
      10108: '高血压',
      10109: '恐高症',
      10111: '四肢骨关节及运动功能障碍',
      10113: '未治愈的肺结核病',
      10117: '暗适应：＞30s',
      10118: '复视、立体盲、严重视野缺损',
      10119: '梅尼埃病',
      10120: '眩晕',
      10121: '癔病',
      10122: '震颤麻痹',
      10124: '痴呆',
      10125: '影响肢体活动的神经系统疾病',
      10127: '腕管综合征',
      10128: '颈椎病',
      10129: '矫正视力小于4.5',
      10130: '红细胞增多症',
      10138: '红或绿色盲',
      10201: '中度贫血',
      10202: '已确诊并仍需要医学监护的精神障碍性疾病',
      10203: '慢性皮肤溃疡',
      10204: '慢性肾脏病',
      10205: '骨质疏松症',
      10206: '萎缩性鼻炎',
      10207: '未控制的甲状腺功能亢进症',
      10208: '严重慢性皮肤疾病',
      10209: '慢性肝病',
      10210: '慢性器质性心脏病',
      10211: '视网膜及视神经病',
      10212: '呼吸系统疾病史及有关症状',
      10213:
        '血常规检出有如下异常者：白细胞计数低于4.5×10^9/L； 血小板计数低于8×10^10/L；  红细胞计数男性低于4×10^12/L，女性低于3.5×10^12/L或血红蛋白定',
      10214: '血系统疾病',
      10215: '严重慢性皮肤疾患',
      10216: '伴有气道高反应的过敏性鼻炎',
      10217: '伴气道高反应的过敏性鼻炎',
      10218: '严重的皮肤疾病',
      10219: '牙酸蚀病',
      10220:
        '各种原因引起永久性感音神经性听力损失（500Hz、1000Hz和2000Hz中任一频率的纯音气导听阈＞25dB）',
      10221: '任一耳传导性耳聋，平均语频听力损失≥41dB',
      10222: '高频段3000Hz，4000Hz，6000Hz双耳平均听阈≥40dB',
      10223:
        '噪声敏感者（上岗前职业健康体检纯音听力检查各频率听力损失均≤25dB，但噪声作业1年之内，高频段3000Hz，4000Hz，6000Hz中任一耳，任一频率听阈≥65dB）',
      10224:
        '除噪声外各种原因引起的永久性感音神经性听力损失（500Hz，l000Hz和2000Hz中任一频率的纯音气导听阈>25dB）',
      10225: '未控制的高血压',
      10226: '未控制的糖尿病',
      10227: '全身瘢痕面积≥20%以上（工伤标准的八级）',
      10228: '各类器质性心脏病（风湿性心脏病、心肌病、冠心病、先天性心脏病等）',
      10229: '器质性心律不齐、直立性低血压、周围血管病',
      10230: '慢性支气管炎、支气管哮喘、肺结核、结核性胸膜炎、自发性气胸及病史',
      10231:
        '食道、胃、十二指肠、肝、胆、脾、胰疾病、慢性细菌性痢疾、慢性肠炎、腹部包块、消化系统、泌尿系统结石',
      10232: '泌尿、血液、内分泌及代谢系统疾病',
      10233: '结缔组织疾病，过敏体质',
      10234: '中枢神经系统及周围神经系统疾病和病史',
      10235: '癫痫、精神病、晕厥史、神经症和癔病精神活性物质滥用和依赖',
      10236:
        '各种原因引起的头颅异常影响戴面罩者，胸廓畸形，脊椎疾病，损伤及进行性病变，脊椎活动范围受限或明显异常，慢性眼腿痛，关节活动受限或疼痛',
      10237:
        '多发性肝、肾及骨囊肿，多发性脂肪瘤，瘢痕体质或全身瘢痕面积≥20%以上者',
      10238:
        '有颅脑、胸腔及腹腔手术史等外科疾病。阑尾炎术时间未超过半年，腹股沟斜疝和股疝修补术未超过1年者',
      10239: '脉管炎、动脉瘤、动静脉瘘，静脉曲张',
      10240: '脱肛，肛瘘，陈旧性肛裂，多发性痔疮及单纯性痔疮经常出血者',
      10241:
        '腋臭，头癖，泛发性体癣，疥疮，慢性湿疹，神经性皮炎，白癜风，银屑病',
      10242: '单眼裸视力不得低于4.8(0.6)，色弱，色盲，夜盲及眼科其他器质性疾患',
      10243: '外耳畸形耳、鼻、喉及前庭器官的器质性疾病，咽鼓管功能异常者',
      10244: '手足部习惯性冻疮',
      10245:
        '淋病、梅毒、软下疳、性病淋巴肉芽肿、非淋球菌性尿道炎、尖锐湿疣、生殖器疱疹、艾滋病及艾滋病毒携带者',
      10246:
        '纯音听力测试任一耳500Hz听力损失不得超过30dB，l000Hz、2000Hz听力损失不得超过25dB，4000Hz听力损失不得超过35dB',
      10247: '生殖系统疾病',
      10248: '泛发慢性湿疹',
      10249: '泛发慢性皮炎',
      10250: '晕厥（近一年内有晕厥发作史）',
      10251: '2级及以上高血压（未控制）',
      10252: '器质性心脏病或各种心律失常',
      10253: '晕厥，眩晕症',
      10254: '双耳语言频段平均听力损失>25dB',
      10255: '器质性心脏病或心律失常',
      10256: '肺结核',
      10257: '身高：大型机动车驾驶员＜155cm，小型机动车驾驶员＜150cm',
      10258: '听力：双耳平均听阈＞30dB（纯音气导）',
      10259: '深视力：<（－22mm）或>（＋22mm）',
      10260: '各类精神障碍疾病',
      10261: '吸食、注射毒品、长期服用依赖性精神药品成瘾尚未戒除者',
      10262:
        '远视力（对数视力表）：大型机动车驾驶员：两裸眼＜4.0，并＜5.0（矫正）；小型机动车驾驶员：两裸眼＜4.0，并＜4.9（矫正）',
      10263:
        '血压：大型机动车驾驶员：收缩压≥18.7kPa（≥140mmHg）和舒张压≥12kPa（≥90mmHg）；小型机动车驾驶员：2级及以上高血压（未控制）。',
      10264: '颈肩腕综合征',
      10265: '2级及以上高血压或低血压',
      10266: '活动的，潜在的，急性或慢性疾病',
      10267: '创伤性后遗症',
      10268: '影响功能的变形，缺损或损伤及影响功能的肌肉系统疾病',
      10269: '恶性肿瘤或影响生理功能的良性肿瘤',
      10270: '急性感染性，中毒性精神障碍治愈后留有后遗症',
      10271: '神经症，经常性头痛，睡眠障碍',
      10272: '药物成瘾，酒精成瘾者',
      10273: '中枢神经系统疾病，损伤',
      10274: '严重周围神经系统疾病及植物神经系统疾病',
      10275: '呼吸系统慢性疾病及功能障碍，肺结核，自发性气胸，胸腔脏器手术史',
      10276: '心血管器质性疾病，房室传导阻滞以及难以治愈的周围血管疾病',
      10277: '严重消化系统疾病，功能障碍或手术后遗症，病毒性肝炎',
      10278: '泌尿系统疾病，损伤以及严重生殖系统疾病',
      10279: '造血系统疾病',
      10280: '新陈代谢，免疫，内分泌系统系统疾病',
      10281: '运动系统疾病，损伤及其后遗症',
      10282: '难以治愈的皮肤及其附属器疾病（不含非暴露部位范围小的白癜风）',
      10283:
        '任一眼裸眼远视力低于0.7，任一眼裸眼近视力低于1.0；视野异常；色盲，色弱；夜盲治疗无效 者；眼及其附属器疾病治愈后遗有眼功能障碍',
      10284:
        '任一耳纯音听力图气导听力曲线在500HZ，1000Hz，2000Hz任一频率听力损失不得超过35dB或3000Hz频率听力损失不得超过50dB',
      10285:
        '耳气压功能不良治疗无效者，中耳慢性进行性疾病。内耳疾病或眩晕症不合格',
      10286:
        '影响功能的鼻，鼻窦慢性进行性疾病，嗅觉丧失，影响功能且不易矫治的咽喉部慢性进行性疾病者',
      10287: '影响功能的口腔及颞下领关节慢性进行性疾病',
      10288: '职业性航空病',
      10289: '职业性噪声聋',
      10290: '严重的呼吸系统疾病',
      10291: '严重的循环系统疾病',
      10292: '严重的消化系统疾病',
      10293: '严重的造血系统疾病',
      10294: '严重的神经和精神系统疾病',
      10295: '严重的泌尿生殖系统疾病',
      10296: '严重的内分泌系统疾病',
      10297: '严重的免疫系统疾病',
      10299: '严重的视听障碍、严重的听力障碍',
      10300: '恶性肿瘤、有碍于工作的巨大的、复发性良性肿瘤',
      10301: '严重的、有碍于工作的残疾，先天畸形和遗传性疾病',
      10302: '手术后而不能恢复正常功能者',
      10303: '未完全恢复的放射性疾病或其他职业病等',
      10304: '有吸毒、酗酒或其他恶习不能改正者',
      10305:
        '血常规检出有如下异常者：白细胞计数低于4×10^9/L或中性粒细胞低于2×10^9/L；血小板计数低于8×10^10/L。',
      10411: '伴肺功能损害的呼吸系统疾病',
    };
    return codeObj[value] || '';
  }
  /** 处理症状编码 3.12
   * @param value 编码
   * @return {string} 症状内容
   */
  getSymptom(value) {
    // value转换为数字
    value = +value;
    const codeObj = {
      10001: '神经系统',
      10002: '头晕',
      10003: '头痛',
      10004: '眩晕',
      10005: '失眠',
      10006: '嗜睡',
      10007: '多梦',
      10008: '记忆力减退',
      10009: '易激动',
      10010: '疲乏无力',
      10011: '四肢麻木',
      10012: '动作不灵活',
      10013: '肌肉抽搐',
      10014: '呼吸系统',
      10015: '胸痛',
      10016: '胸闷',
      10017: '咳嗽',
      10018: '咳痰',
      10019: '咯血',
      10020: '气促',
      10021: '气短',
      10022: '心血管系统',
      10023: '心悸',
      10024: '心前区不适',
      10025: '心前区疼痛',
      10026: '消化系统',
      10027: '食欲不振',
      10028: '恶心',
      10029: '呕吐',
      10030: '腹胀',
      10031: '腹痛',
      10032: '肝区疼痛',
      10033: '便秘',
      10034: '便血',
      10035: '造血系统、内分泌系统',
      10036: '皮下出血',
      10037: '月经异常',
      10038: '低热',
      10039: '盗汗',
      10040: '多汗',
      10041: '口渴',
      10042: '消瘦',
      10043: '脱发',
      10044: '皮疹',
      10045: '皮肤瘙痒',
      10046: '泌尿生殖系统',
      10047: '尿频',
      10048: '尿急',
      10049: '尿痛',
      10050: '血尿',
      10051: '浮肿',
      10052: '性欲减退',
      10053: '肌肉及四肢关节',
      10054: '全身酸痛',
      10055: '肌肉疼痛',
      10056: '肌无力及关节疼痛',
      10057: '眼、耳、鼻、咽喉及口腔',
      10058: '视物模糊',
      10059: '视力下降',
      10060: '眼痛',
      10061: '羞明',
      10062: '流泪',
      10063: '嗅觉减退',
      10064: '鼻干燥',
      10065: '鼻塞',
      10066: '流鼻血',
      10067: '流涕',
      10068: '耳鸣',
      10069: '耳聋',
      10070: '流涎',
      10071: '牙痛',
      10072: '牙齿松动',
      10073: '刷牙出血',
      10074: '口腔异味',
      10075: '口腔溃疡',
      10076: '咽部疼痛',
      10077: '声嘶',
      10078: '皮肤及附属器',
      10079: '色素脱失或沉着',
      10080: '皮 疹',
      10081: '出血点（斑）',
      10082: '赘生物',
      10083: '水疱或大疱',
      10084: '无异常',
      10085: '目前无不适症状',
      10086: '其他',
      10087: '呼吸困难',
      10088: '喷嚏',
    };
    return codeObj[value] || '';
  }
  /** 处理行业类别
   * @param {String} value - 行业类别值
   * @return {String} - 返回行业类别
   */
  getIndustryCategory(value) {
    // value转换为数字
    value = +value;
    const codeObj = {
      200000: '农、林、牧、渔业',
      200001: '农业',
      200002: '谷物种植',
      200003: '稻谷种植',
      200004: '小麦种植',
      200005: '玉米种植',
      200006: '其他谷物种植',
      200007: '豆类、油料和薯类种植',
      200008: '豆类种植',
      200009: '油料种植',
      200010: '薯类种植',
      200011: '棉、麻、糖、烟草种植',
      200012: '棉花种植',
      200013: '麻类种植',
      200014: '糖料种植',
      200015: '烟草种植',
      200016: '蔬菜、食用菌及园艺作物种植',
      200017: '蔬菜种植',
      200018: '食用菌种植',
      200019: '花卉种植',
      200020: '其他园艺作物种植',
      200021: '水果种植',
      200022: '仁果类和核果类水果种植',
      200023: '葡萄种植',
      200024: '柑橘类种植',
      200025: '香蕉等亚热带水果种植',
      200026: '其他水果种植',
      200027: '坚果、含油果、香料和饮料作物种植',
      200028: '坚果种植',
      200029: '含油果种植',
      200030: '香料作物种植',
      200031: '茶叶种植',
      200032: '其他饮料作物种植',
      200033: '中药材种植',
      200034: '中草药种植*',
      200035: '其他中药材种植',
      200036: '草种植及割草',
      200037: '草种植',
      200038: '天然草原割草',
      200039: '其他农业',
      200040: '其他农业*',
      200041: '林业',
      200042: '林木育种和育苗',
      200043: '林木育种',
      200044: '林木育苗',
      200045: '造林和更新',
      200046: '造林和更新*',
      200047: '森林经营、管护和改培',
      200048: '森林经营和管护',
      200049: '森林改培',
      200050: '木材和竹材采运',
      200051: '木材采运',
      200052: '竹材采运',
      200053: '林产品采集',
      200054: '木竹材林产品采集',
      200055: '非木竹材林产品采集',
      200056: '畜牧业',
      200057: '牲畜饲养',
      200058: '牛的饲养',
      200059: '马的饲养',
      200060: '猪的饲养',
      200061: '羊的饲养',
      200062: '骆驼饲养',
      200063: '其他牲畜饲养',
      200064: '家禽饲养',
      200065: '鸡的饲养',
      200066: '鸭的饲养',
      200067: '鹅的饲养',
      200068: '其他家禽饲养',
      200069: '狩猎和捕捉动物',
      200070: '狩猎和捕捉动物*',
      200071: '其他畜牧业',
      200072: '兔的饲养',
      200073: '蜜蜂饲养',
      200074: '其他未列明畜牧业',
      200075: '渔业',
      200076: '水产养殖',
      200077: '海水养殖',
      200078: '内陆养殖',
      200079: '水产捕捞',
      200080: '海水捕捞',
      200081: '内陆捕捞',
      200082: '农、林、牧、渔专业及辅助性活动',
      200083: '农业专业及辅助性活动',
      200084: '种子种苗培育活动',
      200085: '农业机械活动',
      200086: '灌溉活动',
      200087: '农产品初加工活动',
      200088: '农作物病虫害防治活动',
      200089: '其他农业专业及辅助性活动',
      200090: '林业专业及辅助性活动',
      200091: '林业有害生物防治活动',
      200092: '森林防火活动',
      200093: '林产品初级加工活动',
      200094: '其他林业专业及辅助性活动',
      200095: '畜牧专业及辅助性活动',
      200096: '畜牧良种繁殖活动',
      200097: '畜禽粪污处理活动',
      200098: '其他畜牧专业及辅助性活动',
      200099: '渔业专业及辅助性活动',
      200100: '鱼苗及鱼种场活动',
      200101: '其他渔业专业及辅助性活动',
      200102: '采矿业',
      200103: '煤炭开采和洗选业',
      200104: '烟煤和无烟煤开采洗选',
      200105: '烟煤和无烟煤开采洗选*',
      200106: '褐煤开采洗选',
      200107: '褐煤开采洗选*',
      200108: '其他煤炭采选',
      200109: '其他煤炭采选*',
      200110: '石油和天然气开采业',
      200111: '石油开采',
      200112: '陆地石油开采',
      200113: '海洋石油开采',
      200114: '天然气开采',
      200115: '陆地天然气开采',
      200116: '海洋天然气及可燃冰开采',
      200117: '黑色金属矿采选业',
      200118: '铁矿采选',
      200119: '铁矿采选*',
      200120: '锰矿、铬矿采选',
      200121: '锰矿、铬矿采选*',
      200122: '其他黑色金属矿采选',
      200123: '其他黑色金属矿采选*',
      200124: '有色金属矿采选业',
      200125: '常用有色金属矿采选',
      200126: '铜矿采选',
      200127: '铅锌矿采选',
      200128: '镍钴矿采选',
      200129: '锡矿采选',
      200130: '锑矿采选',
      200131: '铝矿采选',
      200132: '镁矿采选',
      200133: '其他常用有色金属矿采选',
      200134: '贵金属矿采选',
      200135: '金矿采选',
      200136: '银矿采选',
      200137: '其他贵金属矿采选',
      200138: '稀有稀土金属矿采选',
      200139: '钨钼矿采选',
      200140: '稀土金属矿采选',
      200141: '放射性金属矿采选',
      200142: '其他稀有金属矿采选',
      200143: '非金属矿采选业',
      200144: '土砂石开采',
      200145: '石灰石、石膏开采',
      200146: '建筑装饰用石开采',
      200147: '耐火土石开采',
      200148: '粘土及其他土砂石开采',
      200149: '化学矿开采',
      200150: '化学矿开采*',
      200151: '采盐',
      200152: '采盐*',
      200153: '石棉及其他非金属矿采选',
      200154: '石棉、云母矿采选',
      200155: '石墨、滑石采选',
      200156: '宝石、玉石采选',
      200157: '其他未列明非金属矿采选',
      200158: '开采专业及辅助性活动',
      200159: '煤炭开采和洗选专业及辅助性活动',
      200160: '煤炭开采和洗选专业及辅助性活动*',
      200161: '石油和天然气开采专业及辅助性活动',
      200162: '石油和天然气开采专业及辅助性活动*',
      200163: '其他开采专业及辅助性活动',
      200164: '其他开采专业及辅助性活动*',
      200165: '其他采矿业',
      200166: '其他采矿业*',
      200167: '其他采矿业**',
      200168: '制造业',
      200169: '农副食品加工业',
      200170: '谷物磨制',
      200171: '稻谷加工',
      200172: '小麦加工',
      200173: '玉米加工',
      200174: '杂粮加工',
      200175: '其他谷物磨制',
      200176: '饲料加工',
      200177: '宠物饲料加工',
      200178: '其他饲料加工',
      200179: '植物油加工',
      200180: '食用植物油加工',
      200181: '非食用植物油加工',
      200182: '制糖业',
      200183: '制糖业*',
      200184: '屠宰及肉类加工',
      200185: '牲畜屠宰',
      200186: '禽类屠宰',
      200187: '肉制品及副产品加工',
      200188: '水产品加工',
      200189: '水产品冷冻加工',
      200190: '鱼糜制品及水产品干腌制加工',
      200191: '鱼油提取及制品制造',
      200192: '其他水产品加工',
      200193: '蔬菜、菌类、水果和坚果加工',
      200194: '蔬菜加工',
      200195: '食用菌加工',
      200196: '水果和坚果加工',
      200197: '其他农副食品加工',
      200198: '淀粉及淀粉制品制造',
      200199: '豆制品制造',
      200200: '蛋品加工',
      200201: '其他未列明农副食品加工',
      200202: '食品制造业',
      200203: '焙烤食品制造',
      200204: '糕点、面包制造',
      200205: '饼干及其他焙烤食品制造',
      200206: '糖果、巧克力及蜜饯制造',
      200207: '糖果、巧克力制造',
      200208: '蜜饯制作',
      200209: '方便食品制造',
      200210: '米、面制品制造',
      200211: '速冻食品制造',
      200212: '方便面制造',
      200213: '其他方便食品制造',
      200214: '乳制品制造',
      200215: '液体乳制造',
      200216: '乳粉制造',
      200217: '其他乳制品制造',
      200218: '罐头食品制造',
      200219: '肉、禽类罐头制造',
      200220: '水产品罐头制造',
      200221: '蔬菜、水果罐头制造',
      200222: '其他罐头食品制造',
      200223: '调味品、发酵制品制造',
      200224: '味精制造',
      200225: '酱油、食醋及类似制品制造',
      200226: '其他调味品、发酵制品制造',
      200227: '其他食品制造',
      200228: '营养食品制造',
      200229: '保健食品制造',
      200230: '冷冻饮品及食用冰制造',
      200231: '盐加工',
      200232: '食品及饲料添加剂制造',
      200233: '其他未列明食品制造',
      200234: '酒、饮料及精制茶制造业',
      200235: '酒的制造',
      200236: '酒精制造',
      200237: '白酒制造',
      200238: '啤酒制造',
      200239: '黄酒制造',
      200240: '葡萄酒制造',
      200241: '其他酒制造',
      200242: '饮料制造',
      200243: '碳酸饮料制造',
      200244: '瓶（罐）装饮用水制造',
      200245: '果菜汁及果菜汁饮料制造',
      200246: '含乳饮料和植物蛋白饮料制造',
      200247: '固体饮料制造',
      200248: '茶饮料及其他饮料制造',
      200249: '精制茶加工',
      200250: '精制茶加工*',
      200251: '烟草制品业',
      200252: '烟叶复烤',
      200253: '烟叶复烤*',
      200254: '卷烟制造',
      200255: '卷烟制造*',
      200256: '其他烟草制品制造',
      200257: '其他烟草制品制造*',
      200258: '纺织业',
      200259: '棉纺织及印染精加工',
      200260: '棉纺纱加工',
      200261: '棉织造加工',
      200262: '棉印染精加工',
      200263: '毛纺织及染整精加工',
      200264: '毛条和毛纱线加工',
      200265: '毛织造加工',
      200266: '毛染整精加工',
      200267: '麻纺织及染整精加工',
      200268: '麻纤维纺前加工和纺纱',
      200269: '麻织造加工',
      200270: '麻染整精加工',
      200271: '丝绢纺织及印染精加工',
      200272: '缫丝加工',
      200273: '绢纺和丝织加工',
      200274: '丝印染精加工',
      200275: '化纤织造及印染精加工',
      200276: '化纤织造加工',
      200277: '化纤织物染整精加工',
      200278: '针织或钩针编织物及其制品制造',
      200279: '针织或钩针编织物织造',
      200280: '针织或钩针编织物印染精加工',
      200281: '针织或钩针编织品制造',
      200282: '家用纺织制成品制造',
      200283: '床上用品制造',
      200284: '毛巾类制品制造',
      200285: '窗帘、布艺类产品制造',
      200286: '其他家用纺织制成品制造',
      200287: '产业用纺织制成品制造',
      200288: '非织造布制造',
      200289: '绳、索、缆制造',
      200290: '纺织带和帘子布制造',
      200291: '篷、帆布制造',
      200292: '其他产业用纺织制成品制造',
      200293: '纺织服装、服饰业',
      200294: '机织服装制造',
      200295: '运动机织服装制造',
      200296: '其他机织服装制造',
      200297: '针织或钩针编织服装制造',
      200298: '运动休闲针织服装制造',
      200299: '其他针织或钩针编织服装制造',
      200300: '服饰制造',
      200301: '服饰制造*',
      200302: '皮革、毛皮、羽毛及其制品和制鞋业',
      200303: '皮革鞣制加工',
      200304: '皮革鞣制加工*',
      200305: '皮革制品制造',
      200306: '皮革服装制造',
      200307: '皮箱、包(袋)制造',
      200308: '皮手套及皮装饰制品制造',
      200309: '其他皮革制品制造',
      200310: '毛皮鞣制及制品加工',
      200311: '毛皮鞣制加工',
      200312: '毛皮服装加工',
      200313: '其他毛皮制品加工',
      200314: '羽毛(绒)加工及制品制造',
      200315: '羽毛(绒)加工',
      200316: '羽毛(绒)制品加工',
      200317: '制鞋业',
      200318: '纺织面料鞋制造',
      200319: '皮鞋制造',
      200320: '塑料鞋制造',
      200321: '橡胶鞋制造',
      200322: '其他制鞋业',
      200323: '木材加工和木、竹、藤、棕、草制品业',
      200324: '木材加工',
      200325: '锯材加工',
      200326: '木片加工',
      200327: '单板加工',
      200328: '其他木材加工',
      200329: '人造板制造',
      200330: '胶合板制造',
      200331: '纤维板制造',
      200332: '刨花板制造',
      200333: '其他人造板制造',
      200334: '木制品制造',
      200335: '建筑用木料及木材组件加工',
      200336: '木门窗制造',
      200337: '木楼梯制造',
      200338: '木地板制造',
      200339: '木制容器制造',
      200340: '软木制品及其他木制品制造',
      200341: '竹、藤、棕、草制品制造',
      200342: '竹制品制造',
      200343: '藤制品制造',
      200344: '棕制品制造',
      200345: '草及其他制品制造',
      200346: '家具制造业',
      200347: '木质家具制造',
      200348: '木质家具制造*',
      200349: '竹、藤家具制造',
      200350: '竹、藤家具制造*',
      200351: '金属家具制造',
      200352: '金属家具制造*',
      200353: '塑料家具制造',
      200354: '塑料家具制造*',
      200355: '其他家具制造',
      200356: '其他家具制造*',
      200357: '造纸和纸制品业',
      200358: '纸浆制造',
      200359: '木竹浆制造',
      200360: '非木竹浆制造',
      200361: '造纸',
      200362: '机制纸及纸板制造',
      200363: '手工纸制造',
      200364: '加工纸制造',
      200365: '纸制品制造',
      200366: '纸和纸板容器制造',
      200367: '其他纸制品制造',
      200368: '印刷和记录媒介复制业',
      200369: '印刷',
      200370: '书、报刊印刷',
      200371: '本册印制',
      200372: '包装装潢及其他印刷',
      200373: '装订及印刷相关服务',
      200374: '装订及印刷相关服务*',
      200375: '记录媒介复制',
      200376: '记录媒介复制*',
      200377: '文教、工美、体育和娱乐用品制造业',
      200378: '文教办公用品制造',
      200379: '文具制造',
      200380: '笔的制造',
      200381: '教学用模型及教具制造',
      200382: '墨水、墨汁制造',
      200383: '其他文教办公用品制造',
      200384: '乐器制造',
      200385: '中乐器制造',
      200386: '西乐器制造',
      200387: '电子乐器制造',
      200388: '其他乐器及零件制造',
      200389: '工艺美术及礼仪用品制造',
      200390: '雕塑工艺品制造',
      200391: '金属工艺品制造',
      200392: '漆器工艺品制造',
      200393: '花画工艺品制造',
      200394: '天然植物纤维编织工艺品制造',
      200395: '抽纱刺绣工艺品制造',
      200396: '地毯、挂毯制造',
      200397: '珠宝首饰及有关物品制造',
      200398: '其他工艺美术及礼仪用品制造',
      200399: '体育用品制造',
      200400: '球类制造',
      200401: '专项运动器材及配件制造',
      200402: '健身器材制造',
      200403: '运动防护用具制造',
      200404: '其他体育用品制造',
      200405: '玩具制造',
      200406: '电玩具制造',
      200407: '塑胶玩具制造',
      200408: '金属玩具制造',
      200409: '弹射玩具制造',
      200410: '娃娃玩具制造',
      200411: '儿童乘骑玩耍的童车类产品制造',
      200412: '其他玩具制造',
      200413: '游艺器材及娱乐用品制造',
      200414: '露天游乐场所游乐设备制造',
      200415: '游艺用品及室内游艺器材制造',
      200416: '其他娱乐用品制造',
      200417: '石油、煤炭及其他燃料加工业',
      200418: '精炼石油产品制造',
      200419: '原油加工及石油制品制造',
      200420: '其他原油制造',
      200421: '煤炭加工',
      200422: '炼焦',
      200423: '煤制合成气生产',
      200424: '煤制液体燃料生产',
      200425: '煤制品制造',
      200426: '其他煤炭加工',
      200427: '核燃料加工',
      200428: '核燃料加工*',
      200429: '生物质燃料加工',
      200430: '生物质液体燃料生产',
      200431: '生物质致密成型燃料加工',
      200432: '化学原料和化学制品制造业',
      200433: '基础化学原料制造',
      200434: '无机酸制造',
      200435: '无机碱制造',
      200436: '无机盐制造',
      200437: '有机化学原料制造',
      200438: '其他基础化学原料制造',
      200439: '肥料制造',
      200440: '氮肥制造',
      200441: '磷肥制造',
      200442: '钾肥制造',
      200443: '复混肥料制造',
      200444: '有机肥料及微生物肥料制造',
      200445: '其他肥料制造',
      200446: '农药制造',
      200447: '化学农药制造',
      200448: '生物化学农药及微生物农药制造',
      200449: '涂料、油墨、颜料及类似产品制造',
      200450: '涂料制造',
      200451: '油墨及类似产品制造',
      200452: '工业颜料制造',
      200453: '工艺美术颜料制造',
      200454: '染料制造',
      200455: '密封用填料及类似品制造',
      200456: '合成材料制造',
      200457: '初级形态塑料及合成树脂制造',
      200458: '合成橡胶制造',
      200459: '合成纤维单(聚合)体制造',
      200460: '其他合成材料制造',
      200461: '专用化学产品制造',
      200462: '化学试剂和助剂制造',
      200463: '专项化学用品制造',
      200464: '林产化学产品制造',
      200465: '文化用信息化学品制造',
      200466: '医学生产用信息化学品制造',
      200467: '环境污染处理专用药剂材料制造',
      200468: '动物胶制造',
      200469: '其他专用化学产品制造',
      200470: '炸药、火工及焰火产品制造',
      200471: '炸药及火工产品制造',
      200472: '焰火、鞭炮产品制造',
      200473: '日用化学产品制造',
      200474: '肥皂及洗涤剂制造',
      200475: '化妆品制造',
      200476: '口腔清洁用品制造',
      200477: '香料、香精制造',
      200478: '其他日用化学产品制造',
      200479: '医药制造业',
      200480: '化学药品原料药制造',
      200481: '化学药品原料药制造*',
      200482: '化学药品制剂制造',
      200483: '化学药品制剂制造*',
      200484: '中药饮片加工',
      200485: '中药饮片加工*',
      200486: '中成药生产',
      200487: '中成药生产*',
      200488: '兽用药品制造',
      200489: '兽用药品制造*',
      200490: '生物药品制品制造',
      200491: '生物药品制造',
      200492: '基因工程药物和疫苗制造',
      200493: '卫生材料及医药用品制造',
      200494: '卫生材料及医药用品制造*',
      200495: '药用辅料及包装材料',
      200496: '药用辅料及包装材料*',
      200497: '化学纤维制造业',
      200498: '纤维素纤维原料及纤维制造',
      200499: '化纤浆粕制造',
      200500: '人造纤维（纤维素纤维）制造',
      200501: '合成纤维制造',
      200502: '锦纶纤维制造',
      200503: '涤纶纤维制造',
      200504: '腈纶纤维制造',
      200505: '维纶纤维制造',
      200506: '丙纶纤维制造',
      200507: '氨纶纤维制造',
      200508: '其他合成纤维制造',
      200509: '生物基材料制造',
      200510: '生物基化学纤维制造',
      200511: '生物基、淀粉基新材料制造',
      200512: '橡胶和塑料制品业',
      200513: '橡胶制品业',
      200514: '轮胎制造',
      200515: '橡胶板、管、带制造',
      200516: '橡胶零件制造',
      200517: '再生橡胶制造',
      200518: '日用及医用橡胶制品制造',
      200519: '运动场地用塑胶制造',
      200520: '其他橡胶制品制造',
      200521: '塑料制品业',
      200522: '塑料薄膜制造',
      200523: '塑料板、管、型材制造',
      200524: '塑料丝、绳及编织品制造',
      200525: '泡沫塑料制造',
      200526: '塑料人造革、合成革制造',
      200527: '塑料包装箱及容器制造',
      200528: '日用塑料制品制造',
      200529: '人造草坪制造',
      200530: '塑料零件及其他塑料制品制造',
      200531: '非金属矿物制品业',
      200532: '水泥、石灰和石膏制造',
      200533: '水泥制造',
      200534: '石灰和石膏制造',
      200535: '石膏、水泥制品及类似制品制造',
      200536: '水泥制品制造',
      200537: '砼结构构件制造',
      200538: '石棉水泥制品制造',
      200539: '轻质建筑材料制造',
      200540: '其他水泥类似制品制造',
      200541: '砖瓦、石材等建筑材料制造',
      200542: '粘土砖瓦及建筑砌块制造',
      200543: '建筑用石加工',
      200544: '防水建筑材料制造',
      200545: '隔热和隔音材料制造',
      200546: '其他建筑材料制造',
      200547: '玻璃制造',
      200548: '平板玻璃制造',
      200549: '特种玻璃制造',
      200550: '其他玻璃制造',
      200551: '玻璃制品制造',
      200552: '技术玻璃制品制造',
      200553: '光学玻璃制造',
      200554: '玻璃仪器制造',
      200555: '日用玻璃制品制造',
      200556: '玻璃包装容器制造',
      200557: '玻璃保温容器制造',
      200558: '制镜及类似品加工',
      200559: '其他玻璃制品制造',
      200560: '玻璃纤维和玻璃纤维增强塑料制品制造',
      200561: '玻璃纤维及制品制造',
      200562: '玻璃纤维增强塑料制品制造',
      200563: '陶瓷制品制造',
      200564: '建筑陶瓷制品制造',
      200565: '卫生陶瓷制品制造',
      200566: '特种陶瓷制品制造',
      200567: '日用陶瓷制品制造',
      200568: '陈设艺术陶瓷制造',
      200569: '园艺陶瓷制造',
      200570: '其他陶瓷制品制造',
      200571: '耐火材料制品制造',
      200572: '石棉制品制造',
      200573: '云母制品制造',
      200574: '耐火陶瓷制品及其他耐火材料制造',
      200575: '石墨及其他非金属矿物制品制造',
      200576: '石墨及碳素制品制造',
      200577: '其他非金属矿物制品制造',
      200578: '黑色金属冶炼和压延加工业',
      200579: '炼铁',
      200580: '炼铁*',
      200581: '炼钢',
      200582: '炼钢*',
      200583: '钢压延加工',
      200584: '钢压延加工*',
      200585: '铁合金冶炼',
      200586: '铁合金冶炼*',
      200587: '有色金属冶炼和压延加工业',
      200588: '常用有色金属冶炼',
      200589: '铜冶炼',
      200590: '铅锌冶炼',
      200591: '镍钴冶炼',
      200592: '锡冶炼',
      200593: '锑冶炼',
      200594: '铝冶炼',
      200595: '镁冶炼',
      200596: '硅冶炼',
      200597: '其他常用有色金属冶炼',
      200598: '贵金属冶炼',
      200599: '金冶炼',
      200600: '银冶炼',
      200601: '其他贵金属冶炼',
      200602: '稀有稀土金属冶炼',
      200603: '钨钼冶炼',
      200604: '稀土金属冶炼',
      200605: '其他稀有金属冶炼',
      200606: '有色金属合金制造',
      200607: '有色金属合金制造*',
      200608: '有色金属压延加工',
      200609: '铜压延加工',
      200610: '铝压延加工',
      200611: '贵金属压延加工',
      200612: '稀有稀土金属压延加工',
      200613: '其他有色金属压延加工',
      200614: '金属制品业',
      200615: '结构性金属制品制造',
      200616: '金属结构制造',
      200617: '金属门窗制造',
      200618: '金属工具制造',
      200619: '切削工具制造',
      200620: '手工具制造',
      200621: '农用及园林用金属工具制造',
      200622: '刀剪及类似日用金属工具制造',
      200623: '其他金属工具制造',
      200624: '集装箱及金属包装容器制造',
      200625: '集装箱制造',
      200626: '金属压力容器制造',
      200627: '金属包装容器及材料制造',
      200628: '金属丝绳及其制品制造',
      200629: '金属丝绳及其制品制造*',
      200630: '建筑、安全用金属制品制造',
      200631: '建筑、家具用金属配件制造',
      200632: '建筑装饰及水暖管道零件制造',
      200633: '安全、消防用金属制品制造',
      200634: '其他建筑、安全用金属制品制造',
      200635: '金属表面处理及热处理加工',
      200636: '金属表面处理及热处理加工*',
      200637: '搪瓷制品制造',
      200638: '生产专用搪瓷制品制造',
      200639: '建筑装饰搪瓷制品制造',
      200640: '搪瓷卫生洁具制造',
      200641: '搪瓷日用品及其他搪瓷制品制造',
      200642: '金属制日用品制造',
      200643: '金属制厨房用器具制造',
      200644: '金属制餐具和器皿制造',
      200645: '金属制卫生器具制造',
      200646: '其他金属制日用品制造',
      200647: '锻造及其他金属制品制造',
      200648: '黑色金属铸造',
      200649: '有色金属铸造',
      200650: '锻件及粉末冶金制品制造',
      200651: '交通及公共管理用金属标牌制造',
      200652: '其他未列明金属制品制造',
      200653: '通用设备制造业',
      200654: '锅炉及原动设备制造',
      200655: '锅炉及辅助设备制造',
      200656: '内燃机及配件制造',
      200657: '汽轮机及辅机制造',
      200658: '水轮机及辅机制造',
      200659: '风能原动设备制造',
      200660: '其他原动设备制造',
      200661: '金属加工机械制造',
      200662: '金属切削机床制造',
      200663: '金属成形机床制造',
      200664: '铸造机械制造',
      200665: '金属切割及焊接设备制造',
      200666: '机床功能部件及附件制造',
      200667: '其他金属加工机械制造',
      200668: '物料搬运设备制造',
      200669: '轻小型起重设备制造',
      200670: '生产专用起重机制造',
      200671: '生产专用车辆制造',
      200672: '连续搬运设备制造',
      200673: '电梯、自动扶梯及升降机制造',
      200674: '客运索道制造',
      200675: '机械式停车设备制造',
      200676: '其他物料搬运设备制造',
      200677: '泵、阀门、压缩机及类似机械制造',
      200678: '泵及真空设备制造',
      200679: '气体压缩机械制造',
      200680: '阀门和旋塞制造',
      200681: '液压动力机械及元件制造',
      200682: '液力动力机械元件制造',
      200683: '气压动力机械及元件制造',
      200684: '轴承、齿轮和传动部件制造',
      200685: '滚动轴承制造',
      200686: '滑动轴承制造',
      200687: '齿轮及齿轮减、变速箱制造',
      200688: '其他传动部件制造',
      200689: '烘炉、风机、包装等设备制造',
      200690: '烘炉、熔炉及电炉制造',
      200691: '风机、风扇制造',
      200692: '气体、液体分离及纯净设备制造',
      200693: '制冷、空调设备制造',
      200694: '风动和电动工具制造',
      200695: '喷枪及类似器具制造',
      200696: '包装专用设备制造',
      200697: '文化、办公用机械制造',
      200698: '电影机械制造',
      200699: '幻灯及投影设备制造',
      200700: '照相机及器材制造',
      200701: '复印和胶印设备制造',
      200702: '计算器及货币专用设备制造',
      200703: '其他文化、办公用机械制造',
      200704: '通用零部件制造',
      200705: '金属密封件制造',
      200706: '紧固件制造',
      200707: '弹簧制造',
      200708: '机械零部件加工',
      200709: '其他通用零部件制造',
      200710: '其他通用设备制造',
      200711: '工业机器人制造',
      200712: '特殊作业机器人制造',
      200713: '增材制造装备制造',
      200714: '其他未列明通用设备制造业',
      200715: '专用设备制造业',
      200716: '采矿、冶金、建筑专用设备制造',
      200717: '矿山机械制造',
      200718: '石油钻采专用设备制造',
      200719: '深海石油钻探设备制造',
      200720: '建筑工程用机械制造',
      200721: '建筑材料生产专用机械制造',
      200722: '冶金专用设备制造',
      200723: '隧道施工专用机械制造',
      200724: '化工、木材、非金属加工专用设备制造',
      200725: '炼油、化工生产专用设备制造',
      200726: '橡胶加工专用设备制造',
      200727: '塑料加工专用设备制造',
      200728: '木竹材加工机械制造',
      200729: '模具制造',
      200730: '其他非金属加工专用设备制造',
      200731: '食品、饮料、烟草及饲料生产专用设备制造',
      200732: '食品、酒、饮料及茶生产专用设备制造',
      200733: '农副食品加工专用设备制造',
      200734: '烟草生产专用设备制造',
      200735: '饲料生产专用设备制造',
      200736: '印刷、制药、日化及日用品生产专用设备制造',
      200737: '制浆和造纸专用设备制造',
      200738: '印刷专用设备制造',
      200739: '日用化工专用设备制造',
      200740: '制药专用设备制造',
      200741: '照明器具生产专用设备制造',
      200742: '玻璃、陶瓷和搪瓷制品生产专用设备制造',
      200743: '其他日用品生产专用设备制造',
      200744: '纺织、服装和皮革加工专用设备制造',
      200745: '纺织专用设备制造',
      200746: '皮革、毛皮及其制品加工专用设备制造',
      200747: '缝制机械制造',
      200748: '洗涤机械制造',
      200749: '电子和电工机械专用设备制造',
      200750: '电工机械专用设备制造',
      200751: '半导体器件专用设备制造',
      200752: '电子元器件与机电组件设备制造',
      200753: '其他电子专用设备制造',
      200754: '农、林、牧、渔专用机械制造',
      200755: '拖拉机制造',
      200756: '机械化农业及园艺机具制造',
      200757: '营林及木竹采伐机械制造',
      200758: '畜牧机械制造',
      200759: '渔业机械制造',
      200760: '农林牧渔机械配件制造',
      200761: '棉花加工机械制造',
      200762: '其他农、林、牧、渔业机械制造',
      200763: '医疗仪器设备及器械制造',
      200764: '医疗诊断、监护及治疗设备制造',
      200765: '口腔科用设备及器具制造',
      200766: '医疗实验室及医用消毒设备和器具制造',
      200767: '医疗、外科及兽医用器械制造',
      200768: '机械治疗及病房护理设备制造',
      200769: '康复辅具制造',
      200770: '眼镜制造',
      200771: '其他医疗设备及器械制造',
      200772: '环保、邮政、社会公共服务及其他专用设备制造',
      200773: '环境保护专用设备制造',
      200774: '地质勘查专用设备制造',
      200775: '邮政专用机械及器材制造',
      200776: '商业、饮食、服务专用设备制造',
      200777: '社会公共安全设备及器材制造',
      200778: '交通安全、管制及类似专用设备制造',
      200779: '水资源专用机械制造',
      200780: '其他专用设备制造',
      200781: '汽车制造业',
      200782: '汽车整车制造',
      200783: '汽柴油车整车制造',
      200784: '新能源车整车制造',
      200785: '汽车用发动机制造',
      200786: '汽车用发动机制造*',
      200787: '改装汽车制造',
      200788: '改装汽车制造*',
      200789: '低速汽车制造',
      200790: '低速汽车制造*',
      200791: '电车制造',
      200792: '电车制造*',
      200793: '汽车车身、挂车制造',
      200794: '汽车车身、挂车制造*',
      200795: '汽车零部件及配件制造',
      200796: '汽车零部件及配件制造*',
      200797: '铁路、船舶、航空航天和其他运输设备制造业',
      200798: '铁路运输设备制造',
      200799: '高铁车组制造',
      200800: '铁路机车车辆制造',
      200801: '窄轨机车车辆制造',
      200802: '高铁设备、配件制造',
      200803: '铁路机车车辆配件制造',
      200804: '铁路专用设备及器材、配件制造',
      200805: '其他铁路运输设备制造',
      200806: '城市轨道交通设备制造',
      200807: '城市轨道交通设备制造*',
      200808: '船舶及相关装置制造',
      200809: '金属船舶制造',
      200810: '非金属船舶制造',
      200811: '娱乐船和运动船制造',
      200812: '船用配套设备制造',
      200813: '船舶改装',
      200814: '船舶拆除',
      200815: '海洋工程装备制造',
      200816: '航标器材及其他相关装置制造',
      200817: '航空、航天器及设备制造',
      200818: '飞机制造',
      200819: '航天器及运载火箭制造',
      200820: '航天相关设备制造',
      200821: '航空相关设备制造',
      200822: '其他航空航天器制造',
      200823: '摩托车制造',
      200824: '摩托车整车制造',
      200825: '摩托车零部件及配件制造',
      200826: '自行车和残疾人座车制造',
      200827: '自行车制造',
      200828: '残疾人座车制造',
      200829: '助动车制造',
      200830: '助动车制造*',
      200831: '非公路休闲车及零配件制造',
      200832: '非公路休闲车及零配件制造*',
      200833: '潜水救捞及其他未列明运输设备制造',
      200834: '潜水装备制造',
      200835: '水下救捞装备制造',
      200836: '其他未列明运输设备制造',
      200837: '电气机械和器材制造业',
      200838: '电机制造',
      200839: '发电机及发电机组制造',
      200840: '电动机制造',
      200841: '微特电机及组件制造',
      200842: '其他电机制造',
      200843: '输配电及控制设备制造',
      200844: '变压器、整流器和电感器制造',
      200845: '电容器及其配套设备制造',
      200846: '配电开关控制设备制造',
      200847: '电力电子元器件制造',
      200848: '光伏设备及元器件制造',
      200849: '其他输配电及控制设备制造',
      200850: '电线、电缆、光缆及电工器材制造',
      200851: '电线、电缆制造',
      200852: '光纤制造',
      200853: '光缆制造',
      200854: '绝缘制品制造',
      200855: '其他电工器材制造',
      200856: '电池制造',
      200857: '锂离子电池制造',
      200858: '镍氢电池制造',
      200859: '铅蓄电池制造',
      200860: '锌锰电池制造',
      200861: '其他电池制造',
      200862: '家用电力器具制造',
      200863: '家用制冷电器具制造',
      200864: '家用空气调节器制造',
      200865: '家用通风电器具制造',
      200866: '家用厨房电器具制造',
      200867: '家用清洁卫生电器具制造',
      200868: '家用美容、保健护理电器具制造',
      200869: '家用电力器具专用配件制造',
      200870: '其他家用电力器具制造',
      200871: '非电力家用器具制造',
      200872: '燃气及类似能源家用器具制造',
      200873: '太阳能器具制造',
      200874: '其他非电力家用器具制造',
      200875: '照明器具制造',
      200876: '电光源制造',
      200877: '照明灯具制造',
      200878: '舞台及场地用灯制造',
      200879: '智能照明器具制造',
      200880: '灯用电器附件及其他照明器具制造',
      200881: '其他电气机械及器材制造',
      200882: '电气信号设备装置制造',
      200883: '其他未列明电气机械及器材制造',
      200884: '计算机、通信和其他电子设备制造业',
      200885: '计算机制造',
      200886: '计算机整机制造',
      200887: '计算机零部件制造',
      200888: '计算机外围设备制造',
      200889: '工业控制计算机及系统制造',
      200890: '信息安全设备制造',
      200891: '其他计算机制造',
      200892: '通信设备制造',
      200893: '通信系统设备制造',
      200894: '通信终端设备制造',
      200895: '广播电视设备制造',
      200896: '广播电视节目制作及发射设备制造',
      200897: '广播电视接收设备制造',
      200898: '广播电视专用配件制造',
      200899: '专业音响设备制造',
      200900: '应用电视设备及其他广播电视设备制造',
      200901: '雷达及配套设备制造',
      200902: '雷达及配套设备制造*',
      200903: '视听设备制造',
      200904: '电视机制造',
      200905: '音响设备制造',
      200906: '影视录放设备制造',
      200907: '智能消费设备制造',
      200908: '可穿戴智能设备制造',
      200909: '智能车载设备制造',
      200910: '智能无人飞行器制造',
      200911: '服务消费机器人制造',
      200912: '其他智能消费设备制造',
      200913: '电子器件制造',
      200914: '电子真空器件制造',
      200915: '半导体分立器件制造',
      200916: '集成电路制造',
      200917: '显示器件制造',
      200918: '半导体照明器件制造',
      200919: '光电子器件制造',
      200920: '其他电子器件制造',
      200921: '电子元件及电子专用材料制造',
      200922: '电阻电容电感元件制造',
      200923: '电子电路制造',
      200924: '敏感元件及传感器制造',
      200925: '电声器件及零件制造',
      200926: '电子专用材料制造',
      200927: '其他电子元件制造',
      200928: '其他电子设备制造',
      200929: '其他电子设备制造*',
      200930: '仪器仪表制造业',
      200931: '通用仪器仪表制造',
      200932: '工业自动控制系统装置制造',
      200933: '电工仪器仪表制造',
      200934: '绘图、计算及测量仪器制造',
      200935: '实验分析仪器制造',
      200936: '试验机制造',
      200937: '供应用仪器仪表制造',
      200938: '其他通用仪器制造',
      200939: '专用仪器仪表制造',
      200940: '环境监测专用仪器仪表制造',
      200941: '运输设备及生产用计数仪表制造',
      200942: '导航、测绘、气象及海洋专用仪器制造',
      200943: '农林牧渔专用仪器仪表制造',
      200944: '地质勘探和地震专用仪器制造',
      200945: '教学专用仪器制造',
      200946: '核子及核辐射测量仪器制造',
      200947: '电子测量仪器制造',
      200948: '其他专用仪器制造',
      200949: '钟表与计时仪器制造',
      200950: '钟表与计时仪器制造*',
      200951: '光学仪器制造',
      200952: '光学仪器制造*',
      200953: '衡器制造',
      200954: '衡器制造*',
      200955: '其他仪器仪表制造业',
      200956: '其他仪器仪表制造业*',
      200957: '其他制造业',
      200958: '日用杂品制造',
      200959: '鬃毛加工、制刷及清扫工具制造',
      200960: '其他日用杂品制造',
      200961: '核辐射加工',
      200962: '核辐射加工*',
      200963: '其他未列明制造业',
      200964: '其他未列明制造业*',
      200965: '废弃资源综合利用业',
      200966: '金属废料和碎屑加工处理',
      200967: '非金属废料和碎屑加工处理',
      200968: '非金属废料和碎屑加工处理*',
      200969: '金属制品、机械和设备修理业',
      200970: '金属制品修理',
      200971: '金属制品修理*',
      200972: '通用设备修理',
      200973: '通用设备修理*',
      200974: '专用设备修理',
      200975: '专用设备修理*',
      200976: '铁路、船舶、航空航天等运输设备修理',
      200977: '铁路运输设备修理',
      200978: '船舶修理',
      200979: '航空航天器修理',
      200980: '其他运输设备修理',
      200981: '电气设备修理',
      200982: '电气设备修理*',
      200983: '仪器仪表修理',
      200984: '仪器仪表修理*',
      200985: '其他机械和设备修理业',
      200986: '其他机械和设备修理业*',
      200987: '电力、热力、燃气及水生产和供应业',
      200988: '电力、热力生产和供应业',
      200989: '电力生产',
      200990: '火力发电',
      200991: '热电联产',
      200992: '水力发电',
      200993: '核力发电',
      200994: '风力发电',
      200995: '太阳能发电',
      200996: '生物质能发电',
      200997: '其他电力生产',
      200998: '电力供应',
      200999: '电力供应*',
      201000: '热力生产和供应',
      201001: '热力生产和供应*',
      201002: '燃气生产和供应业',
      201003: '燃气生产和供应业*',
      201004: '天然气生产和供应业',
      201005: '液化石油气生产和供应业',
      201006: '煤气生产和供应业',
      201007: '生物质燃气生产和供应业',
      201008: '生物质燃气生产和供应业*',
      201009: '水的生产和供应业',
      201010: '自来水生产和供应',
      201011: '自来水生产和供应*',
      201012: '污水处理及其再生利用',
      201013: '污水处理及其再生利用*',
      201014: '海水淡化处理',
      201015: '海水淡化处理*',
      201016: '其他水的处理、利用与分配',
      201017: '其他水的处理、利用与分配*',
      201018: '建筑业',
      201019: '房屋建筑业',
      201020: '住宅房屋建筑',
      201021: '住宅房屋建筑*',
      201022: '体育场馆建筑',
      201023: '体育场馆建筑*',
      201024: '其他房屋建筑业',
      201025: '其他房屋建筑业*',
      201026: '土木工程建筑业',
      201027: '铁路、道路、隧道和桥梁工程建筑',
      201028: '铁路工程建筑',
      201029: '公路工程建筑',
      201030: '市政道路工程建筑',
      201031: '城市轨道交通工程建筑',
      201032: '其他道路、隧道和桥梁工程建筑',
      201033: '水利和水运工程建筑',
      201034: '水源及供水设施工程建筑',
      201035: '河湖治理及防洪设施工程建筑',
      201036: '港口及航运设施工程建筑',
      201037: '海洋工程建筑',
      201038: '海洋油气资源开发利用工程建筑',
      201039: '海洋能源开发利用工程建筑',
      201040: '海底隧道工程建筑',
      201041: '海底设施铺设工程建筑',
      201042: '其他海洋工程建筑',
      201043: '工矿工程建筑',
      201044: '工矿工程建筑*',
      201045: '架线和管道工程建筑',
      201046: '架线及设备工程建筑',
      201047: '管道工程建筑',
      201048: '地下综合管廊工程建筑',
      201049: '节能环保工程施工',
      201050: '节能工程施工',
      201051: '环保工程施工',
      201052: '生态保护工程施工',
      201053: '电力工程施工',
      201054: '火力发电工程施工',
      201055: '水力发电工程施工',
      201056: '核电工程施工',
      201057: '风能发电工程施工',
      201058: '太阳能发电工程施工',
      201059: '其他电力工程施工',
      201060: '其他土木工程建筑',
      201061: '园林绿化工程施工',
      201062: '体育场地设施工程施工',
      201063: '游乐设施工程施工',
      201064: '其他土木工程建筑施工',
      201065: '建筑安装业',
      201066: '电气安装',
      201067: '电气安装*',
      201068: '管道和设备安装',
      201069: '管道和设备安装*',
      201070: '其他建筑安装业',
      201071: '体育场地设施安装',
      201072: '其他建筑安装',
      201073: '建筑装饰、装修和其他建筑业',
      201074: '建筑装饰和装修业',
      201075: '公共建筑装饰和装修',
      201076: '住宅装饰和装修',
      201077: '建筑幕墙装饰和装修',
      201078: '建筑物拆除和场地准备活动',
      201079: '建筑物拆除活动',
      201080: '场地准备活动',
      201081: '提供施工设备服务',
      201082: '提供施工设备服务*',
      201083: '其他未列明建筑业',
      201084: '其他未列明建筑业*',
      201085: '批发和零售业',
      201086: '批发业',
      201087: '农、林、牧、渔产品批发',
      201088: '谷物、豆及薯类批发',
      201089: '种子批发',
      201090: '畜牧渔业饲料批发',
      201091: '棉、麻批发',
      201092: '林业产品批发',
      201093: '牲畜批发',
      201094: '渔业产品批发',
      201095: '其他农牧产品批发',
      201096: '食品、饮料及烟草制品批发',
      201097: '米、面制品及食用油批发',
      201098: '糕点、糖果及糖批发',
      201099: '果品、蔬菜批发',
      201100: '肉、禽、蛋、奶及水产品批发',
      201101: '盐及调味品批发',
      201102: '营养和保健品批发',
      201103: '酒、饮料及茶叶批发',
      201104: '烟草制品批发',
      201105: '其他食品批发',
      201106: '纺织、服装及家庭用品批发',
      201107: '纺织品、针织品及原料批发',
      201108: '服装批发',
      201109: '鞋帽批发',
      201110: '化妆品及卫生用品批发',
      201111: '厨具卫具及日用杂品批发',
      201112: '灯具、装饰物品批发',
      201113: '家用视听设备批发',
      201114: '日用家电批发',
      201115: '其他家庭用品批发',
      201116: '文化、体育用品及器材批发',
      201117: '文具用品批发',
      201118: '体育用品及器材批发',
      201119: '图书批发',
      201120: '报刊批发',
      201121: '音像制品、电子和数字出版物批发',
      201122: '首饰、工艺品及收藏品批发',
      201123: '乐器批发',
      201124: '其他文化用品批发',
      201125: '医药及医疗器材批发',
      201126: '西药批发',
      201127: '中药批发',
      201128: '动物用药品批发',
      201129: '医疗用品及器材批发',
      201130: '矿产品、建材及化工产品批发',
      201131: '煤炭及制品批发',
      201132: '石油及制品批发',
      201133: '非金属矿及制品批发',
      201134: '金属及金属矿批发',
      201135: '建材批发',
      201136: '化肥批发',
      201137: '农药批发',
      201138: '农用薄膜批发',
      201139: '其他化工产品批发',
      201140: '机械设备、五金产品及电子产品批发',
      201141: '农业机械批发',
      201142: '汽车及零配件批发',
      201143: '摩托车及零配件批发',
      201144: '五金产品批发',
      201145: '电气设备批发',
      201146: '计算机、软件及辅助设备批发',
      201147: '通讯设备批发',
      201148: '广播影视设备批发',
      201149: '其他机械设备及电子产品批发',
      201150: '贸易经纪与代理',
      201151: '贸易代理',
      201152: '一般物品拍卖',
      201153: '艺术品、收藏品拍卖',
      201154: '艺术品代理',
      201155: '其他贸易经纪与代理',
      201156: '其他批发业',
      201157: '再生物资回收与批发',
      201158: '宠物食品用品批发',
      201159: '互联网批发',
      201160: '其他未列明批发业',
      201161: '零售业',
      201162: '综合零售',
      201163: '百货零售',
      201164: '超级市场零售',
      201165: '便利店零售',
      201166: '其他综合零售',
      201167: '食品、饮料及烟草制品专门零售',
      201168: '粮油零售',
      201169: '糕点、面包零售',
      201170: '果品、蔬菜零售',
      201171: '肉、禽、蛋、奶及水产品零售',
      201172: '营养和保健品零售',
      201173: '酒、饮料及茶叶零售',
      201174: '烟草制品零售',
      201175: '其他食品零售',
      201176: '纺织、服装及日用品专门零售',
      201177: '纺织品及针织品零售',
      201178: '服装零售',
      201179: '鞋帽零售',
      201180: '化妆品及卫生用品零售',
      201181: '厨具卫具及日用杂品零售',
      201182: '钟表、眼镜零售',
      201183: '箱包零售',
      201184: '自行车等代步设备零售',
      201185: '其他日用品零售',
      201186: '文化、体育用品及器材专门零售',
      201187: '文具用品零售',
      201188: '体育用品及器材零售',
      201189: '图书、报刊零售',
      201190: '音像制品、电子和数字出版物零售',
      201191: '珠宝首饰零售',
      201192: '工艺美术品及收藏品零售',
      201193: '乐器零售',
      201194: '照相器材零售',
      201195: '其他文化用品零售',
      201196: '医药及医疗器材专门零售',
      201197: '西药零售',
      201198: '中药零售',
      201199: '动物用药品零售',
      201200: '医疗用品及器材零售',
      201201: '保健辅助治疗器材零售',
      201202: '汽车、摩托车、零配件和燃料及其他动力销售',
      201203: '汽车新车零售',
      201204: '汽车旧车零售',
      201205: '汽车零配件零售',
      201206: '摩托车及零配件零售',
      201207: '机动车燃油零售',
      201208: '机动车燃气零售',
      201209: '机动车充电销售',
      201210: '家用电器及电子产品专门零售',
      201211: '家用视听设备零售',
      201212: '日用家电零售',
      201213: '计算机、软件及辅助设备零售',
      201214: '通信设备零售',
      201215: '其他电子产品零售',
      201216: '五金、家具及室内装饰材料专门零售',
      201217: '五金零售',
      201218: '灯具零售',
      201219: '家具零售',
      201220: '涂料零售',
      201221: '卫生洁具零售',
      201222: '木质装饰材料零售',
      201223: '陶瓷、石材装饰材料零售',
      201224: '其他室内装饰材料零售',
      201225: '货摊、无店铺及其他零售业',
      201226: '流动货摊零售',
      201227: '互联网零售',
      201228: '邮购及电视、电话零售',
      201229: '自动售货机零售',
      201230: '旧货零售',
      201231: '生活用燃料零售',
      201232: '宠物食品用品零售',
      201233: '其他未列明零售业',
      201234: '交通运输、仓储和邮政业',
      201235: '铁路运输业',
      201236: '铁路旅客运输',
      201237: '高速铁路旅客运输',
      201238: '城际铁路旅客运输',
      201239: '普通铁路旅客运输',
      201240: '铁路货物运输',
      201241: '铁路货物运输*',
      201242: '铁路运输辅助活动',
      201243: '客运火车站',
      201244: '货运火车站（场）',
      201245: '铁路运输维护活动',
      201246: '其他铁路运输辅助活动',
      201247: '道路运输业',
      201248: '城市公共交通运输',
      201249: '公共电汽车客运',
      201250: '城市轨道交通',
      201251: '出租车客运',
      201252: '公共自行车服务',
      201253: '其他城市公共交通运输',
      201254: '公路旅客运输',
      201255: '长途客运',
      201256: '旅游客运',
      201257: '其他公路客运',
      201258: '道路货物运输',
      201259: '普通货物道路运输',
      201260: '冷藏车道路运输',
      201261: '集装箱道路运输',
      201262: '大型货物道路运输',
      201263: '危险货物道路运输',
      201264: '邮件包裹道路运输',
      201265: '城市配送',
      201266: '搬家运输',
      201267: '其他道路货物运输',
      201268: '道路运输辅助活动',
      201269: '客运汽车站',
      201270: '货运枢纽（站）',
      201271: '公路管理与养护',
      201272: '其他道路运输辅助活动',
      201273: '水上运输业',
      201274: '水上旅客运输',
      201275: '海上旅客运输',
      201276: '内河旅客运输',
      201277: '客运轮渡运输',
      201278: '水上货物运输',
      201279: '远洋货物运输',
      201280: '沿海货物运输',
      201281: '内河货物运输',
      201282: '水上运输辅助活动',
      201283: '客运港口',
      201284: '货运港口',
      201285: '其他水上运输辅助活动',
      201286: '航空运输业',
      201287: '航空客货运输',
      201288: '航空旅客运输',
      201289: '航空货物运输',
      201290: '通用航空服务',
      201291: '通用航空生产服务',
      201292: '观光游览航空服务',
      201293: '体育航空运动服务',
      201294: '其他通用航空服务',
      201295: '航空运输辅助活动',
      201296: '机场',
      201297: '空中交通管理',
      201298: '其他航空运输辅助活动',
      201299: '管道运输业',
      201300: '海底管道运输',
      201301: '海底管道运输*',
      201302: '陆地管道运输',
      201303: '陆地管道运输*',
      201304: '多式联运和运输代理业',
      201305: '多式联运',
      201306: '多式联运*',
      201307: '运输代理业',
      201308: '货物运输代理',
      201309: '旅客票务代理',
      201310: '其他运输代理业',
      201311: '装卸搬运和仓储业',
      201312: '装卸搬运',
      201313: '装卸搬运*',
      201314: '通用仓储',
      201315: '通用仓储*',
      201316: '低温仓储',
      201317: '低温仓储*',
      201318: '危险品仓储',
      201319: '油气仓储',
      201320: '危险化学品仓储',
      201321: '其他危险品仓储',
      201322: '谷物、棉花等农产品仓储',
      201323: '谷物仓储',
      201324: '棉花仓储',
      201325: '其他农产品仓储',
      201326: '中药材仓储',
      201327: '中药材仓储*',
      201328: '其他仓储业',
      201329: '其他仓储业*',
      201330: '邮政业',
      201331: '邮政基本服务',
      201332: '邮政基本服务*',
      201333: '快递服务',
      201334: '快递服务*',
      201335: '其他寄递服务',
      201336: '其他寄递服务*',
      201337: '住宿和餐饮业',
      201338: '住宿业',
      201339: '旅游饭店',
      201340: '一般旅馆',
      201341: '经济型连锁酒店',
      201342: '其他一般旅馆',
      201343: '民宿服务',
      201344: '民宿服务*',
      201345: '露营地服务',
      201346: '露营地服务*',
      201347: '其他住宿业',
      201348: '其他住宿业*',
      201349: '餐饮业',
      201350: '正餐服务',
      201351: '正餐服务*',
      201352: '快餐服务',
      201353: '快餐服务*',
      201354: '饮料及冷饮服务',
      201355: '茶馆服务',
      201356: '咖啡馆服务',
      201357: '酒吧服务',
      201358: '其他饮料及冷饮服务',
      201359: '餐饮配送及外卖送餐服务',
      201360: '餐饮配送服务',
      201361: '外卖送餐服务',
      201362: '其他餐饮业',
      201363: '小吃服务',
      201364: '其他未列明餐饮业',
      201365: '信息传输、软件和信息技术服务业',
      201366: '电信、广播电视和卫星传输服务',
      201367: '电信',
      201368: '固定电信服务',
      201369: '移动电信服务',
      201370: '其他电信服务',
      201371: '广播电视传输服务',
      201372: '有线广播电视传输服务',
      201373: '无线广播电视传输服务',
      201374: '卫星传输服务',
      201375: '广播电视卫星传输服务',
      201376: '其他卫星传输服务',
      201377: '互联网和相关服务',
      201378: '互联网接入及相关服务',
      201379: '互联网接入及相关服务*',
      201380: '互联网信息服务',
      201381: '互联网搜索服务',
      201382: '互联网游戏服务',
      201383: '互联网其他信息服务',
      201384: '互联网平台',
      201385: '互联网生产服务平台',
      201386: '互联网生活服务平台',
      201387: '互联网科技创新平台',
      201388: '互联网公共服务平台',
      201389: '其他互联网平台',
      201390: '互联网安全服务',
      201391: '互联网安全服务*',
      201392: '互联网数据服务',
      201393: '互联网数据服务*',
      201394: '其他互联网服务',
      201395: '其他互联网服务*',
      201396: '软件和信息技术服务业',
      201397: '软件开发',
      201398: '基础软件开发',
      201399: '支撑软件开发',
      201400: '应用软件开发',
      201401: '其他软件开发',
      201402: '集成电路设计',
      201403: '集成电路设计*',
      201404: '信息系统集成和物联网技术服务',
      201405: '信息系统集成服务',
      201406: '物联网技术服务',
      201407: '运行维护服务',
      201408: '运行维护服务*',
      201409: '信息处理和存储支持服务',
      201410: '信息处理和存储支持服务*',
      201411: '信息技术咨询服务',
      201412: '信息技术咨询服务*',
      201413: '数字内容服务',
      201414: '地理遥感信息服务',
      201415: '动漫、游戏数字内容服务',
      201416: '其他数字内容服务',
      201417: '其他信息技术服务业',
      201418: '呼叫中心',
      201419: '其他未列明信息技术服务业',
      201420: '金融业',
      201421: '货币金融服务',
      201422: '中央银行服务',
      201423: '中央银行服务*',
      201424: '货币银行服务',
      201425: '商业银行服务',
      201426: '政策性银行服务',
      201427: '信用合作社服务',
      201428: '农村资金互助社服务',
      201429: '其他货币银行服务',
      201430: '非货币银行服务',
      201431: '融资租赁服务',
      201432: '财务公司服务',
      201433: '典当',
      201434: '汽车金融公司服务',
      201435: '小额贷款公司服务',
      201436: '消费金融公司服务',
      201437: '网络借贷服务',
      201438: '其他非货币银行服务',
      201439: '银行理财服务',
      201440: '银行理财服务*',
      201441: '银行监管服务',
      201442: '银行监管服务*',
      201443: '资本市场服务',
      201444: '证券市场服务',
      201445: '证券市场管理服务',
      201446: '证券经纪交易服务',
      201447: '公开募集证券投资基金',
      201448: '公开募集证券投资基金*',
      201449: '非公开募集证券投资基金',
      201450: '创业投资基金',
      201451: '天使投资',
      201452: '其他非公开募集证券投资基金',
      201453: '期货市场服务',
      201454: '期货市场管理服务',
      201455: '其他期货市场服务',
      201456: '证券期货监管服务',
      201457: '证券期货监管服务*',
      201458: '资本投资服务',
      201459: '资本投资服务*',
      201460: '其他资本市场服务',
      201461: '其他资本市场服务*',
      201462: '保险业',
      201463: '人身保险',
      201464: '人寿保险',
      201465: '年金保险',
      201466: '健康保险',
      201467: '意外伤害保险',
      201468: '财产保险',
      201469: '财产保险*',
      201470: '再保险',
      201471: '再保险*',
      201472: '商业养老金',
      201473: '商业养老金*',
      201474: '保险中介服务',
      201475: '保险经纪服务',
      201476: '保险代理服务',
      201477: '保险公估服务',
      201478: '保险资产管理',
      201479: '保险资产管理*',
      201480: '保险监管服务',
      201481: '保险监管服务*',
      201482: '其他保险活动',
      201483: '其他保险活动*',
      201484: '其他金融业',
      201485: '金融信托与管理服务',
      201486: '信托公司',
      201487: '其他金融信托与管理服务',
      201488: '控股公司服务',
      201489: '控股公司服务*',
      201490: '非金融机构支付服务',
      201491: '非金融机构支付服务*',
      201492: '金融信息服务',
      201493: '金融信息服务*',
      201494: '金融资产管理公司',
      201495: '金融资产管理公司*',
      201496: '其他未列明金融业',
      201497: '货币经纪公司服务',
      201498: '其他未包括金融业',
      201499: '房地产业',
      201500: '房地产业*',
      201501: '房地产开发经营',
      201502: '房地产开发经营*',
      201503: '物业管理',
      201504: '物业管理*',
      201505: '房地产中介服务',
      201506: '房地产中介服务*',
      201507: '房地产租赁经营',
      201508: '房地产租赁经营*',
      201509: '其他房地产业',
      201510: '其他房地产业*',
      201511: '租赁和商务服务业',
      201512: '租赁业',
      201513: '机械设备经营租赁',
      201514: '汽车租赁',
      201515: '农业机械经营租赁',
      201516: '建筑工程机械与设备经营租赁',
      201517: '计算机及通讯设备经营租赁',
      201518: '医疗设备经营租赁',
      201519: '其他机械与设备经营租赁',
      201520: '文体设备和用品出租',
      201521: '休闲娱乐用品设备出租',
      201522: '体育用品设备出租',
      201523: '文化用品设备出租',
      201524: '图书出租',
      201525: '音像制品出租',
      201526: '其他文体设备和用品出租',
      201527: '日用品出租',
      201528: '日用品出租*',
      201529: '商务服务业',
      201530: '组织管理服务',
      201531: '企业总部管理',
      201532: '投资与资产管理',
      201533: '资源与产权交易服务',
      201534: '单位后勤管理服务',
      201535: '农村集体经济组织管理',
      201536: '其他组织管理服务',
      201537: '综合管理服务',
      201538: '园区管理服务',
      201539: '商业综合体管理服务',
      201540: '市场管理服务',
      201541: '供应链管理服务',
      201542: '其他综合管理服务',
      201543: '法律服务',
      201544: '律师及相关法律服务',
      201545: '公证服务',
      201546: '其他法律服务',
      201547: '咨询与调查',
      201548: '会计、审计及税务服务',
      201549: '市场调查',
      201550: '社会经济咨询',
      201551: '健康咨询',
      201552: '环保咨询',
      201553: '体育咨询',
      201554: '其他专业咨询与调查',
      201555: '广告业',
      201556: '互联网广告服务',
      201557: '其他广告服务',
      201558: '人力资源服务',
      201559: '公共就业服务',
      201560: '职业中介服务',
      201561: '劳务派遣服务',
      201562: '创业指导服务',
      201563: '其他人力资源服务',
      201564: '安全保护服务',
      201565: '安全服务',
      201566: '安全系统监控服务',
      201567: '其他安全保护服务',
      201568: '会议、展览及相关服务',
      201569: '科技会展服务',
      201570: '旅游会展服务',
      201571: '体育会展服务',
      201572: '文化会展服务',
      201573: '其他会议、展览及相关服务',
      201574: '其他商务服务业',
      201575: '旅行社及相关服务',
      201576: '包装服务',
      201577: '办公服务',
      201578: '翻译服务',
      201579: '信用服务',
      201580: '非融资担保服务',
      201581: '商务代理代办服务',
      201582: '票务代理服务',
      201583: '其他未列明商务服务业',
      201584: '科学研究和技术服务业',
      201585: '研究和试验发展',
      201586: '自然科学研究和试验发展',
      201587: '自然科学研究和试验发展*',
      201588: '工程和技术研究和试验发展',
      201589: '工程和技术研究和试验发展*',
      201590: '农业科学研究和试验发展',
      201591: '农业科学研究和试验发展*',
      201592: '医学研究和试验发展',
      201593: '医学研究和试验发展*',
      201594: '社会人文科学研究',
      201595: '社会人文科学研究*',
      201596: '专业技术服务业',
      201597: '气象服务',
      201598: '气象服务*',
      201599: '地震服务',
      201600: '地震服务*',
      201601: '海洋服务',
      201602: '海洋气象服务',
      201603: '海洋环境服务',
      201604: '其他海洋服务',
      201605: '测绘地理信息服务',
      201606: '遥感测绘服务',
      201607: '其他测绘地理信息服务',
      201608: '质检技术服务',
      201609: '检验检疫服务',
      201610: '检测服务',
      201611: '计量服务',
      201612: '标准化服务',
      201613: '认证认可服务',
      201614: '其他质检技术服务',
      201615: '环境与生态监测',
      201616: '环境保护监测',
      201617: '生态资源监测',
      201618: '野生动物疫源疫病防控监测',
      201619: '地质勘查',
      201620: '能源矿产地质勘查',
      201621: '固体矿产地质勘查',
      201622: '水、二氧化碳等矿产地质勘查',
      201623: '基础地质勘查',
      201624: '地质勘查技术服务',
      201625: '工程技术与设计服务',
      201626: '工程管理服务',
      201627: '工程监理服务',
      201628: '工程勘察活动',
      201629: '工程设计活动',
      201630: '规划设计管理',
      201631: '土地规划服务',
      201632: '工业与专业设计及其他专业技术服务',
      201633: '工业设计服务',
      201634: '专业设计服务',
      201635: '兽医服务',
      201636: '其他未列明专业技术服务业',
      201637: '科技推广和应用服务',
      201638: '技术推广服务',
      201639: '农林牧渔技术推广服务',
      201640: '生物技术推广服务',
      201641: '新材料技术推广服务',
      201642: '节能技术推广服务',
      201643: '新能源技术推广服务',
      201644: '环保技术推广服务',
      201645: '三维（3D)打印技术推广服务',
      201646: '其他技术推广服务',
      201647: '知识产权服务',
      201648: '知识产权服务*',
      201649: '科技中介服务',
      201650: '科技中介服务*',
      201651: '创业空间服务',
      201652: '创业空间服务*',
      201653: '其他科技推广服务业',
      201654: '其他科技推广服务业*',
      201655: '水利、环境和公共设施管理业',
      201656: '水利管理业',
      201657: '防洪除涝设施管理',
      201658: '防洪除涝设施管理*',
      201659: '水资源管理',
      201660: '水资源管理*',
      201661: '天然水收集与分配',
      201662: '天然水收集与分配*',
      201663: '水文服务',
      201664: '水文服务*',
      201665: '其他水利管理业',
      201666: '其他水利管理业*',
      201667: '生态保护和环境治理业',
      201668: '生态保护',
      201669: '自然生态系统保护管理',
      201670: '自然遗迹保护管理',
      201671: '野生动物保护',
      201672: '野生植物保护',
      201673: '动物园、水族馆管理服务',
      201674: '植物园管理服务',
      201675: '其他自然保护',
      201676: '环境治理业',
      201677: '水污染治理',
      201678: '大气污染治理',
      201679: '固体废物治理',
      201680: '危险废物治理',
      201681: '放射性废物治理',
      201682: '土壤污染治理与修复服务',
      201683: '噪声与振动控制服务',
      201684: '其他污染治理',
      201685: '公共设施管理业',
      201686: '市政设施管理',
      201687: '市政设施管理*',
      201688: '环境卫生管理',
      201689: '环境卫生管理*',
      201690: '城乡市容管理',
      201691: '城乡市容管理*',
      201692: '绿化管理',
      201693: '绿化管理*',
      201694: '城市公园管理',
      201695: '城市公园管理*',
      201696: '游览景区管理',
      201697: '名胜风景区管理',
      201698: '森林公园管理',
      201699: '其他游览景区管理',
      201700: '土地管理业',
      201701: '土地整治服务',
      201702: '土地整治服务*',
      201703: '土地调查评估服务',
      201704: '土地调查评估服务*',
      201705: '土地登记服务',
      201706: '土地登记服务*',
      201707: '土地登记代理服务',
      201708: '土地登记代理服务*',
      201709: '其他土地管理服务',
      201710: '其他土地管理服务*',
      201711: '居民服务、修理和其他服务业',
      201712: '居民服务业',
      201713: '家庭服务',
      201714: '家庭服务*',
      201715: '托儿所服务',
      201716: '托儿所服务*',
      201717: '洗染服务',
      201718: '洗染服务*',
      201719: '理发及美容服务',
      201720: '理发及美容服务*',
      201721: '洗浴和保健养生服务',
      201722: '洗浴服务',
      201723: '足浴服务',
      201724: '养生保健服务',
      201725: '摄影扩印服务',
      201726: '摄影扩印服务*',
      201727: '婚姻服务',
      201728: '婚姻服务*',
      201729: '殡葬服务',
      201730: '殡葬服务*',
      201731: '其他居民服务业',
      201732: '其他居民服务业*',
      201733: '机动车、电子产品和日用产品修理业',
      201734: '汽车、摩托车等修理与维护',
      201735: '汽车修理与维护',
      201736: '大型车辆装备修理与维护',
      201737: '摩托车修理与维护',
      201738: '助动车等修理与维护',
      201739: '计算机和办公设备维修',
      201740: '计算机和辅助设备修理',
      201741: '通讯设备修理',
      201742: '其他办公设备维修',
      201743: '家用电器修理',
      201744: '家用电子产品修理',
      201745: '日用电器修理',
      201746: '其他日用产品修理业',
      201747: '自行车修理',
      201748: '鞋和皮革修理',
      201749: '家具和相关物品修理',
      201750: '其他未列明日用产品修理业',
      201751: '其他服务业',
      201752: '清洁服务',
      201753: '建筑物清洁服务',
      201754: '其他清洁服务',
      201755: '宠物服务',
      201756: '宠物饲养',
      201757: '宠物医院服务',
      201758: '宠物美容服务',
      201759: '宠物寄托收养服务',
      201760: '其他宠物服务',
      201761: '其他未列明服务业',
      201762: '其他未列明服务业*',
      201763: '教育',
      201764: '教育*',
      201765: '学前教育',
      201766: '学前教育*',
      201767: '初等教育',
      201768: '普通小学教育',
      201769: '成人小学教育',
      201770: '中等教育',
      201771: '普通初中教育',
      201772: '职业初中教育',
      201773: '成人初中教育',
      201774: '普通高中教育',
      201775: '成人高中教育',
      201776: '中等职业学校教育',
      201777: '高等教育',
      201778: '普通高等教育',
      201779: '成人高等教育',
      201780: '特殊教育',
      201781: '特殊教育*',
      201782: '技能培训、教育辅助及其他教育',
      201783: '职业技能培训',
      201784: '体校及体育培训',
      201785: '文化艺术培训',
      201786: '教育辅助服务',
      201787: '其他未列明教育',
      201788: '卫生和社会工作',
      201789: '卫生',
      201790: '医院',
      201791: '综合医院',
      201792: '中医医院',
      201793: '中西医结合医院',
      201794: '民族医院',
      201795: '专科医院',
      201796: '疗养院',
      201797: '基层医疗卫生服务',
      201798: '社区卫生服务中心（站）',
      201799: '街道卫生院',
      201800: '乡镇卫生院',
      201801: '村卫生室',
      201802: '门诊部（所）',
      201803: '专业公共卫生服务',
      201804: '疾病预防控制中心',
      201805: '专科疾病防治院（所、站)',
      201806: '妇幼保健院（所、站）',
      201807: '急救中心（站）服务',
      201808: '采供血机构服务',
      201809: '计划生育技术服务活动',
      201810: '其他卫生活动',
      201811: '健康体检服务',
      201812: '临床检验服务',
      201813: '其他未列明卫生服务',
      201814: '社会工作',
      201815: '提供住宿社会工作',
      201816: '干部休养所',
      201817: '护理机构服务',
      201818: '精神康复服务',
      201819: '老年人、残疾人养护服务',
      201820: '临终关怀服务',
      201821: '孤残儿童收养和庇护服务',
      201822: '其他提供住宿社会救助',
      201823: '不提供住宿社会工作',
      201824: '社会看护与帮助服务',
      201825: '康复辅具适配服务',
      201826: '其他不提供住宿社会工作',
      201827: '文化、体育和娱乐业',
      201828: '新闻和出版业',
      201829: '新闻业',
      201830: '新闻业*',
      201831: '出版业',
      201832: '图书出版',
      201833: '报纸出版',
      201834: '期刊出版',
      201835: '音像制品出版',
      201836: '电子出版物出版',
      201837: '数字出版',
      201838: '其他出版业',
      201839: '广播、电视、电影和影视录音制作业',
      201840: '广播',
      201841: '广播*',
      201842: '电视',
      201843: '电视*',
      201844: '影视节目制作',
      201845: '影视节目制作*',
      201846: '广播电视集成播控',
      201847: '广播电视集成播控*',
      201848: '电影和广播电视节目发行',
      201849: '电影和广播电视节目发行*',
      201850: '电影放映',
      201851: '电影放映*',
      201852: '录音制作',
      201853: '录音制作*',
      201854: '文化艺术业',
      201855: '文艺创作与表演',
      201856: '文艺创作与表演*',
      201857: '艺术表演场馆',
      201858: '艺术表演场馆*',
      201859: '图书馆与档案馆',
      201860: '图书馆',
      201861: '档案馆',
      201862: '文物及非物质文化遗产保护',
      201863: '文物及非物质文化遗产保护*',
      201864: '博物馆',
      201865: '博物馆*',
      201866: '烈士陵园、纪念馆',
      201867: '烈士陵园、纪念馆*',
      201868: '群众文体活动',
      201869: '群众文体活动*',
      201870: '其他文化艺术业',
      201871: '其他文化艺术业*',
      201872: '体育',
      201873: '体育组织',
      201874: '体育竞赛组织',
      201875: '体育保障组织',
      201876: '其他体育组织',
      201877: '体育场地设施管理',
      201878: '体育场馆管理',
      201879: '其他体育场地设施管理',
      201880: '健身休闲活动',
      201881: '健身休闲活动*',
      201882: '其他体育',
      201883: '体育中介代理服务',
      201884: '体育健康服务',
      201885: '其他未列明体育',
      201886: '娱乐业',
      201887: '室内娱乐活动',
      201888: '歌舞厅娱乐活动',
      201889: '电子游艺厅娱乐活动',
      201890: '网吧活动',
      201891: '其他室内娱乐活动',
      201892: '游乐园',
      201893: '游乐园*',
      201894: '休闲观光活动',
      201895: '休闲观光活动*',
      201896: '彩票活动',
      201897: '体育彩票服务',
      201898: '福利彩票服务',
      201899: '其他彩票服务',
      201900: '文化娱乐体育活动和经纪代理服务',
      201901: '文化活动服务',
      201902: '体育表演服务',
      201903: '文化娱乐经纪人',
      201904: '体育经纪人',
      201905: '其他文化艺术经纪代理',
      201906: '其他娱乐业',
      201907: '其他娱乐业*',
      201908: '公共管理、社会保障和社会组织',
      201909: '中国共产党机关',
      201910: '中国共产党机关*',
      201911: '中国共产党机关**',
      201912: '国家机构',
      201913: '国家权力机构',
      201914: '国家权力机构*',
      201915: '国家行政机构',
      201916: '综合事务管理机构',
      201917: '对外事务管理机构',
      201918: '公共安全管理机构',
      201919: '社会事务管理机构',
      201920: '经济事务管理机构',
      201921: '行政监督检查机构',
      201922: '人民法院和人民检察院',
      201923: '人民法院',
      201924: '人民检察院',
      201925: '其他国家机构',
      201926: '消防管理机构',
      201927: '其他未列明国家机构',
      201928: '人民政协、民主党派',
      201929: '人民政协',
      201930: '人民政协*',
      201931: '民主党派',
      201932: '民主党派*',
      201933: '社会保障',
      201934: '基本保险',
      201935: '基本养老保险',
      201936: '基本医疗保险',
      201937: '失业保险',
      201938: '工伤保险',
      201939: '生育保险',
      201940: '其他基本保险',
      201941: '补充保险',
      201942: '补充保险*',
      201943: '其他社会保障',
      201944: '其他社会保障*',
      201945: '群众团体、社会团体和其他成员组织',
      201946: '群众团体',
      201947: '工会',
      201948: '妇联',
      201949: '共青团',
      201950: '其他群众团体',
      201951: '社会团体',
      201952: '专业性团体',
      201953: '行业性团体',
      201954: '其他社会团体',
      201955: '基金会',
      201956: '基金会*',
      201957: '宗教组织',
      201958: '宗教团体服务',
      201959: '宗教活动场所服务',
      201960: '基层群众自治组织及其他组织',
      201961: '社区居民自治组织',
      201962: '社区居民自治组织*',
      201963: '村民自治组织',
      201964: '村民自治组织*',
      201965: '国际组织',
      201966: '国际组织*',
      201967: '国际组织**',
      201968: '国际组织***',
    };
    return codeObj[value] || '';
  }
  /** 获取职业病 3.16
   * @param {String} value 职业病编码
   * @return {String} 职业病名称
   */
  getOccupationalDisease(value) {
    // value转换为数字
    value = +value;
    const occdiseObj = {
      1006: '职业性尘肺病及其他呼吸系统疾病',
      1007: '其他职业病',
      2001: '职业性皮肤病',
      3001: '职业性眼病',
      4001: '职业性耳鼻喉口腔疾病',
      5000: '职业性化学中毒',
      6001: '物理因素所致职业病',
      7100: '职业性放射性疾病',
      8001: '职业性传染病',
      9100: '职业性肿瘤',
      9999: '职业卫生现场',
    };
    return occdiseObj[value] || '';
  }
  /** 获取规模的 3.5
   * @param {String} value 规模编码
   * @return {String} 规模名称
   */
  getScale(value) {
    // value转换为数字
    value = +value;
    const scaleObj = {
      10000: '大',
      10001: '中',
      10002: '小',
      10003: '微',
    };
    return scaleObj[value] || '';
  }
  /**
   * 过滤对象中的空字段
   * @param {Object} obj - 需要过滤的对象
   * @return {Object} - 过滤后的对象
   */
  filterEmptyFields(obj) {
    const result = {};
    for (const key in obj) {
      if (obj[key] !== '') {
        result[key] = obj[key];
      }
    }
    return result;
  }
  // #endregion

  /** 随机判断是否抛出异常用于调试异常情况
   * @param {Number} rate - 异常概率
   */
  randomThrowError(rate) {
    if (Math.random() < rate) {
      throw new Error('随机抛出异常');
    }
  }

  /** 获取企业地址
   * @param value  code
   * @return {Array} 企业地址
   */
  getAddress(value) {
    value = +value;
    const obj = {
      3501000000: '福建省_福州市',

      3501020000: '福建省_福州市_鼓楼区',

      3501020100: '福建省_福州市_鼓楼区_温泉街道',

      3501020200: '福建省_福州市_鼓楼区_鼓东街道',

      3501020300: '福建省_福州市_鼓楼区_东街街道',

      3501020400: '福建省_福州市_鼓楼区_水部街道',

      3501020500: '福建省_福州市_鼓楼区_安泰街道',

      3501020600: '福建省_福州市_鼓楼区_南街街道',

      3501020700: '福建省_福州市_鼓楼区_鼓西街道',

      3501020800: '福建省_福州市_鼓楼区_华大街道',

      3501020900: '福建省_福州市_鼓楼区_五凤街道',

      3501021000: '福建省_福州市_鼓楼区_洪山镇',

      3501030000: '福建省_福州市_台江区',

      3501030100: '福建省_福州市_台江区_茶亭街道',

      3501030200: '福建省_福州市_台江区_洋中街道',

      3501030300: '福建省_福州市_台江区_苍霞街道',

      3501030500: '福建省_福州市_台江区_义洲街道',

      3501030600: '福建省_福州市_台江区_后洲街道',

      3501030700: '福建省_福州市_台江区_瀛洲街道',

      3501030800: '福建省_福州市_台江区_鳌峰街道',

      3501030900: '福建省_福州市_台江区_上海街道',

      3501031000: '福建省_福州市_台江区_新港街道',

      3501031100: '福建省_福州市_台江区_宁化街道',

      3501040000: '福建省_福州市_仓山区',

      3501040100: '福建省_福州市_仓山区_临江街道',

      3501040200: '福建省_福州市_仓山区_仓前街道',

      3501040300: '福建省_福州市_仓山区_对湖街道',

      3501040400: '福建省_福州市_仓山区_下渡街道',

      3501040500: '福建省_福州市_仓山区_上渡街道',

      3501040600: '福建省_福州市_仓山区_三叉街街道',

      3501040700: '福建省_福州市_仓山区_盖山镇',

      3501040800: '福建省_福州市_仓山区_城门镇',

      3501040900: '福建省_福州市_仓山区_建新镇',

      3501041000: '福建省_福州市_仓山区_仓山镇',

      3501041100: '福建省_福州市_仓山区_螺洲镇',

      3501041200: '福建省_福州市_仓山区_东升街道',

      3501041300: '福建省_福州市_仓山区_金山街道',

      3501050000: '福建省_福州市_马尾区',

      3501050100: '福建省_福州市_马尾区_罗星街道',

      3501050200: '福建省_福州市_马尾区_马尾镇',

      3501050300: '福建省_福州市_马尾区_亭江镇',

      3501050400: '福建省_福州市_马尾区_琅岐镇',

      3501110000: '福建省_福州市_晋安区',

      3501110100: '福建省_福州市_晋安区_王庄街道',

      3501110200: '福建省_福州市_晋安区_象园街道',

      3501110300: '福建省_福州市_晋安区_茶园街道',

      3501110400: '福建省_福州市_晋安区_岳峰镇',

      3501110500: '福建省_福州市_晋安区_鼓山镇',

      3501110600: '福建省_福州市_晋安区_新店镇',

      3501110700: '福建省_福州市_晋安区_宦溪镇',

      3501110900: '福建省_福州市_晋安区_寿山乡',

      3501111000: '福建省_福州市_晋安区_日溪乡',

      3501210000: '福建省_福州市_闽侯县',

      3501210100: '福建省_福州市_闽侯县_甘蔗镇',

      3501210200: '福建省_福州市_闽侯县_荆溪镇',

      3501210300: '福建省_福州市_闽侯县_上街镇',

      3501210400: '福建省_福州市_闽侯县_南屿镇',

      3501210500: '福建省_福州市_闽侯县_南通镇',

      3501210600: '福建省_福州市_闽侯县_尚干镇',

      3501210700: '福建省_福州市_闽侯县_祥谦镇',

      3501210800: '福建省_福州市_闽侯县_青口镇',

      3501210900: '福建省_福州市_闽侯县_白沙镇',

      3501211100: '福建省_福州市_闽侯县_竹岐乡',

      3501211200: '福建省_福州市_闽侯县_鸿尾乡',

      3501211300: '福建省_福州市_闽侯县_大湖乡',

      3501211400: '福建省_福州市_闽侯县_洋里乡',

      3501211500: '福建省_福州市_闽侯县_廷坪乡',

      3501211700: '福建省_福州市_闽侯县_小箬乡',

      3501220000: '福建省_福州市_连江县',

      3501220100: '福建省_福州市_连江县_凤城镇',

      3501220200: '福建省_福州市_连江县_敖江镇',

      3501220300: '福建省_福州市_连江县_官头镇',

      3501220400: '福建省_福州市_连江县_东湖镇',

      3501220500: '福建省_福州市_连江县_丹阳镇',

      3501220600: '福建省_福州市_连江县_蓼沿乡',

      3501220700: '福建省_福州市_连江县_小沧乡',

      3501220800: '福建省_福州市_连江县_潘渡乡',

      3501220900: '福建省_福州市_连江县_长龙镇',

      3501221000: '福建省_福州市_连江县_晓澳镇',

      3501221100: '福建省_福州市_连江县_浦口镇',

      3501221200: '福建省_福州市_连江县_东岱镇',

      3501221300: '福建省_福州市_连江县_筱埕镇',

      3501221400: '福建省_福州市_连江县_坑园镇',

      3501221500: '福建省_福州市_连江县_下宫乡',

      3501221600: '福建省_福州市_连江县_官坂镇',

      3501221700: '福建省_福州市_连江县_马鼻镇',

      3501221800: '福建省_福州市_连江县_透堡镇',

      3501221900: '福建省_福州市_连江县_安凯乡',

      3501222000: '福建省_福州市_连江县_黄岐镇',

      3501222100: '福建省_福州市_连江县_苔录镇',

      3501222200: '福建省_福州市_连江县_江南乡',

      3501230000: '福建省_福州市_罗源县',

      3501230100: '福建省_福州市_罗源县_凤山镇',

      3501230200: '福建省_福州市_罗源县_起步镇',

      3501230300: '福建省_福州市_罗源县_洪洋乡',

      3501230400: '福建省_福州市_罗源县_中房镇',

      3501230500: '福建省_福州市_罗源县_白塔乡',

      3501230600: '福建省_福州市_罗源县_西兰乡',

      3501230700: '福建省_福州市_罗源县_飞竹镇',

      3501230800: '福建省_福州市_罗源县_霍口乡',

      3501230900: '福建省_福州市_罗源县_松山镇',

      3501231000: '福建省_福州市_罗源县_鉴江镇',

      3501231100: '福建省_福州市_罗源县_碧里乡',

      3501240000: '福建省_福州市_闽清县',

      3501240100: '福建省_福州市_闽清县_梅城镇',

      3501240200: '福建省_福州市_闽清县_梅溪镇',

      3501240300: '福建省_福州市_闽清县_云龙乡',

      3501240400: '福建省_福州市_闽清县_白樟镇',

      3501240500: '福建省_福州市_闽清县_金沙镇',

      3501240600: '福建省_福州市_闽清县_白中镇',

      3501240700: '福建省_福州市_闽清县_池园镇',

      3501240800: '福建省_福州市_闽清县_上莲乡',

      3501241000: '福建省_福州市_闽清县_坂东镇',

      3501241100: '福建省_福州市_闽清县_三溪乡',

      3501241200: '福建省_福州市_闽清县_塔庄镇',

      3501241300: '福建省_福州市_闽清县_省_璜镇',

      3501241400: '福建省_福州市_闽清县_雄江镇',

      3501241500: '福建省_福州市_闽清县_桔林乡',

      3501241600: '福建省_福州市_闽清县_东桥镇',

      3501241700: '福建省_福州市_闽清县_下祝乡',

      3501250000: '福建省_福州市_永泰县',

      3501250100: '福建省_福州市_永泰县_樟城镇',

      3501250200: '福建省_福州市_永泰县_嵩口镇',

      3501250300: '福建省_福州市_永泰县_梧桐镇',

      3501250400: '福建省_福州市_永泰县_葛岭镇',

      3501250500: '福建省_福州市_永泰县_城峰镇',

      3501250600: '福建省_福州市_永泰县_清凉镇',

      3501250700: '福建省_福州市_永泰县_长庆镇',

      3501250800: '福建省_福州市_永泰县_同安镇',

      3501250900: '福建省_福州市_永泰县_大洋镇',

      3501251000: '福建省_福州市_永泰县_塘前乡',

      3501251100: '福建省_福州市_永泰县_富泉乡',

      3501251200: '福建省_福州市_永泰县_岭路乡',

      3501251300: '福建省_福州市_永泰县_赤锡乡',

      3501251400: '福建省_福州市_永泰县_伏口乡',

      3501251500: '福建省_福州市_永泰县_盖洋乡',

      3501251600: '福建省_福州市_永泰县_东洋乡',

      3501251700: '福建省_福州市_永泰县_下拔乡',

      3501251800: '福建省_福州市_永泰县_盘谷乡',

      3501251900: '福建省_福州市_永泰县_红星乡',

      3501252000: '福建省_福州市_永泰县_白云乡',

      3501252100: '福建省_福州市_永泰县_丹云乡',

      3501810000: '福建省_福州市_福清市',

      3501810100: '福建省_福州市_福清市_玉屏街道',

      3501810200: '福建省_福州市_福清市_音西街道',

      3501810300: '福建省_福州市_福清市_宏路街道',

      3501810400: '福建省_福州市_福清市_阳下街道',

      3501810500: '福建省_福州市_福清市_龙田镇',

      3501810600: '福建省_福州市_福清市_江镜镇',

      3501810700: '福建省_福州市_福清市_港头镇',

      3501810800: '福建省_福州市_福清市_高山镇',

      3501810900: '福建省_福州市_福清市_沙埔镇',

      3501811000: '福建省_福州市_福清市_三山镇',

      3501811100: '福建省_福州市_福清市_东瀚镇',

      3501811200: '福建省_福州市_福清市_渔溪镇',

      3501811300: '福建省_福州市_福清市_上迳镇',

      3501811400: '福建省_福州市_福清市_新厝镇',

      3501811500: '福建省_福州市_福清市_江阴镇',

      3501811600: '福建省_福州市_福清市_海口镇',

      3501811700: '福建省_福州市_福清市_城头镇',

      3501811800: '福建省_福州市_福清市_南岭镇',

      3501811900: '福建省_福州市_福清市_东张镇',

      3501812000: '福建省_福州市_福清市_镜洋镇',

      3501812100: '福建省_福州市_福清市_一都镇',

      3501812200: '福建省_福州市_福清市_龙山街道',

      3501812300: '福建省_福州市_福清市_龙江街道',

      3501812400: '福建省_福州市_福清市_石竹街道',

      3501820000: '福建省_福州市_长乐市',

      3501820100: '福建省_福州市_长乐市_吴航街道',

      3501820200: '福建省_福州市_长乐市_航城街道',

      3501820300: '福建省_福州市_长乐市_营前街道',

      3501820400: '福建省_福州市_长乐市_首占镇',

      3501820500: '福建省_福州市_长乐市_玉田镇',

      3501820600: '福建省_福州市_长乐市_罗联乡',

      3501820700: '福建省_福州市_长乐市_江田镇',

      3501820800: '福建省_福州市_长乐市_松下镇',

      3501820900: '福建省_福州市_长乐市_古槐镇',

      3501821000: '福建省_福州市_长乐市_文武砂镇',

      3501821100: '福建省_福州市_长乐市_鹤上镇',

      3501821200: '福建省_福州市_长乐市_漳港街道',

      3501821300: '福建省_福州市_长乐市_金峰镇',

      3501821400: '福建省_福州市_长乐市_湖南镇',

      3501821500: '福建省_福州市_长乐市_梅花镇',

      3501821600: '福建省_福州市_长乐市_文岭镇',

      3501821700: '福建省_福州市_长乐市_潭头镇',

      3501821800: '福建省_福州市_长乐市_猴屿乡',
    };
    return obj[value].split('_') || [];
  }

  /**
   * 获取体检项目名称和分类
   * @param code
   */
  getBhklist(code) {
    code = +code;
    const obj = {
      1530: { name: '外周血淋巴细胞染色体', classify: '染色体畸变分析' },
      10000: { name: '消化系统', classify: '内科' },
      10001: { name: '肝', classify: '内科' },
      10002: { name: '脾', classify: '内科' },
      10003: { name: '肺部', classify: '内科' },
      10006: { name: '泌尿生殖器官', classify: '外科' },
      10033: { name: '心电图', classify: '心电图' },
      10034: { name: '单核细胞计数', classify: '血常规' },
      10035: { name: '单核细胞比率', classify: '血常规' },
      10042: { name: '嗜酸性粒细胞计数', classify: '血常规' },
      10043: { name: '嗜酸性粒细胞比率', classify: '血常规' },
      10044: { name: '嗜碱性粒细胞比率', classify: '血常规' },
      10045: { name: '红细胞压积', classify: '血常规' },
      10046: { name: '血红蛋白', classify: '血常规' },
      10047: { name: '平均血红蛋白量', classify: '血常规' },
      10048: { name: '平均血红蛋白浓度', classify: '血常规' },
      10049: { name: '变应原皮肤试验', classify: '免疫学化验' },
      10050: { name: '丙型肝炎抗体', classify: '免疫学化验' },
      10051: { name: '超敏C反应蛋白', classify: '免疫学化验' },
      10052: { name: '甲型肝炎抗体', classify: '免疫学化验' },
      10053: { name: '抗链球菌溶血素‘O’测定', classify: '免疫学化验' },
      10054: { name: '血小板计数', classify: '血常规' },
      10055: { name: '肌酸激酶同功酶(CK-MB)', classify: '生化检验' },
      10056: { name: '尿酸', classify: '生化检验' },
      10057: { name: '低密度脂蛋白', classify: '生化检验' },
      10058: { name: '淀粉酶', classify: '生化检验' },
      10059: { name: '碱性磷酸酶', classify: '生化检验' },
      10060: { name: '血清脂蛋白(a)', classify: '生化检验' },
      10061: { name: 'γ-谷氨酰氨转肽酶', classify: '生化检验' },
      10063: { name: '载脂蛋白A1', classify: '生化检验' },
      10064: { name: '载脂蛋白B', classify: '生化检验' },
      10066: { name: '白蛋白', classify: '生化检验' },
      10067: { name: '球蛋白', classify: '生化检验' },
      10068: { name: '白球比', classify: '生化检验' },
      10070: { name: '甘油三酯', classify: '生化检验' },
      10108: { name: '血小板压积', classify: '血常规' },
      10110: { name: '平均血小板体积', classify: '血常规' },
      10111: { name: '血小板分布宽度', classify: '血常规' },
      10112: { name: '大型血小板比率', classify: '血常规' },
      10113: { name: '网织红细胞', classify: '血常规' },
      10114: { name: '中性粒细胞计数', classify: '血常规' },
      10115: { name: '尿维生素C', classify: '尿常规' },
      10116: { name: '总胆红素', classify: '生化检验' },
      10117: { name: '中性细胞比率', classify: '血常规' },
      10128: { name: '血清钙', classify: '血液特殊项' },
      10131: { name: '血清镁（Mg）', classify: '生化检验' },
      10132: { name: '血清铁（lron）', classify: '生化检验' },
      10133: { name: '血清尿素（Urea）', classify: '血液特殊项' },
      10134: { name: '血清肌酐（Crea）', classify: '血液特殊项' },
      10138: { name: '平均红细胞血红蛋白量（MCH）', classify: '生化检验' },
      10139: {
        name: '平均红细胞血红蛋白浓度（MCHC）',
        classify: '生化检验',
      },
      10140: { name: '直接胆红素', classify: '生化检验' },
      10887: { name: '尿蛋白', classify: '尿常规' },
      10889: { name: '比重', classify: '尿常规' },
      10891: { name: '尿隐血', classify: '尿常规' },
      10892: { name: '尿胆红素', classify: '尿常规' },
      10893: { name: '尿胆原', classify: '尿常规' },
      10894: { name: '血溴', classify: '血液特殊项' },
      10897: { name: '裸眼远视力(左)', classify: '视力色觉' },
      10898: { name: '裸眼远视力(右)', classify: '视力色觉' },
      10901: { name: '耳部', classify: '五官科' },
      10902: { name: '扁桃体', classify: '五官科' },
      10905: { name: '鼻中隔', classify: '五官科' },
      10906: { name: '听力(右)', classify: '五官科' },
      10907: { name: '耳廓', classify: '五官科' },
      10908: { name: '外耳道', classify: '五官科' },
      10909: { name: '咽部', classify: '五官科' },
      10910: { name: '喉部', classify: '五官科' },
      10911: { name: '鼻部', classify: '五官科' },
      10912: { name: '咽黏膜', classify: '五官科' },
      10914: { name: '鼻窦', classify: '五官科' },
      10917: { name: '鼻黏膜', classify: '五官科' },
      10919: { name: '一般状况', classify: '一般情况' },
      10920: { name: '肛门指检', classify: '外科' },
      10921: { name: '前列腺(外科)', classify: '外科' },
      10922: { name: '腹部', classify: '外科' },
      10923: { name: '乳房', classify: '外科' },
      10924: { name: '体重', classify: '一般情况' },
      10925: { name: '收缩压', classify: '一般情况' },
      10926: { name: '四肢关节', classify: '外科' },
      10928: { name: '舒张压', classify: '一般情况' },
      10929: { name: '内科其他', classify: '内科' },
      10930: { name: '骨密度', classify: '骨密度' },
      10931: { name: '鼻窦X光摄片', classify: 'X线摄片' },
      10937: { name: '裸眼近视力(左)', classify: '视力色觉' },
      10938: { name: '裸眼近视力(右)', classify: '视力色觉' },
      10941: { name: '外眼', classify: '眼科' },
      10942: { name: '裸眼视力(右)', classify: '眼科' },
      10944: { name: '眼压', classify: '眼科' },
      10945: { name: '色觉', classify: '眼科' },
      10946: { name: '虹膜', classify: '眼科' },
      10951: { name: '宫颈', classify: '妇科' },
      10952: { name: '妇科其他', classify: '妇科' },
      10956: { name: '皮肤', classify: '外科' },
      10960: { name: '血镍', classify: '血液特殊项' },
      10961: { name: '血苯', classify: '血液特殊项' },
      10971: { name: '指鼻试验', classify: '神经科' },
      10973: { name: '指指试验', classify: '神经科' },
      10974: { name: '共济运动', classify: '神经科' },
      10975: { name: '三颤', classify: '神经科' },
      10976: { name: '深感觉（神经系统检查）', classify: '神经科' },
      10977: { name: '膝反射', classify: '神经科' },
      10978: { name: '跟-膝-胫试验', classify: '神经科' },
      10979: { name: '前庭功能', classify: '神经科' },
      10980: { name: '跟腱反射', classify: '神经科' },
      10981: { name: '浅感觉（神经系统检查）', classify: '神经科' },
      10982: { name: '尿酮体', classify: '尿常规' },
      10988: { name: '镜检白细胞', classify: '尿常规' },
      10989: { name: '镜检上皮细胞', classify: '尿常规' },
      10991: { name: '镜检管型', classify: '尿常规' },
      10993: { name: '镜检红细胞', classify: '尿常规' },
      10994: { name: '间接胆红素', classify: '生化检验' },
      10995: { name: '磷酸肌酸激酶', classify: '生化检验' },
      10996: { name: '幽门螺旋菌抗体', classify: '生化检验' },
      10997: { name: 'a-羟丁酸脱氢酶', classify: '生化检验' },
      10999: { name: '前白蛋白', classify: '生化检验' },
      11001: { name: '乙肝核心抗体', classify: '免疫学化验' },
      11002: { name: '抗双链DNA抗体', classify: '免疫学化验' },
      11003: { name: '类风湿因子', classify: '免疫学化验' },
      11004: { name: '淋球菌', classify: '免疫学化验' },
      11005: { name: '梅毒滴度(RPR)', classify: '免疫学化验' },
      11007: { name: 'β2微球蛋白', classify: '免疫学化验' },
      11008: { name: '乙肝核心抗体(定量)', classify: '免疫学化验' },
      11009: { name: '乙型肝炎核心抗体HBc-IgM', classify: '免疫学化验' },
      11010: { name: '免疫球蛋白IgA', classify: '免疫学化验' },
      11011: { name: '免疫球蛋白IgG', classify: '免疫学化验' },
      11013: { name: '牙齿', classify: '口腔科' },
      11014: { name: '牙龈', classify: '口腔科' },
      11017: { name: '血甲醇', classify: '血液特殊项' },
      11019: { name: '五官科其他', classify: '五官科' },
      11024: { name: '血砷', classify: '血液特殊项' },
      11033: { name: '脉搏', classify: '一般情况' },
      11034: { name: '身高', classify: '一般情况' },
      11036: { name: '问诊', classify: '问诊' },
      11037: { name: '镜检结晶', classify: '尿常规' },
      11044: { name: '免疫球蛋白IgM', classify: '免疫学化验' },
      11045: { name: '肾', classify: '内科' },
      11047: { name: '乙肝表面抗原(定量)', classify: '免疫学化验' },
      11061: { name: '腰骶椎正侧位片', classify: 'X线摄片' },
      11063: { name: '手部X线摄片', classify: 'X线摄片' },
      11065: { name: '下颌骨 X 射线摄片', classify: 'X线摄片' },
      11066: { name: '肝脏', classify: '超声' },
      11067: { name: '脾脏', classify: '超声' },
      11068: { name: '胆囊', classify: '超声' },
      11072: { name: '头颅正侧位片', classify: 'X线摄片' },
      11073: { name: '骨盆正位片', classify: 'X线摄片' },
      11074: { name: '腕关节正侧位片', classify: 'X线摄片' },
      11075: { name: '左足部正侧片', classify: 'X线摄片' },
      11076: { name: '肱骨正侧位片', classify: 'X线摄片' },
      11077: { name: '肘关节正侧位片', classify: 'X线摄片' },
      11078: { name: '肩关节正位片', classify: 'X线摄片' },
      11079: { name: '股骨正侧位片', classify: 'X线摄片' },
      11081: { name: '髋关节正位片', classify: 'X线摄片' },
      11082: { name: '同侧胫、腓骨正、侧位片', classify: 'X线摄片' },
      11083: { name: '膝关节正侧位片', classify: 'X线摄片' },
      11084: { name: '踝关节正侧位片', classify: 'X线摄片' },
      11086: { name: '肾脏', classify: '超声' },
      11087: { name: '乳酸脱氢酶', classify: '生化检验' },
      11088: { name: '血清无机磷', classify: '血液特殊项' },
      11094: { name: '尿铅', classify: '尿液特殊项' },
      11099: { name: '尿铬', classify: '尿液特殊项' },
      11104: { name: '血镉', classify: '血液特殊项' },
      11106: { name: '尿β2微球蛋白', classify: '尿液特殊项' },
      11111: { name: '血铬', classify: '血液特殊项' },
      11112: { name: '尿氟', classify: '尿液特殊项' },
      11116: { name: '意识', classify: '神经科' },
      11121: { name: '尿锰', classify: '尿液特殊项' },
      11123: { name: '肌张力', classify: '神经科' },
      11128: { name: '尿镉', classify: '尿液特殊项' },
      11133: { name: 'C-反应蛋白', classify: '免疫学化验' },
      11141: { name: '胸部正位片', classify: 'X线摄片' },
      11142: { name: 'X 射线高千伏胸片', classify: 'X线摄片' },
      11145: { name: 'PGI/PGII', classify: '免疫学化验' },
      11150: { name: '浅表淋巴结', classify: '外科' },
      11153: { name: '龋齿', classify: '口腔科' },
      11154: { name: '裸眼视力(左)', classify: '眼科' },
      11156: { name: '面色', classify: '一般情况' },
      11158: { name: '体重指数BMI', classify: '一般情况' },
      11165: { name: '淋巴细胞计数', classify: '血常规' },
      11209: { name: '血清总胆汁酸', classify: '生化检验' },
      11210: { name: '补体C3', classify: '生化检验' },
      11211: { name: '补体C4', classify: '生化检验' },
      11212: { name: '血清胆碱脂酶', classify: '生化检验' },
      11213: { name: '总蛋白', classify: '生化检验' },
      11216: { name: '甘脯肽酶', classify: '生化检验' },
      11230: { name: '左耳语频平均听阈', classify: '电测听' },
      11231: { name: '右耳语频平均听阈', classify: '电测听' },
      11232: { name: '双耳语频平均听阈', classify: '电测听' },
      11233: { name: '双耳高频平均听阈', classify: '电测听' },
      11234: { name: '左耳听阈加权', classify: '电测听' },
      11235: { name: '右耳听阈加权', classify: '电测听' },
      11236: { name: '左耳500Hz(骨导)', classify: '电测听' },
      11237: { name: '左耳1000Hz(骨导)', classify: '电测听' },
      11238: { name: '左耳2000Hz(骨导)', classify: '电测听' },
      11239: { name: '左耳6000Hz(骨导)', classify: '电测听' },
      11240: { name: '右耳500Hz(骨导)', classify: '电测听' },
      11241: { name: '右耳1000Hz(骨导)', classify: '电测听' },
      11242: { name: '右耳2000Hz(骨导)', classify: '电测听' },
      11243: { name: '右耳3000Hz(骨导)', classify: '电测听' },
      11244: { name: '右耳4000Hz(骨导)', classify: '电测听' },
      11245: { name: '右耳6000Hz(骨导)', classify: '电测听' },
      11246: { name: '颈椎正侧位片', classify: 'X线摄片' },
      11247: { name: '腰椎正侧位片', classify: 'X线摄片' },
      11248: { name: '后前位X射线高千伏胸片', classify: 'X线摄片' },
      11251: { name: '淋巴细胞比率', classify: '血常规' },
      11252: { name: '尿液颜色', classify: '尿常规' },
      11254: { name: '轮替运动', classify: '神经科' },
      11255: { name: '红细胞分布宽度CV', classify: '血常规' },
      11264: { name: '微白蛋白', classify: '尿常规' },
      11269: { name: '病理反射', classify: '神经科' },
      11270: { name: '乙肝表面抗原', classify: '免疫学化验' },
      11273: { name: '颈椎双斜位X射线摄片', classify: 'X线摄片' },
      11274: { name: '胫、腓骨正位片', classify: 'X线摄片' },
      11275: { name: '胃蛋白酶原II', classify: '免疫学化验' },
      11276: { name: '乙肝e抗体', classify: '免疫学化验' },
      11277: { name: '戊型肝炎抗体', classify: '免疫学化验' },
      11278: { name: '血气分析', classify: '免疫学化验' },
      11287: { name: '乙肝e抗体(定量)', classify: '免疫学化验' },
      11288: {
        name: '淋巴细胞微核率',
        classify: '外周血淋巴细胞微核试验',
      },
      11289: { name: '乙肝e抗原', classify: '免疫学化验' },
      11290: { name: '乙肝e抗原(定量)', classify: '免疫学化验' },
      11291: { name: '乙肝表面抗体', classify: '免疫学化验' },
      11292: { name: '血铅', classify: '血液特殊项' },
      11293: { name: '血钾', classify: '血液特殊项' },
      11299: { name: '附件', classify: '妇科' },
      11303: { name: '发育', classify: '内科' },
      11304: { name: '胸廓', classify: '内科' },
      11305: { name: '既往史', classify: '内科' },
      11306: { name: '鼻外形', classify: '五官科' },
      11310: { name: '缺齿', classify: '口腔科' },
      11313: { name: '指甲', classify: '皮肤科' },
      11314: { name: '毛发', classify: '皮肤科' },
      11317: { name: '心血管系统', classify: '内科' },
      11318: { name: '透明度', classify: '尿常规' },
      11319: { name: '尿白细胞', classify: '尿常规' },
      11320: { name: '酸碱度', classify: '尿常规' },
      11321: { name: '亚硝酸盐', classify: '尿常规' },
      11323: { name: '尿葡萄糖', classify: '尿常规' },
      11338: { name: '中间细胞计数', classify: '血常规' },
      11339: { name: '嗜碱性粒细胞计数', classify: '血常规' },
      11345: { name: '呼吸系统', classify: '内科' },
      11346: { name: '口腔黏膜', classify: '口腔科' },
      11347: { name: '牙周', classify: '口腔科' },
      11378: { name: '握力', classify: '神经科' },
      11381: { name: '肺功能结果', classify: '肺功能' },
      11382: { name: '白细胞计数', classify: '血常规' },
      11401: { name: '中间细胞比率', classify: '血常规' },
      11402: { name: '红细胞计数', classify: '血常规' },
      11425: { name: '听力(左)', classify: '五官科' },
      11426: { name: '左耳 500Hz（气导）', classify: '电测听' },
      11427: { name: '左耳 1000Hz（气导）', classify: '电测听' },
      11428: { name: '左耳 2000Hz（气导）', classify: '电测听' },
      11429: { name: '左耳 3000Hz（气导）', classify: '电测听' },
      11430: { name: '左耳 4000Hz（气导）', classify: '电测听' },
      11431: { name: '左耳 6000Hz（气导）', classify: '电测听' },
      11432: { name: '右耳 500Hz（气导）', classify: '电测听' },
      11433: { name: '右耳 1000Hz（气导）', classify: '电测听' },
      11434: { name: '右耳 2000Hz（气导）', classify: '电测听' },
      11435: { name: '右耳 3000Hz（气导）', classify: '电测听' },
      11436: { name: '右耳 4000Hz（气导）', classify: '电测听' },
      11437: { name: '右耳 6000Hz（气导）', classify: '电测听' },
      11440: { name: '无着丝粒体畸变率', classify: '染色体畸变分析' },
      11441: { name: '分析细胞数', classify: '染色体畸变分析' },
      11442: { name: '无着丝粒断片', classify: '染色体畸变分析' },
      11443: { name: '微小体', classify: '染色体畸变分析' },
      11444: { name: '无着丝粒环', classify: '染色体畸变分析' },
      11445: { name: '着丝粒环', classify: '染色体畸变分析' },
      11446: { name: '双着丝粒体', classify: '染色体畸变分析' },
      11447: { name: '相互易位', classify: '染色体畸变分析' },
      11461: { name: '外阴', classify: '妇科' },
      11462: { name: '阴道', classify: '妇科' },
      11463: { name: '分泌物', classify: '妇科' },
      11464: { name: '子宫体', classify: '妇科' },
      11465: { name: '穹隆', classify: '妇科' },
      11487: { name: 'Tinel试验', classify: '神经科' },
      11488: { name: 'Phalen试验', classify: '神经科' },
      11489: { name: '脊柱', classify: '外科' },
      11490: { name: '梅毒螺旋体特异抗体', classify: '免疫学化验' },
      11491: { name: '右足部正侧位片', classify: 'X线摄片' },
      11492: { name: '一侧桡、尺骨正位片', classify: 'X线摄片' },
      11493: { name: '脊椎X射线摄片', classify: 'X线摄片' },
      11494: { name: '口腔牙体X射线全景片', classify: 'X线摄片' },
      11495: { name: '人免疫缺陷病毒抗体', classify: '免疫学化验' },
      11496: { name: '胃蛋白酶原I', classify: '免疫学化验' },
      11499: { name: '乙肝表面抗体(定量)', classify: '免疫学化验' },
      11500: { name: '甲状腺(外科)', classify: '外科' },
      11501: { name: '用力肺活量 FVC（%）', classify: '肺功能' },
      11503: { name: '精神状况', classify: '神经科' },
      11504: { name: '左耳语频平均听阈(骨导)', classify: '电测听' },
      11505: { name: '右耳语频平均听阈(骨导)', classify: '电测听' },
      11506: { name: '双耳语频平均听阈(骨导)', classify: '电测听' },
      11510: { name: '双耳高频平均听阈(骨导)', classify: '电测听' },
      11511: { name: '左耳听阈加权(骨导)', classify: '电测听' },
      11512: { name: '右耳听阈加权(骨导)', classify: '电测听' },
      11513: { name: '左耳4000Hz(骨导)', classify: '电测听' },
      11515: { name: '左耳3000Hz(骨导)', classify: '电测听' },
      11578: { name: '24H动态心电图', classify: '心电图' },
      11579: { name: '肌力', classify: '神经科' },
      12055: { name: '红细胞分布宽度SD', classify: '血常规' },
      13001: { name: '高铁血红蛋白还原试验', classify: '血液特殊项' },
      13003: { name: '第一秒时间肺活量 FEV1（%）', classify: '肺功能' },
      13004: { name: 'FEV1/FVC', classify: '肺功能' },
      13005: { name: '骨科检查', classify: '外科' },
      13006: { name: '胆', classify: '内科' },
      13007: { name: '外科其他', classify: '外科' },
      13014: { name: '角膜', classify: '眼科' },
      13015: { name: '眼科其他', classify: '眼科' },
      13016: { name: '玻璃体（左）', classify: '眼科' },
      13017: { name: '玻璃体（右）', classify: '眼科' },
      13018: { name: '颊面部', classify: '口腔科' },
      13030: { name: '心', classify: '内科' },
      13035: { name: '侧位X射线高千伏胸片', classify: 'X线摄片' },
      13036: { name: '红细胞平均体积', classify: '血常规' },
      13037: { name: '尿素氮', classify: '生化检验' },
      13039: { name: '胸部X线透视', classify: '胸部透视' },
      13040: { name: '胸部CT', classify: 'CT' },
      13041: { name: '颈椎CT', classify: 'CT' },
      13042: { name: '腰椎CT', classify: 'CT' },
      13043: { name: '鼻咽部CT', classify: 'CT' },
      13044: { name: '腹部CT', classify: 'CT' },
      13045: { name: '双肾、输尿管CT', classify: 'CT' },
      13046: { name: '盆腔CT', classify: 'CT' },
      13047: { name: '肾上腺CT', classify: 'CT' },
      13048: { name: '肺部CT', classify: 'CT' },
      13049: { name: '头颅CT', classify: 'CT' },
      13051: { name: '肾脏CT', classify: 'CT' },
      13052: { name: '胰腺CT', classify: 'CT' },
      13053: { name: '颈部CT', classify: 'CT' },
      13054: { name: '鼻窦冠状位CT', classify: 'CT' },
      13055: { name: '输尿管CT', classify: 'CT' },
      13056: { name: '肾动脉CT', classify: 'CT' },
      13057: { name: '胸椎CT', classify: 'CT' },
      13058: { name: '钾(K)', classify: '微量元素' },
      13059: { name: '铁(Fe)', classify: '微量元素' },
      13060: { name: '镁(Mg)', classify: '微量元素' },
      13061: { name: '钠(Na)', classify: '微量元素' },
      13062: { name: '氯(Cl)', classify: '微量元素' },
      13063: { name: '钙(Ca)', classify: '微量元素' },
      13064: { name: '磷(P)', classify: '微量元素' },
      13065: { name: '深视力第一次偏差', classify: '' },
      13066: { name: '深视力第二次偏差', classify: '深视力' },
      13067: { name: '深视力第三次偏差', classify: '' },
      13068: { name: '深视力平均', classify: '' },
      13069: { name: '暗视力', classify: '暗视力' },
      13072: { name: '甲状腺B超', classify: '超声' },
      13073: { name: '肾动脉', classify: '超声' },
      13074: { name: '心脏B超', classify: '超声' },
      13075: { name: '颈动脉B超', classify: '超声' },
      13076: { name: '乳腺B超', classify: '超声' },
      13077: { name: '双肾', classify: '超声' },
      13078: { name: '膀胱', classify: '超声' },
      13079: { name: '前列腺B超', classify: '超声' },
      13080: { name: '输尿管', classify: '超声' },
      13081: { name: '子宫附件', classify: '超声' },
      13083: { name: '胰', classify: '超声' },
      13100: { name: '晶体(左)', classify: '眼科' },
      13101: { name: '血肌酐（Cr）', classify: '生化检验' },
      13102: { name: '血清丙氨酸氨基转移酶', classify: '生化检验' },
      13103: { name: '总胆固醇', classify: '生化检验' },
      13105: { name: '高密度脂蛋白', classify: '生化检验' },
      13106: { name: '天门冬氨酸氨基转移酶', classify: '生化检验' },
      13107: { name: '天门冬氨酸/丙氨酸氨基转移酶', classify: '生化检验' },
      13108: { name: '血清腺苷脱氨酶', classify: '生化检验' },
      13109: { name: '血葡萄糖', classify: '生化检验' },
      13110: { name: '软组织', classify: '口腔科' },
      13111: { name: '牙齿清洁度', classify: '口腔科' },
      13112: { name: '口腔科其他', classify: '口腔科' },
      13113: { name: '手部皮肤', classify: '皮肤科' },
      13114: { name: '全身皮肤', classify: '皮肤科' },
      13115: { name: '划痕症', classify: '皮肤科' },
      13116: { name: '心律', classify: '内科' },
      13117: { name: '结膜', classify: '眼科' },
      13118: { name: '沙眼左', classify: '眼科' },
      13119: { name: '晶体(右)', classify: '眼科' },
      13120: { name: '眼底', classify: '眼科' },
      13121: { name: '暗适应检查', classify: '眼科' },
      13122: { name: '立体视觉', classify: '眼科' },
      13123: { name: '静态视野', classify: '眼科' },
      13124: { name: '房水', classify: '眼科' },
      13125: { name: '眼睑', classify: '眼科' },
      13126: { name: '内眦', classify: '眼科' },
      13127: { name: '外眦', classify: '眼科' },
      13128: { name: '泪囊', classify: '眼科' },
      13129: { name: '矫正视力(左)', classify: '眼科' },
      13130: { name: '眼前部（左）', classify: '眼科' },
      13131: { name: '沙眼右', classify: '眼科' },
      13132: { name: '矫正视力(右)', classify: '眼科' },
      13133: { name: '锌原卟啉', classify: '血液特殊项' },
      13134: { name: '免疫球蛋白IgE', classify: '免疫学化验' },
      13135: { name: '淋巴细胞分类CD4/CD8', classify: '血常规' },
      13136: { name: '尿甲基甲酰胺', classify: '尿液特殊项' },
      13137: { name: '凝血酶原时间', classify: '血液特殊项' },
      13138: {
        name: '虎红缓冲液玻片凝集实验（RPBT）',
        classify: '免疫学化验',
      },
      13139: { name: '试管凝集反应（Wright）', classify: '免疫学化验' },
      13141: { name: '骨关节', classify: '外科' },
      13142: { name: '复杂反应', classify: '神经科' },
      13143: { name: '速度反应', classify: '神经科' },
      13144: { name: '指端试验', classify: '神经科' },
      13145: { name: '冷水复温试验', classify: '神经科' },
      13146: { name: '白指诱发试验', classify: '神经科' },
      13147: { name: '皮肤划痕症', classify: '神经科' },
      13148: { name: '牙齿冷热刺激试验', classify: '口腔科' },
      13151: { name: '嗅觉', classify: '五官科' },
      13152: { name: '四肢长骨正侧位X射线摄片', classify: 'X线摄片' },
      13153: { name: '神经－肌电图', classify: '神经科' },
      13154: {
        name: '游离三碘甲状腺原氨酸指数（FT3I）',
        classify: '甲状腺',
      },
      13155: { name: '游离甲状腺素指数（FT4I）', classify: '甲状腺' },
      13156: { name: '促甲状腺激素TSH', classify: '甲状腺' },
      13158: { name: '总甲状腺素T4', classify: '甲状腺' },
      13159: { name: '血碳氧血红蛋白', classify: '血液特殊项' },
      13160: { name: '胆碱酯酶活性', classify: '血液特殊项' },
      13161: { name: '红细胞胆碱酯酶活性', classify: '血液特殊项' },
      13162: { name: '血沉', classify: '血液特殊项' },
      13163: { name: '高铁血红蛋白', classify: '血液特殊项' },
      13164: { name: '红细胞赫恩滋小体', classify: '血液特殊项' },
      13165: { name: '抗原特异性IgE抗体', classify: '血液特殊项' },
      13166: { name: '尿甲醇', classify: '尿液特殊项' },
      13167: { name: '粪便潜血试验', classify: '粪便化验' },
      13168: { name: '尿汞', classify: '尿液特殊项' },
      13169: { name: '尿视黄醇结合蛋白', classify: '尿液特殊项' },
      13170: { name: '尿铍', classify: '尿液特殊项' },
      13171: { name: '尿砷', classify: '尿液特殊项' },
      13172: { name: '尿锡', classify: '尿液特殊项' },
      13173: { name: '尿铊', classify: '尿液特殊项' },
      13174: { name: '尿镍', classify: '尿液特殊项' },
      13175: { name: '尿反－反粘糠酸', classify: '尿液特殊项' },
      13176: { name: '尿酚', classify: '尿液特殊项' },
      13177: { name: '尿2，5-己二酮', classify: '血液特殊项' },
      13178: { name: '尿脱落细胞检查', classify: '尿液特殊项' },
      13179: { name: '尿五氯酚测定', classify: '尿液特殊项' },
      13180: { name: '尿三氯乙酸', classify: '尿液特殊项' },
      13181: { name: '尿硫氰酸盐NS', classify: '尿液特殊项' },
      13182: { name: '牙体缺失', classify: '口腔科' },
      13216: { name: '皮肤黏膜（皮肤黏膜）', classify: '皮肤科' },
      13217: { name: '色素沉着（皮肤黏膜）', classify: '皮肤科' },
      13218: { name: '色素减退（皮肤黏膜）', classify: '皮肤科' },
      13219: { name: '皮疹（皮肤黏膜）', classify: '皮肤科' },
      13220: { name: '皮下出血（皮肤黏膜）', classify: '皮肤科' },
      13221: {
        name: '皮肤粘膜的湿度、弹性（皮肤黏膜）',
        classify: '皮肤科',
      },
      13222: { name: '脱屑（皮肤黏膜）', classify: '皮肤科' },
      13223: { name: '皲裂（皮肤黏膜）', classify: '皮肤科' },
      13224: { name: '疣状物（皮肤黏膜）', classify: '皮肤科' },
      13225: { name: '皮肤萎缩（皮肤黏膜）', classify: '皮肤科' },
      13226: { name: '过度角化（皮肤黏膜）', classify: '皮肤科' },
      13227: { name: '溃疡（皮肤黏膜）', classify: '皮肤科' },
      13228: { name: '总前列腺特异性抗原(t-PSA)', classify: '血液特殊项' },
      13229: {
        name: '游离前列腺特异性抗原(f-PSA)',
        classify: '血液特殊项',
      },
      13230: { name: '前列腺酸性磷酸酶（PAP）', classify: '血液特殊项' },
      13231: { name: '染色体畸变细胞率', classify: '染色体畸变分析' },
      13233: { name: '淋巴细胞微核细胞率', classify: '染色体畸变分析' },
      13234: { name: '神经反射检查（神经系统检查）', classify: '神经科' },
      13235: { name: '运动功能检查（神经系统检查）', classify: '神经科' },
      13236: { name: '运动功能（外科检查）', classify: '外科' },
      13320: { name: '眼前部（右）', classify: '眼科' },
      13333: { name: '心率', classify: '内科' },
      13423: { name: '染色体分析细胞数', classify: '染色体畸变分析' },
      13596: { name: '血钠', classify: '血常规' },
      13900: { name: '皮疹', classify: '皮肤科' },
      13901: { name: '皮肤其他', classify: '皮肤科' },
      13902: { name: '皮肤颜色', classify: '皮肤科' },
      13903: { name: '瘀斑、瘀点', classify: '皮肤科' },
      13904: { name: '紫癜', classify: '皮肤科' },
      13905: { name: '心音', classify: '内科' },
      13906: { name: '心界', classify: '内科' },
      13907: { name: '神经系统', classify: '内科' },
      13908: { name: '腹部包块', classify: '内科' },
      13910: { name: '杂音', classify: '内科' },
      13911: { name: '末梢感觉', classify: '内科' },
      13912: { name: '四肢', classify: '外科' },
      13913: { name: '关节', classify: '外科' },
      13914: { name: '眼底（右）', classify: '眼科' },
      13915: { name: '眼底（左）', classify: '眼科' },
      13916: { name: '胸部正侧位片', classify: 'X线摄片' },
      13917: { name: '鼓室图', classify: '五官科' },
      13918: { name: '脑干诱发点位', classify: '五官科' },
      13919: { name: '染色体畸变率', classify: '染色体畸变分析' },
      13920: { name: '大型血小板数目', classify: '血常规' },
      13921: { name: '面部表情', classify: '神经科' },
      13922: { name: '语速', classify: '神经科' },
      13923: { name: '巨大未成熟细胞计数', classify: '血常规' },
      13924: { name: '巨大未成熟细胞比率', classify: '血常规' },
      13925: { name: '异常淋巴细胞计数', classify: '血常规' },
      13926: { name: '异常淋巴细胞比率', classify: '血常规' },
      13927: { name: '尿肌酐', classify: '尿常规' },
      14589: { name: '二氧化碳', classify: '血常规' },
      15324: {
        name: '血清葡萄糖-6-磷酸脱氢酶缺乏症筛查试验',
        classify: '血液特殊项',
      },
      15369: { name: '总三碘甲状腺原氨酸(T3)', classify: '甲状腺' },
      15370: { name: '尿沉渣红细胞形态信息', classify: '尿常规' },
      15371: { name: '沉渣电导率', classify: '尿常规' },
      15372: { name: '糖化血红蛋白', classify: '生化检验' },
      17000: { name: '苯巯基尿酸', classify: '尿液特殊项' },
      17001: { name: '双着丝粒染色体率', classify: '染色体畸变分析' },
    };
    return obj[code] || { name: '', classify: '' };
  }
}

module.exports = FzDataService;
