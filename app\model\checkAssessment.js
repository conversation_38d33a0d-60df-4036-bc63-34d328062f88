const shortid = require('shortid');
module.exports = app => {
  // 生物检测结果
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const checkAssessment = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    category: String, // 类别 1.危害检测结果;2.现状评价 3.控制效果评价
    year: Date, // 年份
    source: {
      type: String,
      default: 'service',
    }, // 数据来源，enterprise、service、super、operate  代码不改，在不同端，这个默认值需要修改
    EnterpriseID: { type: String }, // 企业id
    jobHealthId: { type: String, ref: 'jobHealth' }, // 检测项目id
    chemistryFactors: {
      type: new Schema({ // 化学
        name: { type: String, default: '化学物质' },
        value: { type: String, default: 'chemistryFactors' },
        formData: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          workType: { type: String }, // 工种
          mill: String, // 厂房
          workspace: String, // 车间
          station: String, // 岗位
          checkAddress: String, // 检测点位置
          // checkAddressDetail: String, // 具体监测点位置
          level: String, // 危害因素浓度等级 0:<=1%OEL; I:>1% <=10%OEL; II:>10% <=50%OEL; III:>50% <100%OEL; IV:>100%OEL
          percent: Number, // 危害因素浓度百分比
          checkProject: { type: String }, // 检测项目(危害因素)
          checkResults: [{
            _id: {
              type: String,
              default: shortid.generate,
            },
            date: Date, // 检测日期
            MAC: String,
            TWA: String,
            STEL: String,
            PE: String, // 峰接触浓度
            RF: String,
            PCTWAF: String,
            excursionLimits: { type: String }, // 超限倍数
            checkResultItem: { type: String }, // 判定结果 现状评价
          }],
          touchLimitMAC: String, // MAC接触限值
          touchLimitTWA: String, // twa接触限值
          touchLimitSTEL: String, // STEL接触限值
          touchLimitPE: String, // PE接触限值
          checkResult: { type: String }, // 判定结果
        }],
      }),
      default: undefined,
    },
    dustFactors: { // 粉尘
      default: undefined,
      type: new Schema({
        name: { type: String, default: '粉尘' },
        value: { type: String, default: 'dustFactors' },
        formData: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          workType: { type: String }, // 工种
          mill: String, // 厂房
          workspace: String, // 车间
          station: String, // 岗位
          checkAddress: String, // 检测点位置
          // checkAddressDetail: String, // 具体监测点位置
          checkProject: { type: String }, // 检测项目
          checkResults: [{
            _id: {
              type: String,
              default: shortid.generate,
            },
            date: Date, // 检测日期
            MAC: String,
            TWA: String,
            STEL: String,
            PE: String, // 峰接触浓度
            RF: String,
            PCTWAF: String,
            excursionLimits: { type: String }, // 超限倍数
            checkResultItem: { type: String }, // 判定结果
          }],
          PELimit: String, // PE接触限值
          TWALimit: String, // TWA接触限值
          AllDustPercent: Number, // 总尘百分比
          respirableDustPercent: Number, // 呼尘百分比
          checkResult: { type: String }, // 判定结果
          level: String, // 危害因素浓度等级 0:<=1%OEL; I:>1% <=10%OEL; II:>10% <=50%OEL; III:>50% <100%OEL; IV:>100%OEL
          percent: Number, // 危害因素浓度百分比
        }],
      }),
    },
    noiseFactors: { // 噪声
      default: undefined,
      type: new Schema({
        factorType: {
          type: String,
          default: 'physicsFactors',
        }, // 危害因素类型
        name: { type: String, default: '噪声' },
        value: { type: String, default: 'noiseFactors' },
        formData: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          workType: { type: String }, // 工种
          mill: String, // 厂房
          workspace: String, // 车间
          station: String, // 岗位
          checkAddress: String, // 检测点位置
          // checkAddressDetail: String, // 具体监测点位置
          checkProject: { type: String, default: '噪声' },
          touchTime: { type: String }, // 接触时间
          equalLevel: { type: String }, // 8/40h等效声级检测数值[dB(A)]
          checkData: String, // 检测值[dB(A)]
          touchLimit: { type: String }, // 职业接触限值
          checkResult: { type: String }, // 判定结果
        }],
      }),
    },
    biologicalFactors: { // 生物
      default: undefined,
      type: new Schema({
        name: {
          type: String,
          default: '生物',
        },
        value: {
          type: String, default: 'biologicalFactors',
        },
        formData: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          workType: String, // 工种
          workspace: String, // 车间
          station: String, // 岗位
          checkAddress: { type: String }, // 监测点位置
          checkProject: { type: String }, // 检测项目
          MAC: String,
          TWA: String,
          STEL: String,
          checkResult: { type: String }, // 判定结果
        },
        ],
      }),
    },
    heatFactors: { // 高温
      default: undefined,
      type: new Schema({
        factorType: {
          type: String,
          default: 'physicsFactors',
        }, // 危害因素类型
        name: { type: String, default: '高温' },
        value: { type: String, default: 'heatFactors' },
        formData: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          checkProject: { type: String, default: '高温' },
          mill: String, // 厂房
          workspace: String, // 车间
          station: String, // 岗位
          checkAddress: String, // 检测点位置
          // checkAddressDetail: String, // 具体监测点位置
          workType: String, // 工种
          averageValue: { type: String }, // WBGT平均值
          touchTime: { type: String }, // 接触时间
          labourIntensity: { type: String }, // 体力劳动强度
          touchLimit: String, // 职业接触限值（℃）
          checkResult: { type: String }, // 评价结论
        }],
      }),
    },
    handBorneVibrationFactors: { // 手传震动
      default: undefined,
      type: new Schema({
        factorType: {
          type: String,
          default: 'physicsFactors',
        }, // 危害因素类型
        name: { type: String, default: '手传振动' },
        value: { type: String, default: 'handBorneVibrationFactors' },
        formData: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          checkProject: { type: String, default: '手传振动' },
          workType: String, // 工种
          mill: String,
          workspace: String,
          station: String,
          checkAddress: { type: String }, // 监测点位置
          dayTouchTime: { type: String }, // 日接触时间
          touchLimit: { type: String }, // 接触限值
          ahw: String, // 频率计权振动加速度测量值ahw（m/s2）
          fourHoursAccelerated: String, // 4h等能量频率计权振动加速度值（m/s2）
          checkResult: { type: String }, // 判定结果
        },
        ],
      }),
    },
    highFrequencyEleFactors: { // 高频电磁场检测结果
      default: undefined,
      type: new Schema({
        factorType: {
          type: String,
          default: 'physicsFactors',
        }, // 危害因素类型
        name: { type: String, default: '高频电磁场' },
        value: { type: String, default: 'highFrequencyEleFactors' },
        formData: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          checkProject: { type: String, default: '高频电磁场' },
          workType: String, // 工种
          mill: String, // 厂房
          workspace: String, // 车间
          station: String, // 岗位
          checkAddress: { type: String }, // 监测点位置
          harmFactors: String, // 职业病危害因素名称
          radiationHZ: String, // 辐射频率（MHz）
          electricIntensity: String, // 电场强度（V/m）
          electricIntensityData: String, // 电场强度测量值（V/m）
          magneticIntensity: String, // 磁场强度（A/m）
          magneticIntensityData: String, // 磁场强度测量值（A/m）
          checkResult: { type: String }, // 判定结果
        }],
      }),
    },
    laserFactors: { // 激光辐射
      default: undefined,
      type: new Schema({
        factorType: {
          type: String,
          default: 'physicsFactors',
        }, // 危害因素类型
        name: { type: String, default: '激光辐射' },
        value: { type: String, default: 'laserFactors' },
        formData: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          checkProject: { type: String, default: '激光辐射' },
          workType: String, // 工种
          mill: String, // 厂房
          workspace: String, // 车间
          station: String, // 岗位
          checkAddress: { type: String }, // 监测点位置
          touchLimit: { type: String }, // 接触限值
          testAverage: String, // 平均检测值（W/cm2）
          irradiance: String, // 辐射度
          checkResult: { type: String }, // 判定结果
        }],
      }),
    },
    microwaveFactors: { // 微波辐射
      default: undefined,
      type: new Schema({
        factorType: {
          type: String,
          default: 'physicsFactors',
        }, // 危害因素类型
        name: { type: String, default: '微波辐射' },
        value: { type: String, default: 'microwaveFactors' },
        formData: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          checkProject: { type: String, default: '微波辐射' },
          workType: String, // 工种
          mill: String, // 厂房
          workspace: String, // 车间
          station: String, // 岗位
          checkAddress: { type: String }, // 监测点位置
          shortTimeContactPowerDensity: String, // 短时间接触功率密度最大值（mW/cm2）
          shortTimeLimit: String, // 短时间接触功率密度接触限值（mW/cm2）
          average: String, // 平均值（μW/cm2）
          averagePowerDensityLimit: String, // 8h平均功率密度接触限值（μW/cm2）
          checkResult: { type: String }, // 判定结果
        }],
      }),
    },
    powerFrequencyElectric: {
      default: undefined,
      type: new Schema({
        factorType: {
          type: String,
          default: 'physicsFactors',
        }, // 危害因素类型
        name: { type: String, default: '工频电场' },
        value: { type: String, default: 'powerFrequencyElectric' },
        formData: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          checkProject: { type: String, default: '工频电场' },
          workType: { type: String }, // 工种
          workspace: String, // 车间
          mill: String,
          station: String, // 岗位
          checkAddress: String, // 检测点位置
          // checkAddressDetail: String, // 具体监测点位置
          electricIntensity: String, // 电场强度测量值（kV/m）
          electricIntensityLimit: String, // 电场强度职业接触限值（kV/m）
          checkResult: { type: String }, // 判定结果
        }],
      }),
    },
    SiO2Factors: {
      default: undefined,
      type: new Schema({
        name: { type: String, default: '游离二氧化硅' },
        value: { type: String, default: 'SiO2Factors' },
        formData: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          workType: { type: String }, // 工种
          checkAddress: String, // 监测点位置
          workspace: String, // 车间
          station: String, // 岗位
          mill: String, // 厂房
          checkProject: { type: String }, // 检测项目
          checkResult: { type: String }, // 判定结果
          checkType: String, // 检测结果类型 >=10%矽尘 其余是呼尘
        }],
      }),
    },
    ultraHighRadiationFactors: {
      default: undefined,
      type: new Schema({
        factorType: {
          type: String,
          default: 'physicsFactors',
        }, // 危害因素类型
        name: { type: String, default: '超高频辐射' },
        value: { type: String, default: 'ultraHighRadiationFactors' },
        formData: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          checkProject: { type: String, default: '超高频辐射' },
          workType: { type: String }, // 工种
          workspace: String, // 车间
          station: String, // 岗位
          mill: String, // 厂房
          checkAddress: String, // 检测点位置
          // checkAddressDetail: String, // 具体监测点位置
          electricAverage: String, // 脉冲波电场强度平均值（V/m）
          eightHoursTouchLimit: String, // 8h职业接触限值（V/m）
          checkResult: { type: String }, // 判定结果
        }],
      }),
    },
    ultravioletFactors: {
      default: undefined,
      type: new Schema({
        factorType: {
          type: String,
          default: 'physicsFactors',
        }, // 危害因素类型
        name: { type: String, default: '紫外辐射' },
        value: { type: String, default: 'ultravioletFactors' },
        formData: [{
          _id: {
            type: String,
            default: shortid.generate,
          },
          checkProject: { type: String, default: '紫外辐射' },
          mill: String,
          workspace: String,
          station: String,
          workType: String, // 工种
          checkAddress: { type: String }, // 监测点位置
          irradiance: String, // 有效辐照度（μW/cm2）
          eightHoursTouchLimit: String, // 8h职业接触限值（μW/cm2）
          checkResult: { type: String }, // 判定结果
        }],
      }),
    },
    ionizatioSourceFactors: { // 电离辐射-含源装置
      default: undefined,
      type: new Schema({
        factorType: {
          type: String,
          default: 'radiationFactors',
        }, // 危害因素类型
        name: { type: String, default: '电离辐射-含源装置' },
        value: { type: String, default: 'ionizatioSourceFactors' },
        formData: [{
          _id: { type: String,
            default: shortid.generate },
          checkProject: String, // 检测项目
          device: String, // 装置名称
          sourceNum: String, // 放射源编号
          markNum: String, // 标号
          nuclide: String, // 核素
          installAddress: String, // 安装位置
          curActivity: String, // 当前活度
          deliveryActivity: String, // 出厂活度
          mill: String, // 厂房
          workspace: String, // 车间
          station: String, // 岗位
          checkAddress: String, // 检测点位置
          checkResult: String, // 判定结果
          checkResultValue: String, // 检测结果
        }],
      }),
    },
    ionizatioRadialFactors: { // 电离辐射-射线装置
      default: undefined,
      type: new Schema({
        factorType: {
          type: String,
          default: 'radiationFactors',
        }, // 危害因素类型
        name: { type: String, default: '电离辐射-射线装置' },
        value: { type: String, default: 'ionizatioRadialFactors' },
        formData: [{
          _id: { type: String,
            default: shortid.generate },
          checkProject: String, // 检测项目
          device: String, // 装置名称
          model: String, // 型号
          ratedCapacity: String, // 额定容器
          condition: String, // 检测条件
          mill: String, // 厂房
          workspace: String, // 车间
          station: String, // 岗位
          checkAddress: String, // 检测点位置
          checkResult: String, // 判定结果
          checkResultValue: String, // 检测结果
        }],
      }),
    },
  });
  return mongoose.model('checkAssessment', checkAssessment, 'checkAssessment');
};
