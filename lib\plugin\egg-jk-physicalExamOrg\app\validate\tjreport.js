const validateTool = require('validate');
const info = new validateTool({
  // (必填)
  creditCode: {
    type: String,
    required: true,
    message: {
      type: '体检机构信用代码类型错误，必须为String',
      required: '体检机构信用代码未填写',
    },
  },
});

const project = new validateTool({
  // (必填)
  enterpriseName: {
    type: String,
    required: true,
    message: {
      type: '名称类型错误，必须为String类型',
      required: '名称未填写',
    },
  },
  enterpriseContactsPhoneNumber: {
    type: String,
    required: true,
    match: /^(([1][3,4,5,6,7,8,9][0-9]{9})|(0\d{2,3}-\d{7,8}))$|0\d{2,3}\d{7,8}$/,
    message: {
      type: '联系方式类型错误，必须为String类型',
      required: '联系方式未填写',
      match: '联系方式格式错误',
    },
  },
  workAddress: {
    type: String,
    required: true,
    message: {
      type: '工作场所类型错误，必须为String类型',
      required: '工作场所未填写',
    },
  }, // 工作场所
  projectNumber: {
    type: String,
    required: true,
    message: {
      type: '项目编号类型错误，必须为String类型',
      required: '项目编号未填写',
    },
  }, // 项目编号
  recheck: {
    type: Boolean,
    required: true,
    message: {
      type: '是否复查类型错误，必须为Boolean类型',
      required: '是否复查未填写',
    },
  },
  checkDate: {
    type: String,
    required: true,
    message: {
      type: '体检开始时间类型错误，必须为String类型且值为时间戳',
      required: '体检开始时间未填写',
    },
  },
  checkType: {
    type: String,
    required: true,
    enum: [ '0', '1', '2', '3', '4' ], // 0是上岗前 1是在岗 2是离岗 4应急
    message: {
      type: '体检类型类型错误，必须为String类型',
      required: '体检类型未填写',
      enum: '体检类型不在指定值中，0上岗前，1在岗期间，2离岗时，4应急职业健康检查',
    },
  },
  shouldCheckNum: {
    type: Number,
    required: true,
    message: {
      type: '应检人数类型错误，必须为Number类型',
      required: '应检人数未填写',
    },
  },
  actuallNum: {
    type: Number,
    required: true,
    message: {
      type: '实检人数类型错误，必须为Number类型',
      required: '实检人数未填写',
    },
  },
  approvalDate: {
    type: String,
    required: true,
    message: {
      type: '批准日期类型错误，必须为String类型且值为时间戳',
      required: '批准日期未填写',
    },
  }, // 批准日期
  // (非必填)
  enterpriseContactsName: {
    type: String,
    required: false,
    message: {
      type: '企业联系人类型错误，必须为String类型',
    },
  },
  enterpriseCode: {
    type: String,
    required: false,
    message: {
      type: '企业营业执照代码类型错误，必须为String类型',
    },
  },
  checkEndDate: {
    type: String,
    required: false,
    message: {
      type: '体检结束时间类型错误，必须为String类型',
    },
  },
  checkPlace: {
    type: String,
    required: false,
    message: {
      type: '体检地点类型错误，必须为String',
    },
  },
});

const peopleInfoList = new validateTool({
  peopleInfoList: [{
    // (必填)
    name: {
      type: String,
      required: true,
      message: {
        type: '姓名类型错误，必须为String',
        required: '姓名未填写',
      },
    },
    gender: {
      type: String,
      required: true,
      enum: [ '0', '1' ], // 0男 1女
      message: {
        type: '性别类型错误，必须为String',
        required: '性别未填写',
        enum: '性别类型不在指定值中，0男，1女',
      },
    },
    age: {
      type: String,
      required: true,
      message: {
        type: '年龄类型错误，必须为String',
        required: '年龄未填写',
      },
    },
    workType: {
      type: String,
      required: true,
      message: {
        type: '工种类型错误，必须为String',
        required: '工种未填写',
      },
    },
    workYears: {
      type: String,
      required: false,
      message: {
        type: '工龄类型错误，必须为String',
        required: '工龄未填写',
      },
    },
    harmFactors: {
      type: String,
      required: true,
      message: {
        type: '危害因素类型错误，必须为String',
        required: '危害因素未填写',
      },
    },
    abnormalIndex: {
      type: String,
      required: true,
      message: {
        type: '异常指标类型错误，必须为String',
        required: '异常指标未填写',
      },
    }, // 异常指标 - wzq 0828改为(非必填)
    dedicalAdvice: {
      type: String,
      required: false,
      message: {
        type: '医学建议类型错误，必须为String',
        required: '医学建议未填写',
      },
    },
    CwithO: {
      type: String,
      required: true,
      message: {
        type: '结论类型错误，必须为String',
        required: '结论未填写',
      },
    },
    // (非必填)
    opinion: {
      type: String,
      required: false,
      message: {
        type: '意见类型错误，必须为String',
      },
    },
    IDCard: {
      type: String,
      required: false,
      match: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)|(^[a-zA-Z]\d{9}$)/,
      message: {
        type: '身份证号类型错误，必须为String',
        match: '身份证号格式错误',
      },
    },
  }],
});

const peopleInfoList_notVaildIDCard = new validateTool({
  peopleInfoList: [{
    // (必填)
    name: {
      type: String,
      required: true,
      message: {
        type: '姓名类型错误，必须为String',
        required: '姓名未填写',
      },
    },
    gender: {
      type: String,
      required: true,
      enum: [ '0', '1' ], // 0男 1女
      message: {
        type: '性别类型错误，必须为String',
        required: '性别未填写',
        enum: '性别类型不在指定值中，0男，1女',
      },
    },
    age: {
      type: String,
      required: true,
      message: {
        type: '年龄类型错误，必须为String',
        required: '年龄未填写',
      },
    },
    workType: {
      type: String,
      required: true,
      message: {
        type: '工种类型错误，必须为String',
        required: '工种未填写',
      },
    },
    workYears: {
      type: String,
      required: false,
      message: {
        type: '工龄类型错误，必须为String',
        required: '工龄未填写',
      },
    },
    harmFactors: {
      type: String,
      required: true,
      message: {
        type: '危害因素类型错误，必须为String',
        required: '危害因素未填写',
      },
    },
    abnormalIndex: {
      type: String,
      required: true,
      message: {
        type: '异常指标类型错误，必须为String',
        required: '异常指标未填写',
      },
    }, // 异常指标 - wzq 0828改为(非必填)
    dedicalAdvice: {
      type: String,
      required: false,
      message: {
        type: '医学建议类型错误，必须为String',
        required: '医学建议未填写',
      },
    },
    CwithO: {
      type: String,
      required: true,
      message: {
        type: '结论类型错误，必须为String',
        required: '结论未填写',
      },
    },
    // (非必填)
    opinion: {
      type: String,
      required: false,
      message: {
        type: '意见类型错误，必须为String',
      },
    },
    IDCard: {
      type: String,
      required: false,
      // match: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)|(^[a-zA-Z]\d{9}$)/,
      message: {
        type: '身份证号类型错误，必须为String',
        match: '身份证号格式错误',
      },
    },
  }],
});

module.exports = {
  info,
  project,
  peopleInfoList,
  peopleInfoList_notVaildIDCard,
};
