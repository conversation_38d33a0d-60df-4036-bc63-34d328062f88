/**
 * 企业年度职业卫生档案完成度情况（只记录当前的）
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const FilesCompletenessSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: { // 企业id
      type: String,
      ref: 'Adminorg',
      require: true,
    },
    // totalCompletion: { // 总完成度(企业端展示时需临时计算) 暂未用到
    //   type: Number,
    //   default: 0,
    // },
    // 三同时project(非必填)zhj说先不展示了
    // record: {
    //   completion: { type: Number, default: 0, min: 0, max: 100 }, // 完成度, 目前就只要填0和100就好
    //   validPeriod: Date, // 有效截止日期（开始/当前时间+有效期），不填的话就默认是没有有效期限制的
    // },

    // 职业卫生管理档案
    employees: { // 人员信息
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    millConstruction: { // 岗位信息
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    defendproducts: { // 个人防护用品发放
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    warnNotice: { // 警示标识的张贴
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    onlineDeclaration: { // 危害申报
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    ledger: { // 原料设备的填写
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    manageSystem: { // 管理制度
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    role: { // 职能机构
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    facilities: { // 防护设施管理
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    // warnSignGenerate: { // 警示告知管理
    //   completion: { type: Number, default: 0 },
    //   validPeriod: Date,
    // },
    manageEmergency: { // 应急救援演练
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    diseasesOverhaul: { // 设施设备检测维修
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    preventionFunds: { // 防治经费管理
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },

    // 培训档案
    adminTraining: { // 主要负责人和管理人员培训
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    employeesTrainingPlan: { // 员工培训记录
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    // 检测档案
    jobHealth: { // 定期检测或者现状评价
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
    // sanitaryInspection: { // 日常监测（非必填）zhj说先不展示了
    //   completion: { type: Number, default: 0 },
    //   validPeriod: Date,
    // },

    // 体检档案
    healthcheck: { // 年度体检
      completion: { type: Number, default: 0 },
      validPeriod: Date,
    },
  });


  return mongoose.model('FilesCompleteness', FilesCompletenessSchema, 'filesCompleteness');

};
