const Service = require('egg').Service;
const moment = require('moment');


class tjAppointmentWhService extends Service {

  async createTjAppointment(params) {
    const { ctx } = this;
    const userInfo = await ctx.service.tjAppointment.getUserInfo();
    if (!ctx.session.user._id) {
      throw new Error('用户信息缺失');
    }
    if (!params.hasOwnProperty('appointDate')) {
      throw new Error('必须提交预约日期');
    }
    // 使用moment格式化日期
    params.appointDate = moment.utc(params.appointDate).subtract(8, 'hours').toISOString();
    const result = await ctx.model.TjPlan.findOne({ _id: params.tjPlanId });
    if (!result) {
      throw new Error('体检计划未找到');
    }
    // 查询当天的已预约人数和可预约总额度
    const checkDates = result.checkDates;
    const quota = checkDates.find(date => date.date.toISOString() === params.appointDate).quota;
    const isAppoint = checkDates.find(date => date.date.toISOString() === params.appointDate).isAppoint;
    const remainingSlots = quota - isAppoint;

    // 检查剩余人数
    if (remainingSlots < 1) {
      throw new Error('预约失败，当天可预约人数不足');
    }

    const appointName = result.year + '年体检预约';
    const appointmentDetail = await this.ctx.model.AppointmentDetails.create({
      tjPlanId: params.tjPlanId,
      employeeId: userInfo._id,
      appointDate: new Date(params.appointDate),
      status: '0',
      physicalExaminationOrgId: params.physicalExaminationOrgId,
      physicalExaminationOrgName: params.physicalExaminationOrgName,
      address: params.address,
      appointName,
    });
    // 更新体检计划中的员工预约状态
    console.log('filter', { _id: params.tjPlanId, 'employees.employeeId': userInfo._id });
    await this.ctx.model.TjPlan.updateOne(
      { _id: params.tjPlanId, 'employees.employeeId': userInfo._id },
      {
        $inc: {
          'checkDates.$[elem].isAppoint': 1,
        },
        $set: {
          'employees.$[elem].appointmentStatus': 1,
        },
      },
      { arrayFilters: [{ 'elem.date': new Date(params.appointDate) }] }
    ).catch(err => {
      console.log(err);
      throw new Error('更新体检中员工状态失败');
    });
    // 更新appointPeopleCount当天的已预约人数appointedPerson
    const AppointPeopleCount = await this.ctx.model.AppointPeopleCount.findOne({ organizationId: params.physicalExaminationOrgId, appointDate: new Date(params.appointDate) });
    // 如果有这一天的预约人数记录，更新 appointedPerson 字段使其加1
    if (AppointPeopleCount) {
      await this.ctx.model.AppointPeopleCount.updateOne({ _id: AppointPeopleCount._id }, { $inc: { appointedPerson: 1 } }).catch(err => {
        console.log(err);
        throw new Error('更新预约人数失败');
      });
    } else {
      const physicalExamOrg = await this.ctx.model.PhysicalExamOrg.findOne({ _id: params.physicalExaminationOrgId }, { defaultAppointPeopleCount: 1 });
      const defaultAppointPeopleCount = physicalExamOrg.defaultAppointPeopleCount;
      // 没有则创建这一天的预约人数记录
      await this.ctx.model.AppointPeopleCount.create({
        appointDate: new Date(params.appointDate),
        organizationId: params.physicalExaminationOrgId,
        appointment: defaultAppointPeopleCount,
        appointedPerson: 1,
        physicalExamined: 0,
      }).catch(err => {
        console.log(err);
        throw new Error('创建预约人数失败');
      });
    }

    return appointmentDetail;
  }
  // 取消体检预约
  async cancelTjAppointment(params) {
    if (!params.hasOwnProperty('_id')) {
      throw new Error('必须提交体检预约id');
    }
    // 删除第一个符合条件的文档并返回它
    const res = await this.ctx.model.AppointmentDetails.findOneAndDelete({ _id: params._id }, function(err) {
      if (err) {
        console.error(err);
        throw new Error('取消预约失败');
      }
    });
    // 更新体检计划中的员工预约状态
    await this.ctx.model.TjPlan.updateOne(
      { _id: params.tjPlanId, 'employees.employeeId': this.ctx.session.user._id },
      {
        $inc: {
          'checkDates.$[elem].isAppoint': -1,
        },
        $set: {
          'employees.$[elem].appointmentStatus': 0,
        },
      },
      { arrayFilters: [{ 'elem.date': new Date(params.appointDate) }] }
    ).catch(err => {
      console.log(err);
      throw new Error('更新体检中员工状态失败');
    });
    // 更新appointPeopleCount当天的已预约人数appointedPerson 使其减一
    await this.ctx.model.AppointPeopleCount.updateOne(
      { organizationId: params.physicalExaminationOrgId, appointDate: new Date(params.appointDate) },
      { $inc: { appointedPerson: -1 } }
    ).catch(err => {
      console.log(err);
      throw new Error('更新预约人数失败');
    });
    return res;
  }
  // 更新体检预约
  async updateTjAppointment(params) {
    const appointment = await this.ctx.model.AppointmentDetails.findOne({ _id: params._id });
    if (!appointment) {
      throw new Error('体检预约不存在');
    }
    if (!this.ctx.session.user._id) {
      throw new Error('用户信息缺失');
    }
    const result = await this.ctx.model.TjPlan.findOne({ _id: params.tjPlanId });
    if (!result) {
      throw new Error('体检计划未找到');
    }
    params.appointDate = moment.utc(params.appointDate).subtract(8, 'hours').toISOString();
    if (!moment(appointment.appointDate).isSame(moment(params.appointDate))) {
      const checkDates = result.checkDates;
      const newDateObj = checkDates.find(date => date.date.toISOString() === params.appointDate);
      if (newDateObj) {
        const quota = newDateObj.quota;
        const isAppoint = newDateObj.isAppoint;
        const remainingSlots = quota - isAppoint;
        if (remainingSlots < 1) {
          throw new Error('预约失败，当天可预约人数不足');
        }
      }
      // 更新体检计划中的员工预约状态
      await this.ctx.model.TjPlan.updateOne(
        { _id: params.tjPlanId, 'employees.employeeId': this.ctx.session.user._id },
        {
          $inc: {
            'checkDates.$[elem].isAppoint': 1,
          },
          $set: {
            'employees.$[elem].appointmentStatus': 1,
          },
        },
        { arrayFilters: [{ 'elem.date': new Date(params.appointDate) }] }
      ).catch(err => {
        console.log(err);
        throw new Error('更新体检中员工状态失败');
      });
      await this.ctx.model.TjPlan.updateOne(
        { _id: params.tjPlanId, 'employees.employeeId': this.ctx.session.user._id },
        {
          $inc: {
            'checkDates.$[elem].isAppoint': -1,
          },
          $set: {
            'employees.$[elem].appointmentStatus': 0,
          },
        },
        { arrayFilters: [{ 'elem.date': new Date(appointment.appointDate) }] }
      ).catch(err => {
        console.log(err);
        throw new Error('更新体检中员工状态失败');
      });

      // 更新预约人数
      await this.ctx.model.AppointPeopleCount.updateOne(
        { organizationId: params.physicalExaminationOrgId, appointDate: new Date(appointment.appointDate) },
        { $inc: { appointedPerson: -1 } }
      ).catch(err => {
        console.error(err);
        throw new Error('更新预约人数失败');
      });
      // 更新appointPeopleCount当天的已预约人数appointed
      const appointPeopleCountRecord = await this.ctx.model.AppointPeopleCount.findOne({
        organizationId: params.physicalExaminationOrgId,
        appointDate: new Date(params.appointDate),
      });
      // 如果有这一天的预约人数记录，更新 appointed 字段使其加1
      if (appointPeopleCountRecord) {
        await this.ctx.model.AppointPeopleCount.updateOne(
          { _id: appointPeopleCountRecord._id },
          { $inc: { appointedPerson: 1 } }
        ).catch(err => {
          console.error(err);
          throw new Error('更新预约人数失败');
        });
      } else {
        const physicalExamOrg = await this.ctx.model.PhysicalExamOrg.findOne(
          { _id: params.physicalExaminationOrgId },
          { defaultAppointPeopleCount: 1 }
        );
        const defaultAppointPeopleCount = physicalExamOrg.defaultAppointPeopleCount;
        // 没有则创建这一天的预约人数记录
        await this.ctx.model.AppointPeopleCount.create({
          appointDate: new Date(params.appointDate),
          organizationId: params.physicalExaminationOrgId,
          appointment: defaultAppointPeopleCount,
          appointedPerson: 1,
          physicalExamined: 0,
        }).catch(err => {
          console.error(err);
          throw new Error('创建预约人数失败');
        });
      }
    }

    let res = null;
    await this.ctx.model.AppointmentDetails.findOneAndUpdate(
      { _id: params._id },
      params,
      { new: true, useFindAndModify: false }).then(doc => {
      res = doc;
    }).catch(err => {
      console.error(err);
      throw new Error('更新体检预约详情失败');
    });
    return res;
  }
  async getTjAppointment(params) {
    const { tjPlanId, employeeId } = params;
    const query = {
      // 预约日期不做限制，只要体检计划尚未结束并且未进行体检
      tjPlanId,
      employeeId: this.ctx.session.user._id,
      // appointDate: { $gte: appointDate },
      status: '0',
    };
    if (employeeId) {
      query.employeeId = employeeId;
      delete query.status;
    }
    const res = await this.ctx.model.AppointmentDetails.aggregate([
      { $sort: { appointDate: 1 } },
      { $match: query },
      { $limit: 1 },
    ]);
    return res;
  }
}

module.exports = tjAppointmentWhService;
