const { PDFDocument } = require('pdf-lib');
const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

async function addSignatureToPDF(
  pdfPath,
  signPath,
  x,
  y,
  outPath,
  svgPath = null // 引入的 SVG 文件路径
) {
  try {
    // 从根目录读取 PDF 文件
    const pdfUrl = path.resolve(__dirname, pdfPath);
    const pdfDoc = await PDFDocument.load(fs.readFileSync(pdfUrl));

    // 加载签名图像
    const signUrl = path.resolve(__dirname, signPath);
    const signatureImage = fs.readFileSync(signUrl);

    // 将签名图像嵌入为 PDFImage 对象
    const embeddedSignatureImage = await pdfDoc.embedPng(signatureImage);

    // 初始化嵌入的左侧图片
    let embeddedLeftImage = null;

    // 检查是否传入了 SVG 文件
    if (svgPath && fs.existsSync(path.resolve(__dirname, svgPath))) {
      // 加载 SVG 文件内容
      const svgContent = fs.readFileSync(
        path.resolve(__dirname, svgPath),
        'utf-8'
      );

      // 在 SVG 的右下角添加当前日期（使用 moment）
      const updatedSVGContent = addDateToSVG(svgContent);

      // 转换更新后的 SVG 为 PNG
      const leftImageBuffer = await sharp(Buffer.from(updatedSVGContent))
        .png()
        .toBuffer();

      // 嵌入左侧 PNG 图像
      embeddedLeftImage = await pdfDoc.embedPng(leftImageBuffer);
    }

    // 获取 PDF 文档的第一页
    const firstPage = pdfDoc.getPages()[0];

    // 获取第一页的尺寸
    const { width: pageWidth, height: pageHeight } = firstPage.getSize();

    // 定义左侧图片和签名图片的大小和位置
    let leftImgWidth = 0,
      leftImgHeight = 0,
      imgWidth = 0,
      imgHeight = 0;

    // 如果左侧图片存在
    if (embeddedLeftImage) {
      // 左侧图片最大宽度和高度限制为页面尺寸的 25%
      const maxLeftWidth = pageWidth * 0.3;
      const maxLeftHeight = pageHeight * 0.3;
      const leftImgScale = Math.min(
        maxLeftWidth / embeddedLeftImage.width,
        maxLeftHeight / embeddedLeftImage.height
      );
      leftImgWidth = embeddedLeftImage.width * leftImgScale;
      leftImgHeight = embeddedLeftImage.height * leftImgScale;

      // 根据传入的 x 和 y 参数调整左侧图片位置
      const leftImgX = pageWidth * x - leftImgWidth / 2; // 中心化基于 x
      const leftImgY = pageHeight * y - leftImgHeight / 2; // 中心化基于 y

      firstPage.drawImage(embeddedLeftImage, {
        x: leftImgX,
        y: leftImgY,
        width: leftImgWidth,
        height: leftImgHeight,
      });

      // 计算签名图片的大小（等比缩小到左侧图片的尺寸内）
      const signScale = Math.min(
        leftImgWidth / embeddedSignatureImage.width,
        leftImgHeight / embeddedSignatureImage.height
      );
      imgWidth = embeddedSignatureImage.width * signScale;
      imgHeight = embeddedSignatureImage.height * signScale;

      // 绘制签名图片盖在左侧图片上面
      firstPage.drawImage(embeddedSignatureImage, {
        x: leftImgX + (leftImgWidth - imgWidth) / 2, // 居中盖在左侧图片上
        y: leftImgY + (leftImgHeight - imgHeight) / 2, // 居中盖在左侧图片上
        width: imgWidth,
        height: imgHeight,
      });
    } else {
      // 如果没有左侧图片，仅绘制签名图片
      const maxSignWidth = pageWidth * 0.25;
      const maxSignHeight = pageHeight * 0.25;
      const signScale = Math.min(
        maxSignWidth / embeddedSignatureImage.width,
        maxSignHeight / embeddedSignatureImage.height
      );
      imgWidth = embeddedSignatureImage.width * signScale;
      imgHeight = embeddedSignatureImage.height * signScale;

      // 绘制签名图片
      firstPage.drawImage(embeddedSignatureImage, {
        x: pageWidth * x - imgWidth / 2,
        y: pageHeight * y - imgHeight / 2,
        width: imgWidth,
        height: imgHeight,
      });
    }

    // 将修改后的 PDF 文档保存到新文件中
    const outputFilePath = path.resolve(__dirname, outPath);
    fs.writeFileSync(outputFilePath, await pdfDoc.save());

    console.log('PDF处理完成，保存到：', outputFilePath);
  } catch (error) {
    console.error('处理PDF时发生错误:', error.message);
  }
}

// 动态添加日期到 SVG 的右下角
function addDateToSVG(svgContent) {
  const moment = require('moment');

  // 使用 moment 获取当前日期
  const currentDate = moment().format('YYYY-MM-DD');

  // 解析 SVG 宽度和高度（假设它们在 <svg> 标签内声明）
  const svgWidthMatch = svgContent.match(/width="(\d+)"/);
  const svgHeightMatch = svgContent.match(/height="(\d+)"/);

  const svgWidth = svgWidthMatch ? parseInt(svgWidthMatch[1], 10) : 400; // 默认宽度 400
  const svgHeight = svgHeightMatch ? parseInt(svgHeightMatch[1], 10) : 300; // 默认高度 300

  // 增大字体大小，调整位置
  const fontSize = 52; // 字体大小设置为 52
  const margin = 20; // 距离边缘的间距

  // 在右下角添加日期
  const dateText = `
    <text 
      x="${svgWidth - margin}" 
      y="${svgHeight - margin}" 
      font-family="Arial" 
      font-size="${fontSize}" 
      fill="#333" 
      text-anchor="end" 
      dominant-baseline="middle"
    >
      ${currentDate}
    </text>
  `;

  // 确保在 </svg> 之前插入内容
  if (svgContent.includes('</svg>')) {
    return svgContent.replace('</svg>', `${dateText}</svg>`);
  }
  throw new Error('Invalid SVG content: Missing closing </svg> tag');
}

module.exports = addSignatureToPDF;
