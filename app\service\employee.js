const Service = require('egg').Service;
const path = require('path');
const {
  _unionQuery,
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _removeAll,
} = require(path.join(process.cwd(), 'app/service/general'));
const _ = require('lodash');
const IDValidator = require('id-validator-modify');
const GB2260 = require('id-validator-modify/src/GB2260');
const { updateData, validPhoneAndIDNum } = require('@utils/tools.js');
const moment = require('moment');
class UserService extends Service {

  async find(query) {
    return this.ctx.model.Employee.findOne(query).populate({
      path: 'departs',
      select: 'name',
    });
  }

  getUserIdsInRole(formData) {
    let employeeIds = [];
    if (formData.length > 0) {
      formData.forEach(item => {
        if (item.alisa === 'GroupLeader' || item.alisa === 'VicGroupLeader') {
          item.userId.forEach(user => {
            if (user.length > 0) employeeIds.push(user[user.length - 1]);
          });
        }
      });
    }
    employeeIds = _.uniq(employeeIds);
    return employeeIds;
  }


  async addSomeEmployee(data, EnterpriseID) {
    console.log('addSomeEmployee=======');
    const Validator = new IDValidator(GB2260);
    const { ctx } = this;
    let errType = '';// 错误信息类型 身份证或手机号
    try {
      const res = data.map(async item => {
        item.IDNum = item.IDNum + '';
        item.IDNum.replace(/[\r\n]/g, '');
        try {
          return new Promise(async (resolve, reject) => {
            // 校验
            const isValid = Validator.isValid(item.IDNum);
            if (isValid) {
              const getInfo = Validator.getInfo(item.IDNum);
              item.gender = getInfo.sex === 1 ? 0 : 1;
              item.nativePlace = getInfo.addr;
              item.age = getInfo.age;
            } else if (!isValid && item.submitType === 2) {
              item.gender = 0;
              item.nativePlace = '未知';
              item.age = 0;
            } else {
              const err = { keyValue: { IDNum: item.IDNum } };
              return reject(err);
            }
            await ctx.model.Employee.findOne({ EnterpriseID, IDNum: item.IDNum })
              .then(async originDoc => {
                if (!originDoc) {
                  // htt+++++++ 同步创建或更新绑定adminuser，user前做检验
                  validPhoneAndIDNum(ctx, 'employee', { EnterpriseID, phoneNum: item.phoneNum, IDNum: item.IDNum }).then(valid => {
                    if (valid) {
                      if (valid.indexOf('10') !== -1) {
                        errType = `手机号：${item.phoneNum}`;
                      }
                      if (valid.indexOf('20') !== -1) {
                        errType = `身份证号：${item.IDNum}`;
                      }
                      const err = { keyValue: { reson: errType, IDNum: item.IDNum }, message: valid };
                      err && reject(err);
                    } else {
                      new ctx.model.Employee(item).save()
                        .then(async doc => {
                          resolve({ doc, type: 'add' });
                        })
                        .catch(error => reject(error));
                    }
                  });


                } else {
                  // htt+++++++ 同步创建或更新绑定adminuser，user前做检验
                  validPhoneAndIDNum(ctx, 'employee', { _id: originDoc._id, EnterpriseID, phoneNum: item.phoneNum, oldPhoneNum: originDoc.phoneNum, oldIDNum: originDoc.IDNum, IDNum: item.IDNum }).then(valid => {
                    if (valid) {
                      if (valid.indexOf('10') !== -1) {
                        errType = `手机号：${item.phoneNum}`;
                      }
                      if (valid.indexOf('20') !== -1) {
                        errType = `身份证号：${item.IDNum}`;
                      }
                      const err = { keyValue: { reson: errType, IDNum: item.IDNum }, message: valid };
                      err && reject(err);
                    } else {
                      ctx.model.Employee.findOneAndUpdate({ EnterpriseID, IDNum: item.IDNum }, item, { new: true }).then(doc => {
                        resolve({ doc, type: 'update', originDeparts: originDoc.departs, originstation: originDoc.station });
                      }).catch(error => reject(error));
                    }
                  });
                }
              })
              .catch(err => {
                reject(err);
              });
          });
        } catch (err) {
          return err;
        }
      });
      return res;
    } catch (err) {
      console.log(err);
    }
  }
  // fuction: 钉钉同步通讯录
  // describe: 拼接单个用户单个部门名称的递归函数
  // author: jhw
  async findParent(alldepart, child, arr = [], idarr = []) {
    const result = _.filter(alldepart, doc => {
      return doc.id.toString() === child.toString();
    });
    if (result[0]) {
      arr.push(result[0].name.replace(/\s*/g, ''));
      idarr.push(result[0].id.toString());
    }
    if (result[0] && result[0].parentid !== 1) {
      this.findParent(alldepart, result[0].parentid, arr, idarr);
    }
    return { arr, idarr };
  }
  // describe: 拼接单个用户所有部门
  // fields:
  // **orinal(array) 用户
  // **alldepart 所有的部门
  // author: jhw
  async concatdepart(orinal, alldepart) {
    let departs = [];
    let departIds = [];
    const userdepart = orinal.indexOf('|') !== -1 ? orinal.split('|') : [ orinal ];
    for (let index = 0; index < userdepart.length; index++) {
      const item = userdepart[index];
      if (!item.includes('-1')) {
        const obj = await this.findParent(alldepart, item);
        const arr = obj.arr.reverse();
        const idarr = obj.idarr.reverse();
        departs.push(arr.join('-'));
        departIds.push(idarr.join('-'));
      }
    }
    departs = departs.join(',');
    departIds = departIds.join(',');
    return { departs, departIds };
  }

  // fuction: 钉钉同步通讯录
  // describe: 调用汪周强的批量人员导入接口
  // author: jhw
  async clearup(params, CorpId) {
    console.log('clearup=======');
    const { ctx } = this;
    let employeeInfo = {};
    let value = {};
    const employeeData = params;
    let EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    console.log(EnterpriseID);
    if (!EnterpriseID) {
      EnterpriseID = CorpId;
    }
    /**
     * htt++++++++ start
     */
    const mills = await ctx.model.MillConstruction.aggregate([
      { $match: { EnterpriseID } },
      { $unwind: '$children' },
    ]);
    /**
     * htt++++++++++++ end
     */
    employeeData.forEach(async item => {
      item.EnterpriseID = EnterpriseID;
      item.status = item.status && item.status.includes('离') ? 0 : 1;
      item.departs += '';
      item.departs = item.departs ? item.departs.split(/,|，/gi) : '';
      for (let i = 0; i < item.departs.length; i++) {
        if (!item.departs[i]) {
          item.departs.splice(i, 1);
        } else {
          item.departs[i] = item.departs[i].split(/-|_|\//gi);
        }
      }
      // item.departIds = item.departIds;
      // console.log(item.departs, '部门');
    });
    const res = await ctx.service.employee.addSomeEmployee(employeeData, EnterpriseID);
    // console.log(res)
    const errData = [];
    await Promise.allSettled(res).then(async res => {
      console.log(res);
      // const data = res.filter(item => {
      //   return item.status === 'rejected';
      // });
      for (let i = 0; i < res.length; i++) {
        if (res[i].status === 'rejected') {
          errData.push(res[i].reason);
        }
        // 如果人员导入成功，就处理人员对应的部门
        if (res[i].status === 'fulfilled') {
          value = res[i].value;
          employeeInfo = value.doc;
          // console.log(employeeInfo, 'employeeInfo======')

          // 同步或创建adminUser,user htt start+++++
          await updateData(ctx, 'employee', { _id: employeeInfo._id, EnterpriseID, type: '1' });
          // htt end

          const UDeparts = await ctx.service.employee.departsHandle({
            Uid: employeeInfo._id,
            departs: employeeInfo.departs,
            EnterpriseID,
            departIds: employeeInfo.departIds,
          });

          /**
           * htt++++++++++
           * 同步工作场所人员以及添加转岗和转部门记录
           */
          await ctx.service.employee.handleUpdateMillEmployee(value.type, value.originDeparts, UDeparts, employeeInfo, EnterpriseID, mills, value.originstation);

        }
        if (i === res.length - 1) {
          if (res.length > errData.length) {
            // htt++++++++++++++++++++++=  更新档案完成度 millConstruction
            await ctx.service.mill.updateFilesCompleteness();
            // 更新档案完成度 employees
            await ctx.service.filesCompleteness.update({
              employees: { completion: 100 },
            });

            // await ctx.service.filesComplete.update({ $set: { employees: { completion: 1 }, jobs: { completion: 1 } } });
          }
        }

      }
    }).catch(error => {
      console.log(error);
    });
  }

  async departsHandle({ Uid, departs, EnterpriseID, departIds }) {
    console.log('部门处理');
    const { ctx } = this;
    await ctx.model.Dingtree.update({ EnterpriseID, staff: Uid }, { $pull: { staff: Uid } });
    const UDeparts = [];
    let idstring = '';
    for (let i = 0; i < departs.length; i++) { // [ [ 'IT研发测试222', '后端' ] ]
      /** jhw */
      if (departIds) {
        idstring = departIds[i];
      }
      /** jhw */
      const workShop = departs[i];
      const wsRes = [];
      if (workShop) { // [ 'IT研发测试222', '后端' ]
        for (let j = 0; j < workShop.length; j++) {
          let doc;

          if (j === 0) {
            doc = await ctx.model.Dingtree.findOne({ EnterpriseID, name: workShop[j], parentid: 1 });
          } else {
            doc = await ctx.model.Dingtree.find({ EnterpriseID, name: workShop[j], parentid: { $ne: 1 } });
            doc.forEach(item => {
              if (wsRes[j - 1] && item.parentid === wsRes[j - 1]._id) {
                doc = item;
              }
            });

            if (Object.prototype.toString.call(doc) === '[object Array]') {
              doc = null;
            }
          }
          wsRes.push(doc);
        }
        const nullIndex = wsRes.indexOf(null);
        if (nullIndex !== -1) {
          for (let k = nullIndex; k < wsRes.length; k++) {
            const data = {
              name: workShop[k],
              parentid: k === 0 ? '1' : wsRes[k - 1]._id,
              EnterpriseID,
              type: '1',
              dingId: idstring ? idstring[k] : '',
            };
            const doc = await new ctx.model.Dingtree(data).save();
            wsRes[k] = doc;
          }
        }
      }
      const dingtreeId = wsRes[wsRes.length - 1]._id;
      const departsIDs = wsRes.map(item => {
        return item._id;
      });
      UDeparts.push(departsIDs);
      await ctx.model.Dingtree.update({ _id: dingtreeId }, { $addToSet: { staff: Uid } });
    }
    await ctx.model.Employee.update({ _id: Uid }, { departs: UDeparts });
    // htt+++++++
    return UDeparts;
  }

  async handleUpdateMillEmployee(type, originDeparts, UDeparts, employeeInfo, EnterpriseID, mills, originstation) {
    const { ctx } = this;
    const stationChange = originstation !== employeeInfo.station;
    if (type === 'update') {
      let diffDeparts = [],
        diffDepartObjs = [],
        addDeparts = [],
        deleteDeparts = [];
      // 导入前部门和新导入部门的交集
      diffDeparts = originDeparts.map(item => JSON.stringify(item)).filter(v => UDeparts.map(item => JSON.stringify(item)).includes(v));
      diffDepartObjs = diffDeparts.map(item => JSON.parse(item));
      // 需要删除的部门
      deleteDeparts = originDeparts.map(item => JSON.stringify(item)).concat(diffDeparts).filter(v => !originDeparts.map(item => JSON.stringify(item)).includes(v) || !diffDeparts.includes(v));
      deleteDeparts = deleteDeparts.map(item => JSON.parse(item));
      if (stationChange) {
        // 如果岗位变化的话 处理交集部门的岗位人员,从旧岗位中挪走，添加到新岗位
        await this.updateMillEmployee(originstation, EnterpriseID, diffDepartObjs, employeeInfo._id, 'del', mills);
        await this.updateMillEmployee(employeeInfo.station, EnterpriseID, diffDepartObjs, employeeInfo._id, 'add', mills);
      }
      // 更新工作场所人员以及添加转岗记录
      if (deleteDeparts[0]) {
        await this.updateMillEmployee(originstation, EnterpriseID, deleteDeparts, employeeInfo._id, 'del', mills);
      }
      // 需要新增的部门
      addDeparts = UDeparts.map(item => JSON.stringify(item)).concat(diffDeparts).filter(v => !UDeparts.map(item => JSON.stringify(item)).includes(v) || !diffDeparts.includes(v));
      addDeparts = addDeparts.map(item => JSON.parse(item));
      // 更新工作场所人员以及添加转岗记录
      if (addDeparts[0]) {
        await this.updateMillEmployee(employeeInfo.station, EnterpriseID, addDeparts, employeeInfo._id, 'add', mills);
      }

      // 更新员工工作状态变更信息
      deleteDeparts[0] && await ctx.model.EmployeeStatusChange.updateMany({ employee: employeeInfo._id }, {
        $addToSet: {
          statusChanges: {
            changType: 3,
            EnterpriseID,
            departFrom: deleteDeparts,
            message: '转部门',
          },
        },
      });
      // 新增部门
      // 更新员工工作状态变更信息
      addDeparts[0] && await ctx.model.EmployeeStatusChange.updateMany({ employee: employeeInfo._id }, {
        $addToSet: {
          statusChanges: {
            changType: 3,
            EnterpriseID,
            departsTo: addDeparts,
            message: '转部门',
          },
        },
      });
    } else if (type === 'add') {
      // 新增人员工作状态变更信息
      new ctx.model.EmployeeStatusChange({
        employee: employeeInfo._id,
        changType: 4,
        statusChanges: [{
          changType: 4,
          EnterpriseID,
          departsTo: UDeparts,
          timestamp: employeeInfo.workStart,
          message: '入职',
        }],
      }).save();
      // 更新工作场所员工和添加转岗记录
      this.updateMillEmployee(employeeInfo.station, EnterpriseID, UDeparts, employeeInfo._id, 'add', mills);
    }

  }

  // 同步车间岗位人员,添加转岗记录
  // type：删除(del)或者添加(add)
  async updateMillEmployee(station, EnterpriseID, departsMany, employeeId, type, mills) {
    const { ctx } = this;
    let stationOpt = '';
    let statusChangeOpt = '';
    if (type === 'add') {
      stationOpt = '$addToSet';
      statusChangeOpt = 'stationsTo';
    } else if (type === 'del') {
      stationOpt = '$pull';
      statusChangeOpt = 'stationFrom';
    }
    // 同步工作场所人员
    if (station) {
      let departs = [];
      for (let i = 0; i < departsMany.length; i++) {
        departs = (await ctx.model.Dingtree.find({ _id: { $in: departsMany[i] } }, 'name')).map(item => item.name);
        for (let j = 0; j < mills.length; j++) {
          if (mills[j].category === 'mill') {
            if (departs.length >= 2) { // 要求存在车间和厂房，所以长度必须大于等于2
              for (let k = 0; k < mills[j].children.children.length; k++) {
                if (mills[j].name.trim() === departs.slice(-2, -1)[0].trim() && mills[j].children.name.trim() === departs.slice(-1)[0].trim() && mills[j].children.children[k].name.trim() === station.trim()) {
                  // 如果该人员本来就在该岗位，那么就不添加，避免人员重复添加
                  if ((type === 'add' && mills[j].children.children[k].children.filter(item => item.employees === employeeId).length === 0) || type === 'del') {
                    await ctx.model.MillConstruction.updateOne({ EnterpriseID, _id: mills[j]._id },
                      { [stationOpt]: { 'children.$[i].children.$[j].children': { employees: employeeId } } },
                      {
                        arrayFilters: [
                          { 'i._id': mills[j].children._id },
                          { 'j._id': mills[j].children.children[k]._id },
                        ],
                      });
                    // 添加转岗记录
                    await ctx.model.EmployeeStatusChange.updateOne({ employee: employeeId },
                      {
                        $push: {
                          statusChanges: {
                            changType: 2,
                            EnterpriseID,
                            [statusChangeOpt]: type === 'add' ? [ mills[j].children.children[k]._id ] : mills[j].children.children[k]._id,
                            message: '转岗',
                          },
                        },
                      }
                    );
                  }
                }
              }
            }
          }
          if (mills[j].category === 'workspaces') {
            if (departs.length >= 1) { // 要求存在车间，所以长度必须大于等于1
              if (mills[j].name.trim() === departs.slice(-1)[0].trim() && mills[j].children.name.trim() === station.trim()) {
                // 避免重复添加人员
                if ((type === 'add' && mills[j].children.children.filter(item => item.employees === employeeId).length === 0) || type === 'del') {
                  await ctx.model.MillConstruction.updateOne({ EnterpriseID, name: departs.slice(-1)[0].trim(), 'children.name': station.trim() }, {
                    [stationOpt]: {
                      'children.$.children': { employees: employeeId },
                    },
                  }
                  );
                  // 添加转岗记录
                  await ctx.model.EmployeeStatusChange.updateOne({ employee: employeeId },
                    {
                      $push: {
                        statusChanges: {
                          changType: 2,
                          EnterpriseID,
                          [statusChangeOpt]: type === 'add' ? [ mills[j].children._id ] : mills[j].children._id,
                          message: '转岗',
                        },
                      },
                    }
                  );
                }
              }
            }
          }
        }
      }
    }
  }

  async unionQuery(payload, {
    collections = [],
    unwindArray = [],
    query = {},
    searchKeys = [],
    files = null,
    sort = {},
    statisticsFiles = [],
  } = {}) {
    const listdata = _unionQuery(this.ctx.model.Employee, payload, {
      collections,
      unwindArray,
      query,
      searchKeys,
      files,
      sort,
      statisticsFiles,
    });
    return listdata;
  }

  async findYuan(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
    sort = {},
  } = {}) {
    const listdata = _list(this.ctx.model.Employee, payload, {
      query,
      searchKeys,
      populate,
      files,
      sort,
    });
    return listdata;
  }

  async count(params = {}) {
    return _count(this.ctx.model.Employee, params);
  }

  async create(payload, options) {
    return _create(this.ctx.model.Employee, payload, options);
  }

  async item(res, params = {}, options) {
    return _item(res, this.ctx.model.Employee, params, options);
  }

  async update(res, _id, payload, query, options) {
    return _update(res, this.ctx.model.Employee, _id, payload, query, options);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.Employee, values, key);
  }

  async removeAll() {
    return _removeAll(this.ctx.model.Employee);
  }

  // 查询员工信息
  async findAllEmployees(params) { // 参数员工信息
    const { ctx } = this;
    try {
      const params2 = {};
      for (const key in params) {
        if (key !== 'isOutExcel' && key !== 'sort' && key !== 'name' && key !== 'pageNum' && key !== 'pageSize' && key !== 'departs') {
          if (params[key]) {
            params2[key] = params[key];
          }
        }
      }
      if (params.name) {
        params.name = params.name.trim();
        params2.$or = [{ phoneNum: { $regex: params.name } }, { name: { $regex: params.name } }, { IDNum: { $regex: params.name } }];
      }
      let res = [];
      // let totalLength = 0;
      if (params2._id) {
        // 自动计算工龄
        const employeeInfo = await ctx.model.Employee.find({ $or: [{ phoneNum: { $regex: params.name } }, { name: { $regex: params.name } }, { IDNum: { $regex: params.name } }] });
        moment.locale('zh-cn');
        res = [ await ctx.model.Employee.findOneAndUpdate({ _id: params2.employeeId }, {
          $set: {
            workYears: moment(employeeInfo[0].workStart)
              .fromNow()
              .split('前')[0],
          },
        }, { new: true }) ];
      }
      // params2.status && (params2.status = parseInt(params2.status || '1'));
      // let query = {};
      // if (params.departs) {
      //   query = { ...params2, departs: { $elemMatch: { $elemMatch: { $in: params.departs } } } };
      // } else {
      //   query = { ...params2 };
      // }
      // if (!params.pageNum || params.pageNum <= 0) params.pageNum = 1;
      // query.enable = true;
      // res = await ctx.model.Employee.find(query).skip((params.pageNum - 1) * (params.pageSize || 10))
      //   .limit(parseInt(params.pageSize) || 10)
      //   .collation({ locale: 'zh@collation=gb2312han' })
      //   .sort({ name: params.sort ? 1 : -1 || 1 });
      // totalLength = await ctx.model.Employee.find(query).countDocuments();

      const employees = JSON.parse(JSON.stringify(res));
      for (let i = 0; i < employees.length; i++) {
        employees[i].realGender = employees[i].gender === '0' ? '男' : '女';
        employees[i].realStatus = employees[i].status === 0 ? '离岗' : '在岗';
        employees[i].realWorkStart = moment(employees[i].workStart).format('YYYY.MM.DD');
        employees[i].departsName = [];
        // if (!params.isOutExcel && ctx.session.adminUserInfo.isTrialUser) {
        //   employees[i].IDNum = employees[i].IDNum ? employees[i].IDNum.replace(/^(\d{6})\d{8}(\d+)/, '$1********$2') : '';
        //   employees[i].phoneNum = employees[i].phoneNum ? employees[i].phoneNum.replace(/^(\d{3})\d{4}(\d+)/, '$1****$2') : '';
        // }


        for (let j = 0; j < employees[i].departs.length; j++) {
          employees[i].departsName[j] = [];
          for (let k = 0; k < employees[i].departs[j].length; k++) {
            const depart = await ctx.model.Dingtree.findOne({ _id: employees[i].departs[j][k] });
            employees[i].departsName[j][k] = depart ? depart.name : '';
          }
        }
        employees[i].departsName = employees[i].departsName.map(item => item.join('-')).join(',');
      }
      return { res: employees };
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

}

module.exports = UserService;
