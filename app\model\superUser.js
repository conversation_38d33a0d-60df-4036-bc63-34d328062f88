// 行政端用户对象
const encryptionPlugin = require('../utils/encryptionPlugin');
module.exports = app => {
  const mongoose = app.mongoose;
  const ctx = app.createAnonymousContext();
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const CryptoJS = require('crypto-js');
  const { dbEncryption = false } = app.config;

  require('./adminGroup');
  const SuperUserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    cname: String, // 单位名称
    regAdd: [ String ], // 单位的管辖区域范围, 目前存的是中文名
    area_code: { // 所在辖区区号
      type: String,
      require: true,
    },
    userName: String, // 账户名
    name: String, // 超级管理员姓名
    nameForStore: {
      // 加密姓名
      type: String,
    },
    nameSplitEncrypted: {
      // 分段加密的姓名
      type: String,
    },
    jobTitle: String, // 超级管理员职位
    email: String, // 邮箱
    phoneNum: String, // 超级管理员手机号码
    phoneNumForStore: {
      // 用于加密存储的手机号
      type: String,
    },
    phoneNumSplitEncrypted: {
      // 分段加密的手机号
      type: String,
    },
    countryCode: { // 手机号前国家代码
      type: String,
      default: '86',
    },
    password: { // 超级管理员密码
      type: String,
      set(val) {
        if (!dbEncryption) {
          return CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
        }
        return val;
      },
    },
    comments: String, // 备注
    landline: String, // 座机
    date: { // 创建日期
      type: Date,
      default: Date.now,
    },
    logo: {
      type: String,
      default: '/static/upload/images/defaultlogo.png',
    },
    enable: { // 账号是否受限
      type: Boolean,
      default: false,
    },
    state: { // 账号状态
      type: String,
      default: '1', // 1正常，0删除
    },
    group: { // 资源所属组ID
      type: String,
      ref: 'AdminGroup',
      default: app.config.groupID.superGroupID,
    },
    members: [{ // 本账号的其他管理成员, 也是可以登录的
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: String, // 姓名
      nameForStore: {
        // 加密姓名
        type: String,
      },
      nameSplitEncrypted: {
        // 分段加密的姓名
        type: String,
      },
      phoneNum: String,
      phoneNumForStore: {
        // 用于加密存储的手机号
        type: String,
      },
      phoneNumSplitEncrypted: {
        // 分段加密的手机号
        type: String,
      },
      passwordEncryptionAlgorithm: {
        type: String,
      },
      userName: String, // 默认是手机号
      jobTitle: String, // 职位
      password: {
        type: String,
        set(val) {
          if (!dbEncryption) {
            return CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
          }
          return val;
        },
      },
      user_role_power_hmac: String, // 用户角色权限的hmac
    }],
    powerStatus: { type: Boolean, default: false }, // 是否已开启菜单权限配置
    power: [{ type: String, ref: 'AdminResource' }], // 菜单权限
    // 人员角色权限hmac
    user_role_power_hmac: String,
    passwordEncryptionAlgorithm: { // 密码加密hmac算法
      type: String,
    },
    // 人员角色权限hmac算法
    user_role_power_hmac_algorithm: {
      type: String,
    },
    encryptionAlgorithm: { // 加密算法
      type: String,
    },
  });

  dbEncryption && SuperUserSchema.plugin(encryptionPlugin, {
    fields: {
      name: 3,
      phoneNum: 11,
    },
    model: 'SuperUser',
    ctx,
  });


  return mongoose.model('SuperUser', SuperUserSchema, 'superusers');

};
