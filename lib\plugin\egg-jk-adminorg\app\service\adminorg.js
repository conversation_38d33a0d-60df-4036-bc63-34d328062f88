/* eslint-disable no-undef */

// const { tools } = require('@utils');
const Service = require('egg').Service;
const path = require('path');
const orgValidator = require('../validate/adminorg');
const _ = require('lodash');

// general是一个公共库，可用可不用
const {
  _unionQuery,
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _removeAll,
} = require(path.join(process.cwd(), 'app/service/general'));


class AdminorgService extends Service {

  async unionQuery(payload, {
    collections = [],
    unwindArray = [],
    query = {},
    searchKeys = [],
    files = null,
    sort = {},
    statisticsFiles = [],
  } = {}) {

    const listdata = _unionQuery(this.ctx.model.Adminorg, payload, {
      collections,
      unwindArray,
      query,
      searchKeys,
      files,
      sort,
      statisticsFiles,
    });
    return listdata;
  }

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
    sort = {},
  } = {}) {

    const listdata = _list(this.ctx.model.Adminorg, payload, {
      query,
      searchKeys,
      populate,
      files,
      sort,
    });
    return listdata;

  }


  async count(params = {}) {
    return _count(this.ctx.model.Adminorg, params);
  }

  async create(payload, options) {
    return _create(this.ctx.model.Adminorg, payload, options);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.Adminorg, values, key);
  }

  async removeAll() {
    return _removeAll(this.ctx.model.Adminorg);
  }

  async update(res, _id, payload, query, options) {
    return _update(res, this.ctx.model.Adminorg, _id, payload, query, options);
  }

  async item(res, params = {}, options) {
    return _item(res, this.ctx.model.Adminorg, params, options);
  }

  async item2(res, params = {}, options) {
    return _item(res, this.ctx.model.AdminUser, params, options);
  }

  async sendVerifyMassage(ctx, beforupdate, updateRes) {
    try {
      beforupdate.isactive = parseInt(beforupdate.isactive);
      updateRes.isactive = parseInt(updateRes.isactive);
      let templateCodeName = '';
      const TemplateParam = { userName: beforupdate.adminUserId.userName, company: updateRes.cname };
      if (updateRes.isactive === 1) {
        // 发送过通短信息
        templateCodeName = 'auditPass';
      } else if (updateRes.isactive === 2) {
        // 发送未通过短信息
        templateCodeName = 'auditReject';
        TemplateParam.message = updateRes.message;
      }

      // 发送短消息
      await ctx.curl(`${this.config.iServiceHost}/api/sendSMS`, {
        method: 'POST',
        dataType: 'json', // 返回的数据类型
        data: {
          templateCodeName,
          TemplateParam: JSON.stringify(TemplateParam),
          PhoneNumbers: beforupdate.adminUserId.phoneNum,
        },
      });

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async findOne(params = {}) {
    return this.ctx.model.Adminorg.findOne(params);
  }
  // 根据企业的组织机构代码查询它的其他信息
  async findFirmsByCode(code) {
    return this.ctx.model.Firm.findOne({ $or: [
      { zch: code },
      { jgxydm: code },
    ] });
  }
  async createAdminUser(data) {
    const ctx = this.ctx;
    const oneData = new ctx.model.AdminUser(data);
    return await oneData.save();
  }
  async update2(query, data) {
    return await this.ctx.model.Adminorg.updateOne(query, data);
  }


  // 新加企业
  async createAdminOrg(fields, reportTime, orgId) {
    const { ctx, config } = this;
    try {
      const formObj = fields;
      // 查询统一社会信用代码 xmytodo 根据焦煤情况进行分子公司的查询调整
      let query = {};
      if (config.isAllowCreateOrg === '1') {
        query = { code: fields.code };
      } else {
        query = { _id: fields.enterpriseId };
      }
      const oldItem = await ctx.model.Adminorg.findOne(query);
      // 添加曾用名 生效日期用报告检测日期
      const nameUsedBefore = {
        name: formObj.cname,
        entryIntoForceAt: reportTime,
        note: '报告导入',
        sourceModel: 'ServiceOrg',
        orgId,
      };
      // 查询并创建adminUser
      // let adminUser = await ctx.model.AdminUser.findOne({
      //   phoneNum: fields.phoneNum,
      // });
      // const adminUserForm = {
      //   name: fields.contract,
      //   group: this.config.groupID.adminGroupID,
      //   phoneNum: fields.phoneNum,
      //   userName: fields.phoneNum,
      // };
      if (!_.isEmpty(oldItem)) {
        // adminorg存在当前营业执照
        // if (fields.serviceID) {
        //   if (oldItem.serviceID) {
        //     const serviceID = [];
        //     let hasServiceId = false;
        //     oldItem.serviceID.forEach(item => {
        //       if (Object.prototype.toString.call(item) === '[object Object]' && JSON.stringify(item) !== '{}') {
        //         serviceID.push(item);
        //         if (item._id === fields.serviceID[0]._id) {
        //           hasServiceId = true;
        //         }
        //       }
        //     });
        //     if (!hasServiceId) {
        //       serviceID.push(fields.serviceID[0]);
        //     }
        //     fields.serviceID = serviceID;
        //   }
        // }
        // if (!oldItem.adminUserId) { // 没有绑定adminUserid===》绑定adminUserId
        //   if (!adminUser) {
        //     const info = await tools.validPhoneAndIDNum(ctx, 'adminUser', {
        //       phoneNum: fields.phoneNum, // 需要修改得电话号码 必填
        //     });
        //     if (info) {
        //       return {
        //         status: 500,
        //         message: info,
        //       };
        //     }
        //     // adminUser = await ctx.model.AdminUser.create(adminUserForm);
        //   }
        //   formObj.adminUserId = adminUser._id;
        // }
        const cname = formObj.cname;
        if (cname && oldItem.cname && reportTime && cname !== oldItem.cname) {
          // 判断是否要修改企业名称，如果最近的生效日期在传过来的生效日期之前，那么更新企业名称，否则不更新
          const companyNameUsedBefore = oldcompany.nameUsedBefore;
          companyNameUsedBefore.sort(
            (a, b) => b.entryIntoForceAt - a.entryIntoForceAt
          );
          if (
            new Date(companyNameUsedBefore[0].entryIntoForceAt).getTime() <
            new Date(reportTime).getTime()
          ) {
            // 添加记录
            await ctx.model.Adminorg.updateOne(
              { _id: oldItem._id },
              {
                $push: { nameUsedBefore },
              }
            );
          } else {
            delete formObj.cname;
          }
        }
        await ctx.model.Adminorg.updateOne(
          { _id: oldItem._id },
          {
            $set: formObj,
          }
        );
        return oldItem;
      }
      if (!config.isAllowCreateOrg) {
        ctx.auditLog(
          '企业创建受限',
          `当前环境不允许创建企业: ${fields.cname}`,
          'warn'
        );
        return {
          status: 202, // 使用202表示请求已接受但需要在其他系统处理
          message: '当前环境要求通过OA系统创建企业，请联系系统管理员',
          requireOaIntegration: true,
          enterpriseInfo: {
            code: fields.code,
            name: fields.cname,
          },
        };
      }
      fields.regAdd = fields.regAdd || '';
      // 获取风险等级
      const industryCategoryLevel =
        await ctx.service.industryCategory.getRiskLevelByIndustryCategory(
          fields.industryCategory
        );
      if (industryCategoryLevel && industryCategoryLevel.errMsg) {
        ctx.auditLog(
          '添加企业失败',
          `失败原因：企业名称为 ${fields.cname}的 风险等级获取失败。`,
          'error'
        );
        return {
          status: 403,
          message: industryCategoryLevel.errMsg,
        };
      }

      let item = {};
      for (let i = 0; i < fields.workAddress.length; i++) {
        item = fields.workAddress[i];
        const point = await this.getPoint(
          item.districts.join('') + (item.address || '')
        );
        if (point) {
          item.point = point;
        }
      }
      formObj.PID = '0';
      formObj.level = industryCategoryLevel;

      // 表单校验
      await ctx.validate(orgValidator.adminorgRule(ctx), formObj);
      // 添加数据
      // if (!adminUser) {
      //   adminUser = await ctx.model.AdminUser.create(adminUserForm);
      // }
      formObj.createTime = new Date();
      // formObj.adminUserId = adminUser._id;
      formObj.nameUsedBefore = [ nameUsedBefore ];
      const adminorg = await ctx.model.Adminorg.create(formObj);
      if (fields.serviceID && fields.serviceID.length) {
        await ctx.service.synchroChenKeProjects.handleContract(
          formObj.code,
          fields.serviceID[0]._id,
          fields.contract,
          fields.phoneNum
        );
      }

      // 注册公司的企业名
      ctx.auditLog(
        '添加企业',
        `当前用户未注册企业，提交_id为 ${adminorg._id} 的企业信息 ${adminorg.cname} 成功。`,
        'info'
      );
      return adminorg;
    } catch (err) {
      console.log(err);
    }
  }

  // 根据中文地址查找它的经纬度
  async getPoint(address, bmap = this.app.config.bmap) {
    const getOptions = {
      data: {
        output: 'json',
        ak: bmap.ak,
        address,
      },
      timeout: 5000,
      dataType: 'json',
      method: 'GET',
    };
    const resBack = await this.ctx.curl(bmap.url, getOptions);
    if (resBack.status === 200 && resBack.data.result) {
      return [ resBack.data.result.location.lng, resBack.data.result.location.lat ];
    }
  }

  // 支付成功升级为vip, 同时更新vip的过期时间 xxn add
  async upgradeVip(out_trade_no) {
    const { ctx, config } = this;
    try {
      if (!out_trade_no) return;
      const payInfo = await ctx.model.PayInfo.findOne({ out_trade_no, usable: true, productCategory: 3 });
      if (!payInfo) {
        throw new Error(out_trade_no + '支付信息不存在或不可用');
      }
      if (payInfo.payStatus !== 1) {
        throw new Error(out_trade_no + '支付未成功');
      }
      if (!payInfo.paymentUnitID) {
        throw new Error(out_trade_no + '支付单位不存在');
      }
      const adminorg = await ctx.model.Adminorg.findOne({ _id: payInfo.paymentUnitID }, { isVip: 1, effectiveDate: 1, payInfoID: 1 });
      if (!adminorg) {
        throw new Error(payInfo.paymentUnitID + '支付单位不存在');
      }
      if (adminorg.payInfoID === payInfo._id) {
        ctx.auditLog('企业升级vip', '该支付订单号已经升级vip了', 'info');
        return;
      }
      // 获取套餐信息
      const subscriptionId = payInfo.description.replace('企业套餐', '');
      const subscription = config.subscriptionList.find(item => item._id === subscriptionId);
      if (!subscription) {
        throw new Error('未找到对应的套餐信息: ' + subscriptionId);
      }
      let effectiveDate; // vip过期时间
      if (adminorg.isVip === 'vip' && adminorg.effectiveDate && adminorg.effectiveDate.getTime() > new Date().getTime()) {
        const effectiveDate = new Date(adminorg.effectiveDate);
        effectiveDate.setFullYear(effectiveDate.getFullYear() + subscription.validYears);
      } else {
        effectiveDate = new Date();
        effectiveDate.setFullYear(effectiveDate.getFullYear() + subscription.validYears);
      }
      const updateData = {
        payInfoID: payInfo._id,
        payCategoryID: subscriptionId,
        personCount: subscription.maxPeopleNum,
        effectiveDate,
        isVIP: 'vip',
      };
      const res = await ctx.model.Adminorg.updateOne({ _id: adminorg._id }, updateData);
      if (res.nModified === 0) {
        throw new Error(`升级vip失败，支付订单号为 ${out_trade_no} 的企业套餐信息更新失败`);
      }
    } catch (err) {
      ctx.auditLog('企业升级vip', err.message, 'error');
      throw err;
    }
  }

  /**
   * 企业分页查询
   * @param {Object} query 查询参数
   * @return {Object} { list, pageInfo }
   */
  async queryEnterpriseListV1_0(query = {}) {
    const { ctx } = this;
    // 分页参数
    const page = parseInt(query.page) > 0 ? parseInt(query.page) : 1;
    const limit = parseInt(query.limit) > 0 ? parseInt(query.limit) : 10;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const condition = {};
    if (query.cname) condition.cname = { $regex: query.cname, $options: 'i' };
    if (query.code) condition.code = query.code;
    if (query.enterpriseId) condition._id = query.enterpriseId;
    if (query.parentId) condition.parentId = { $in: Array.isArray(query.parentId) ? query.parentId : [ query.parentId ] };
    if (query.childrenId) condition.childrenId = { $in: Array.isArray(query.childrenId) ? query.childrenId : [ query.childrenId ] };
    if (query.shortName) condition.shortName = { $regex: query.shortName, $options: 'i' };

    // 查询字段
    const projection = {
      enterpriseId: '$_id',
      cname: 1,
      code: 1,
      parentId: { $arrayElemAt: [ '$parentId', 0 ] },
      childrenId: 1,
      shortName: 1,
      regAdd: 1,
      _id: 0,
    };

    // 查询总数
    const total = await ctx.model.Adminorg.countDocuments(condition).skip(skip).limit(limit);
    // 查询数据
    const list = await ctx.model.Adminorg.find(condition, projection)
      .skip(skip)
      .limit(limit)
      .lean();

    return {
      list,
      pageInfo: {
        total,
        page,
        limit,
      },
    };
  }

}

module.exports = AdminorgService;
