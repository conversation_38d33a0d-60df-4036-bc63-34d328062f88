/**
 * Created by Zhanglc on 2022/3/18.
 * 上传文件记录
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const moment = require('moment');

  const UploadFileSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    createTime: { // 创建时间
      type: Date,
    },
    updateTime: { // 修改时间
      type: Date,
    },
    type: {
      type: String,
      emun: [ 'local', 'qn', 'oss' ],
    }, // 上传方式
    uploadPath: String, // 本地上传路径
    qn_bucket: String, // 七牛配置 无用
    qn_accessKey: String,
    qn_secretKey: String,
    qn_zone: String,
    qn_endPoint: String,
    oss_bucket: String, // oss配置 无用
    oss_accessKey: String,
    oss_secretKey: String,
    oss_region: String,
    oss_endPoint: String,
    oss_apiVersion: String,
  });

  UploadFileSchema.set('toJSON', {
    getters: true,
    virtuals: true,
  });
  UploadFileSchema.set('toObject', {
    getters: true,
    virtuals: true,
  });

  UploadFileSchema.path('createTime').get(function(v) {
    return moment(v).format('YYYY-MM-DD HH:mm:ss');
  });
  UploadFileSchema.path('updateTime').get(function(v) {
    return moment(v).format('YYYY-MM-DD HH:mm:ss');
  });

  return mongoose.model('UploadFile', UploadFileSchema, 'uploadfiles');
};
