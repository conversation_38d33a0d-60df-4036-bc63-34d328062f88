module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const violationInfoSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      // 违章日期
      violationDate: {
        type: Date,
        default: Date.now,
      },
      // 违章人
      violator: [{
        _id: {
          type: String,
          default: '',
        },
        name: {
          type: String,
          default: '',
        },
        employeeId: {
          type: String,
          default: '',
        },
        EnterpriseID: {
          type: String,
          default: '',
        },
      }],
      // 违规事件
      violationEvent: {
        type: String,
        default: '',
      },
      // 处罚结果
      punishmentResult: {
        type: String,
        default: '',
      },
      // 处罚单位
      punishmentUnit: {
        type: String,
        default: '',
      },
      // 上传附件
      attachment: [
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          originName: {
            type: String,
            default: '',
          },
          staticName: {
            type: String,
            default: '',
          },
        },
      ],
    },
    { timestamps: true }
  );
  return mongoose.model('violationInfo', violationInfoSchema);
};
