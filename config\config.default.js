const path = require('path');
const fs = require('fs');

module.exports = appInfo => {
  return {
    bodyParser: {
      enable: false, // 配合bodyparser.js中间件
      encoding: 'utf8',
      enableTypes: [ 'json', 'form', 'text' ],
      extendTypes: {
        text: [ 'text/xml', 'application/xml' ],
      },
    },
    session: {
      key: 'DUOPU_SESS',
      maxAge: 30 * 24 * 3600 * 1000, // 1 day
      httpOnly: true,
      encrypt: true,
      renew: true, // 延长会话有效期
    },
    // redis相关配置
    redis: {
      client: {
        host: process.env.redis_host || 'valkey.valkey.svc',
        port: +process.env.redis_port || 6379,
        password: process.env.redis_pass || '',
        db: process.env.redis_db || null,
        retryStrategy: times => {
          // 重试策略
          return Math.min(times * 50, 2000); // 每次重试时间间隔 50ms 递增，最大 2s
        },
      },
    },

    // 后台管理员登录有效时间
    adminUserMaxAge: 1000 * 60 * 60 * 24 * 30, // 30 day
    jwtUserExpiresIn: '30day', // 用户信息结构化赋值有效期
    // 设置网站图标
    siteFile: {
      '/favicon.ico': fs.readFileSync(
        path.join(appInfo.baseDir, 'app/public/favicon.ico')
      ),
    },
    storageType: process.env.storageType
      ? process.env.storageType.replace('\n', '')
      : '', // 存储类型： local, oss
    oss: {
      accessKeyId: process.env.accessKey
        ? process.env.accessKey.replace('\n', '')
        : '',
      accessKeySecret: process.env.secretKey
        ? process.env.secretKey.replace('\n', '')
        : '',
      endPoint: process.env.endPoint
        ? process.env.endPoint.replace('\n', '')
        : '',
      region: process.env.region ? process.env.region.replace('\n', '') : '',
      timeout: process.env.ossTimeout
        ? process.env.ossTimeout.replace('\n', '')
        : '60s',
      buckets: {
        default: {
          name: process.env.bucket ? process.env.bucket.replace('\n', '') : '',
          accessPolicy: process.env.accessPolicy
            ? process.env.accessPolicy.replace('\n', '')
            : '', // 是否是私有的bucket，默认是false
        },
      },
    },
    // gzip压缩
    compress: {
      threshold: 2048,
    },
    middleware: [
      'bodyparser',
      'proxyMiddleware',
      'notfoundHandler',
      'crossHeader',
      'compress',
      'authAdminToken',
      'authAdminPower',
      'authToken',
      'authApiPower',
      'authGovToken',
      'authGovPower',
      'authQlcToken',
      'authQlcPower',
      'authAllUserToken',
    ],
    // api 可跨域的
    crossHeader: {
      match: [
        '/dingSubscript',
        '/app',
        '/api',
        '/manage',
        '/govApp',
        '/govManage',
        '/appjcqlc',
        '/qlcManage',
        '/mqtt',
        '/common',
        '/dpd',
      ],
    },
    // 劳动者APP后台token校验
    authAdminToken: {
      match: [ '/manage', '/admin', '/qy', '/user' ],
    },
    // 劳动者APP后台权限校验
    authAdminPower: {
      match: [ '/manage' ],
    },
    // 行政端APP后台token鉴权
    authGovToken: {
      match: '/govManage',
    },
    // 行政端APP后台权限校验
    authGovPower: {
      match: [ '/govManage' ],
    },
    // 机构VIP端APP后台token鉴权
    authQlcToken: {
      match: '/qlcManage',
    },
    // toolsAPP后台权限校验
    authAllUserToken: {
      match: [ '/app/allUser' ],
    },
    // 机构VIP端APP后台权限校验
    authQlcPower: {
      match: [ '/qlcManage' ],
    },
    authToken: {
      // 未来的token鉴权中间件
      match: [ '/api', '/chenkSystem', '/crm' ],
    },
    // API权限校验
    authApiPower: {
      match: [ '/api', '/chenkSystem', '/crm' ],
    },
    // 文件上传
    multipart: {
      fields: '100',
      fileSize: '50mb',
      mode: 'stream',
      whitelist: [
        '.jpg',
        '.jpeg', // image/jpeg
        '.png', // image/png, image/x-png
        '.gif', // image/gif
        '.bmp', // image/bmp
        // '.wbmp', // image/vnd.wap.wbmp
        // '.webp',
        // '.tif',
        // '.psd',
        // text
        // '.svg',
        // '.js',
        // '.jsx',
        // '.json',
        // '.css',
        // '.less',
        // '.html',
        // '.htm',
        // '.xml',
        // tar
        // '.zip',
        // '.gz',
        // '.tgz',
        // '.gzip',
        // video
        // '.mp3',
        '.mp4',
        '.avi',
        '.doc',
        '.docx',
        '.pdf',
        '.xlsx',
        '.jfif',
      ],
      // fileExtensions: [ //当重写了 whitelist 时，fileExtensions 不生效。
      //   '.doc',
      //   '.docx',
      //   '.pdf',
      //   '.xlsx',
      //   '.jfif',
      // ], // 扩展几种上传的文件格式
    },

    imageType: [ '.png', '.jpeg', '.jpg', '.bmp', '.jfif' ],
    imagePDFType: [ '.png', '.jpeg', '.jpg', '.bmp', '.jfif', '.pdf' ],
    imagePDFWordType: [
      '.png',
      '.jpeg',
      '.jpg',
      '.bmp',
      '.jfif',
      '.pdf',
      '.docx',
      '.doc',
    ],
    phyReportType: [ '.pdf', '.docx', '.doc' ],

    // 检索代号：#0001  后台管理根目录
    admin_base_path: '/admin',
    qy_base_path: '/qy',
    user_base_path: '/user',

    // 存放生成的企业档案的文件系统目录
    enterprise_path: process.cwd() + '/app/public/enterprise',
    // 下载企业档案的http路径
    enterprise_http_path: '/enterprise',
    enterpriseUpload_http_path: '/upload/enterprise',
    enterpriseUpload_path: process.cwd() + '/app/public/upload/enterprise',
    // 用户自行上传文件的文件系统目录，代码拼接: upload_path + /EnterpriseID/ + 文件名
    upload_path: process.cwd() + '/app/public/upload/images',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_http_path + /EnterpriseID/ + 文件名
    upload_http_path: '/upload/images',

    // 上传的app文件，代码拼接: upload_courses_path + '/' + 文件名
    upload_app_path: process.cwd() + '/app/public/upload/appManage',
    // 上传的app文件的http路径， 代码拼接：/static + upload_app_http_path + '/' + 文件名
    upload_app_http_path: '/upload/appManage',

    // CMS上传文件的文件系统目录
    upload_cms_path: {
      upload_path: process.cwd() + '/app/public',
      static_root_path: 'cms', // 针对云存储可设置
    },

    // 存放培训网站相关文件系统目录，代码拼接：trainSitFile_path + 文件名
    trainSitFile_path: process.cwd() + '/app/public/upload/trainSitFile',
    // 浏览培训网站相关http路径，代码拼接：/static+ trainSitFile_http_path + 文件名
    trainSitFile_http_path: '/upload/trainSitFile',

    // 课程封面目录，代码拼接: upload_courses_path + /课程ID/ + 文件名
    upload_courses_path: process.cwd() + '/app/public/upload/courses',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_courses_http_path + /课程ID/ + 文件名
    upload_courses_http_path: '/upload/courses',

    // 存放生成的培训证书的文件系统目录，代码拼接：certificate_path + 文件名
    certificate_path: process.cwd() + '/app/public/certificate',
    // 下载培训证书的http路径，代码拼接：/static+ certificate_http_path + 文件名
    certificate_http_path: '/certificate',

    // 上传体检报告目录，代码拼接: upload_phy_report_path + /体检机构ID/ + 文件名
    upload_phy_report_path: process.cwd() + '/app/public/upload/tjReport',
    // 上行上传体检报告的http路径， 代码拼接：/static + upload_http_phy_report_path + /体检机构ID/ + 文件名
    upload_http_phy_report_path: '/upload/tjReport',

    // 存放生成的报告的文件系统目录，代码拼接：report_path + /EnterpriseID/ + 文件名
    report_path: process.cwd() + '/app/public/report',
    // 用于下载生成报告的文件目录，代码拼接：/static + report_http_path + /EnterpriseID/ + 文件名
    report_http_path: '/report',
    sign_path: process.cwd() + '/app/public/upload/sign', // 员工签名
    sign_http_path: '/upload/sign',

    // 用于生成档案的word模板存放位置，直接获取文件系统目录
    report_template_path: process.cwd() + '/app/public/reportTemplate',

    // 福州职防院系统对接传接zip，代码拼接: upload_fzDataLog_path + /日期20230918/ + 文件名 接口+时间
    upload_fzDataLog_path: process.cwd() + '/app/public/fzData/',
    // 陕西职业健康工作信息数据交换传接zip，代码拼接: upload_shaanxiDataLog_path + /日期20240523/ + 文件名 接口+时间
    upload_shaanxiDataLog_path: process.cwd() + '/app/public/shaanxiData/',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_fzDataLog_http_path + /日期20230918/ + 文件名
    upload_fzDataLog_http_path: '/fzData/',
    // 上行用户自行上传文件的http路径， 代码拼接：/static + upload_shaanxiDataLog_http_path + /日期20240523/ + 文件名
    upload_shaanxiDataLog_http_path: '/shaanxiData/',
    isVersionUpdate: true,
    // 版本号
    version: '1.0.910',
    // 是否开启静态界面自动更新 默认为false  警告：慎重！！！如果改为false，强烈建议手动清空缓存或开启手动定版并更新版本号后再加载，否则有可能加载的并非最新界面
    isAutoUpdate: false,

    dingTalk: {
      // 钉钉
      AgentId: '3158382621',
      AppKey: 'dingzl7qv8tgxsuuccrz',
      AppSecret:
        'gNWpLpL3iXlHR0spPoPo4c1C4rNbsBLTELmrZ61kBhcPIxm0CJB-IDbnToOjq-Fe',
      CorpId: 'ding2fa4df5d78220543',
    },
    // 用于绑定用户表单中各用户类型的ID
    groupID: {
      operateGroupID: 'E1XjEmqA', // 超级管理员，运营用户角色ID
      superGroupID: 'RMwIEyjWK', // 政府用户角色ID
      adminGroupID: 'vGEfBpfsv', // 企业用户角色ID
      serviceGroupID: 'e4Qf2ic6-', // 机构用户角色ID
      userGroupID: 'V7au7L2Rw', // 劳动者用户角色ID
      physicalExamGroupID: 'zHLKFCyXD', // 体检用户角色ID
      enterpriseGroupID: 'WnsDSP177', // 企业端集团用户角色ID
      serviceGroupVIPID: '8e5D3G8s3', // 机构VIP(全流程)用户的角色ID
      pxGroupID: 'TOR4t7OX4', // 培训机构用户角色ID
      groupBaseRoleID: 'vVzeYfEYR', // 集团分支基本角色ID
    },
    dingInfo: {
      AppKey: 'dingzl7qv8tgxsuuccrz',
      AppSecret:
        'gNWpLpL3iXlHR0spPoPo4c1C4rNbsBLTELmrZ61kBhcPIxm0CJB-IDbnToOjq-Fe',
      CorpId: 'ding2fa4df5d78220543',
      aes_key: 'ziURUNyRJlfN3WXaQtyq25b2dEBHfjrB9mrkzxlNbUo',
      token: 'Ut24XKFgH4p8akjtWNXTO',
    },
    filepreviewHost: 'https://fpreview.zyws.cn',
    // 模板配置
    zywsApprovalConfig: {
      JcqlcProject: {
        typeName: '职业卫生',
        nodes: [
          {
            node: 'contractReview',
            name: '合同评审',
            _idField: 'process_instance_id',
            statusField: 'approved',
          },
          {
            node: 'samplingPlanApprove',
            name: '检测方案审核',
            _idField: 'samplingPlanApproveId',
            statusField: 'samplingPlanApprove',
            approveField: 'samplingPlan',
          },
          {
            node: 'PJsamplingPlanApprove',
            name: '评价方案审核',
            _idField: 'samplingPlanApproveId',
            statusField: 'samplingPlanApprove',
            approveField: 'samplingPlan',
          },
          {
            node: 'labReportReview',
            name: '实验室报告单审核',
            _idField: 'reportProcessInstanceId',
            statusField: 'reportApproved',
            approveField: 'labreport',
          },
          {
            node: 'reportReview',
            name: '报告审核',
            _idField: 'reportFinalProcessInstanceId',
            statusField: 'reportFinalApproved',
          },
          {
            node: 'finishProjectApproved',
            name: '项目完成审批',
            _idField: 'finishProjectProcessInstanceId',
            statusField: 'finishProjectApproved',
            approveField: 'finishProject',
          },
          {
            node: 'samplingPlanAmend',
            name: '方案修改审批',
            _idField: 'samplingPlanProcessInstanceId',
            statusField: 'samplingPlanAmendApproved',
          },
          {
            node: 'spotRecordAmend',
            name: '现场采样修改审批',
            _idField: 'spotRecordProcessInstanceId',
            statusField: 'spotRecordAmendApproved',
            approveField: 'spotRecord',
          },
          {
            node: 'labAmend',
            name: '实验室修改审批',
            _idField: 'labProcessInstanceId',
            statusField: 'labAmendApproved',
            approveField: 'lab',
          },
          {
            node: 'applyAcceptance',
            name: '标准物质领用审批',
            _idField: 'applyAcceptanceProcessInstanceId',
            statusField: 'applyAcceptanceApproved',
            approveField: 'applyAcceptance',
          },
        ],
      },
      RadiateqlcProject: {
        typeName: '放射卫生',
        nodes: [
          {
            node: 'contractReview',
            name: '合同评审',
            _idField: 'process_instance_id',
            statusField: 'approved',
          },
          {
            node: 'schemeReview',
            name: '方案审核',
            _idField: 'samplingPlanApproveId',
            statusField: 'samplingSchemeReview',
          },
          {
            node: 'draftReview',
            name: '底稿审核',
            _idField: 'draftReportInstanceId',
            statusField: 'draftReportApproved',
            allowReturn: true, // 允许回退，审批中退回直接回到指定节点
          },
          {
            node: 'officialDraftReview',
            name: '正式稿审核',
            _idField: 'officialDraftReportInstanceId',
            statusField: 'reportApproved',
            allowReturn: true,
          },
          {
            node: 'radiateMApply',
            name: '放射修改审批',
            _idField: 'reportProcessInstanceId',
            statusField: 'reportReview',
          },
        ],
      },
      PersonalDoseProject: {
        typeName: '个人剂量',
        nodes: [
          {
            node: 'contractReview',
            name: '合同评审',
            _idField: 'process_instance_id',
            statusField: 'approved',
          },
          {
            node: 'schemeReview',
            name: '方案审核',
            _idField: 'samplingPlanApproveId',
            statusField: 'samplingSchemeReview',
          },
          {
            node: 'draftReview',
            name: '底稿审核',
            _idField: 'draftReportInstanceId',
            statusField: 'draftReportApproved',
            allowReturn: true, // 允许回退，审批中退回直接回到指定节点
          },
          {
            node: 'officialDraftReview',
            name: '正式稿审核',
            _idField: 'officialDraftReportInstanceId',
            statusField: 'reportApproved',
            allowReturn: true,
          },
          {
            node: 'yearPersonReport',
            name: '年剂量审核',
            _idField: 'yearReportInstanceId',
            statusField: 'yearReportApproved',
          },
        ],
      },
      IndustrialRayProject: {
        typeName: '工业放射',
        nodes: [
          {
            node: 'contractReview',
            name: '合同评审',
            _idField: 'process_instance_id',
            statusField: 'approved',
          },
          {
            node: 'schemeReview',
            name: '方案审核',
            _idField: 'samplingPlanApproveId',
            statusField: 'samplingSchemeReview',
          },
          {
            node: 'draftReview',
            name: '底稿审核',
            _idField: 'draftReportInstanceId',
            statusField: 'draftReportApproved',
            allowReturn: true, // 允许回退，审批中退回直接回到指定节点
          },
          {
            node: 'officialDraftReview',
            name: '正式稿审核',
            _idField: 'officialDraftReportInstanceId',
            statusField: 'reportApproved',
            allowReturn: true,
          },
        ],
      },
    },
    // 个人剂量
    // personProcessCodes: [
    //   {
    //     code: 'PROC-95950656-4C1D-42B5-820A-1643990CBD43',
    //     _idField: 'reportProcessInstanceId', // 报告审批
    //     statusField: 'reportApproved',
    //   },
    // ],
    radProcessCodes: [
      // 放射流程钉钉审批
      {
        node: 'contractReview',
        _idField: 'process_instance_id', // 合同审批
        statusField: 'approved',
      },
      {
        node: 'reportReview',
        _idField: 'reportProcessInstanceId', // 放射报告审核
        statusField: 'reportReview',
      },
      {
        node: 'officialDraftReview',
        _idField: 'reportProcessInstanceId', // 正式稿审核
        statusField: 'reportReview',
      },
      {
        node: 'radiateMApply',
        // code: 'PROC-E6151FA9-2259-4E14-AF23-133AE80C33A7',
        _idField: 'MApplyId', // 修改审批
        statusField: 'MApplyStatus',
      },
      {
        node: 'schemeReview',
        name: '方案审核',
        _idField: 'samplingPlanApproveId',
        statusField: 'samplingSchemeReview',
      },
    ],
    industrialRadProcessCodes: [
      // 工业放射流程钉钉审批
      {
        code: 'PROC-BA0C756C-8B7E-448F-A17B-798CA84D8A4E',
        _idField: 'samplingPlanApproveId', // 合同审批
        statusField: 'samplingSchemeReview',
      },
    ],
    qlcProcessCodes: [
      // 全流程钉钉审批
      // {
      //   node:['JcqlcProject','samplingPlanApprove'],
      //   code: 'PROC-477BF2BA-4D09-4376-BB01-A03EB2FBE22E',
      //   _idField: 'evaluationSchemeId', // 评价方案审核记录
      //   statusField: 'samplingPlanApprove',
      // },
      {
        node: 'labReportReview',
        _idField: 'reportProcessInstanceId', // 实验室审批单
        statusField: 'reportApproved',
        approveField: 'labreport',
      },
      {
        node: 'contractReview',
        _idField: 'process_instance_id', // 合同审批
        statusField: 'approved',
      },
      {
        node: 'reportReview',
        _idField: 'reportFinalProcessInstanceId', // 报告审批
        statusField: 'reportFinalApproved',
      },
      {
        node: 'samplingPlanApprove', // 方案审批
        _idField: 'samplingPlanApproveId',
        statusField: 'samplingPlanApprove',
        approveField: 'samplingPlan',
      },
      {
        node: 'samplingPlanAmend', // 计划单数据修改审批
        _idField: 'samplingPlanProcessInstanceId',
        statusField: 'samplingPlanAmendApproved',
        approveField: 'samplingPlan',
      },
      {
        node: 'spotRecordAmend', // 现场检测数据修改审批
        _idField: 'spotRecordProcessInstanceId',
        statusField: 'spotRecordAmendApproved',
        approveField: 'spotRecord',
      },
      {
        node: 'labAmend', // 实验室修改审批
        _idField: 'labProcessInstanceId',
        statusField: 'labAmendApproved',
        approveField: 'lab',
      },
      {
        node: 'finishProjectApproved', //  完成项目审批
        _idField: 'finishProjectProcessInstanceId',
        statusField: 'finishProjectApproved',
        approveField: 'finishProject',
      },
      {
        node: 'applyAcceptance', //  标准物质领用审批
        _idField: 'applyAcceptanceProcessInstanceId',
        statusField: 'applyAcceptanceApproved',
        approveField: 'applyAcceptance',
      },
    ],

    h5WXAuth: {
      // 微信公众号 目前是测试账号
      appid: 'wxa4809765e6ced84a',
      secret: '046d52d5258b5c5d44b5df239a9296ae',
    },
    wxAutho: {
      // 用户微信授权
      appid: 'wx8d9eada10facd67a',
      secret: '8a0356bdd0c2cc106d4826af474a49b8',
    },
    qywxAutho: {
      // 企业微信授权
      corpid: 'ww17f8d10783494584',
      corpsecret: 'i5t-rh8bXeNCgihcYPrG9ZPpWkivzPJ69sv570osk6I',
    },
    alipayAutho: {
      // 支付宝
      mp_appid: '2021002116629798',
      h5_appid: '2021002117637851',
    },

    China: {
      area_code: '100000',
      name: '中国',
    }, // 全国行政辖区，district中添加则不规范。

    // 阿里云短信接口配置
    // aliMessage: {
    //   accessKeyId: process.env.aliMessage_accessKeyId,
    //   accessKeySecret: process.env.aliMessage_accessKeySecret,
    //   endpoint: 'https://dysmsapi.aliyuncs.com',
    //   apiVersion: '2017-05-25',
    // },

    keys: 'duopu_jkqy',
    cluster: {
      listen: {
        port: 7009,
        hostname: '',
      },
    },

    uploadFileRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/uploadFile'),
        ctx => ctx.path.startsWith('/api/upload'),
      ],
    },

    adminorgRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/adminorg'),
        ctx => ctx.path.startsWith('/api/adminorg'),
        ctx => ctx.path.startsWith('/app/adminorg'),
      ],
    },

    adminuserRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/adminuser'),
        ctx => ctx.path.startsWith('/api/adminuser'),
        ctx => ctx.path.startsWith('/app/adminuser'),
      ],
    },
    // 试卷管理
    testPaperRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/testPaper'),
        ctx => ctx.path.startsWith('/manage/questionBank'),
      ],
    },
    // 培训管理
    trainingRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/adminTraining'),
        ctx => ctx.path.startsWith('/manage/employeeTraining'),
        ctx => ctx.path.startsWith('/manage/courses'),
        ctx => ctx.path.startsWith('/manage/training'),
        ctx => ctx.path.startsWith('/manage/agent'),
      ],
    },
    pPESelfLiftCabinetRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/PPESelfLiftCabinet'),
        ctx => ctx.path.startsWith('/api/PPESelfLiftCabinet'),
      ],
    },
    physicalexamorgRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/physicalExamOrg'),
        ctx => ctx.path.startsWith('/api/physical'),
      ],
    },
    // 考试大纲
    examSyllabusRouter: {
      match: [ ctx => ctx.path.startsWith('/manage/examSyllabus') ],
    },
    // pxapp
    // pxappRouter: {
    //   match: [
    //     ctx => ctx.path.startsWith('/manage/pxapp'),
    //   ],
    // },
    // 福州问卷小游戏
    quizGameRouter: {
      match: [
        ctx => ctx.path.startsWith('/manage/quizGame'),
        ctx => ctx.path.startsWith('/api/quizGame'),
      ],
    },

    // 数据备份定时
    backUpTick: '0 0 0 */1 * ?', // 每天凌晨0点执行一次  cron表达式  */1表示刚开始0点触发一次，之后每一天0点触发一次

    // cdn域名
    origin: 'http://localhost:7009',
    // 加密解密
    session_secret: 'duopu_secret',
    auth_cookie_name: 'zwkj_zyws_oapi',
    auth_toolscookie_name: 'zwkj_zyws_tools',
    auth_govcookie_name: 'zwkj_zyws_super',
    auth_jcqlccookie_name: 'zwkj_zyws_jcqlc',
    encrypt_key: '243d477d-d6bb-4ab1-b0a9-a1b1c9419562',
    cms_encrypt_key: 'duopu_jkqy',
    salt_aes_key: 'duopu_jkqy',
    salt_sha2_key: '7a58917b-d493-4940-87d4-ab2b0fa8a3b7',
    encryptApp_key: '751f621ea5c8f930',
    encryptApp_vi: '2624750004598718',
    enterpriseUserGroup: 'vGEfBpfsv',

    // 不同端的域名
    domainNames: {
      px: '127.0.0.1:3001',
    },
    // 安全性校验
    security: {
      csrf: {
        enable: false,
      },
    },
    // nunjucks模板引擎
    view: {
      defaultViewEngine: 'nunjucks',
      mapping: {
        '.html': 'nunjucks',
      },
    },

    // 国际化
    i18n: {
      defaultLocale: 'zh-CN',
    },

    proxy: true,
    ipHeaders: 'X-Real-Ip, X-Forwarded-For',
    // 输出日志到终端
    logger: {
      disableConsoleAfterReady: false,
      dir: '/opt/log/oapi/',
    },

    // 百度地图 url和ak
    bmap: {
      url: 'https://api.map.baidu.com/geocoding/v3/',
      ak: 'klt8PdaqU2RBUn3noMpzKtRw5xvMml9y',
      styleId: 'c5212ceaa7fe7147d983983febb8ecbd', // xxn的百度地图id
    },

    // 颜金潭的阿里云视频
    aliVideo: {
      accessKeyId: process.env.aliVideo_accessKeyId,
      secretAccessKey: process.env.aliVideo_secretAccessKey,
      userId: process.env.aliVideo_userId,
    },
    // 人脸识别
    facebody: {
      accessKeyId: process.env.facebody_accessKeyId,
      accessKeySecret: process.env.facebody_accessKeySecret,
    },

    // 本地环境：文档类别id设置 对应的是表contentCategory的id
    categories: {
      learning: [
        { name: '法律', id: 'E1lagiaw' }, // law
        { name: '法规', id: 'V1U2-a9le' }, // legislation
        { name: '规章', id: '4JknAqTv' }, // regulation
        { name: '规范性文件', id: 'N7c-AjP_' }, // normativeDocs
      ],
      industryNews: {
        // 首页上的
        name: '行业动态',
        id: 'PUc7czJc',
      },
      informationPlatform: {
        name: '健康企业信息平台', // 首页上的
        id: '293sdcDe',
      },
      trainingEducation: {
        // 培训首页上的
        name: '培训教育平台',
        id: '7GN5gLomj',
      },
      health: [
        { name: '健康达人', id: 'gUVZmfDjLw' },
        { name: '健康企业', id: 'DsTkLv_O8b' },
      ],
    },
    gmap: {
      url: 'https://restapi.amap.com/v3/geocode/geo',
      key: 'c2755a184bf2bcbd83cb73aa0b501187',
    },
    mqtt: {
      ip: 'tcp://mqtt.zyws.cn',
      port: 1883,
    },
    rabbitMq: {
      url: 'amqp://su:<EMAIL>:5672',
      queues: [
        {
          name: 'shaanxiHealthExamRecordData',
          exchange: 'shaanxi',
          service: 'shaanxiData',
          callback: 'uploadShaanxiHealthExamRecordData',
          routingKey: 'shaanxiHealthExamRecordData',
        },
        // {
        //   name: 'whPersonInfo',
        //   exchange: 'whohs',
        //   service: 'whData',
        //   callback: 'processPersonInfo',
        //   routingKey: 'whPersonInfo',
        // },
        // {
        //   name: 'whAccountData',
        //   exchange: 'whohs',
        //   service: 'whData',
        //   callback: 'processPersonAndDingtree',
        //   routingKey: 'whAccountData',
        // },
        // {
        //   name: 'whOrgData',
        //   exchange: 'whohs',
        //   service: 'whData',
        //   callback: 'processRedisData',
        //   routingKey: 'whOrgData',
        // },
        // {
        //   name: 'whPostInfo',
        //   exchange: 'whohs',
        //   service: 'whData',
        //   callback: 'postInfoReceive',
        //   routingKey: 'whPostInfo',
        // },
        // {
        //   name: 'whPostAndPersonInfo',
        //   exchange: 'whohs',
        //   service: 'whData',
        //   callback: 'postAndPersonInfoReceive',
        //   routingKey: 'whPostAndPersonInfo',
        // },
        // {
        //   name: 'dingTalkWorkFlow',
        //   exchange: 'dingApproval',
        //   service: 'dingTalkWorkFlow',
        //   callback: 'handleDingApproval',
        //   routingKey: 'dingTalkWorkFlow',
        // },
      ],
      // 其余配置 参考 https://www.npmjs.com/package/amqplib
    },
    iServiceHost: 'http://localhost:8666',
    iService2Host: process.env.iService2Host || 'http://localhost:3000',
    coolHost: 'https://cool.zyws.cn',
    defaultMessageCode: 'dp6789', // 默认短信验证码，对其不需要做校验
    pay: {
      wxpay: {
        appid: 'wxcc23b9f409a2bdba',
        mchid: '**********',
        publicKey: require('fs').readFileSync(
          path.join(appInfo.baseDir, 'app/public/wxpem/apiclient_cert.pem')
        ),
        // 公钥
        privateKey: process.env.wxpay_privateKey, // 秘钥
      },
      alipay: {
        // 参考下方 SDK 配置
        appId: '****************',
        privateKey: process.env.alipay_privateKey, // 秘钥
        // 沙箱环境支付宝网关为：https://openapi.alipaydev.com/gateway.do
        // 正式环境支付宝网关为：https://openapi.alipay.com/gateway.do
        gateway: 'https://openapi.alipay.com/gateway.do',
        alipayRootCertPath: path.join(
          appInfo.baseDir,
          'app/public/alipem/alipayRootCert.crt'
        ),
        alipayPublicCertPath: path.join(
          appInfo.baseDir,
          'app/public/alipem/alipayPublicCert.crt'
        ),
        appCertPath: path.join(
          appInfo.baseDir,
          'app/public/alipem/appPublicCert.crt'
        ),
      },
    },
    // 微信支付宝回调地址
    wxpay_notify_url: 'https://oapi.zyws.net/app/pay/wxNotify',
    wxpay_refund_notify_url: 'https://oapi.zyws.net/app/pay/wxRefundsNotify',
    alipay_notify_url: 'https://oapi.zyws.net/app/pay/alipaysNotify',
    swaggerdoc: {
      enable: false,
    },
    byMonitor: {
      isMock: 'N', // 是否开启北元监控模拟数据
      scheduleJob: false, // 是否开启北元监控定时任务
      scheduleCron: '*/5 * * * * *', // 定时任务时间
    },
    isAllowCreateOrgAndEmployee: true, // 是否允许创建机构和员工
    // 证书配置
    certificateConfig: {
      effectiveYear: 3, // 证书有效时间 单位：年
    },
    subscriptionVerification: false, // 套餐支付校验开关
    subscriptionList: [
      // 套餐列表
      { _id: '1', name: '<100', maxPeopleNum: 99, price: 480, validYears: 1 },
      {
        _id: '2',
        name: '100-500',
        maxPeopleNum: 500,
        price: 980,
        validYears: 1,
      },
      {
        _id: '3',
        name: '500-1000',
        maxPeopleNum: 1000,
        price: 1680,
        validYears: 1,
      },
      {
        _id: '4',
        name: '>1000',
        maxPeopleNum: 100000,
        price: '按功能一企一议',
        validYears: 1,
      },
    ],

    isSxccPhysicalCron: process.env.isSxccPhysicalCron || true, // 是否启动获取山西焦煤数据的定时任务 除了山西焦煤其他地方不要启动否则导致数据丢失 true表示启动
    // 山西焦煤定时任务配置
    // 山西焦煤体检是否体检
    sxccPhysicalCheckCron: {
      // 每天中午12点执行一次
      cron: '0 0 12 * * *',
      disable: true, // 是否禁用,false表示不禁用
      immediate: false, // 启动服务时立即执行一次任务
      type: 'worker', // 指定每次只有一个随机的 worker 执行
    },
    // 山西焦煤体检预约提醒
    sxccPhysicalAppointmentCron: {
      // 每天下午5点执行一次
      cron: '0 0 17 * * *',
      disable: true, // 是否禁用,false表示不禁用
      immediate: false, // 启动服务时立即执行一次任务
      type: 'worker', // 指定每次只有一个随机的 worker 执行
    },
    // 山西焦煤体检签到
    sxccIndividualCheckCron: {
      // 每小时执行一次
      cron: '0 * * * *', // 在每小时的开始时执行
      // disable: !this.isSxccPhysicalCron, // 是否禁用,false表示不禁用
      disable: true, // 是否禁用,false表示不禁用
      immediate: true, // 启动服务时立即执行一次任务
      type: 'worker', // 指定每次只有一个随机的 worker 执行
    },
    // 山西焦煤讯康体检接口地址
    sxccXkHost: 'http://n3health.zhihuitijian.com',
    // sxccXkHost: process.env.sxccXkHost,
    // 个人体检报告对接是否用企业id和员工身份证号查询
    // 在线监测开关
    disabledOnlineMonitor: process.env.disabledOnlineMonitor || true, // 是否禁用在线监测,true 禁用,false不禁用
    isUseOrgIdAndIdCardQuery: true,
    // 微信服务号配置信息
    wxAuth: {
      appid: process.env.WX_APP_ID || 'wxcc23b9f409a2bdba',
      secret: process.env.WX_APP_SECRET || 'a80a9edcd0f7d33ae342cfe5e60db2ec',
    },
    fzxyx_token: {
      agentId: 'HGeWT3hdB',
      appKey: 'de1Gcd3d8TeW515c2G98',
      appSecret:
        '87eaad8771a094eb9e08eede9b02c819b186ab00eda7c96d89a9de985ba49507',
    },
    // fzxyx_token: process.env.fzxyx_token,
    // master和集团分支功能区分开关， 0为功能相同不需要区分，1为需要区分
    isGroupBranch: process.env.isGroupBranch || '0',
    // 是否开启数据库加密，true为开启，false为关闭
    dbEncryption: process.env.DB_ENCRYPTION === 'true',
    // 是否允许福州国密接口调用
    ALLOW_FZGM: process.env.ALLOW_FZGM === 'true',
    // 用于代码层面的域名认证配置
    authTxt: process.env.authTxt || '404.txt:404,asda.txt:asdaContent',
    whDepartData: {
      disable: process.env.whDepartData !== 'true',
      immediate: false,
      interval: '20m',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    whPolicyData: {
      disable: process.env.whPolicyData !== 'true',
      immediate: true,
      interval: '1d',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    whBaseData: {
      disable: process.env.whBaseData !== 'true',
      immediate: true,
      interval: '60m',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    shaanxiDataInterfaceLog: {
      disable: process.env.shaanxiDataInterfaceLog !== 'true',
      immediate: true,
      interval: '1d',
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },
    whDataOnline: {
      // 每天中午12点执行一次
      interval: '1h', // 每5分钟执行一次
      disable: process.env.whDataOnline !== 'true', // 是否禁用,false表示不禁用
      immediate: true, // 启动服务时立即执行一次任务
      type: 'worker', // 指定每次只有一个随机的 worker 执行
    },
    // model字段值是否唯一
    isUnique: process.env.ENABLE_UNIQUE !== 'false',
    // 是否全量处理陕西标准interfaceLog数据
    isAllHandle: process.env.isAllHandle === 'true',
    whRequest: {
      userPositionPush: process.env.WH_USER_POSITION_PUSH ||
        'http://************/api/testDataPermission',
      host: process.env.WH_HOST || 'http://************',
      qy: process.env.WH_QY || 'https://ohs-qy.cloudqas.whchem.com',
      h5sp: process.env.WH_H5SP || 'https://ohs-h5.cloudqas.whchem.com/#/pages_user/pages/user/approvalfh',
      username: process.env.WH_REQUEST_USERNAME || 'user_ohs',
      password: process.env.WH_REQUEST_PASSWORD || 'Ohs@2024',
      whWechatUrl: process.env.WH_WECHAT_URL || 'https://apigwqas.whchem.com:643/moa_api_msg',
      whWechatApiKey: process.env.WH_WECHAT_API_KEY || '7ea6dcfbdb7f43eea6a0cd6b6bc2a5c8',
    },
    // 培训是否开启宽松模式（是否要开始签字和人脸识别）
    isTrainingLooseMode: process.env.isTrainingLooseMode === 'true',
    // 工作场所名称
    workplaceName: {
      mill: '厂房',
      workspaces: '车间',
      stations: '岗位',
    },
    // 福州小游戏行为行为分析配置
    fzxyxBehaviorAnalysis: {
      disable: process.env.fzxyx_disable === 'true',
      maxRecords: +process.env.fzxyx_maxRecords || 10, // 最大记录数
      minInterval: +process.env.fzxyx_minInterval || 500, // 最小间隔时间（毫秒）
      stdDevThreshold: +process.env.fzxyx_stdDevThreshold || 800, // 时间间隔标准差阈值（毫秒）
    },
    // 文件保存路径EnterpriseID取值开关, 默认1 master, 0 表示集团
    tjReportEnterpriseID: process.env.TJREPORT_ENTERPRISE_ID || '1',
    // 防护用品二级审批万华 'open' 表示开启， 'close' 表示关闭
    whPpeSecondLevelApproval: process.env.WH_PPE_SECOND_LEVEL_APPROVAL || 'open',
    // user中为adminuer管理员的分组_id
    adminGroupId: process.env.ADMIN_GROUP_ID || 'xxxxxx',
    // report/reportData 检测报告对接是否允许创建企业 默认开启，环境变量设为0表示不允许创建企业
    isAllowCreateOrg: process.env.isAllowCreateOrg || '1',
  };
};
