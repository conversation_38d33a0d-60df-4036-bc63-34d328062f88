module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const medicalExamInfoSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      // 体检员工
      employeeId: {
        type: String,
        ref: 'Employee',
      },
      // 绑定的体检清单
      medicalExamList: {
        type: String,
        ref: 'OccupationalHealthCheckList',
      },
      // 危害因素
      harmFactors: {
        type: Array,
      },
      // 转岗时间点
      changeStationTime: {
        type: Date,
      },
      // 体检类型
      medicalExamType: {
        type: String,
        enum: [ '1', '2', '3', '4' ], // 1.离岗体检 2.岗前体检 3.在岗体检 4.周期体检
      },
      medicalExamCycle: {
        type: String,
        ref: 'medicalExamCycle',
      },
      // 绑定的危害因素的id
      harmFactorsId: {
        type: Array,
        ref: 'OccupationalexposureLimits',
      },
      // 原岗位
      originalStation: {
        type: String,
      },
      // 现在的岗位
      currentStation: {
        type: String,
      },
    },
    { timestamps: true }
  );
  return mongoose.model('MedicalExamInfo', medicalExamInfoSchema);
};
