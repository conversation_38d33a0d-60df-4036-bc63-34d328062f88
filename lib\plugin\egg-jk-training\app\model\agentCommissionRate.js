module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 代理等级及抽佣比例--代理方案 wx 2023/3/23
  const agentCommissionRateSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 代理方案名字
      type: String,
      required: true,
    },
    subordinatesUseable: { // 下级是否可见代理功能
      type: Boolean,
      default: true,
    },
    takeCommissionRates: [// 分层的代理结构
      [
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          level: { type: String, required: true }, // 代理级别，如"level_1"
          rate: { type: Number, required: true }, // 佣金比例，如0.2
          condition: { // 代理条件
            min: Number,
            max: {
              type: Number,
              default: Infinity,
              required: true,
            },
          },
        },
      ],
    ],
    usable: { // 是否可用,假删备用的字段
      type: Boolean,
      default: true,
    },
  }, {
    timestamps: true,
  });
  return mongoose.model('AgentCommissionRate', agentCommissionRateSchema, 'agentCommissionRate');
};
