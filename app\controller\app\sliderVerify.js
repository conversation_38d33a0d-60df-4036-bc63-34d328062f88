const Controller = require('egg').Controller;
class SliderVerifyController extends Controller {

  // 获取验证码
  async getCaptcha() {
    const { ctx, service } = this;
    try {
      const result = await service.captcha.generateCaptcha();
      ctx.helper.renderSuccess(ctx, {
        message: '获取验证码成功',
        data: result,
      });
    } catch (error) {
      ctx.logger.error('获取验证码失败：', error);
      ctx.helper.renderFail(ctx, {
        message: '获取验证码失败',
      });
    }
  }

  // 验证滑块位置
  async verifyCaptcha() {
    const { ctx, service } = this;
    const { captchaId, moveX } = ctx.request.body;

    if (!captchaId || moveX === undefined) {
      ctx.helper.renderCustom(ctx, {
        message: '参数错误',
        status: 400,
      });
      return;
    }

    try {
      const result = await service.captcha.verifyCaptcha(captchaId, moveX);

      if (result.valid) {
        ctx.helper.renderSuccess(ctx, {
          message: '验证成功',
          data: result,
        });
      } else {
        ctx.helper.renderCustom(ctx, {
          message: '验证失败，请重试',
          status: 400,
        });
      }
    } catch (error) {
      ctx.logger.error('验证失败：', error);
      ctx.helper.renderFail(ctx, {
        message: '验证失败，请重试',
      });
    }
  }

}
module.exports = SliderVerifyController;
