const moment = require('moment');
const { tools, siteFunc } = require('@utils');
const {
  oAppGroupRule,
} = require('@validate/oAppGroup');
const Controller = require('egg').Controller;
class OAppGroupController extends Controller {
  async list() {
    const {
      ctx,
    } = this;
    try {

      const field = ctx.query || {},
        current = field.current ? field.current : '1',
        pageSize = field.pageSize ? field.pageSize : '3',
        agentId = field.agentId ? field.agentId : '';
      let oAppGroupList = { docs: [], pageInfo: {} };
      if (agentId !== '') {
        oAppGroupList = await ctx.service.oAppGroup.find({ current, pageSize }, { query: { agentId } });
        oAppGroupList = tools.convertToEditJson(oAppGroupList);
        const oAppGroupListCount = oAppGroupList.docs.length;
        for (let i = 0; i < oAppGroupListCount; i++) {
          oAppGroupList.docs[i].date = moment(oAppGroupList.docs[i].date).format('YYYY-MM-DD HH:mm:ss');
        }
      }
      ctx.helper.renderSuccess(ctx, {
        data: oAppGroupList,
      });

    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });

    }
  }

  async getOne() {
    const { ctx } = this;
    try {
      const query = ctx.query || {},
        _id = query.id,
        isPower = query.isPower,
        files = isPower === 'true' ? {
          _id: 1,
          agentId: 1,
          apiPower: 1,
        } : {
          _id: 1,
          agentId: 1,
          name: 1,
          comments: 1,
          enable: 1,
        };
      let targetItem = await ctx.service.oAppGroup.item(ctx, {
        files,
        query: {
          _id,
        },
      });

      if (!targetItem) {
        targetItem = {};
      }

      ctx.helper.renderSuccess(ctx, {
        data: targetItem,
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async create() {
    const { ctx, config } = this;
    try {
      const fields = ctx.request.body || {},
        agentId = fields.agentId || '',
        name = fields.name || '',
        comments = fields.comments || '',
        enable = fields.enable || 'false';

      const formObj = {
        agentId,
        name,
        comments,
        enable,
      };
      ctx.validate(oAppGroupRule.form(ctx), formObj);

      const appKey = siteFunc.getAppKey(agentId);
      Object.assign(formObj, {
        appKey,
        appSecret: siteFunc.getAppSecret(ctx, appKey, config.salt_sha2_key),
      });

      ctx.service.oAppGroup.create(formObj);
      ctx.helper.renderSuccess(ctx, {
        message: '操作成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async update() {
    const { ctx } = this;
    try {

      const fields = ctx.request.body || {},
        isPower = fields.isPower || false,
        _id = fields._id || '',
        agentId = fields.agentId || '',
        name = fields.name || '',
        comments = fields.comments || '',
        apiPower = fields.apiPower || [],
        enable = fields.enable || false;

      if (_id === '') {
        ctx.helper.renderFail(ctx, {
          message: '哎呀！网络开小差了！要不，稍后再试试？',
        });
        return;
      }
      let formObj = {};
      if (isPower) {
        formObj = {
          apiPower,
        };
      } else {
        formObj = {
          agentId,
          name,
          comments,
          enable,
        };
        ctx.validate(oAppGroupRule.form(ctx), formObj);
      }

      await ctx.service.oAppGroup.update(ctx, fields._id, formObj);

      ctx.helper.renderSuccess(ctx, {
        message: '操作成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

  async removes() {
    const { ctx } = this;
    try {
      const targetIds = ctx.query.ids;
      await ctx.service.oAppGroup.removes(ctx, targetIds);
      ctx.helper.renderSuccess(ctx, {
        message: '操作成功',
      });
    } catch (err) {
      ctx.helper.renderFail(ctx, {
        message: err,
      });
    }
  }

}

module.exports = OAppGroupController;
