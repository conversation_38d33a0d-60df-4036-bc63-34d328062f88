// const await = require('await-stream-ready/lib/await');
// const moment = require('moment');
const path = require('path');
const fs = require('fs');

const Service = require('egg').Service;

class QuestionBankService extends Service {
  // 获取 题库
  async getQB(data) {
    const query = {};
    if (data.keyWord) {
      query.name = { $regex: data.keyWord };
    }
    const res = await this.ctx.model.QuestionBank.find(query)
      .sort({ createdAt: -1 })
      .skip((data.pageCurrent - 1) * data.size)
      .limit(data.size);

    const pageInfo = await this.getPageInfo('QuestionBank', data.size, data.pageCurrent, query);
    return {
      res,
      pageInfo,
    };
  }
  async getPageInfo(collection, size, pageCurrent, query = {}) {
    const total = await this.ctx.model[collection].find(query).count();
    const pageInfo = {
      total,
      size,
      pageCurrent,
    };
    return pageInfo;
  }
  // data 存在_id 就更新，不存在就 新建
  async createQB(data) {
    const { ctx } = this;
    const newData = {
      name: data.questionBankName,
    };
    const doc = await ctx.model.QuestionBank.findOne(newData);
    if (!doc) {
      let res;
      if (!data._id) {
        res = await new ctx.model.QuestionBank(newData).save();
      } else {
        res = await ctx.model.QuestionBank.findByIdAndUpdate(data._id, { name: data.questionBankName });
      }
      if (res) {
        const newDoc = await ctx.model.QuestionBank.find({}).sort({ createdAt: -1 }).limit(10);
        const pageInfo = await this.getPageInfo('QuestionBank', data.size, 1);
        return {
          data: newDoc,
          status: 200,
          message: 'success',
          pageInfo,
        };
      }
    } else {
      return {
        status: 400,
        message: '题库名称已存在',
      };
    }
  }
  async delQB(_id) {
    const { ctx } = this;
    const docCount = await ctx.model.Topic.find({ questionBankID: _id }).count();
    console.log(222, docCount);
    if (docCount > 0) {
      return {
        message: `题库内存在${docCount}道题目，请先删除题目`,
      };
    }
    const res = await ctx.model.QuestionBank.findByIdAndRemove(_id);
    if (res) {
      return {
        message: 'success',
      };
    }
  }
  // 创建题目
  async createTopic(newData) {
    const { ctx } = this;
    const doc = await new ctx.model.Topic(newData).save();
    if (doc && doc.questionBankID) {
      await this.updateQB(doc.questionBankID, doc.topicType, 1);
    }
    return doc;
  }
  // 更新题库的 数量
  async updateQB(ID, topicType, count) {
    const { ctx } = this;
    const query = { $inc: { count } };
    switch (topicType) {
      case 1:
        query.$inc.singleTopic = count;
        break;
      case 2:
        query.$inc.multipleTopic = count;
        break;
      case 3:
        query.$inc.judgeTopic = count;
        break;
      default:
        break;
    }
    await ctx.model.QuestionBank.findByIdAndUpdate(ID, query);

  }
  // 批量导入试题
  async addSomeTopic(data) {
    const { ctx } = this;
    for (let i = 0; i < data.length; i++) {
      let formatLabel = [];
      if (data[i].labels) {
        const labelArr = data[i].labels.split('|');
        const doc = await ctx.model.TopicLabel.find({ name: { $in: labelArr } });
        formatLabel = doc.map(item => {
          return item._id;
        });
      }
      data[i].labels = formatLabel;
    }
    const doc = await ctx.model.Topic.insertMany(data);
    for (let i = 0; i < doc.length; i++) {
      await this.updateQB(doc[i].questionBankID, doc[i].topicType, 1);
    }
    return doc;
  }
  // 更新题目
  async updateTopic(newData) {
    const ctx = this.ctx;
    const updateRes = await ctx.model.Topic.findByIdAndUpdate(newData._id, newData);
    return await ctx.model.Topic.findById(updateRes._id || newData._id);
  }
  // 获取题目
  async getTopic(data) {
    const { ctx } = this;
    const query = {
      questionBankID: data.questionBankID,
    };
    if (data.query.keyWords) {
      query.steam = { $regex: data.query.keyWords };
    }
    if (data.query.topicType) {
      query.topicType = parseInt(data.query.topicType);
    }
    if (data.query.label) {
      query.labels = data.query.label;
    }
    const doc = await ctx.model.Topic.find(query)
      .sort({ createdAt: -1 })
      .skip((data.pageCurrent - 1) * data.size)
      .limit(data.size);
    const pageInfo = await this.getPageInfo('Topic', data.size, data.pageCurrent, query);
    const topicLable = await this.findLabel();
    return {
      doc,
      pageInfo,
      topicLable,
    };
  }
  // 删除题目
  async delTopic(ids) {
    try {
      const { ctx, app } = this;
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo._id : '';
      // const doc = await ctx.model.Topic.find({ _id: { $in: ids } });
      // const res = await ctx.model.Topic.findByIdAndDelete({ _id: { $in: ids } });
      const doc = [];
      for (let i = 0; i < ids.length; i++) {
        const res = await ctx.model.Topic.findByIdAndDelete({ _id: ids[i] });
        doc.push(res);
      }
      for (let i = 0; i < doc.length; i++) {
        await this.updateQB(doc[i].questionBankID, doc[i].topicType, -1);
      }
      const fields = [ 'steamPic', 'answerAnalysisPic' ];
      doc.forEach(item => {
        fields.forEach(item2 => {
          if (item[item2].length) {
            item[item2].forEach(val => {
              fs.unlinkSync(path.resolve(app.config.upload_path, EnterpriseID, val.staticName));
            });
          }
        });
      });
      return {
        message: 'success delete',
        status: 200,
      };
    } catch (error) {
      console.log(error);
      return {
        message: 'error delete',
        status: 400,
      };
    }
  }
  // 查找标签
  async findLabel(data) {
    let doc;
    let pageInfo = {};
    const EnterpriseID = this.ctx.session.adminUserInfo.EnterpriseID || '';
    if (data) {
      const query = {
        $or: [{ EnterpriseID }, { EnterpriseID: '' }, { EnterpriseID: undefined }],
      };
      if (data.keyWord) query.name = { $regex: data.keyWord };
      doc = await this.ctx.model.TopicLabel.find(query, { name: 1, _id: 1 })
        .sort({ createdAt: -1 })
        .skip((data.pageCurrent - 1) * data.size)
        .limit(data.size);
      pageInfo = await this.getPageInfo('TopicLabel', data.size, data.pageCurrent, query);
    } else {
      doc = await this.ctx.model.TopicLabel.find({}, { name: 1, _id: 1 });
    }
    const newData = doc.map(item => {
      return {
        value: item._id,
        label: item.name,
      };
    });
    return {
      doc: newData,
      pageInfo,
    };
  }
  // 新增标签
  async addLabel(data) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID || '';
    data.EnterpriseID = EnterpriseID;
    const doc = await ctx.model.TopicLabel.findOne(data);
    if (doc) {
      return {
        status: 400,
        message: '标签已存在',
      };
    }
    const res = await new this.ctx.model.TopicLabel(data).save();
    if (res) {
      const data = await this.findLabel();
      return {
        data,
        status: 200,
        message: 'success',
      };
    }
  }
  // 删除标签
  async delLabel(data) {
    const res = await this.ctx.model.TopicLabel.findByIdAndRemove(data.value);
    await this.ctx.model.Topic.updateMany({ labels: res._id }, { $pull: { labels: res._id } });
    return res;
  }
  // 更新标签
  async updateLabel(data) {
    const doc = await this.ctx.model.TopicLabel.findOne({ name: data.label, EnterpriseID: this.ctx.session.adminUserInfo.EnterpriseID });
    if (doc) {
      return {
        status: 400,
        message: '标签已存在',
      };
    }
    const doc2 = await this.ctx.model.TopicLabel.findByIdAndUpdate(data.value, { $set: { name: data.label } });
    if (doc2) {
      return {
        status: 200,
        message: 'success',
      };
    }
  }

  // 获取随机题目
  async getRandomTopic(data) {
    const { num = 20, model = 'GameQuestionBank' } = data;
    const questionBankID = data.questionBankID;
    const query = {};
    if (questionBankID) {
      query.questionBankID = questionBankID;
    } else {
      const questionBank = await this.ctx.model[model].find({}, { _id: 1 });
      if (!questionBank || !questionBank.length) return [];
      query.questionBankID = { $in: questionBank.map(ele => ele._id) };
    }

    return await this.ctx.model.Topic.aggregate([
      { $match: query },
      { $sample: { size: num } },
    ]);
  }
}

module.exports = QuestionBankService;
