const Service = require('egg').Service;


// const _ = require('lodash');
const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _safeDelete,
  _unionQuery,
} = require('./general');


class MessageNotificationService extends Service {

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {

    const listdata = _list(this.ctx.model.MessageNotification, payload, {
      query,
      searchKeys,
      populate,
      files,
    });
    return listdata;

  }

  async count(params = {}) {
    return _count(this.ctx.model.MessageNotification, params);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.MessageNotification, values, key);
  }

  async safeDelete(res, values) {
    return _safeDelete(res, this.ctx.model.MessageNotification, values);
  }

  async update(res, _id, payload) {
    return _update(res, this.ctx.model.MessageNotification, _id, payload);
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.MessageNotification, params);
  }

  /** 发送消息
   * @param {String} title 标题
   * @param {String} message 消息
   * @param {Array} reader 读者
   * @param {String} authorID 作者id  作者的_id，就是发消息者的_id
   * @param {String} authorGroup 作者的分组 发消息者的group
   * @param {String} informer 体检预约人(不是体检非必须)
   * @param {String} infoCompany 体检预约人的所属公司(不是体检预约不填)
   * 这是最关键的参数
   * [{
   * readerID:'', 这是对应每个接收消息用户的_id，在对应用户表的_id，
   * readerGroup: '', 这是该用户对应adminGroup表的_id，用处是监管端管理，需要从不同表去查，比较复杂
   * }]
   * 所有的group一定要对应正确，否则就会让这条数据变成冗余的死数据
   */
  async sendMessage(title, message, reader, authorID, authorGroup, informer, infoCompany) {
    const payload = {
      date: new Date(),
      authorID,
      authorGroup,
      reader,
      title,
      message,
      informer,
      infoCompany,
    };
    const res = _create(this.ctx.model.MessageNotification, payload);
    if (res) {
      return true;
    }
    return false;

  }

  /** 获取接收的消息
   * 这是读者获取自己的消息，就是别人发送给你的消息
   * @param {String} id 你的id，查询者的id
   * @param {String} group 查询者的group
   * @param {Number} isread 是否已读，已读为1，未读为0
   * @param {Number} pageSize 1
   * @param {Number} current 2
   * @param {String} searchkey 3
   */
  async getMessage(id, group, isread,
    pageSize = 10,
    current = 1,
    searchkey
  ) {
    const searchKeys = [ 'title', 'message' ];
    const reader = [{
      readerID: id,
      readerGroup: group,
      isRead: isread ? 1 : 0,
    }];
    const playLoad = {
      pageSize: pageSize || 10,
      current: current || 1,
    };
    if (searchkey) playLoad.searchkey = searchkey;
    const argv = {
      collections: [],
      unwindArray: [],
      query: {
        reader,
      },
      searchKeys,
      files: {
        date: 1,
        title: 1,
        message: 1,
        authorID: 1,
        authorGroup: 1,
        reader: 1,
      },
      sort: {
        date: -1,
      },
      statisticsFiles: [],
    };
    const listdata = await _unionQuery(this.ctx.model.MessageNotification, playLoad, argv);
    // console.log('listdata', listdata);
    if (listdata.docs.length) {
      for (let index = 0; index < listdata.docs.length; index++) {
        const element = listdata.docs[index];
        const group = this.config.groupID;
        switch (element.authorGroup) {
          case group.superGroupID: {
            const user = await this.ctx.model.SuperUser.find({
              _id: element.authorID,
            }, {
              cname: 1,
              email: 1,
            });
            // console.log('user', user);
            if (!user.length) {
              element.user = {
                cname: '该政府用户没了',
                email: '',
              };
              break;
            }
            element.user = user[0];
            break;
          }
          case group.serviceGroupID: {
            const user = await this.ctx.model.ServiceOrg.find({
              _id: element.authorID,
            }, {
              name: 1,
            });
            if (!user) {
              element.user = {
                cname: '该用户没了',
                email: '',
              };
              break;
            }
            // 前端使用的是cname
            user.cname = user[0].name;
            element.user = user;
            break;
          }
          default:
            break;
        }
      }
      return listdata;
    }
    return listdata;
  }


  /**
   *
   * @param {*} id id
   * @param {*} group group
   * @param {*} pageSize pageSize
   * @param {*} current current
   * @param {*} searchkey searchkey
   */
  async getMyMessage(
    id,
    group,
    pageSize = 10,
    current = 1,
    searchkey
  ) {
    const searchKeys = [ 'title', 'message' ];
    const playLoad = {
      pageSize: pageSize || 10,
      current: current || 1,
    };
    const query = {
      authorID: id,
      authorGroup: group,
    };
    const populate = [];
    const files = null;
    if (searchkey) playLoad.searchkey = searchkey;
    const listData = await _list(this.ctx.model.MessageNotification, playLoad, {
      query,
      searchKeys,
      populate,
      files,
    });
    // 下面就是根据不同类型查不同表了
    if (listData) {
      for (let i = 0; i < listData.docs.length; i++) {
        for (let j = 0; j < listData.docs[i].reader.length; j++) {
          const reader = listData.docs[i].reader[j];
          const group = this.config.groupID; // 获取config里的对应表关系
          switch (reader.readerGroup) {
            case group.adminGroupID: { // 企业用户
              const user = await this.ctx.service.adminorg.item(this.ctx, {
                query: {
                  _id: reader.readerID,
                },
                populate: [{
                  path: 'adminUserId',
                  select: 'logo',
                }],
                files: 'cname adminUserId',
              });
              if (!user) {
                // 没有企业，很多原因， 比如这个企业被删除了
                reader.info = {
                  cname: '这个企业不存在',
                  logo: '', // 这里可以搞一个默认头像
                  _id: '',
                };
              } else {
                reader.info = {
                  cname: user.cname,
                  logo: user.adminUserId.logo,
                  _id: user.adminUserId._id,
                };
              }
              break;
            }
            case group.operateGroupID: { // 运营端
              const user = await this.ctx.service.operateUser.item(this.ctx, {
                query: {
                  _id: reader.readerID,
                },
                files: 'cname logo',
              });
              if (!user) {
                reader.info = {
                  cname: '这个用户不存在',
                  logo: '',
                  _id: '',
                };
              } else {
                reader.info = {
                  cname: user.cname,
                  logo: user.logo,
                  _id: reader.readerID,
                };
              }
              break;
            }
            case group.serviceGroupID: {
              const user = await this.ctx.model.ServiceOrg.find({
                _id: reader.readerID,
              }, {
                name: 1,
              });
              if (!user) {
                reader.info = {
                  cname: '这个企业不存在',
                  logo: '', // 这里可以搞一个默认头像
                  _id: '',
                };
                break;
              }
              // 前端使用的是cname
              reader.info = {
                cname: user[0].name,
                // logo: user.logo,
                _id: reader.readerID,
              };
              break;
            }
            case group.superGroupID: {
              const user = await this.ctx.service.superUser.item(this.ctx, {
                query: {
                  _id: reader.readerID,
                },
                files: 'cname logo',
              });
              if (!user) {
                reader.info = {
                  cname: '这个用户不存在',
                  logo: '',
                  _id: '',
                };
              } else {
                reader.info = {
                  cname: user.cname,
                  logo: user.logo,
                  _id: reader.readerID,
                };
              }
              break;
            }
            default: {
              reader.info = {
                cname: '这个用户类型不匹配',
                logo: '',
                _id: '',
              };
              break;
            }
          }
        }
      }
      return listData;
    }
    return listData; // 没有消息
  }

}

module.exports = MessageNotificationService;
