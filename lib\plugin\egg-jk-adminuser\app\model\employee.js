/*
 * @Author: 黄婷婷
 * @Date: 2020-06-01 09:00
 * @LastEditors: 肖小年
 * @LastEditTime: 2022-10-33 15:52
 * @Description: 企业端员工表
 *
 */
module.exports = app => {
  const shortid = require('shortid');
  // 员工信息表
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const employeeSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      userId: {
        // 对应的是users表中的id
        type: String,
        ref: 'User',
      },
      source: {
        type: String,
        default: 'oapi',
      }, // 文件存储来源端
      signatrue: String,
      management: Number, // 是否高管 1是 0 否
      hasRecocrd: { type: Boolean, default: false }, // 是否已生成档案
      departs: [[{ type: String, ref: 'Dingtree', index: true }]], // 部门
      EnterpriseID: {
        // 企业id
        type: String,
        ref: 'Adminorg',
      },
      status: { type: Number, default: 1 }, // 1表示在岗、0表示离岗
      name: {
        type: String,
        index: true,
      }, // 员工姓名
      gender: String, // 员工性别
      nativePlace: String, // 籍贯
      IDNum: String, // 身份证号
      phoneNum: String, // 手机号码
      education: String, // 文化程度
      hobby: String, // 嗜好
      workingSystem: String, // 工作制度
      station: String, // 岗位
      workType: String, // 工种
      workStart: Date, // 开始工作时间
      workYears: String, // 工龄
      headImg: String, // 头像
      marriage: String, // 婚姻
      email: String, // 邮箱
      age: Number, // 年龄
      dingId: String, // 钉钉id
      laborDispatching: {
        // 劳务派遣  默认 非劳务派遣
        type: String,
        default: '否',
      },
      employmentInjuryInsuranceValidity: Number, // 工伤保险有效期
      departIds: [[{ type: String }]], // 钉钉部门id
      enable: { type: Boolean, default: true }, // 是否可用，用于假删除
    },
    { timestamps: true }
  );

  employeeSchema.index({ departs: 1 });
  employeeSchema.index({ name: 1 });
  employeeSchema.index(
    { phoneNum: -1 },
    {
      unique: true,
      partialFilterExpression: { phoneNum: { $exists: true } },
    }
  );
  employeeSchema.index(
    { phoneNum: 1 },
    {
      unique: true,
      partialFilterExpression: { phoneNum: { $exists: true } },
    }
  );
  employeeSchema.index(
    { userName: -1 },
    {
      unique: true,
      partialFilterExpression: { userName: { $exists: true } },
    }
  );
  employeeSchema.index(
    { userName: 1 },
    {
      unique: true,
      partialFilterExpression: { userName: { $exists: true } },
    }
  );

  return mongoose.model('Employees', employeeSchema, 'employees');
};
