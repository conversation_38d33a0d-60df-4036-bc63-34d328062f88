require('shelljs/global');
const fs = require('fs');
const path = require('path');
const muri = require('muri');
const exec = require('child_process').exec;
const settings = require('../config/config.local');
const localPath = path.join(process.cwd(), '');

const mongoUri = settings({
  baseDir: '.',
}).mongoose.client.url;

const deleteFolder = path => {
  let files = [];
  if (fs.existsSync(path)) {
    files = fs.readdirSync(path);
    // eslint-disable-next-line no-unused-vars
    files.forEach(function(file, index) {
      const curPath = path + '/' + file;
      if (fs.statSync(curPath).isDirectory()) { // recurse
        deleteFolder(curPath);
      } else { // delete file
        fs.unlinkSync(curPath);
      }
    });
    fs.rmdirSync(path);
  }
};

console.log('初始化...');

const parsedUri = muri(mongoUri);
const parameters = [];

if (parsedUri.hosts && parsedUri.hosts.length) {
  const host = parsedUri.hosts[0];
  parameters.push(`-h ${host.host}`, `--port ${host.port}`);
}

if (parsedUri.auth) {
  parameters.push(`-u "${parsedUri.auth.user}"`, `-p "${parsedUri.auth.pass}"`);
}

if (parsedUri.db) {
  parameters.push(`-d "${parsedUri.db}"`);
}

parameters.push('-o .\\databak');

const authSource = parsedUri.options.authSource || '';
if (authSource) {
  parameters.push(`--authenticationDatabase ${authSource}`);
}

const cmd = `mongodump ${parameters.join(' ')}`;

deleteFolder(`${localPath}/databak/${parsedUri.db}`);

console.log(cmd);
console.log(`数据库${parsedUri.db}初始化完成！开始备份...`);
exec(cmd).stdout;
