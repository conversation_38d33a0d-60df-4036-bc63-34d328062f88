<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>福州数据处理日志管理系统</title>
  <link rel="stylesheet" href="{{staticRootPath}}/plugins/element-ui/2.11.1/theme-chalk/index.css">
  <style>
    /* 全局样式 */
    body { 
      margin: 0; 
      padding: 20px; 
      background: #f8f9fa;
      min-height: 100vh;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    }
    
    .main-container { 
      max-width: 1400px; 
      margin: 0 auto; 
      padding: 20px;
      background: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid #e9ecef;
    }
    
    /* 页面头部 */
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 32px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      margin: -20px -20px 30px -20px;
      border-radius: 8px 8px 0 0;
      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      pointer-events: none;
    }

    .header-title {
      font-size: 28px;
      font-weight: 700;
      letter-spacing: 1px;
      display: flex;
      align-items: center;
      gap: 12px;
      position: relative;
      z-index: 1;
    }

    .header-title::before {
      content: '📊';
      font-size: 32px;
      filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;
      position: relative;
      z-index: 1;
    }

    .header-time {
      font-size: 14px;
      opacity: 0.9;
      font-weight: 500;
    }

    .header-user {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 20px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .header-user i {
      font-size: 16px;
    }
    
    /* 统计卡片优化 */
    .stats-cards {
      margin-bottom: 30px;
    }

    .stats-card {
      border: none;
      border-radius: 16px;
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      overflow: hidden;
      position: relative;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stats-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
      background-size: 200% 100%;
      animation: gradientShift 3s ease infinite;
    }

    @keyframes gradientShift {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }

    .stats-card:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stats-card .el-card__body {
      padding: 32px 24px;
      text-align: center;
      position: relative;
    }

    .stats-card .el-card__body::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 100px;
      height: 100px;
      background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      z-index: 0;
    }

    .stats-number {
      font-size: 42px;
      font-weight: 800;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-align: center;
      margin-bottom: 12px;
      position: relative;
      z-index: 1;
      filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
    }

    .stats-label {
      font-size: 15px;
      color: #4a5568;
      text-align: center;
      font-weight: 600;
      letter-spacing: 0.8px;
      text-transform: uppercase;
      position: relative;
      z-index: 1;
    }
    
    /* 搜索表单优化 */
    .search-form {
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      padding: 24px;
      border-radius: 16px;
      margin-bottom: 24px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.08);
      border: 1px solid rgba(255, 255, 255, 0.2);
      position: relative;
      overflow: hidden;
    }

    .search-form::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
      background-size: 200% 100%;
      animation: gradientShift 4s ease infinite;
    }

    .search-form .el-form-item__label {
      font-weight: 600;
      color: #2c3e50;
      font-size: 14px;
    }

    .search-form .el-input__inner,
    .search-form .el-select .el-input__inner {
      border-radius: 12px;
      border: 2px solid #e9ecef;
      transition: all 0.3s ease;
      font-weight: 500;
    }

    .search-form .el-input__inner:focus,
    .search-form .el-select .el-input__inner:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .search-form .el-button {
      border-radius: 12px;
      font-weight: 600;
      padding: 12px 24px;
      transition: all 0.3s ease;
    }

    .search-form .el-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    }

    /* 关键词搜索框特殊样式 */
    .search-form .el-input.keyword-search .el-input__inner {
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      border: 2px solid #e3f2fd;
      font-weight: 500;
    }

    .search-form .el-input.keyword-search .el-input__inner:focus {
      border-color: #2196f3;
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
      background: #ffffff;
    }

    .search-form .el-input.keyword-search .el-input__prefix {
      color: #2196f3;
    }
    
    /* 操作按钮优化 */
    .action-buttons {
      margin-bottom: 24px;
      padding: 24px;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.08);
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      position: relative;
      overflow: hidden;
    }

    .action-buttons::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
      background-size: 200% 100%;
      animation: gradientShift 4s ease infinite;
    }

    .action-buttons .el-button {
      border-radius: 12px;
      padding: 14px 28px;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      border: none;
      box-shadow: 0 4px 16px rgba(0,0,0,0.1);
      position: relative;
      overflow: hidden;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .action-buttons .el-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.6s;
    }

    .action-buttons .el-button:hover::before {
      left: 100%;
    }

    .action-buttons .el-button:hover {
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 8px 24px rgba(0,0,0,0.2);
    }

    .action-buttons .el-button i {
      margin-right: 8px;
      font-size: 16px;
    }
    
    /* 表格优化 */
    .log-table {
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0,0,0,0.08);
      background: white;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .log-table .el-table__header-wrapper {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .log-table .el-table__header th {
      background: transparent;
      color: white;
      font-weight: 700;
      border-bottom: none;
      padding: 20px 16px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-size: 13px;
    }

    .log-table .el-table__row {
      transition: all 0.3s ease;
    }

    .log-table .el-table__row:hover {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%) !important;
      transform: scale(1.01);
      box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }

    .log-table .el-table__body td {
      padding: 16px;
      border-bottom: 1px solid #f1f3f4;
      font-weight: 500;
    }
    
    .el-table .success-row { 
      background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%); 
    }
    .el-table .warning-row { 
      background: linear-gradient(135deg, #fdf6ec 0%, #fef5e7 100%); 
    }
    .el-table .danger-row { 
      background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%); 
    }
    
    /* 分页优化 */
    .pagination-container {
      margin-top: 20px;
      padding: 20px;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.08);
      text-align: center;
    }
    
    /* 任务管理界面样式 */
    .status-item {
      text-align: center;
      padding: 20px;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      border-radius: 12px;
      border: 1px solid rgba(0,0,0,0.05);
      transition: all 0.3s ease;
      box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }
    
    .status-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    }
    
    .status-number {
      font-size: 32px;
      font-weight: 700;
      color: #409EFF;
      margin-bottom: 8px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .status-label {
      font-size: 14px;
      color: #666;
      font-weight: 500;
      letter-spacing: 0.5px;
    }
    
    /* 标签页优化 */
    .el-tabs--card>.el-tabs__header .el-tabs__nav {
      border: 1px solid #e4e7ed;
      border-radius: 8px 8px 0 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    
    .el-tabs--card>.el-tabs__header .el-tabs__item {
      border-radius: 8px 8px 0 0;
      transition: all 0.3s ease;
    }
    
    .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
      background: linear-gradient(135deg, #409EFF 0%, #36a3f7 100%);
      color: white;
      border-color: #409EFF;
    }
    
    /* 卡片通用样式 */
    .el-card {
      box-shadow: 0 4px 16px rgba(0,0,0,0.08);
      border: 1px solid rgba(0,0,0,0.05);
      border-radius: 12px;
      overflow: hidden;
      transition: all 0.3s ease;
    }
    
    .el-card:hover {
      box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    }
    
    .el-card__header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e9ecef;
      font-weight: 600;
      color: #2c3e50;
    }
    
    /* 性能监控卡片样式 */
    .performance-card {
      height: 140px;
      margin-bottom: 20px;
      transition: all 0.3s ease;
    }
    
    .performance-card:hover {
      transform: translateY(-4px);
    }
    
    .performance-card .el-card__body {
      padding: 20px;
    }
    
    .performance-number {
      font-size: 32px;
      font-weight: 700;
      color: #67C23A;
      text-align: center;
      margin-bottom: 8px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .performance-label {
      color: #606266;
      font-size: 12px;
      text-align: center;
      margin-bottom: 12px;
      font-weight: 500;
      letter-spacing: 0.5px;
    }
    
    .performance-card .el-card__header {
      padding: 12px 20px;
      border-bottom: 1px solid #EBEEF5;
      font-weight: 600;
      font-size: 14px;
    }
    
    /* 对话框优化 */
    .el-dialog {
      border-radius: 16px;
      overflow: hidden;
    }
    
    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px 24px;
    }
    
    .el-dialog__title {
      color: white;
      font-weight: 600;
      font-size: 18px;
    }
    
    .el-dialog__headerbtn .el-dialog__close {
      color: white;
      font-size: 20px;
    }
    
    .el-dialog__body {
      padding: 24px;
    }
    
    /* 按钮优化 */
    .el-button {
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;
    }
    
    .el-button:hover {
      transform: translateY(-1px);
    }
    
    .el-button--primary {
      background: linear-gradient(135deg, #409EFF 0%, #36a3f7 100%);
      border-color: #409EFF;
    }
    
    .el-button--success {
      background: linear-gradient(135deg, #67C23A 0%, #5daf34 100%);
      border-color: #67C23A;
    }
    
    .el-button--warning {
      background: linear-gradient(135deg, #E6A23C 0%, #d39e00 100%);
      border-color: #E6A23C;
    }
    
    .el-button--danger {
      background: linear-gradient(135deg, #F56C6C 0%, #f04747 100%);
      border-color: #F56C6C;
    }
    
    .el-button--info {
      background: linear-gradient(135deg, #909399 0%, #7d8592 100%);
      border-color: #909399;
    }
    
    /* 标签优化 */
    .el-tag {
      border-radius: 6px;
      font-weight: 500;
      padding: 4px 8px;
    }
    
    /* 进度条优化 */
    .el-progress-bar__outer {
      border-radius: 10px;
      overflow: hidden;
    }
    
    .el-progress-bar__inner {
      border-radius: 10px;
    }
    
    /* 表单优化 */
    .el-form-item__label {
      font-weight: 500;
      color: #2c3e50;
    }
    
    .el-input__inner, .el-select .el-input__inner, .el-textarea__inner {
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    
    .el-input__inner:focus, .el-select .el-input__inner:focus, .el-textarea__inner:focus {
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
    
    /* 解密按钮样式 */
    .decrypt-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      border: none !important;
      color: white !important;
      font-weight: 500 !important;
      transition: all 0.3s ease !important;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
    }

    .decrypt-btn:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4) !important;
    }

    .decrypt-btn:active {
      transform: translateY(0) !important;
      box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3) !important;
    }

    .decrypt-btn i {
      margin-right: 3px !important;
      font-size: 12px !important;
    }

    /* 操作按钮组样式优化 */
    .el-table .el-button + .el-button {
      margin-left: 6px;
    }

    .el-table .el-button--mini {
      padding: 5px 10px;
      font-size: 12px;
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .el-table .el-button--warning {
      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
      border: none;
      color: white;
      box-shadow: 0 2px 6px rgba(255, 152, 0, 0.3);
    }

    .el-table .el-button--warning:hover {
      background: linear-gradient(135deg, #f57c00 0%, #e65100 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 10px rgba(255, 152, 0, 0.4);
    }

    .el-table .el-button--primary {
      background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
      border: none;
      color: white;
      box-shadow: 0 2px 6px rgba(33, 150, 243, 0.3);
    }

    .el-table .el-button--primary:hover {
      background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 10px rgba(33, 150, 243, 0.4);
    }

    .el-table .el-button--success {
      background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
      border: none;
      color: white;
      box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
    }

    .el-table .el-button--success:hover {
      background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 10px rgba(76, 175, 80, 0.4);
    }

    .el-table .el-button--info {
      background: linear-gradient(135deg, #607d8b 0%, #455a64 100%);
      border: none;
      color: white;
      box-shadow: 0 2px 6px rgba(96, 125, 139, 0.3);
    }

    .el-table .el-button--info:hover {
      background: linear-gradient(135deg, #455a64 0%, #37474f 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 10px rgba(96, 125, 139, 0.4);
    }

    /* 解密对话框样式 */
    .decrypt-dialog .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px 24px;
      border-radius: 8px 8px 0 0;
    }

    .decrypt-dialog .el-dialog__title {
      color: white;
      font-weight: 600;
      font-size: 18px;
    }

    .decrypt-dialog .el-dialog__headerbtn .el-dialog__close {
      color: white;
      font-size: 20px;
    }

    .decrypt-dialog .el-dialog__body {
      padding: 0;
    }

    .decrypt-intro {
      padding: 30px;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .task-info-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      margin-bottom: 30px;
      overflow: hidden;
      border: 1px solid #e9ecef;
    }

    .task-info-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 16px 20px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .task-info-header i {
      font-size: 18px;
    }

    .task-info-content {
      padding: 24px;
    }

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .info-item:last-child {
      margin-bottom: 0;
      border-bottom: none;
    }

    .info-item label {
      font-weight: 600;
      color: #333;
      min-width: 100px;
      margin-right: 16px;
    }

    .task-id {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      background: #f8f9fa;
      padding: 4px 8px;
      border-radius: 4px;
      color: #667eea;
      font-weight: 600;
    }

    .task-type {
      background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
      color: white;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 600;
    }

    .file-name {
      color: #666;
      font-style: italic;
    }

    .decrypt-action {
      text-align: center;
    }

    .decrypt-action-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      border: none !important;
      padding: 16px 40px !important;
      font-size: 16px !important;
      font-weight: 600 !important;
      border-radius: 12px !important;
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3) !important;
      transition: all 0.3s ease !important;
    }

    .decrypt-action-btn:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
    }

    .decrypt-action-btn:active {
      transform: translateY(0) !important;
    }

    .decrypt-action-btn i {
      margin-right: 8px !important;
      font-size: 16px !important;
    }

    .decrypt-tip {
      margin-top: 16px;
      color: #999;
      font-size: 14px;
      line-height: 1.5;
    }

    /* 解密结果样式 */
    .decrypt-result {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .result-header {
      background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
      color: white;
      padding: 16px 24px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      font-size: 16px;
    }

    .result-header i {
      font-size: 18px;
    }

    .result-tabs {
      padding: 24px;
    }

    .result-tabs .el-tabs__header {
      margin-bottom: 24px;
    }

    .result-tabs .el-tabs__item {
      font-weight: 600;
      font-size: 14px;
    }

    .result-tabs .el-tabs__item i {
      margin-right: 6px;
    }

    .overview-content {
      padding: 16px;
    }

    .result-descriptions {
      border-radius: 8px;
      overflow: hidden;
    }

    .result-descriptions .el-descriptions__label {
      background: #f8f9fa;
      font-weight: 600;
      color: #333;
    }

    .desc-value {
      color: #666;
      font-weight: 500;
    }

    .record-count {
      color: #667eea;
      font-weight: 700;
      font-size: 16px;
    }

    .content-controls {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .search-input {
      width: 300px;
    }

    .record-info {
      color: #999;
      font-size: 14px;
    }

    .result-table {
      border-radius: 8px;
      overflow: hidden;
    }

    .result-table .el-table__header {
      background: #f8f9fa;
    }

    .result-table .el-table__header th {
      background: #f8f9fa;
      color: #333;
      font-weight: 600;
    }

    .json-content {
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .json-textarea {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
      font-size: 13px !important;
      line-height: 1.5 !important;
    }

    .json-textarea .el-textarea__inner {
      background: #2d3748 !important;
      color: #e2e8f0 !important;
      border: none !important;
      border-radius: 8px !important;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      body {
        padding: 10px;
      }
      
      .main-container {
        padding: 15px;
      }
      
      .header {
        font-size: 20px;
        padding: 15px 0 20px;
        flex-direction: column;
        gap: 10px;
      }
      
      .header-actions {
        flex-direction: column;
        gap: 8px;
      }
      
      .action-buttons {
        padding: 15px;
      }
      
      .action-buttons .el-button {
        padding: 8px 16px;
        margin-bottom: 8px;
      }
      
      .stats-number {
        font-size: 28px;
      }
      
      .performance-card {
        height: auto;
      }
      
      /* 登录界面响应式 */
      .login-box {
        width: 90%;
        max-width: 400px;
        padding: 30px 20px;
      }
      
      .login-header h2 {
        font-size: 20px;
      }
      
      .login-form .el-input__inner {
        height: 40px;
      }
      
      .login-form .el-button {
        height: 40px;
        font-size: 14px;
      }
    }
    
    /* 动画效果 */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .main-container > * {
      animation: fadeInUp 0.6s ease-out;
    }
    
    /* 任务详情对话框样式优化 */
    .el-dialog[aria-label="任务详情"] {
      margin-top: 5vh !important;
    }
    
    .el-dialog[aria-label="任务详情"] .el-dialog__body {
      padding: 20px 24px;
    }
    
    /* 任务详情卡片样式 */
    .detail-card {
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.08);
      border: 1px solid rgba(0,0,0,0.05);
      overflow: hidden;
      transition: all 0.3s ease;
    }
    
    .detail-card:hover {
      box-shadow: 0 8px 24px rgba(0,0,0,0.12);
      transform: translateY(-2px);
    }
    
    .card-header {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .card-header i {
      color: #409EFF;
      font-size: 18px;
    }
    
    .detail-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding: 8px 0;
    }
    
    .detail-label {
      min-width: 100px;
      font-weight: 600;
      color: #606266;
      margin-right: 12px;
      font-size: 14px;
    }
    
    .detail-value {
      flex: 1;
      color: #2c3e50;
      font-weight: 500;
    }
    
    .detail-tag {
      font-weight: 500;
      border-radius: 6px;
      padding: 4px 8px;
    }
    
    .detail-tag i {
      margin-right: 4px;
    }
    
    .clickable {
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .clickable:hover {
      color: #409EFF;
      transform: scale(1.02);
    }
    
    .task-id {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-weight: 600;
      color: #6f42c1;
      background: rgba(111, 66, 193, 0.1);
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 13px;
    }
    
    .file-name {
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }
    
    .status-tag {
      font-weight: 600;
      border-radius: 6px;
      padding: 4px 12px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .status-tag i {
      margin-right: 4px;
    }
    
    .retry-count {
      font-weight: 500;
      color: #606266;
    }
    
    .retry-count i {
      margin-right: 4px;
      color: #909399;
    }
    
    .time-field {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-weight: 500;
      color: #6c757d;
      font-size: 13px;
    }
    
    .time-field i {
      margin-right: 6px;
      color: #409EFF;
    }
    
    .duration-tag {
      font-weight: 600;
      border-radius: 6px;
      padding: 4px 8px;
    }
    
    .duration-tag i {
      margin-right: 4px;
    }
    
    /* 统计项目样式 */
    .stat-item {
      display: flex;
      align-items: center;
      padding: 16px;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      border-radius: 12px;
      border: 1px solid rgba(0,0,0,0.05);
      transition: all 0.3s ease;
      height: 80px;
    }
    
    .stat-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }
    
    .stat-item.success {
      background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
      border-color: rgba(103, 194, 58, 0.2);
    }
    
    .stat-item.error {
      background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
      border-color: rgba(245, 108, 108, 0.2);
    }
    
    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      background: linear-gradient(135deg, #409EFF 0%, #36a3f7 100%);
      color: white;
      font-size: 20px;
    }
    
    .stat-item.success .stat-icon {
      background: linear-gradient(135deg, #67C23A 0%, #5daf34 100%);
    }
    
    .stat-item.error .stat-icon {
      background: linear-gradient(135deg, #F56C6C 0%, #f04747 100%);
    }
    
    .stat-content {
      flex: 1;
    }
    
    .stat-value {
      font-size: 28px;
      font-weight: 700;
      color: #2c3e50;
      line-height: 1;
      margin-bottom: 4px;
    }
    
    .stat-item.success .stat-value {
      color: #67C23A;
    }
    
    .stat-item.error .stat-value {
      color: #F56C6C;
    }
    
    .stat-label {
      font-size: 12px;
      color: #909399;
      font-weight: 500;
      letter-spacing: 0.5px;
    }

    /* 进度统计框样式 */
    .stat-box {
      text-align: center;
      padding: 12px 8px;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      border-radius: 8px;
      border: 1px solid rgba(0,0,0,0.05);
      transition: all 0.3s ease;
    }

    .stat-box:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .stat-box.success {
      background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
      border-color: rgba(103, 194, 58, 0.2);
    }

    .stat-box.error {
      background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
      border-color: rgba(245, 108, 108, 0.2);
    }

    .stat-box.warning {
      background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
      border-color: rgba(234, 179, 8, 0.2);
    }

    .stat-box.processing {
      background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
      border-color: rgba(59, 130, 246, 0.2);
    }

    .stat-box.pending {
      background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
      border-color: rgba(107, 114, 128, 0.2);
    }

    .stat-box .stat-number {
      font-size: 20px;
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: 4px;
    }

    .stat-box.success .stat-number {
      color: #67C23A;
    }

    .stat-box.error .stat-number {
      color: #F56C6C;
    }

    .stat-box.warning .stat-number {
      color: #E6A23C;
    }

    .stat-box.processing .stat-number {
      color: #409EFF;
    }

    .stat-box.pending .stat-number {
      color: #909399;
    }

    .stat-box .stat-label {
      font-size: 11px;
      color: #666;
      font-weight: 500;
    }
    
    .success-rate-container {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-top: 8px;
    }
    
    .success-rate-text {
      font-weight: 700;
      font-size: 16px;
      color: #2c3e50;
    }
    
    .efficiency-field {
      font-weight: 600;
      color: #E6A23C;
      background: rgba(230, 162, 60, 0.1);
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .efficiency-field i {
      margin-right: 4px;
    }
    
    .message-container {
      width: 100%;
    }
    
    .message-content {
      background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
      border: 1px solid #e9ecef;
      border-radius: 12px;
      padding: 20px;
    }
    
    .message-text {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.6;
      color: #495057;
      word-wrap: break-word;
      white-space: pre-wrap;
      max-height: 200px;
      overflow-y: auto;
    }
    
    .message-actions {
      text-align: right;
    }
    
    .no-message {
      text-align: center;
      color: #adb5bd;
      font-style: italic;
      padding: 40px 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;
      border: 2px dashed #dee2e6;
    }
    
    .no-message i {
      font-size: 24px;
      margin-right: 8px;
      color: #E6A23C;
    }
    
    .task-detail-descriptions .task-id {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-weight: 600;
      color: #6f42c1;
      background: rgba(111, 66, 193, 0.1);
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 13px;
    }
    
    .task-detail-descriptions .task-type {
      font-weight: 600;
      color: #495057;
      background: rgba(73, 80, 87, 0.1);
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 13px;
    }
    
    .task-detail-descriptions .file-name {
      color: #495057;
      font-weight: 500;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }
    
    .task-detail-descriptions .file-type {
      font-weight: 500;
      color: #6c757d;
      font-style: italic;
    }
    
    .task-detail-descriptions .status-tag {
      font-weight: 600 !important;
      border-radius: 6px !important;
      padding: 4px 12px !important;
      font-size: 12px !important;
      border: none !important;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .task-detail-descriptions .number-field {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-weight: 600;
      color: #495057;
      background: rgba(248, 249, 250, 0.8);
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .task-detail-descriptions .number-field.success {
      color: #28a745;
      background: rgba(40, 167, 69, 0.1);
    }
    
    .task-detail-descriptions .number-field.error {
      color: #dc3545;
      background: rgba(220, 53, 69, 0.1);
    }
    
    .task-detail-descriptions .time-field {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-weight: 500;
      color: #6c757d;
      font-size: 13px;
    }
    
    .task-detail-descriptions .duration-field {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      color: #28a745;
      background: rgba(40, 167, 69, 0.1);
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .task-detail-descriptions .duration-field strong {
      font-weight: 700;
    }
    
    .task-detail-descriptions .success-rate {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      color: #007bff;
      background: rgba(0, 123, 255, 0.1);
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .task-detail-descriptions .success-rate strong {
      font-weight: 700;
    }
    
    .task-detail-descriptions .message-field {
      max-width: 100%;
      word-wrap: break-word;
      font-style: italic;
      color: #6c757d;
      line-height: 1.6;
    }
    
    .task-detail-descriptions .no-message {
      color: #adb5bd;
      font-style: italic;
    }
    
    .el-descriptions.is-bordered .el-descriptions__header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      font-weight: 600;
      color: #2c3e50;
      padding: 12px 16px;
      border-bottom: 1px solid #e9ecef;
    }
    
    .el-descriptions.is-bordered .el-descriptions__label {
      background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
      font-weight: 600;
      color: #2c3e50;
      padding: 12px 16px;
      border-right: 1px solid #e9ecef;
      min-width: 120px;
      text-align: right;
    }
    
    .el-descriptions.is-bordered .el-descriptions__content {
      padding: 12px 16px;
      color: #495057;
      background: #ffffff;
      font-weight: 500;
    }
    
    .el-descriptions.is-bordered .el-descriptions__table {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    }
    
    .el-descriptions.is-bordered .el-descriptions__table th,
    .el-descriptions.is-bordered .el-descriptions__table td {
      border: 1px solid #e9ecef;
    }
    
    /* 基本信息中的标签样式 */
    .el-descriptions .el-tag {
      font-weight: 600;
      border-radius: 6px;
      padding: 4px 12px;
      font-size: 12px;
      border: none;
    }
    
    .el-descriptions .el-tag--success {
      background: linear-gradient(135deg, #67C23A 0%, #5daf34 100%);
      color: white;
    }
    
    .el-descriptions .el-tag--warning {
      background: linear-gradient(135deg, #E6A23C 0%, #d39e00 100%);
      color: white;
    }
    
    .el-descriptions .el-tag--danger {
      background: linear-gradient(135deg, #F56C6C 0%, #f04747 100%);
      color: white;
    }
    
    .el-descriptions .el-tag--info {
      background: linear-gradient(135deg, #909399 0%, #7d8592 100%);
      color: white;
    }
    
    /* 特殊字段样式 */
    .el-descriptions__content[title*="ms"] {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-weight: 600;
      color: #67C23A;
    }
    
    .el-descriptions__content[title*="%"] {
      font-weight: 700;
      color: #409EFF;
    }
    
    /* 数字类型字段样式 */
    .el-descriptions__content[data-type="number"] {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-weight: 600;
      color: #E6A23C;
    }
    
    /* 处理消息字段样式 */
    .el-descriptions__table tr:last-child .el-descriptions__content {
      font-style: italic;
      color: #6c757d;
      max-width: 400px;
      word-wrap: break-word;
    }
    
    /* 任务详情标签页优化 */
    .el-dialog .el-tabs__header {
      margin-bottom: 20px;
    }
    
    .el-dialog .el-tabs__item {
      font-weight: 500;
      color: #6c757d;
      padding: 0 24px;
      height: 44px;
      line-height: 44px;
    }
    
    .el-dialog .el-tabs__item.is-active {
      color: #409EFF;
      font-weight: 600;
    }
    
    .el-dialog .el-tabs__nav-wrap::after {
      background-color: #e9ecef;
    }
    
    .el-dialog .el-tabs__active-bar {
      background-color: #409EFF;
      height: 3px;
      border-radius: 2px;
    }
    
    /* 解析数据标签页样式 */
    .el-dialog .el-tab-pane[label="解析数据"] pre {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
      border: 1px solid #dee2e6;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      line-height: 1.5;
      color: #495057;
    }
    
    .el-dialog .el-tab-pane[label="解析数据"] .el-card__header {
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      border-bottom: 1px solid #e9ecef;
      padding: 16px 20px;
      font-weight: 600;
      color: #2c3e50;
    }
    
    .el-dialog .el-tab-pane[label="解析数据"] .el-button--text {
      color: #409EFF;
      font-weight: 500;
      padding: 8px 12px;
      border-radius: 6px;
      transition: all 0.3s ease;
    }
    
    .el-dialog .el-tab-pane[label="解析数据"] .el-button--text:hover {
      background-color: rgba(64, 158, 255, 0.1);
      color: #36a3f7;
    }
    
    /* 登录界面样式 */
    .login-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }
    
    .login-box {
      width: 400px;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 16px;
      padding: 40px;
      box-shadow: 0 20px 40px rgba(31, 38, 135, 0.3);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.18);
      animation: fadeInScale 0.6s ease-out;
    }
    
    @keyframes fadeInScale {
      from {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
      }
      to {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }
    
    .login-header {
      text-align: center;
      margin-bottom: 30px;
    }
    
    .login-header h2 {
      color: #2c3e50;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .login-header p {
      color: #6c757d;
      font-size: 14px;
      margin: 0;
    }
    
    .login-form .el-form-item {
      margin-bottom: 25px;
    }
    
    .login-form .el-form-item__label {
      color: #2c3e50;
      font-weight: 500;
    }
    
    .login-form .el-input__inner {
      height: 45px;
      border-radius: 8px;
      border: 2px solid #e9ecef;
      transition: all 0.3s ease;
      padding-left: 40px;
      background-color: white;
      color: #2c3e50;
    }
    
    .login-form .el-input__inner:focus {
      border-color: #409EFF;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
    }
    
    .login-form .el-input__prefix {
      left: 12px;
      color: #909399;
    }
    
    .login-form .el-button {
      height: 45px;
      border-radius: 8px;
      font-weight: 500;
      font-size: 16px;
      background: linear-gradient(135deg, #409EFF 0%, #36a3f7 100%);
      border: none;
      transition: all 0.3s ease;
    }
    
    .login-form .el-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(64, 158, 255, 0.3);
    }
    
    /* 头部用户信息样式 */
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    
    .welcome-text {
      color: rgba(255, 255, 255, 0.9);
      font-size: 14px;
      font-weight: 500;
    }
    
    .header-actions .el-button {
      color: rgba(255, 255, 255, 0.9);
      padding: 8px 12px;
      border-radius: 6px;
      transition: all 0.3s ease;
    }
    
    .header-actions .el-button:hover {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
    }

    /* 滚动条优化 */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    }

    /* 页面加载动画 */
    .main-container {
      animation: fadeInUp 0.8s ease-out;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* 卡片悬停效果增强 */
    .stats-card,
    .action-buttons,
    .search-form,
    .log-table {
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    }

    /* 响应式优化 */
    @media (max-width: 1200px) {
      .header-title {
        font-size: 24px;
      }

      .stats-number {
        font-size: 36px;
      }
    }

    @media (max-width: 768px) {
      .header {
        flex-direction: column;
        gap: 16px;
        padding: 20px;
      }

      .header-actions {
        flex-direction: column;
        gap: 12px;
        width: 100%;
      }

      .action-buttons {
        flex-direction: column;
        gap: 12px;
      }

      .action-buttons .el-button {
        width: 100%;
        justify-content: center;
      }

      .stats-number {
        font-size: 32px;
      }
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- 登录界面 -->
    <div v-if="!isLoggedIn" class="login-container">
      <div class="login-box">
        <div class="login-header">
          <h2>福州数据处理日志管理系统</h2>
          <p>请登录后使用</p>
        </div>
        <el-form :model="loginForm" :rules="loginRules" ref="loginForm" label-width="80px" class="login-form">
          <el-form-item label="用户名" prop="userName">
            <el-input 
              v-model="loginForm.userName" 
              placeholder="请输入用户名" 
              clearable
              autocomplete="username">
              <i slot="prefix" class="el-icon-user"></i>
            </el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input 
              v-model="loginForm.password" 
              type="password" 
              placeholder="请输入密码" 
              clearable
              autocomplete="current-password"
              show-password
              @keyup.enter.native="handleLogin">
              <i slot="prefix" class="el-icon-lock"></i>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button 
              type="primary" 
              @click="handleLogin" 
              :loading="loginLoading" 
              style="width: 100%;">
              登录
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 主界面 -->
    <div v-if="isLoggedIn">
      <!-- 页面头部 -->
      <div class="header">
        <div class="header-title">
          福州数据处理日志管理系统
        </div>
        <div class="header-actions">
          <div class="header-time">
            <i class="el-icon-time"></i>
            [[ new Date().toLocaleString() ]]
          </div>
          <div class="header-user">
            <i class="el-icon-user"></i>
            [[ currentUser.userName || 'Admin' ]]
          </div>
          <el-button type="text" @click="handleLogout" style="color: white; margin-left: 16px;">
            <i class="el-icon-switch-button"></i>
            退出登录
          </el-button>
        </div>
      </div>

      <div class="main-container">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="5">
          <el-card class="stats-card">
            <div class="stats-number">[[ statistics.totalTasks ? statistics.totalTasks : 0 ]]</div>
            <div class="stats-label">总任务数</div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card class="stats-card">
            <div class="stats-number" style="color: #67C23A;">[[ statistics.successTasks ? statistics.successTasks : 0 ]]</div>
            <div class="stats-label">成功任务</div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card class="stats-card">
            <div class="stats-number" style="color: #F56C6C;">[[ statistics.failedTasks ? statistics.failedTasks : 0 ]]</div>
            <div class="stats-label">失败任务</div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card class="stats-card">
            <div class="stats-number" style="color: #909399;">[[ statistics.skippedTasks ? statistics.skippedTasks : 0 ]]</div>
            <div class="stats-label">跳过任务</div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stats-card">
            <div class="stats-number" style="color: #E6A23C;">[[ successRate ]]</div>
            <div class="stats-label">成功率</div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 搜索表单 -->
      <div class="search-form">
        <el-form :inline="true" :model="searchForm" size="small">
          <el-form-item label="关键词搜索">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索任务ID、文件名或处理消息"
              clearable
              class="keyword-search"
              style="width: 250px;"
              @keyup.enter.native="searchLogs">
              <i slot="prefix" class="el-icon-search"></i>
            </el-input>
          </el-form-item>
          <el-form-item label="任务类型">
            <el-select v-model="searchForm.taskType" placeholder="请选择任务类型" clearable>
              <el-option label="企业数据" value="company"></el-option>
              <el-option label="体检数据" value="healthCheck"></el-option>
              <el-option label="诊断数据" value="diagnosis"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="处理状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="等待处理" value="pending"></el-option>
              <el-option label="处理中" value="processing"></el-option>
              <el-option label="处理成功" value="success"></el-option>
              <el-option label="处理失败" value="failed"></el-option>
              <el-option label="跳过处理" value="skipped"></el-option>
              <el-option label="重试中" value="retrying"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchLogs">
              <i class="el-icon-search"></i>
              查询
            </el-button>
            <el-button @click="resetSearch">
              <i class="el-icon-refresh-left"></i>
              重置
            </el-button>
            <el-button type="success" @click="refreshStats">
              <i class="el-icon-refresh"></i>
              刷新统计
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="success" @click="showCreateTaskDialog">
          <i class="el-icon-plus"></i>
          新建任务
        </el-button>
        <el-button type="warning" @click="showRetryDialog">
          <i class="el-icon-refresh"></i>
          批量重试
        </el-button>
        <el-button type="info" @click="showTestDialog">
          <i class="el-icon-cpu"></i>
          数据清洗测试
        </el-button>
        <el-button type="primary" @click="showProcessDialog">
          <i class="el-icon-setting"></i>
          串行数据处理
        </el-button>
        <el-button type="success" @click="showFileList">
          <i class="el-icon-folder-opened"></i>
          查看文件列表
        </el-button>
        <el-button type="danger" @click="showCleanupDialog">
          <i class="el-icon-delete"></i>
          清理重复任务
        </el-button>
        <el-button type="info" @click="clearCache">
          <i class="el-icon-brush"></i>
          清理缓存
        </el-button>
        <el-button type="warning" @click="showRedisTaskDialog">
          <i class="el-icon-monitor"></i>
          Redis任务管理
        </el-button>
        <el-button type="primary" @click="manualGetAllZipFromApi" :loading="manualSyncLoading">
          <i class="el-icon-download"></i>
          手动同步福州数据
        </el-button>
      </div>

      <!-- 日志表格 -->
      <el-table
        :data="logs"
        class="log-table"
        v-loading="loading"
        stripe
        border>
        <el-table-column prop="taskId" label="任务ID" width="120"></el-table-column>
        <el-table-column prop="taskType" label="任务类型" width="100">
          <template slot-scope="scope">
            <el-tag size="mini" :type="getTaskTypeColor(scope.row.taskType)">
              [[ getTaskTypeName(scope.row.taskType) ]]
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fileName" label="文件名" width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag size="mini" :type="getStatusColor(scope.row.status)">
              [[ getStatusName(scope.row.status) ]]
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="totalRecords" label="总记录" width="80"></el-table-column>
        <el-table-column prop="processedRecords" label="成功" width="80"></el-table-column>
        <el-table-column prop="failedRecords" label="失败" width="80"></el-table-column>
        <el-table-column prop="skippedRecords" label="跳过" width="80"></el-table-column>
        <el-table-column prop="successRate" label="成功率" width="80">
          <template slot-scope="scope">
            [[ scope.row.successRate ]]%
          </template>
        </el-table-column>
        <el-table-column prop="retryCount" label="重试次数" width="80"></el-table-column>
        <el-table-column prop="duration" label="耗时(ms)" width="100"></el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template slot-scope="scope">
            [[ formatDate(scope.row.createdAt) ]]
          </template>
        </el-table-column>
        <el-table-column label="操作" width="260" fixed="right">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status === 'pending'"
              type="warning"
              size="mini"
              @click="processSingleTask(scope.row)">
              处理
            </el-button>
            <el-button
              v-if="scope.row.isRetryable"
              type="primary"
              size="mini"
              @click="retrySingleTask(scope.row)">
              重试
            </el-button>
            <el-button
              v-if="scope.row.status === 'processing'"
              type="info"
              size="mini"
              @click="showProgress(scope.row.taskId)">
              进度
            </el-button>
            <el-button
              type="success"
              size="mini"
              @click="viewTaskDetails(scope.row)">
              详情
            </el-button>
            <el-button
              type="info"
              size="mini"
              @click="decryptTaskZip(scope.row)"
              class="decrypt-btn">
              <i class="el-icon-view"></i>
              解密
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>

      <!-- 新建任务对话框 -->
      <el-dialog title="新建处理任务" :visible.sync="createTaskDialogVisible" width="600px">
        <el-alert
          title="任务创建说明"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 20px;">
          <div>
            系统将扫描数据目录中的文件，为每个文件创建对应的处理任务。
            <br>
            创建的任务状态为"待处理"，可以通过串行处理功能进行处理。
          </div>
        </el-alert>

        <el-form :model="createTaskForm" label-width="120px">
          <el-form-item label="任务类型">
            <el-radio-group v-model="createTaskForm.taskType">
              <el-radio label="all">全部类型</el-radio>
              <el-radio label="company">企业数据</el-radio>
              <el-radio label="healthCheck">体检数据</el-radio>
              <el-radio label="diagnosis">诊断数据</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="处理策略">
            <el-checkbox v-model="createTaskForm.clearExisting">清理现有任务</el-checkbox>
            <div style="font-size: 12px; color: #999; margin-top: 5px;">
              勾选后将删除所有现有任务，然后重新创建
            </div>
          </el-form-item>
          <el-form-item label="跳过策略">
            <el-checkbox v-model="createTaskForm.skipExisting">跳过已存在的任务</el-checkbox>
            <div style="font-size: 12px; color: #999; margin-top: 5px;">
              勾选后将跳过已经存在的任务，避免重复创建
            </div>
          </el-form-item>
          <el-form-item label="文件数量限制">
            <el-input-number
              v-model="createTaskForm.maxFiles"
              :min="0"
              placeholder="0表示不限制">
            </el-input-number>
            <div style="font-size: 12px; color: #999; margin-top: 5px;">
              限制每种类型最多创建的任务数量，0表示不限制
            </div>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-checkbox v-model="createTaskForm.recentOnly">只处理最近几天的文件</el-checkbox>
            <div style="margin-top: 10px;" v-if="createTaskForm.recentOnly">
              <el-input-number
                v-model="createTaskForm.recentDays"
                :min="1"
                :max="30"
                size="small"
                style="width: 120px;">
              </el-input-number>
              <span style="margin-left: 8px;">天</span>
            </div>
            <div style="font-size: 12px; color: #999; margin-top: 5px;">
              勾选后只会处理最近指定天数内的文件，可以提高处理效率
            </div>
          </el-form-item>
        </el-form>

        <!-- 创建结果显示 -->
        <div v-if="createTaskResult" style="margin-top: 20px;">
          <el-card>
            <div slot="header">
              <span>创建结果</span>
            </div>
            <div>
              <p><strong>状态：</strong>
                <el-tag :type="createTaskResult.success ? 'success' : 'danger'">
                  [[ createTaskResult.success ? '成功' : '失败' ]]
                </el-tag>
              </p>
              <p><strong>消息：</strong>[[ createTaskResult.message ]]</p>
              <p v-if="createTaskResult.totalFiles"><strong>扫描文件数：</strong>[[ createTaskResult.totalFiles ]]</p>
              <p v-if="createTaskResult.totalCreated"><strong>创建任务数：</strong>[[ createTaskResult.totalCreated ]]</p>
              <p v-if="createTaskResult.totalSkipped"><strong>跳过任务数：</strong>[[ createTaskResult.totalSkipped ]]</p>
              <p v-if="createTaskResult.totalErrors"><strong>失败任务数：</strong>[[ createTaskResult.totalErrors ]]</p>

              <!-- 批量插入信息 -->
              <div v-if="createTaskResult.batchInfo" style="margin-top: 15px;">
                <el-divider>批量处理信息</el-divider>
                <p><strong>批次大小：</strong>[[ createTaskResult.batchInfo.batchSize ]] 条/批</p>
                <p><strong>总批次数：</strong>[[ createTaskResult.batchInfo.totalBatches ]] 批</p>
                <p><strong>处理模式：</strong>
                  <el-tag type="success" size="mini">
                    <i class="el-icon-lightning"></i>
                    批量插入优化
                  </el-tag>
                </p>
              </div>

              <!-- 按类型统计 -->
              <div v-if="createTaskResult.tasksByType" style="margin-top: 15px;">
                <el-divider>按类型统计</el-divider>
                <el-row :gutter="20">
                  <el-col :span="8" v-for="(stats, type) in createTaskResult.tasksByType" :key="type">
                    <div class="stat-item">
                      <div class="stat-content">
                        <div class="stat-value">[[ stats.created ]]</div>
                        <div class="stat-label">[[ getTaskTypeName(type) ]]</div>
                        <div style="font-size: 12px; color: #999;">
                          总计: [[ stats.total ]] | 跳过: [[ stats.skipped ]] | 失败: [[ stats.errors ]]
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-card>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="createTaskDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="executeCreateTasks" :loading="createTaskLoading">创建任务</el-button>
        </div>
      </el-dialog>

      <!-- 重试对话框 -->
      <el-dialog title="重试任务" :visible.sync="retryDialogVisible" width="600px">
        <el-form :model="retryForm" label-width="120px">
          <el-form-item label="重试模式">
            <el-radio-group v-model="retryForm.retryMode">
              <el-radio label="batch">批量重试失败任务</el-radio>
              <el-radio label="specific">指定任务ID重试</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 指定任务ID模式 -->
          <div v-if="retryForm.retryMode === 'specific'">
            <el-form-item label="任务ID列表">
              <el-input
                v-model="retryForm.taskIdsText"
                type="textarea"
                :rows="4"
                placeholder="请输入任务ID，多个ID用换行或逗号分隔&#10;例如：&#10;task_company_20241211_001&#10;task_healthCheck_20241211_002">
              </el-input>
              <div style="font-size: 12px; color: #999; margin-top: 5px;">
                支持多个任务ID，用换行或逗号分隔
              </div>
            </el-form-item>
          </div>

          <!-- 批量重试模式 -->
          <div v-if="retryForm.retryMode === 'batch'">
            <el-form-item label="任务类型">
              <el-select v-model="retryForm.taskType" placeholder="请选择任务类型" clearable>
                <el-option label="全部" value=""></el-option>
                <el-option label="企业数据" value="company"></el-option>
                <el-option label="体检数据" value="healthCheck"></el-option>
                <el-option label="诊断数据" value="diagnosis"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="批次大小">
              <el-input-number v-model="retryForm.batchSize" :min="1" :max="100"></el-input-number>
              <div style="font-size: 12px; color: #999; margin-top: 5px;">
                每次处理的任务数量
              </div>
            </el-form-item>
          </div>

          <el-form-item label="最大重试次数">
            <el-input-number v-model="retryForm.maxRetries" :min="1" :max="10"></el-input-number>
            <div style="font-size: 12px; color: #999; margin-top: 5px;">
              超过此次数的任务将被跳过
            </div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="retryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="executeRetry" :loading="retryLoading">执行重试</el-button>
        </div>
      </el-dialog>

      <!-- 测试对话框 -->
      <el-dialog title="数据清洗测试" :visible.sync="testDialogVisible" width="600px">
        <el-form :model="testForm" label-width="100px">
          <el-form-item label="测试类型">
            <el-radio-group v-model="testForm.testType">
              <el-radio label="company">企业数据</el-radio>
              <el-radio label="healthCheck">体检数据</el-radio>
              <el-radio label="diagnosis">诊断数据</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="文件路径">
            <el-input v-model="testForm.filePath" placeholder="留空则随机选择文件"></el-input>
          </el-form-item>
          <el-form-item label="文件索引">
            <el-input-number v-model="testForm.index" :min="0" placeholder="指定文件索引"></el-input-number>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="testDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="executeTest" :loading="testLoading">开始测试</el-button>
        </div>
      </el-dialog>

      <!-- 串行处理对话框 -->
      <el-dialog title="串行数据处理" :visible.sync="processDialogVisible" width="700px">
        <el-alert
          title="处理说明"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 20px;">
          <div>
            串行处理将按顺序处理数据库中状态为"待处理"或"失败"的任务，不会创建新任务。
            <br>
            • 选择"全部类型"将处理所有类型的所有待处理任务
            <br>
            • 不设置"处理数量"将处理所有符合条件的任务
            <br>
            • 处理进度将通过 Redis 实时更新，您可以在下方监控处理状态
          </div>
        </el-alert>

        <el-form :model="processForm" label-width="120px">
          <el-form-item label="处理类型">
            <el-radio-group v-model="processForm.taskType">
              <el-radio label="all">全部类型</el-radio>
              <el-radio label="company">企业数据</el-radio>
              <el-radio label="healthCheck">体检数据</el-radio>
              <el-radio label="diagnosis">诊断数据</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="文件路径">
            <el-input v-model="processForm.filePath" placeholder="留空则处理所有符合条件的任务">
              <template slot="prepend">过滤</template>
            </el-input>
          </el-form-item>
          <el-form-item label="任务索引">
            <el-input-number
              v-model="processForm.index"
              :min="0"
              placeholder="指定从第几个任务开始，留空从头开始">
            </el-input-number>
            <div style="font-size: 12px; color: #999; margin-top: 5px;">
              设置后将从指定位置开始处理，不设置则从第一个任务开始
            </div>
          </el-form-item>
          <el-form-item label="处理数量">
            <el-input-number
              v-model="processForm.maxTasks"
              :min="1"
              placeholder="最多处理多少个任务，留空处理全部">
            </el-input-number>
            <div style="font-size: 12px; color: #999; margin-top: 5px;">
              不设置则处理所有符合条件的任务
            </div>
          </el-form-item>
        </el-form>
        
        <!-- 处理结果显示 -->
        <div v-if="processResult" style="margin-top: 20px;">
          <el-card>
            <div slot="header">
              <span>处理结果</span>
              <el-button
                v-if="processResult.taskId && processResult.status === 'pending'"
                type="text"
                @click="startProcessMonitoring(processResult.taskId)"
                style="float: right; margin-left: 10px;">
                <i class="el-icon-view"></i>
                监控进度
              </el-button>
              <el-button
                v-if="processResult.taskId && (processResult.status === 'failed' || processResult.status === 'cancelled' || processResult.status === 'interrupted')"
                type="primary"
                size="small"
                @click="continueProcessing(processResult.taskId)"
                :loading="continueProcessingLoading"
                style="float: right;">
                <i class="el-icon-refresh"></i>
                继续处理
              </el-button>
            </div>
            <div v-if="processResult.taskId">
              <p><strong>任务ID：</strong>[[ processResult.taskId ]]</p>
              <p><strong>状态：</strong>
                <el-tag :type="getTaskStatusType(processResult.status)">
                  [[ getTaskStatusText(processResult.status) ]]
                </el-tag>
              </p>
              <p><strong>消息：</strong>[[ processResult.message ]]</p>

              <!-- 实时进度显示 -->
              <div v-if="processMonitoring && processMonitoring.taskId === processResult.taskId">
                <el-divider>实时进度</el-divider>
                <div style="margin-bottom: 15px;">
                  <el-progress
                    :percentage="processMonitoring.progress"
                    :status="processMonitoring.status === 'success' ? 'success' : (processMonitoring.status === 'failed' ? 'exception' : null)"
                    :stroke-width="8">
                  </el-progress>
                </div>

                <!-- 详细统计信息 -->
                <div v-if="processMonitoring.total" style="margin-bottom: 15px;">
                  <el-row :gutter="10">
                    <el-col :span="6">
                      <div class="stat-box">
                        <div class="stat-number">[[ processMonitoring.total || 0 ]]</div>
                        <div class="stat-label">总任务</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="stat-box success">
                        <div class="stat-number">[[ processMonitoring.success || 0 ]]</div>
                        <div class="stat-label">已完成</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="stat-box processing">
                        <div class="stat-number">[[ processMonitoring.processing || 0 ]]</div>
                        <div class="stat-label">处理中</div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="stat-box pending">
                        <div class="stat-number">[[ processMonitoring.pending || 0 ]]</div>
                        <div class="stat-label">等待中</div>
                      </div>
                    </el-col>
                  </el-row>

                  <el-row :gutter="10" style="margin-top: 10px;" v-if="processMonitoring.failed > 0">
                    <el-col :span="12">
                      <div class="stat-box error">
                        <div class="stat-number">[[ processMonitoring.failed || 0 ]]</div>
                        <div class="stat-label">失败任务</div>
                      </div>
                    </el-col>
                    <el-col :span="12">
                      <div class="stat-box warning">
                        <div class="stat-number">[[ processMonitoring.skipped || 0 ]]</div>
                        <div class="stat-label">跳过任务</div>
                      </div>
                    </el-col>
                  </el-row>
                </div>

                <p><strong>当前状态：</strong>
                  <el-tag :type="getTaskStatusType(processMonitoring.status)">
                    [[ getTaskStatusText(processMonitoring.status) ]]
                  </el-tag>
                </p>
                <p v-if="processMonitoring.message"><strong>进度信息：</strong>[[ processMonitoring.message ]]</p>
                <p v-if="processMonitoring.processed"><strong>已处理任务：</strong>[[ processMonitoring.processed ]] / [[ processMonitoring.total ]]</p>
                <p v-if="processMonitoring.processedRecords"><strong>处理记录数：</strong>[[ processMonitoring.processedRecords ]] 条</p>
                <p v-if="processMonitoring.duration"><strong>耗时：</strong>[[ processMonitoring.duration ]]ms</p>
              </div>
            </div>
            <div v-else>
              <!-- 兼容旧版本的直接结果显示 -->
              <p><strong>状态：</strong>[[ processResult.success ? '成功' : '失败' ]]</p>
              <p><strong>消息：</strong>[[ processResult.message ]]</p>
              <p v-if="processResult.processed"><strong>处理文件数：</strong>[[ processResult.processed ]]</p>
              <p v-if="processResult.duration"><strong>处理耗时：</strong>[[ processResult.duration ]]ms</p>
            </div>
          </el-card>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="processDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="executeSerialProcess" :loading="processLoading">开始处理</el-button>
        </div>
      </el-dialog>

      <!-- 文件列表对话框 -->
      <el-dialog title="文件列表" :visible.sync="fileDialogVisible" width="800px">
        <div style="margin-bottom: 20px;">
          <el-alert
            title="文件统计"
            type="info"
            :closable="false"
            show-icon>
            <div>
              总文件数: [[ fileListData.summary.totalFiles ]] | 
              企业数据: [[ fileListData.summary.companyFiles ]] | 
              体检数据: [[ fileListData.summary.healthCheckFiles ]] | 
              诊断数据: [[ fileListData.summary.diagnosisFiles ]]
            </div>
          </el-alert>
        </div>
        
        <el-tabs v-model="fileActiveTab">
          <el-tab-pane label="企业数据" name="company">
            <div style="margin-bottom: 10px;">
              <el-input
                v-model="fileSearchText"
                placeholder="搜索文件名"
                style="width: 200px;"
                clearable>
              </el-input>
              <span style="margin-left: 10px; color: #999;">
                显示前100个文件，如需查看更多请使用搜索
              </span>
            </div>
            <el-table
              :data="getFilteredFiles('company')"
              v-loading="fileLoading"
              stripe
              border
              height="400">
              <el-table-column prop="fileName" label="文件名" show-overflow-tooltip></el-table-column>
              <el-table-column prop="index" label="索引" width="80"></el-table-column>
              <el-table-column label="操作" width="100" fixed="right">
                <template slot-scope="scope">
                  <el-button
                    type="primary"
                    size="mini"
                    @click="testWithFile(scope.row, 'company')">
                    测试
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="体检数据" name="healthCheck">
            <div style="margin-bottom: 10px;">
              <el-input
                v-model="fileSearchText"
                placeholder="搜索文件名"
                style="width: 200px;"
                clearable>
              </el-input>
              <span style="margin-left: 10px; color: #999;">
                显示前100个文件，如需查看更多请使用搜索
              </span>
            </div>
            <el-table
              :data="getFilteredFiles('healthCheck')"
              v-loading="fileLoading"
              stripe
              border
              height="400">
              <el-table-column prop="fileName" label="文件名" show-overflow-tooltip></el-table-column>
              <el-table-column prop="index" label="索引" width="80"></el-table-column>
              <el-table-column label="操作" width="100" fixed="right">
                <template slot-scope="scope">
                  <el-button
                    type="primary"
                    size="mini"
                    @click="testWithFile(scope.row, 'healthCheck')">
                    测试
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="诊断数据" name="diagnosis">
            <div style="margin-bottom: 10px;">
              <el-input
                v-model="fileSearchText"
                placeholder="搜索文件名"
                style="width: 200px;"
                clearable>
              </el-input>
              <span style="margin-left: 10px; color: #999;">
                显示前100个文件，如需查看更多请使用搜索
              </span>
            </div>
            <el-table
              :data="getFilteredFiles('diagnosis')"
              v-loading="fileLoading"
              stripe
              border
              height="400">
              <el-table-column prop="fileName" label="文件名" show-overflow-tooltip></el-table-column>
              <el-table-column prop="index" label="索引" width="80"></el-table-column>
              <el-table-column label="操作" width="100" fixed="right">
                <template slot-scope="scope">
                  <el-button
                    type="primary"
                    size="mini"
                    @click="testWithFile(scope.row, 'diagnosis')">
                    测试
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-dialog>

      <!-- 单个任务处理对话框 -->
      <el-dialog title="处理单个任务" :visible.sync="singleTaskDialogVisible" width="600px">
        <el-alert
          title="任务处理说明"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 20px;">
          <div>
            将立即处理选中的任务，处理过程中可以监控进度。
            <br>
            处理完成后任务状态将更新为"成功"或"失败"。
          </div>
        </el-alert>

        <div v-if="selectedTask">
          <el-card style="margin-bottom: 20px;">
            <div slot="header">
              <span>任务信息</span>
            </div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="任务ID">
                <span class="task-id">[[ selectedTask.taskId ]]</span>
              </el-descriptions-item>
              <el-descriptions-item label="任务类型">
                <el-tag :type="getTaskTypeColor(selectedTask.taskType)">
                  [[ getTaskTypeName(selectedTask.taskType) ]]
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="文件名">
                <span class="file-name" :title="selectedTask.fileName">[[ selectedTask.fileName ]]</span>
              </el-descriptions-item>
              <el-descriptions-item label="当前状态">
                <el-tag :type="getStatusColor(selectedTask.status)">
                  [[ getStatusName(selectedTask.status) ]]
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间" :span="2">
                [[ formatDate(selectedTask.createdAt) ]]
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </div>

        <!-- 处理结果显示 -->
        <div v-if="singleTaskResult" style="margin-top: 20px;">
          <el-card>
            <div slot="header">
              <span>处理结果</span>
              <el-button
                v-if="singleTaskResult.taskId && singleTaskResult.status === 'pending'"
                type="text"
                @click="startSingleTaskMonitoring(singleTaskResult.taskId)"
                style="float: right; margin-left: 10px;">
                <i class="el-icon-view"></i>
                监控进度
              </el-button>
              <el-button
                v-if="singleTaskResult.taskId && (singleTaskResult.status === 'failed' || singleTaskResult.status === 'cancelled' || singleTaskResult.status === 'interrupted')"
                type="primary"
                size="small"
                @click="continueProcessing(singleTaskResult.taskId)"
                :loading="continueProcessingLoading"
                style="float: right;">
                <i class="el-icon-refresh"></i>
                继续处理
              </el-button>
            </div>
            <div v-if="singleTaskResult.taskId">
              <p><strong>任务ID：</strong>[[ singleTaskResult.taskId ]]</p>
              <p><strong>状态：</strong>
                <el-tag :type="getTaskStatusType(singleTaskResult.status)">
                  [[ getTaskStatusText(singleTaskResult.status) ]]
                </el-tag>
              </p>
              <p><strong>消息：</strong>[[ singleTaskResult.message ]]</p>

              <!-- 实时进度显示 -->
              <div v-if="singleTaskMonitoring && singleTaskMonitoring.taskId === singleTaskResult.taskId">
                <el-divider>实时进度</el-divider>
                <div style="margin-bottom: 15px;">
                  <el-progress
                    :percentage="singleTaskMonitoring.progress"
                    :status="singleTaskMonitoring.status === 'success' ? 'success' : (singleTaskMonitoring.status === 'failed' ? 'exception' : null)"
                    :stroke-width="8">
                  </el-progress>
                </div>
                <p><strong>当前状态：</strong>
                  <el-tag :type="getTaskStatusType(singleTaskMonitoring.status)">
                    [[ getTaskStatusText(singleTaskMonitoring.status) ]]
                  </el-tag>
                </p>
                <p v-if="singleTaskMonitoring.message"><strong>进度信息：</strong>[[ singleTaskMonitoring.message ]]</p>
                <p v-if="singleTaskMonitoring.processed"><strong>已处理：</strong>[[ singleTaskMonitoring.processed ]] 条记录</p>
                <p v-if="singleTaskMonitoring.duration"><strong>耗时：</strong>[[ singleTaskMonitoring.duration ]]ms</p>
              </div>
            </div>
            <div v-else>
              <p><strong>状态：</strong>[[ singleTaskResult.success ? '成功' : '失败' ]]</p>
              <p><strong>消息：</strong>[[ singleTaskResult.message ]]</p>
              <p v-if="singleTaskResult.processed"><strong>处理记录数：</strong>[[ singleTaskResult.processed ]]</p>
              <p v-if="singleTaskResult.duration"><strong>处理耗时：</strong>[[ singleTaskResult.duration ]]ms</p>
            </div>
          </el-card>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="singleTaskDialogVisible = false">关闭</el-button>
          <el-button
            type="primary"
            @click="executeSingleTaskProcess"
            :loading="singleTaskLoading"
            :disabled="!selectedTask || selectedTask.status !== 'pending'">
            开始处理
          </el-button>
        </div>
      </el-dialog>

      <!-- Redis任务管理对话框 -->
      <el-dialog title="Redis任务管理" :visible.sync="redisTaskDialogVisible" width="1200px" :close-on-click-modal="false">
        <el-tabs v-model="redisActiveTab">
          <!-- 任务列表标签页 -->
          <el-tab-pane label="任务列表" name="tasks">
            <!-- 过滤条件 -->
            <div style="margin-bottom: 20px;">
              <el-form :inline="true" :model="redisTaskFilter" size="small">
                <el-form-item label="任务类型">
                  <el-select v-model="redisTaskFilter.taskType" placeholder="选择任务类型" clearable style="width: 150px;">
                    <el-option label="企业数据" value="company"></el-option>
                    <el-option label="体检数据" value="healthCheck"></el-option>
                    <el-option label="诊断数据" value="diagnosis"></el-option>
                    <el-option label="批处理" value="batch_process"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="任务状态">
                  <el-select v-model="redisTaskFilter.status" placeholder="选择状态" clearable style="width: 150px;">
                    <el-option label="等待处理" value="pending"></el-option>
                    <el-option label="处理中" value="processing"></el-option>
                    <el-option label="已完成" value="success"></el-option>
                    <el-option label="失败" value="failed"></el-option>
                    <el-option label="已取消" value="cancelled"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="关键词">
                  <el-input v-model="redisTaskFilter.pattern" placeholder="搜索任务ID或关键词" clearable style="width: 200px;"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="loadRedisTaskList" :loading="redisTaskLoading">
                    <i class="el-icon-search"></i>
                    查询
                  </el-button>
                  <el-button @click="resetRedisTaskFilter">
                    <i class="el-icon-refresh-left"></i>
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>



            <!-- 任务列表表格 -->
            <el-table
              :data="redisTaskList"
              v-loading="redisTaskLoading"
              stripe
              border
              height="400"
              row-key="taskKey">
              <el-table-column label="任务ID" width="200" show-overflow-tooltip>
                <template slot-scope="scope">
                  [[ scope.row.taskId || scope.row.taskKey.replace('task:', '') ]]
                </template>
              </el-table-column>
              <el-table-column prop="taskType" label="类型" width="100">
                <template slot-scope="scope">
                  <el-tag size="mini" :type="getTaskTypeColor(scope.row.taskType || scope.row.dataType)">
                    [[ getTaskTypeName(scope.row.taskType || scope.row.dataType) ]]
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                  <el-tag size="mini" :type="getStatusColor(scope.row.status)">
                    [[ getStatusName(scope.row.status) ]]
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="progress" label="进度" width="80">
                <template slot-scope="scope">
                  [[ scope.row.progress || 0 ]]%
                </template>
              </el-table-column>
              <el-table-column prop="message" label="消息" show-overflow-tooltip></el-table-column>
              <el-table-column prop="createdAt" label="创建时间" width="160">
                <template slot-scope="scope">
                  [[ formatDate(scope.row.createdAt) ]]
                </template>
              </el-table-column>
              <el-table-column prop="updatedAt" label="更新时间" width="160">
                <template slot-scope="scope">
                  [[ formatDate(scope.row.updatedAt) ]]
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180" fixed="right">
                <template slot-scope="scope">
                  <el-button
                    v-if="scope.row.status === 'failed' || scope.row.status === 'cancelled' || scope.row.status === 'interrupted'"
                    type="primary"
                    size="mini"
                    @click="continueRedisTask(scope.row)"
                    :loading="scope.row.continuing"
                    style="margin-right: 5px;">
                    <i class="el-icon-refresh"></i>
                    继续
                  </el-button>
                  <el-button
                    type="danger"
                    size="mini"
                    @click="deleteRedisTask(scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div style="margin-top: 20px; text-align: center;">
              <el-pagination
                @current-change="handleRedisTaskPageChange"
                :current-page="redisTaskPagination.page"
                :page-size="redisTaskPagination.pageSize"
                layout="total, prev, pager, next, jumper"
                :total="redisTaskPagination.total">
              </el-pagination>
            </div>
          </el-tab-pane>

          <!-- 队列统计标签页 -->
          <el-tab-pane label="队列统计" name="stats">
            <div style="margin-bottom: 20px;">
              <el-button type="primary" @click="loadRedisQueueStats" :loading="redisStatsLoading">
                <i class="el-icon-refresh"></i>
                刷新统计
              </el-button>
            </div>

            <el-row :gutter="20">
              <!-- 队列统计 -->
              <el-col :span="12">
                <el-card>
                  <div slot="header">
                    <span>队列统计</span>
                  </div>
                  <el-table :data="Object.entries(redisQueueStats.queues || {})" border>
                    <el-table-column prop="0" label="队列类型" width="150">
                      <template slot-scope="scope">
                        <el-tag :type="getTaskTypeColor(scope.row[0])">
                          [[ getTaskTypeName(scope.row[0]) ]]
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="1.length" label="队列长度" width="100"></el-table-column>
                    <el-table-column prop="1.queueKey" label="Redis键" show-overflow-tooltip></el-table-column>
                  </el-table>
                </el-card>
              </el-col>

              <!-- 任务统计 -->
              <el-col :span="12">
                <el-card>
                  <div slot="header">
                    <span>任务统计</span>
                  </div>
                  <div style="margin-bottom: 15px;">
                    <p><strong>总任务数：</strong>[[ redisQueueStats.tasks.total || 0 ]]</p>
                  </div>

                  <div v-if="redisQueueStats.tasks.byStatus">
                    <h4>按状态统计：</h4>
                    <el-table :data="Object.entries(redisQueueStats.tasks.byStatus)" border size="small">
                      <el-table-column prop="0" label="状态" width="100">
                        <template slot-scope="scope">
                          <el-tag size="mini" :type="getStatusColor(scope.row[0])">
                            [[ getStatusName(scope.row[0]) ]]
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="1" label="数量"></el-table-column>
                    </el-table>
                  </div>

                  <div v-if="redisQueueStats.tasks.byType" style="margin-top: 15px;">
                    <h4>按类型统计：</h4>
                    <el-table :data="Object.entries(redisQueueStats.tasks.byType)" border size="small">
                      <el-table-column prop="0" label="类型" width="100">
                        <template slot-scope="scope">
                          <el-tag size="mini" :type="getTaskTypeColor(scope.row[0])">
                            [[ getTaskTypeName(scope.row[0]) ]]
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column prop="1" label="数量"></el-table-column>
                    </el-table>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 清理任务标签页 -->
          <el-tab-pane label="清理任务" name="cleanup">
            <el-alert
              title="清理说明"
              type="warning"
              :closable="false"
              show-icon
              style="margin-bottom: 20px;">
              <div>
                清理Redis中的任务数据。建议先使用预览模式查看要清理的任务，确认无误后再执行实际清理。
                <br>
                <strong>注意：</strong>清理操作不可恢复，请谨慎操作！
              </div>
            </el-alert>

            <el-form :model="redisCleanupForm" label-width="120px">
              <el-form-item label="任务类型">
                <el-select v-model="redisCleanupForm.taskType" placeholder="选择要清理的任务类型" clearable>
                  <el-option label="全部类型" value=""></el-option>
                  <el-option label="企业数据" value="company"></el-option>
                  <el-option label="体检数据" value="healthCheck"></el-option>
                  <el-option label="诊断数据" value="diagnosis"></el-option>
                  <el-option label="批处理" value="batch_process"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="任务状态">
                <el-select v-model="redisCleanupForm.status" placeholder="选择要清理的任务状态" clearable>
                  <el-option label="全部状态" value=""></el-option>
                  <el-option label="已完成" value="success"></el-option>
                  <el-option label="失败" value="failed"></el-option>
                  <el-option label="已取消" value="cancelled"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="时间条件">
                <el-select v-model="redisCleanupForm.olderThan" placeholder="选择时间条件" clearable>
                  <el-option label="无时间限制" value=""></el-option>
                  <el-option label="1小时前" value="1h"></el-option>
                  <el-option label="6小时前" value="6h"></el-option>
                  <el-option label="1天前" value="1d"></el-option>
                  <el-option label="3天前" value="3d"></el-option>
                  <el-option label="7天前" value="7d"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="执行模式">
                <el-radio-group v-model="redisCleanupForm.dryRun">
                  <el-radio :label="true">预览模式（不实际删除）</el-radio>
                  <el-radio :label="false">实际清理</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>

            <div style="margin-bottom: 20px;">
              <el-button type="primary" @click="executeRedisCleanup" :loading="redisCleanupLoading">
                <i class="el-icon-delete"></i>
                [[ redisCleanupForm.dryRun ? '预览清理' : '执行清理' ]]
              </el-button>
            </div>

            <!-- 清理结果 -->
            <div v-if="redisCleanupResult">
              <el-card>
                <div slot="header">
                  <span>清理结果</span>
                </div>
                <p><strong>模式：</strong>[[ redisCleanupResult.dryRun ? '预览模式' : '实际清理' ]]</p>
                <p><strong>总任务数：</strong>[[ redisCleanupResult.totalCount ]]</p>
                <p><strong>清理数量：</strong>[[ redisCleanupResult.cleanedCount ]]</p>

                <div v-if="redisCleanupResult.cleanedTasks && redisCleanupResult.cleanedTasks.length > 0">
                  <h4>清理的任务示例（前10个）：</h4>
                  <el-table :data="redisCleanupResult.cleanedTasks" border size="small">
                    <el-table-column prop="taskId" label="任务ID" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="taskType" label="类型" width="100"></el-table-column>
                    <el-table-column prop="status" label="状态" width="100"></el-table-column>
                    <el-table-column prop="createdAt" label="创建时间" width="160"></el-table-column>
                  </el-table>
                </div>
              </el-card>
            </div>
          </el-tab-pane>
        </el-tabs>

        <div slot="footer" class="dialog-footer">
          <el-button @click="redisTaskDialogVisible = false">关闭</el-button>
        </div>
      </el-dialog>

      <!-- 清理重复任务对话框 -->
      <el-dialog title="清理重复任务" :visible.sync="cleanupDialogVisible" width="600px" :close-on-click-modal="false">
        <el-form :model="cleanupForm" label-width="120px">
          <el-form-item label="任务类型">
            <el-select v-model="cleanupForm.taskType" placeholder="选择要清理的任务类型">
              <el-option label="全部类型" value=""></el-option>
              <el-option label="企业数据" value="company"></el-option>
              <el-option label="体检数据" value="healthCheck"></el-option>
              <el-option label="诊断数据" value="diagnosis"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="执行模式">
            <el-radio-group v-model="cleanupForm.dryRun">
              <el-radio :label="true">预览模式（不实际删除）</el-radio>
              <el-radio :label="false">实际清理</el-radio>
            </el-radio-group>
            <div style="margin-top: 5px; font-size: 12px; color: #999;">
              建议先使用预览模式查看要删除的记录，确认无误后再执行实际清理
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="executeCleanup" :loading="cleanupLoading">
              [[ cleanupForm.dryRun ? '预览清理' : '执行清理' ]]
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 清理结果显示 -->
        <div v-if="cleanupResult" style="margin-top: 20px;">
          <el-card>
            <div slot="header">清理结果</div>
            <el-alert 
              :title="cleanupResult.message" 
              :type="cleanupResult.success ? 'success' : 'error'" 
              :closable="false" 
              show-icon>
            </el-alert>
            
            <div v-if="cleanupResult.stats" style="margin-top: 15px;">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="重复组数">[[ cleanupResult.stats.duplicateGroups ]]</el-descriptions-item>
                <el-descriptions-item label="重复记录总数">[[ cleanupResult.stats.duplicateRecords ]]</el-descriptions-item>
                <el-descriptions-item label="可删除记录数">[[ cleanupResult.stats.toDelete ]]</el-descriptions-item>
                <el-descriptions-item label="保留记录数">[[ cleanupResult.stats.toKeep ]]</el-descriptions-item>
                <el-descriptions-item v-if="cleanupResult.stats.actualDeleted !== undefined" label="实际删除数">[[ cleanupResult.stats.actualDeleted ]]</el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 示例记录（预览模式） -->
            <div v-if="cleanupResult.examples && cleanupResult.examples.length > 0" style="margin-top: 15px;">
              <h4>要删除的记录示例（前5条）：</h4>
              <el-table :data="cleanupResult.examples" size="mini" border>
                <el-table-column prop="taskId" label="任务ID" width="120"></el-table-column>
                <el-table-column prop="taskType" label="类型" width="80">
                  <template slot-scope="scope">
                    <el-tag size="mini" :type="getTaskTypeColor(scope.row.taskType)">
                      [[ getTaskTypeName(scope.row.taskType) ]]
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="fileName" label="文件名"></el-table-column>
                <el-table-column prop="status" label="状态" width="80">
                  <template slot-scope="scope">
                    <el-tag size="mini" :type="getStatusColor(scope.row.status)">
                      [[ getStatusName(scope.row.status) ]]
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createdAt" label="创建时间" width="160">
                  <template slot-scope="scope">
                    [[ formatDate(scope.row.createdAt) ]]
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div v-if="cleanupResult.note" style="margin-top: 10px;">
              <el-alert :title="cleanupResult.note" type="info" :closable="false"></el-alert>
            </div>
          </el-card>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="cleanupDialogVisible = false">关闭</el-button>
        </div>
      </el-dialog>

      <!-- 进度对话框 -->
      <el-dialog title="任务进度" :visible.sync="progressDialogVisible" width="600px">
        <el-form :model="currentTaskProgress" label-width="100px">
          <el-form-item label="任务ID">
            <el-input v-model="currentTaskProgress.taskId" readonly></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-input v-model="currentTaskProgress.status" readonly></el-input>
          </el-form-item>
          <el-form-item label="进度">
            <el-progress :percentage="currentTaskProgress.progress"></el-progress>
          </el-form-item>
          <el-form-item label="详细信息">
            <el-input v-model="currentTaskProgress.message" type="textarea" :rows="4" readonly></el-input>
          </el-form-item>
        </el-form>
      </el-dialog>

      <!-- 任务详情对话框 -->
      <el-dialog title="任务详情" :visible.sync="taskDetailDialogVisible" width="900px">
        <el-tabs v-model="detailActiveTab">
          <el-tab-pane label="基本信息" name="basic">
            <!-- 任务基本信息卡片 -->
            <el-card class="detail-card" style="margin-bottom: 20px;">
              <div slot="header" class="card-header">
                <i class="el-icon-info"></i>
                任务基本信息
              </div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">任务ID：</label>
                    <el-tooltip content="点击复制任务ID" placement="top">
                      <span 
                        class="detail-value task-id clickable" 
                        @click="copyToClipboard(currentTaskDetail.taskId)">
                        [[ currentTaskDetail.taskId ]]
                      </span>
                    </el-tooltip>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">任务类型：</label>
                    <el-tag 
                      :type="getTaskTypeColor(currentTaskDetail.taskType)" 
                      class="detail-tag">
                      <i :class="getTaskTypeIcon(currentTaskDetail.taskType)"></i>
                      [[ getTaskTypeName(currentTaskDetail.taskType) ]]
                    </el-tag>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">文件名：</label>
                    <el-tooltip :content="currentTaskDetail.fileName" placement="top">
                      <span class="detail-value file-name">[[ currentTaskDetail.fileName ]]</span>
                    </el-tooltip>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">文件类型：</label>
                    <el-tag size="small" type="info" class="detail-tag">
                      <i class="el-icon-document"></i>
                      [[ currentTaskDetail.fileType || 'N/A' ]]
                    </el-tag>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">处理状态：</label>
                    <el-tag 
                      :type="getStatusColor(currentTaskDetail.status)" 
                      class="detail-tag status-tag"
                      effect="dark">
                      <i :class="getStatusIcon(currentTaskDetail.status)"></i>
                      [[ getStatusName(currentTaskDetail.status) ]]
                    </el-tag>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">重试次数：</label>
                    <el-badge 
                      :value="currentTaskDetail.retryCount || 0" 
                      :type="(currentTaskDetail.retryCount || 0) > 0 ? 'warning' : 'success'"
                      :hidden="(currentTaskDetail.retryCount || 0) === 0">
                      <span class="detail-value retry-count">
                        <i class="el-icon-refresh"></i>
                        [[ currentTaskDetail.retryCount || 0 ]] 次
                      </span>
                    </el-badge>
                  </div>
                </el-col>
              </el-row>
            </el-card>

            <!-- 时间信息卡片 -->
            <el-card class="detail-card" style="margin-bottom: 20px;">
              <div slot="header" class="card-header">
                <i class="el-icon-time"></i>
                时间信息
              </div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">创建时间：</label>
                    <span class="detail-value time-field">
                      <i class="el-icon-time"></i>
                      [[ formatDate(currentTaskDetail.createdAt) ]]
                    </span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">开始时间：</label>
                    <span class="detail-value time-field">
                      <i class="el-icon-video-play"></i>
                      [[ formatDate(currentTaskDetail.startTime) ]]
                    </span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">结束时间：</label>
                    <span class="detail-value time-field">
                      <i class="el-icon-video-pause"></i>
                      [[ formatDate(currentTaskDetail.endTime) ]]
                    </span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="detail-item">
                    <label class="detail-label">处理耗时：</label>
                    <el-tag 
                      :type="getDurationColor(currentTaskDetail.duration)"
                      class="detail-tag duration-tag"
                      effect="plain">
                      <i class="el-icon-stopwatch"></i>
                      [[ formatDuration(currentTaskDetail.duration) ]]
                    </el-tag>
                  </div>
                </el-col>
              </el-row>
            </el-card>

            <!-- 处理统计卡片 -->
            <el-card class="detail-card" style="margin-bottom: 20px;">
              <div slot="header" class="card-header">
                <i class="el-icon-data-analysis"></i>
                处理统计
              </div>
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-icon">
                      <i class="el-icon-files"></i>
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">[[ currentTaskDetail.totalRecords || 0 ]]</div>
                      <div class="stat-label">总记录数</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item success">
                    <div class="stat-icon">
                      <i class="el-icon-circle-check"></i>
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">[[ currentTaskDetail.processedRecords || 0 ]]</div>
                      <div class="stat-label">成功处理</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item error">
                    <div class="stat-icon">
                      <i class="el-icon-circle-close"></i>
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">[[ currentTaskDetail.failedRecords || 0 ]]</div>
                      <div class="stat-label">失败记录</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20" style="margin-top: 20px;">
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-icon">
                      <i class="el-icon-remove"></i>
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">[[ currentTaskDetail.skippedRecords || 0 ]]</div>
                      <div class="stat-label">跳过记录</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="detail-item">
                    <label class="detail-label">成功率：</label>
                    <div class="success-rate-container">
                      <el-progress 
                        :percentage="parseFloat(currentTaskDetail.successRate) || 0"
                        :status="getSuccessRateStatus(currentTaskDetail.successRate)"
                        :show-text="false"
                        :stroke-width="8"
                        style="width: 100px;">
                      </el-progress>
                      <span class="success-rate-text">[[ currentTaskDetail.successRate || 0 ]]%</span>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="detail-item">
                    <label class="detail-label">处理效率：</label>
                    <span class="detail-value efficiency-field">
                      <i class="el-icon-odometer"></i>
                      [[ calculateEfficiency(currentTaskDetail) ]]
                    </span>
                  </div>
                </el-col>
              </el-row>
            </el-card>

            <!-- 处理消息卡片 -->
            <el-card class="detail-card">
              <div slot="header" class="card-header">
                <i class="el-icon-chat-line-square"></i>
                处理消息
              </div>
              <div class="message-container">
                <div v-if="currentTaskDetail.message" class="message-content">
                  <div class="message-text">
                    [[ currentTaskDetail.message ]]
                  </div>
                  <div class="message-actions">
                    <el-button 
                      type="primary" 
                      size="mini" 
                      @click="copyToClipboard(currentTaskDetail.message)">
                      <i class="el-icon-document-copy"></i>
                      复制消息
                    </el-button>
                  </div>
                </div>
                <div v-else class="no-message">
                  <i class="el-icon-warning"></i>
                  暂无处理消息
                </div>
              </div>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="解析数据" name="data">
            <div v-loading="detailLoading">
              <el-alert
                title="解析数据详情"
                type="info"
                :closable="false"
                show-icon
                style="margin-bottom: 20px;">
                <div>
                  以下是任务处理过程中解密和解析出的原始数据内容
                </div>
              </el-alert>

              <!-- 数据获取状态信息 -->
              <el-card v-if="!detailLoading" shadow="never" style="margin-bottom: 20px;">
                <div slot="header">
                  <span>数据获取状态</span>
                  <el-button style="float: right; padding: 3px 0" type="text" @click="loadTaskDetails(currentTaskDetail.taskId)">
                    <i class="el-icon-refresh"></i>
                    刷新数据
                  </el-button>
                </div>
                <div>
                  <p><strong>任务ID：</strong>[[ currentTaskDetail.taskId ]]</p>
                  <p><strong>任务状态：</strong>[[ getStatusName(currentTaskDetail.status) ]]</p>
                  <p><strong>API响应：</strong>
                    <el-tag v-if="hasValidData" type="success">有数据</el-tag>
                    <el-tag v-else type="warning">无数据</el-tag>
                  </p>
                  <p><strong>数据字段：</strong>
                    <el-tag v-if="taskDetailData.decryptedData" type="success" size="mini">解密数据</el-tag>
                    <el-tag v-if="taskDetailData.dataAnalysis" type="success" size="mini">分析结果</el-tag>
                    <el-tag v-if="taskDetailData.processingResult" type="success" size="mini">处理结果</el-tag>
                    <el-tag v-if="!hasValidData" type="info" size="mini">暂无数据</el-tag>
                  </p>
                </div>
              </el-card>
              
              <!-- 原始taskDetailData数据显示 -->
              <el-card v-if="!detailLoading" shadow="never" style="margin-bottom: 20px;">
                <div slot="header">
                  <span>原始API响应数据</span>
                  <el-button style="float: right; padding: 3px 0" type="text" @click="copyToClipboard(JSON.stringify(taskDetailData, null, 2))">复制全部</el-button>
                </div>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; max-height: 200px; overflow-y: auto; border: 1px solid #e9ecef;">[[ JSON.stringify(taskDetailData, null, 2) ]]</pre>
              </el-card>

              <!-- 解密后的原始数据 -->
              <el-card v-if="taskDetailData.decryptedData && hasNonEmptyObject(taskDetailData.decryptedData)" shadow="never" style="margin-bottom: 20px;">
                <div slot="header">
                  <span>解密后的原始数据</span>
                  <el-button style="float: right; padding: 3px 0" type="text" @click="copyToClipboard(JSON.stringify(taskDetailData.decryptedData, null, 2))">复制</el-button>
                </div>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; max-height: 300px; overflow-y: auto; border: 1px solid #e9ecef;">[[ JSON.stringify(taskDetailData.decryptedData, null, 2) ]]</pre>
              </el-card>

              <!-- 数据分析结果 -->
              <el-card v-if="taskDetailData.dataAnalysis && hasNonEmptyObject(taskDetailData.dataAnalysis)" shadow="never" style="margin-bottom: 20px;">
                <div slot="header">
                  <span>数据分析结果</span>
                  <el-button style="float: right; padding: 3px 0" type="text" @click="copyToClipboard(JSON.stringify(taskDetailData.dataAnalysis, null, 2))">复制</el-button>
                </div>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; max-height: 200px; overflow-y: auto; border: 1px solid #e9ecef;">[[ JSON.stringify(taskDetailData.dataAnalysis, null, 2) ]]</pre>
              </el-card>

              <!-- 处理结果详情 -->
              <el-card v-if="taskDetailData.processingResult && hasNonEmptyObject(taskDetailData.processingResult)" shadow="never" style="margin-bottom: 20px;">
                <div slot="header">
                  <span>处理结果详情</span>
                  <el-button style="float: right; padding: 3px 0" type="text" @click="copyToClipboard(JSON.stringify(taskDetailData.processingResult, null, 2))">复制</el-button>
                </div>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; max-height: 300px; overflow-y: auto; border: 1px solid #e9ecef;">[[ JSON.stringify(taskDetailData.processingResult, null, 2) ]]</pre>
              </el-card>

              <!-- 处理消息显示 -->
              <el-card v-if="currentTaskDetail.message" shadow="never" style="margin-bottom: 20px;">
                <div slot="header">
                  <span>任务处理消息</span>
                  <el-button style="float: right; padding: 3px 0" type="text" @click="copyToClipboard(currentTaskDetail.message)">复制</el-button>
                </div>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; max-height: 200px; overflow-y: auto; border: 1px solid #e9ecef; white-space: pre-wrap;">[[ currentTaskDetail.message ]]</pre>
              </el-card>

              <!-- 无数据提示 -->
              <div v-if="!hasValidData && !detailLoading" style="text-align: center; padding: 60px 20px;">
                <i class="el-icon-info" style="font-size: 48px; color: #909399; margin-bottom: 20px;"></i>
                <h3 style="color: #666; margin-bottom: 20px;">暂无数据</h3>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-dialog>

      <!-- 解密ZIP包对话框 -->
      <el-dialog
        :title="'解密任务ZIP包'"
        :visible.sync="decryptDialogVisible"
        width="85%"
        :close-on-click-modal="false"
        class="decrypt-dialog">

        <div v-if="currentDecryptTask && !decryptResult" class="decrypt-intro">
          <div class="task-info-card">
            <div class="task-info-header">
              <i class="el-icon-document"></i>
              <span>任务信息</span>
            </div>
            <div class="task-info-content">
              <div class="info-item">
                <label>任务ID：</label>
                <span class="task-id">[[ currentDecryptTask.taskId ]]</span>
              </div>
              <div class="info-item">
                <label>任务类型：</label>
                <span class="task-type">[[ getTaskTypeName(currentDecryptTask.taskType) ]]</span>
              </div>
              <div class="info-item">
                <label>文件名：</label>
                <span class="file-name">[[ currentDecryptTask.fileName ]]</span>
              </div>
            </div>
          </div>

          <div class="decrypt-action">
            <el-button
              type="primary"
              @click="decryptTaskZipFile"
              :loading="decryptLoading"
              size="large"
              class="decrypt-action-btn">
              <i class="el-icon-view"></i>
              <span v-if="!decryptLoading">解密查看数据</span>
              <span v-else>正在解密中...</span>
            </el-button>
            <p class="decrypt-tip">点击按钮解密并查看ZIP包中的数据内容</p>
          </div>
        </div>

        <!-- 解密结果展示 -->
        <div v-if="decryptResult" class="decrypt-result">
          <div class="result-header">
            <i class="el-icon-success"></i>
            <span>解密成功</span>
          </div>

          <el-tabs v-model="decryptResultTab" class="result-tabs">
            <el-tab-pane name="overview">
              <span slot="label">
                <i class="el-icon-data-line"></i>
                数据概览
              </span>
              <div class="overview-content">
                <el-descriptions :column="2" border class="result-descriptions">
                  <el-descriptions-item label="文件名">
                    <span class="desc-value">[[ decryptResult.fileName ]]</span>
                  </el-descriptions-item>
                  <el-descriptions-item label="数据类型">
                    <el-tag type="success">[[ decryptResult.dataType ]]</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="记录总数">
                    <span class="record-count">[[ decryptResult.totalRecords ]]</span> 条
                  </el-descriptions-item>
                  <el-descriptions-item label="解密时间">
                    <span class="desc-value">[[ formatDate(decryptResult.decryptTime) ]]</span>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-tab-pane>

            <el-tab-pane name="content">
              <span slot="label">
                <i class="el-icon-table"></i>
                数据内容
              </span>
              <div class="content-controls">
                <el-input
                  v-model="decryptSearchText"
                  placeholder="搜索数据内容"
                  class="search-input"
                  clearable>
                  <i slot="prefix" class="el-icon-search"></i>
                </el-input>
                <span class="record-info">
                  显示前100条记录，共 [[ decryptResult.totalRecords ]] 条
                </span>
              </div>

              <el-table
                :data="getFilteredDecryptData()"
                v-loading="decryptLoading"
                stripe
                border
                height="450"
                class="result-table">
                <el-table-column
                  v-for="(column, index) in decryptTableColumns"
                  :key="index"
                  :prop="column.prop"
                  :label="column.label"
                  :width="column.width"
                  show-overflow-tooltip>
                </el-table-column>
              </el-table>
            </el-tab-pane>

            <el-tab-pane name="json">
              <span slot="label">
                <i class="el-icon-document"></i>
                原始JSON
              </span>
              <div class="json-content">
                <el-input
                  type="textarea"
                  :rows="25"
                  v-model="decryptResult.rawJson"
                  readonly
                  class="json-textarea">
                </el-input>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-dialog>
      </div>
    </div>
  </div>

  <!-- 引入vue -->
  <script src="{{staticRootPath}}/plugins/vue/2.6.10/vue.min.js"></script>
  <script src="{{staticRootPath}}/plugins/element-ui/2.11.1/index.js"></script>
  <script src="{{staticRootPath}}/plugins/axios/0.19.0-beta.1/axios.min.js"></script>
  <!-- 引入组件库 -->
  <script>
    // 配置axios请求拦截器，自动添加认证头
    axios.interceptors.request.use(function (config) {
      // 登录接口不需要认证头
      if (config.url && config.url.includes('/app/loginAction')) {
        return config;
      }

      // 获取用户信息
      const userInfo = localStorage.getItem('fzDataUserInfo');
      if (userInfo) {
        try {
          const userData = JSON.parse(userInfo);
          if (userData.token) {
            config.headers['Authorization'] = `Bearer ${userData.token}`;
            config.headers['User-Id'] = userData.userId || userData._id;
          }
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }
      return config;
    }, function (error) {
      return Promise.reject(error);
    });

    // 配置axios响应拦截器，处理401错误
    axios.interceptors.response.use(function (response) {
      return response;
    }, function (error) {
      if (error.response && error.response.status === 401) {
        // 清除本地存储的用户信息
        localStorage.removeItem('fzDataUserInfo');
        // 重新加载页面到登录状态
        window.location.reload();
      }
      return Promise.reject(error);
    });

    new Vue({
      el: '#app',
      // 修改Vue模板分隔符，避免与nunjucks冲突
      delimiters: ['[[', ']]'],
      data() {
        return {
          // 登录相关
          isLoggedIn: false,
          currentUser: {},
          loginForm: {
            userName: '',
            password: ''
          },
          loginRules: {
            userName: [
              { required: true, message: '请输入用户名', trigger: 'blur' }
            ],
            password: [
              { required: true, message: '请输入密码', trigger: 'blur' },
              { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
            ]
          },
          loginLoading: false,
          
          // 日志数据
          logs: [],
          loading: false,
          
          // 统计数据
          statistics: {
            totalTasks: 0,
            successTasks: 0,
            failedTasks: 0,
            pendingTasks: 0
          },
          
          // 搜索表单
          searchForm: {
            keyword: '',
            taskType: '',
            status: '',
            dateRange: []
          },
          
          // 分页
          pagination: {
            pageNum: 1,
            pageSize: 20,
            total: 0
          },
          
          // 重试对话框
          retryDialogVisible: false,
          retryForm: {
            retryMode: 'batch', // 'batch' 或 'specific'
            taskType: '',
            maxRetries: 3,
            batchSize: 10,
            taskIdsText: '' // 任务ID文本输入
          },
          retryLoading: false,
          
          // 测试对话框
          testDialogVisible: false,
          testForm: {
            testType: 'company',
            filePath: '',
            index: 0
          },
          testLoading: false,
          
          // 串行处理对话框
          processDialogVisible: false,
          processForm: {
            taskType: 'all',
            filePath: '',
            index: null,
            maxTasks: null
          },
          processLoading: false,
          processResult: null,
          processMonitoring: null,
          processEventSource: null,
          continueProcessingLoading: false,

          // 文件列表对话框
          fileDialogVisible: false,
          fileListData: {
            summary: {},
            files: {
              company: [],
              healthCheck: [],
              diagnosis: []
            }
          },
          fileLoading: false,
          fileActiveTab: 'company',
          
          // SSE连接和进度
          sseConnections: new Map(),
          taskProgress: new Map(),
          
          // 进度对话框
          progressDialogVisible: false,
          currentTaskProgress: {
            taskId: '',
            status: '',
            message: '',
            progress: 0,
            details: {}
          },
          fileSearchText: '',
          
          // 任务详情对话框
          taskDetailDialogVisible: false,
          detailActiveTab: 'basic',
          currentTaskDetail: {},
          taskDetailData: {},
          detailLoading: false,

          // 清理重复任务对话框
          cleanupDialogVisible: false,
          cleanupForm: {
            taskType: '',
            dryRun: true
          },
          cleanupLoading: false,
          cleanupResult: null,

          // 新建任务对话框
          createTaskDialogVisible: false,
          createTaskForm: {
            taskType: 'all',
            clearExisting: false,
            skipExisting: true,
            maxFiles: 0,
            recentOnly: false,
            recentDays: 2
          },
          createTaskLoading: false,
          createTaskResult: null,

          // 单个任务处理对话框
          singleTaskDialogVisible: false,
          selectedTask: null,
          singleTaskLoading: false,
          singleTaskResult: null,
          singleTaskMonitoring: null,
          singleTaskEventSource: null,

          // Redis任务管理对话框
          redisTaskDialogVisible: false,
          redisActiveTab: 'tasks',
          redisTaskList: [],
          redisTaskLoading: false,
          redisTaskPagination: {
            page: 1,
            pageSize: 20,
            total: 0,
            totalPages: 0
          },
          redisTaskFilter: {
            taskType: '',
            status: '',
            pattern: ''
          },
          redisQueueStats: {
            queues: {},
            tasks: {},
            system: {}
          },
          redisStatsLoading: false,
          redisCleanupForm: {
            taskType: '',
            status: '',
            olderThan: '',
            dryRun: true
          },
          redisCleanupLoading: false,
          redisCleanupResult: null,

          // 解密ZIP包对话框
          decryptDialogVisible: false,
          currentDecryptTask: null,
          decryptLoading: false,
          decryptResult: null,
          decryptResultTab: 'overview',
          decryptSearchText: '',
          decryptTableColumns: [],

          // 手动同步相关
          manualSyncLoading: false
        };
      },
      
      computed: {
        successRate() {
          if (this.statistics.totalTasks === 0) return '0%';
          return ((this.statistics.successTasks / this.statistics.totalTasks) * 100).toFixed(2) + '%';
        },
        
        // 检查是否有有效的解析数据
        hasValidData() {
          return this.hasNonEmptyObject(this.taskDetailData.decryptedData) ||
                 this.hasNonEmptyObject(this.taskDetailData.dataAnalysis) ||
                 this.hasNonEmptyObject(this.taskDetailData.processingResult) ||
                 (this.currentTaskDetail.message && this.currentTaskDetail.message.trim().length > 0);
        }
      },
      
      mounted() {
        // 检查登录状态
        this.checkLoginStatus();
      },
      
      methods: {
        // 检查登录状态
        async checkLoginStatus() {
          try {
            // 从localStorage获取登录信息
            const userInfo = localStorage.getItem('fzDataUserInfo');
            if (userInfo) {
              const userData = JSON.parse(userInfo);
              this.currentUser = userData;
              this.isLoggedIn = true;
              // 初始化页面数据
              await this.initializeData();
            }
          } catch (error) {
            console.error('检查登录状态失败:', error);
            this.handleLogout();
          }
        },

        // 初始化页面数据
        async initializeData() {
          this.loadLogs();
          this.loadStatistics();
        },

        // 处理登录
        async handleLogin() {
          this.$refs.loginForm.validate(async (valid) => {
            if (!valid) {
              return false;
            }

            this.loginLoading = true;
            try {
              const response = await axios.post('/app/user/doLogin', {
                userName: this.loginForm.userName,
                password: this.loginForm.password
              });

              if (response.data.status === 200) {
                const userData = response.data.data;
                const userToken = response.data.userToken;

                // 构建完整的用户信息，包含token
                const userInfo = {
                  ...userData,
                  token: userToken,
                  userId: userData._id
                };

                // 保存用户信息到localStorage
                localStorage.setItem('fzDataUserInfo', JSON.stringify(userInfo));

                this.currentUser = userInfo;
                this.isLoggedIn = true;
                
                this.$message.success('登录成功！');
                
                // 清空登录表单
                this.loginForm = {
                  userName: '',
                  password: ''
                };

                // 初始化页面数据
                await this.initializeData();
              } else {
                this.$message.error(response.data.message || '登录失败');
              }
            } catch (error) {
              console.error('登录失败:', error);
              this.$message.error('登录异常: ' + (error.response?.data?.message || error.message));
            } finally {
              this.loginLoading = false;
            }
          });
        },

        // 处理退出登录
        handleLogout() {
          this.$confirm('确定要退出登录吗？', '确认退出', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // 清除localStorage
            localStorage.removeItem('fzDataUserInfo');
            
            // 重置状态
            this.isLoggedIn = false;
            this.currentUser = {};
            
            // 停止所有定时器和连接
            this.stopStatusRefresh();
            this.stopPerformanceRefresh();
            this.sseConnections.forEach((eventSource, taskId) => {
              this.closeSSE(taskId);
            });
            
            this.$message.success('已退出登录');
          }).catch(() => {
            // 用户取消退出
          });
        },

        // 获取认证headers
        getAuthHeaders() {
          const userInfo = localStorage.getItem('fzDataUserInfo');
          if (userInfo) {
            try {
              const userData = JSON.parse(userInfo);
              return {
                'Authorization': `Bearer ${userData.token}`,
                'User-Id': userData.userId || userData._id,
                'Content-Type': 'application/json'
              };
            } catch (e) {
              console.error('解析用户信息失败:', e);
            }
          }
          return {};
        },

        // 获取token
        getToken() {
          const userInfo = localStorage.getItem('fzDataUserInfo');
          if (userInfo) {
            try {
              const userData = JSON.parse(userInfo);
              return userData.token || '';
            } catch (e) {
              console.error('解析用户信息失败:', e);
            }
          }
          return '';
        },

        // 加载日志列表
        async loadLogs() {
          this.loading = true;
          try {
            const params = {
              pageSize: this.pagination.pageSize,
              pageNum: this.pagination.pageNum,
              taskType: this.searchForm.taskType,
              status: this.searchForm.status,
              keyword: this.searchForm.keyword
            };

            if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
              params.dateRange = this.searchForm.dateRange.join(',');
            }
            
            const response = await axios.get('/manage/fzData/logs', {
              params,
              headers: this.getAuthHeaders()
            });
            if (response.data.status === 200) {
              this.logs = response.data.data.list;
              this.pagination.total = response.data.data.total;
            } else {
              this.$message.warning('获取日志数据失败: ' + (response.data.message || '未知错误'));
            }
          } catch (error) {
            console.error('加载日志失败:', error);
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
            } else {
              this.$message.error('加载日志失败: ' + error.message);
            }
          } finally {
            this.loading = false;
          }
        },
        
        // 加载统计信息
        async loadStatistics() {
          try {
            const params = {
              taskType: this.searchForm.taskType
            };
            
            if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
              params.dateRange = this.searchForm.dateRange.join(',');
            }
            
            const response = await axios.get('/manage/fzData/stats', {
              params,
              headers: this.getAuthHeaders()
            });
            if (response.data.status === 200) {
              this.statistics = response.data.data.summary;
            }
          } catch (error) {
            this.$message.error('加载统计信息失败: ' + error.message);
          }
        },
        
        // 搜索日志
        searchLogs() {
          this.pagination.pageNum = 1;
          this.loadLogs();
          this.loadStatistics();
        },
        
        // 重置搜索
        resetSearch() {
          this.searchForm = {
            keyword: '',
            taskType: '',
            status: '',
            dateRange: []
          };
          this.pagination.pageNum = 1;
          this.loadLogs();
          this.loadStatistics();
        },
        
        // 刷新统计
        refreshStats() {
          this.loadStatistics();
        },
        
        // 分页相关
        handleSizeChange(val) {
          this.pagination.pageSize = val;
          this.loadLogs();
        },
        
        handleCurrentChange(val) {
          this.pagination.pageNum = val;
          this.loadLogs();
        },
        
        // 显示新建任务对话框
        showCreateTaskDialog() {
          this.createTaskDialogVisible = true;
          this.createTaskResult = null; // 重置结果
        },

        // 执行创建任务
        async executeCreateTasks() {
          this.createTaskLoading = true;
          this.createTaskResult = null;

          try {
            const response = await axios.post('/manage/fzData/createTasks', this.createTaskForm, {
              headers: this.getAuthHeaders()
            });

            if (response.data.code === 200) {
              this.createTaskResult = response.data.data;
              this.$message.success('任务创建完成: ' + response.data.message);

              // 刷新日志列表和统计信息
              await this.loadLogs();
              await this.loadStatistics();
            } else {
              this.$message.error('创建任务失败: ' + (response.data.message || '未知错误'));
            }
          } catch (error) {
            console.error('创建任务失败:', error);
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
            } else {
              this.$message.error('创建任务失败: ' + error.message);
            }
          } finally {
            this.createTaskLoading = false;
          }
        },

        // 显示单个任务处理对话框
        processSingleTask(task) {
          this.selectedTask = task;
          this.singleTaskResult = null;
          this.singleTaskMonitoring = null;
          this.singleTaskDialogVisible = true;

          // 关闭之前的SSE连接
          if (this.singleTaskEventSource) {
            this.singleTaskEventSource.close();
            this.singleTaskEventSource = null;
          }
        },

        // 执行单个任务处理
        async executeSingleTaskProcess() {
          if (!this.selectedTask) {
            this.$message.error('请选择要处理的任务');
            return;
          }

          this.singleTaskLoading = true;
          this.singleTaskResult = null;

          try {
            // 调用单个任务处理接口
            const requestData = {
              taskId: this.selectedTask.taskId
            };

            const response = await axios.post('/manage/fzData/processSingleTask', requestData, {
              headers: this.getAuthHeaders()
            });

            if (response.data.status === 200) {
              this.singleTaskResult = response.data.data;
              this.$message.success('任务处理完成: ' + response.data.message);

              // 刷新日志列表和统计信息
              await this.loadLogs();
              await this.loadStatistics();

              // 更新选中任务的状态
              if (this.selectedTask) {
                this.selectedTask.status = 'success';
              }
            } else {
              this.$message.error('任务处理失败: ' + (response.data.message || '未知错误'));
            }
          } catch (error) {
            console.error('单个任务处理失败:', error);
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
            } else {
              this.$message.error('任务处理失败: ' + error.message);
            }
          } finally {
            this.singleTaskLoading = false;
          }
        },

        // 开始单个任务进度监控
        startSingleTaskMonitoring(taskId) {
          if (this.singleTaskEventSource) {
            this.singleTaskEventSource.close();
          }

          const token = this.getToken();
          const sseUrl = `/manage/fzData/progress/${taskId}?token=${encodeURIComponent(token)}`;

          this.singleTaskEventSource = new EventSource(sseUrl);

          this.singleTaskEventSource.onmessage = (event) => {
            try {
              const data = JSON.parse(event.data);
              this.singleTaskMonitoring = {
                taskId: taskId,
                status: data.status || 'processing',
                progress: parseInt(data.progress) || 0,
                message: data.message || '',
                processed: data.processed || 0,
                duration: data.duration || 0
              };

              // 如果任务完成，关闭SSE连接并刷新列表
              if (data.status === 'success' || data.status === 'failed' || data.status === 'skipped') {
                setTimeout(() => {
                  this.singleTaskEventSource.close();
                  this.singleTaskEventSource = null;
                  this.loadLogs(); // 刷新任务列表
                  this.loadStatistics(); // 刷新统计信息
                }, 2000);
              }
            } catch (error) {
              console.error('解析SSE数据失败:', error);
            }
          };

          this.singleTaskEventSource.onerror = (error) => {
            console.error('SSE连接错误:', error);
            this.singleTaskEventSource.close();
            this.singleTaskEventSource = null;
          };
        },

        // 显示重试对话框
        showRetryDialog() {
          // 重置表单
          this.retryForm = {
            retryMode: 'batch',
            taskType: '',
            maxRetries: 3,
            batchSize: 10,
            taskIdsText: ''
          };
          this.retryDialogVisible = true;
        },

        // 显示指定任务ID重试对话框
        showSpecificRetryDialog(taskIds = []) {
          this.retryForm = {
            retryMode: 'specific',
            taskType: '',
            maxRetries: 3,
            batchSize: 10,
            taskIdsText: Array.isArray(taskIds) ? taskIds.join('\n') : taskIds
          };
          this.retryDialogVisible = true;
        },
        
        // 执行重试
        async executeRetry() {
          this.retryLoading = true;
          try {
            // 准备请求数据
            const requestData = {
              maxRetries: this.retryForm.maxRetries
            };

            if (this.retryForm.retryMode === 'specific') {
              // 指定任务ID模式
              if (!this.retryForm.taskIdsText.trim()) {
                this.$message.error('请输入要重试的任务ID');
                return;
              }

              // 解析任务ID列表
              const taskIds = this.retryForm.taskIdsText
                .split(/[\n,，]/) // 支持换行、英文逗号、中文逗号分隔
                .map(id => id.trim())
                .filter(id => id.length > 0);

              if (taskIds.length === 0) {
                this.$message.error('请输入有效的任务ID');
                return;
              }

              requestData.taskIds = taskIds;
            } else {
              // 批量重试模式
              requestData.taskType = this.retryForm.taskType;
              requestData.batchSize = this.retryForm.batchSize;
            }

            const response = await axios.post('/manage/fzData/retry', requestData, {
              headers: this.getAuthHeaders()
            });

            if (response.data.status === 200) {
              const result = response.data.data;
              let message = response.data.message;

              // 显示详细结果
              if (result && result.retryResults && result.retryResults.length > 0) {
                const successCount = result.retryResults.filter(r => r.status === 'queued').length;
                const errorCount = result.retryResults.filter(r => r.status === 'error').length;
                const skippedCount = result.retryResults.filter(r => r.status === 'skipped').length;

                message += `\n成功: ${successCount}, 错误: ${errorCount}, 跳过: ${skippedCount}`;
              }

              this.$message.success(message);
              this.retryDialogVisible = false;
              this.loadLogs();
              this.loadStatistics();
            } else {
              this.$message.error('重试失败: ' + response.data.message);
            }
          } catch (error) {
            console.error('重试异常:', error);
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
            } else {
              this.$message.error('重试异常: ' + error.message);
            }
          } finally {
            this.retryLoading = false;
          }
        },
        
        // 单个任务重试
        async retrySingleTask(row) {
          try {
            this.$confirm(`确定要重试任务 "${row.taskId}" 吗？`, '确认重试', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(async () => {
              const loading = this.$loading({
                lock: true,
                text: '正在重试任务...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });

              try {
                const response = await axios.post('/manage/fzData/retry', {
                  taskIds: [row.taskId],
                  maxRetries: 3
                }, {
                  headers: this.getAuthHeaders()
                });

                if (response.data.status === 200) {
                  this.$message.success(`任务 ${row.taskId} 重试成功`);
                  this.loadLogs();
                  this.loadStatistics();
                } else {
                  this.$message.error('重试失败: ' + response.data.message);
                }
              } catch (error) {
                console.error('单个任务重试失败:', error);
                if (error.response?.status === 401) {
                  this.$message.error('登录已过期，请重新登录');
                  this.handleLogout();
                } else {
                  this.$message.error('重试失败: ' + error.message);
                }
              } finally {
                loading.close();
              }
            }).catch(() => {
              // 用户取消操作
            });
          } catch (error) {
            this.$message.error('重试失败: ' + error.message);
          }
        },
        
        // 显示测试对话框
        showTestDialog() {
          this.testDialogVisible = true;
        },
        
        // 执行测试
        async executeTest() {
          this.testLoading = true;
          try {
            const endpoint = `/manage/fzData/test/${this.testForm.testType}`;
            const data = {
              filePath: this.testForm.filePath,
              index: this.testForm.index
            };
            
            const response = await axios.post(endpoint, data, {
              headers: this.getAuthHeaders()
            });
            if (response.data.status === 200) {
              this.$message.success('测试完成: ' + response.data.message);
              this.testDialogVisible = false;
              this.loadLogs();
            } else {
              this.$message.warning('测试失败: ' + response.data.message);
            }
          } catch (error) {
            this.$message.error('测试异常: ' + error.message);
          } finally {
            this.testLoading = false;
          }
        },
        
        // 显示串行处理对话框
        showProcessDialog() {
          this.processDialogVisible = true;
        },
        
        // 执行串行处理
        async executeSerialProcess() {
          this.processLoading = true;
          this.processResult = null;

          try {
            // 根据选择的类型确定端点
            let endpoint;
            if (this.processForm.taskType === 'all') {
              // 对于全部类型，使用企业数据端点但传递特殊参数
              endpoint = '/manage/fzData/serial/company';
            } else {
              endpoint = `/manage/fzData/serial/${this.processForm.taskType}`;
            }

            const data = {
              filePath: this.processForm.filePath,
              index: this.processForm.index,
              maxTasks: this.processForm.maxTasks,
              // 如果是全部类型，传递特殊标识
              dataType: this.processForm.taskType === 'all' ? 'all' : this.processForm.taskType
            };

            const response = await axios.post(endpoint, data, {
              headers: this.getAuthHeaders()
            });

            if (response.data.status === 200) {
              this.processResult = response.data.data;

              if (this.processResult.totalTasks === 0) {
                this.$message.info(this.processResult.message);
              } else {
                this.$message.success(`串行处理已启动，共 ${this.processResult.totalTasks} 个任务`);
              }

              // 如果返回了taskId，自动开始监控进度
              if (this.processResult.taskId) {
                this.startProcessMonitoring(this.processResult.taskId);
              }

              // 刷新日志和统计
              this.loadLogs();
              this.loadStatistics();
            } else {
              this.$message.error('串行处理失败: ' + (response.data.message || '未知错误'));
            }
          } catch (error) {
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
            } else {
              this.$message.error('串行处理异常: ' + error.message);
            }
          } finally {
            this.processLoading = false;
          }
        },

        // 开始进度监控
        startProcessMonitoring(taskId) {
          // 如果已经在监控相同的任务，不重复创建连接
          if (this.processEventSource && this.processMonitoring && this.processMonitoring.taskId === taskId) {
            return;
          }

          // 关闭之前的连接
          if (this.processEventSource) {
            this.processEventSource.close();
            this.processEventSource = null;
          }

          this.processMonitoring = {
            taskId: taskId,
            status: 'pending',
            progress: 0,
            message: '正在连接监控...',
            processed: 0,
            duration: 0
          };

          // 创建SSE连接
          const eventSource = new EventSource(`/manage/fzData/progress/${taskId}?token=${this.getToken()}`);
          this.processEventSource = eventSource;

          eventSource.onopen = () => {
            this.processMonitoring.message = '监控连接已建立';
          };

          eventSource.onmessage = (event) => {
            try {
              const data = JSON.parse(event.data);

              this.processMonitoring = {
                ...this.processMonitoring,
                status: data.status,
                progress: data.progress || 0,
                message: data.message,
                processed: data.processed || 0,
                duration: data.duration || 0
              };

              // 如果任务完成，关闭连接并刷新列表
              if (data.status === 'success' || data.status === 'failed' || data.status === 'skipped') {
                setTimeout(() => {
                  eventSource.close();
                  this.processEventSource = null;
                  this.loadLogs();
                  this.loadStatistics();
                }, 2000); // 2秒后关闭连接
              }
            } catch (error) {
              console.error('解析进度数据失败:', error);
            }
          };

          eventSource.onerror = (error) => {
            console.error('进度监控连接错误:', error);
            this.processMonitoring.message = '监控连接出现错误';
            eventSource.close();
            this.processEventSource = null;
          };
        },

        // 停止进度监控
        stopProcessMonitoring() {
          if (this.processEventSource) {
            this.processEventSource.close();
            this.processEventSource = null;
          }
          this.processMonitoring = null;
        },

        // 继续处理中断的任务
        async continueProcessing(taskId) {
          this.continueProcessingLoading = true;
          try {
            const response = await axios.post('/manage/fzData/continue-processing', {
              taskId: taskId
            }, {
              headers: this.getAuthHeaders()
            });

            if (response.data.status === 200) {
              const resultData = response.data.data;
              this.$message.success(resultData.message || '任务已重新启动处理');

              // 更新任务结果状态
              if (this.processResult && this.processResult.taskId === taskId) {
                this.processResult.status = 'pending';
                this.processResult.message = resultData.message || '任务已重新启动';
                this.processResult.totalTasks = resultData.totalTasks;
                this.processResult.isContinued = resultData.isContinued;
              }

              // 自动开始监控进度
              this.startProcessMonitoring(taskId);

              // 刷新任务列表
              this.loadLogs();
              this.loadStatistics();
            } else {
              this.$message.error('重新启动任务失败: ' + (response.data.message || '未知错误'));
            }
          } catch (error) {
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.logout();
            } else {
              this.$message.error('重新启动任务失败: ' + (error.response?.data?.message || error.message));
            }
          } finally {
            this.continueProcessingLoading = false;
          }
        },

        // 获取任务状态类型（用于标签颜色）
        getTaskStatusType(status) {
          const statusMap = {
            'pending': 'info',
            'processing': 'warning',
            'success': 'success',
            'failed': 'danger',
            'skipped': 'warning',
            'cancelled': 'info'
          };
          return statusMap[status] || 'info';
        },

        // 获取任务状态文本
        getTaskStatusText(status) {
          const statusMap = {
            'pending': '等待处理',
            'processing': '处理中',
            'success': '处理成功',
            'failed': '处理失败',
            'skipped': '已跳过',
            'cancelled': '已取消'
          };
          return statusMap[status] || status;
        },
        

        
        // 清理缓存
        async clearCache() {
          try {
            const response = await axios.delete('/manage/fzData/cache', {
              headers: this.getAuthHeaders()
            });
            if (response.data.status === 200) {
              this.$message.success('缓存清理成功');
            }
          } catch (error) {
            this.$message.error('清理缓存失败: ' + error.message);
          }
        },

        // 手动执行从API获取所有ZIP数据
        async manualGetAllZipFromApi() {
          try {
            await this.$confirm('确定要手动同步福州数据吗？此操作可能需要较长时间。', '确认同步', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            });

            this.manualSyncLoading = true;
            this.$message.info('开始同步福州数据，请耐心等待...');

            const response = await axios.post('/manage/fzData/manual/getAllZipFromApi', {}, {
              headers: this.getAuthHeaders(),
              timeout: 1000 * 60 * 30 // 30分钟超时
            });

            if (response.data.status === 200) {
              this.$message.success('福州数据同步完成！');
              // 刷新日志列表和统计信息
              await this.loadLogs();
              await this.loadStatistics();
            } else {
              this.$message.error('同步失败: ' + (response.data.message || '未知错误'));
            }
          } catch (error) {
            if (error.message === 'cancel') {
              // 用户取消操作
              return;
            }
            console.error('手动同步福州数据失败:', error);
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
            } else if (error.code === 'ECONNABORTED') {
              this.$message.error('同步超时，请检查网络连接或稍后重试');
            } else {
              this.$message.error('同步失败: ' + error.message);
            }
          } finally {
            this.manualSyncLoading = false;
          }
        },

        // 显示清理重复任务对话框
        showCleanupDialog() {
          this.cleanupDialogVisible = true;
          this.cleanupResult = null;
        },

        // 执行清理重复任务
        async executeCleanup() {
          this.cleanupLoading = true;
          try {
            const response = await axios.post('/manage/fzData/cleanup', this.cleanupForm, {
              headers: this.getAuthHeaders()
            });

            if (response.data.status === 200) {
              this.cleanupResult = response.data.data;
              this.$message.success(this.cleanupForm.dryRun ? '预览完成' : '清理完成');

              // 如果是实际清理，刷新日志和统计
              if (!this.cleanupForm.dryRun) {
                this.loadLogs();
                this.loadStatistics();
              }
            } else {
              this.$message.error('清理失败: ' + (response.data.message || '未知错误'));
            }
          } catch (error) {
            console.error('清理重复任务异常:', error);
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
            } else {
              this.$message.error('清理异常: ' + error.message);
            }
          } finally {
            this.cleanupLoading = false;
          }
        },
        
        // 查看详情
        viewTaskDetails(row) {
          this.currentTaskDetail = row;
          this.taskDetailDialogVisible = true;
          this.loadTaskDetails(row.taskId);
        },
        
        // 加载任务详情
        async loadTaskDetails(taskId) {
          this.detailLoading = true;
          try {
            const response = await axios.get('/manage/fzData/taskDetails', {
              params: { taskId },
              headers: this.getAuthHeaders()
            });

            if (response.data.status === 200) {
              this.taskDetailData = response.data.data || {};

              // 如果数据为空，尝试从其他字段获取
              if (!this.taskDetailData.decryptedData && !this.taskDetailData.dataAnalysis && !this.taskDetailData.processingResult) {
                // 检查是否有其他数据字段
                if (this.currentTaskDetail.message) {
                  try {
                    // 尝试解析message字段中的数据
                    const messageData = JSON.parse(this.currentTaskDetail.message);

                    if (messageData.decryptedData) {
                      this.taskDetailData.decryptedData = messageData.decryptedData;
                    }
                    if (messageData.analysis) {
                      this.taskDetailData.dataAnalysis = messageData.analysis;
                    }
                    if (messageData.result) {
                      this.taskDetailData.processingResult = messageData.result;
                    }
                  } catch (e) {
                    // 解析失败，忽略
                  }
                }

                // 尝试从response的其他字段获取数据
                if (response.data.decryptedData) {
                  this.taskDetailData.decryptedData = response.data.decryptedData;
                }
                if (response.data.dataAnalysis) {
                  this.taskDetailData.dataAnalysis = response.data.dataAnalysis;
                }
                if (response.data.processingResult) {
                  this.taskDetailData.processingResult = response.data.processingResult;
                }
              }
            } else {
              this.$message.warning('获取任务详情失败: ' + (response.data.message || '未知错误'));
            }
          } catch (error) {
            console.error('加载任务详情异常:', error);
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
            } else if (error.response?.status === 404) {
              this.$message.warning('任务详情不存在或已被删除');
            } else {
              this.$message.error('加载任务详情失败: ' + error.message);
            }
          } finally {
            this.detailLoading = false;
          }
        },
        
        // 辅助方法
        getTaskTypeName(type) {
          const names = {
            company: '企业数据',
            healthCheck: '体检数据',
            diagnosis: '诊断数据',
            unknown: '未知类型'
          };

          // 处理 undefined 或空值的情况
          if (!type || type === 'undefined') {
            return '未知类型';
          }

          return names[type] || type;
        },
        
        getTaskTypeColor(type) {
          const colors = {
            company: 'primary',
            healthCheck: 'success',
            diagnosis: 'warning'
          };
          return colors[type] || '';
        },
        
        getStatusName(status) {
          const names = {
            pending: '等待处理',
            processing: '处理中',
            success: '处理成功',
            failed: '处理失败',
            skipped: '跳过处理',
            retrying: '重试中',
            cancelled: '已取消',
            interrupted: '已中断'
          };
          return names[status] || status;
        },

        getStatusColor(status) {
          const colors = {
            pending: 'info',
            processing: 'warning',
            success: 'success',
            failed: 'danger',
            skipped: 'info',
            retrying: 'warning',
            cancelled: 'info',
            interrupted: 'warning'
          };
          return colors[status] || '';
        },
        
        formatDate(dateStr) {
          if (!dateStr) return '-';
          return new Date(dateStr).toLocaleString('zh-CN');
        },
        
        downloadFile(fileName) {
          // 创建下载链接
          const link = document.createElement('a');
          link.href = `/app/public/fzData/${fileName}`;
          link.download = fileName;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        },
        
        // SSE连接管理
        connectSSE(taskId) {
          if (this.sseConnections.has(taskId)) {
            return; // 已经连接
          }

          const eventSource = new EventSource(`/manage/fzData/progress?taskId=${taskId}`);

          eventSource.onopen = () => {
            // SSE连接已建立
          };

          eventSource.onmessage = (event) => {
            try {
              const data = JSON.parse(event.data);

              switch (data.type) {
                case 'connected':
                  // SSE连接成功
                  break;
                case 'progress':
                  this.updateTaskProgress(taskId, data.data);
                  break;
                case 'error':
                  this.$message.error(data.message);
                  this.closeSSE(taskId);
                  break;
              }
            } catch (error) {
              // 解析SSE消息失败
            }
          };

          eventSource.onerror = (error) => {
            this.closeSSE(taskId);
          };

          this.sseConnections.set(taskId, eventSource);
        },

        closeSSE(taskId) {
          if (this.sseConnections.has(taskId)) {
            this.sseConnections.get(taskId).close();
            this.sseConnections.delete(taskId);
          }
        },

        updateTaskProgress(taskId, progressData) {
          this.taskProgress.set(taskId, progressData);
          
          // 如果正在显示进度对话框且是当前任务，更新显示
          if (this.progressDialogVisible && this.currentTaskProgress.taskId === taskId) {
            this.currentTaskProgress = {
              taskId,
              status: progressData.status,
              message: progressData.message || '',
              progress: progressData.progress || 0,
              details: progressData.details || {}
            };
          }

          // 任务完成后刷新日志列表
          if (progressData.status === 'completed' || progressData.status === 'failed') {
            this.loadLogs();
            this.loadStatistics();
            this.closeSSE(taskId);
          }
        },

        showProgress(taskId) {
          this.currentTaskProgress = {
            taskId,
            status: '连接中...',
            message: '正在连接进度服务...',
            progress: 0,
            details: {}
          };
          
          this.progressDialogVisible = true;
          this.connectSSE(taskId);
        },

        // 显示文件列表
        async showFileList() {
          this.fileDialogVisible = true;
          this.fileLoading = true;
          
          try {
            const response = await axios.get('/manage/fzData/files', {
              headers: this.getAuthHeaders()
            });
             if (response.data.status === 200) {
               this.fileListData = response.data.data;
             }
          } catch (error) {
            this.$message.error('获取文件列表失败: ' + error.message);
          } finally {
            this.fileLoading = false;
          }
        },

        downloadFile(fileName) {
          // 创建下载链接
          const link = document.createElement('a');
          link.href = `/app/public/fzData/${fileName}`;
          link.download = fileName;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        },

        getFilteredFiles(type) {
          if (!this.fileListData.files[type]) {
            return [];
          }
          
          let files = this.fileListData.files[type];
          
          // 如果有搜索文本，进行过滤
          if (this.fileSearchText) {
            files = files.filter(file =>
              file.fileName.toLowerCase().includes(this.fileSearchText.toLowerCase())
            );
          }
          
          // 限制显示数量，避免渲染过多数据
          return files.slice(0, 100);
        },

        testWithFile(file, type) {
          // 设置测试表单数据
          this.testForm.testType = type === 'healthCheck' ? 'healthCheck' : type;
          this.testForm.filePath = file.fileName;
          this.testForm.index = file.index;
          
          // 关闭文件列表对话框，打开测试对话框
          this.fileDialogVisible = false;
          this.testDialogVisible = true;
        },

        copyToClipboard(text) {
          const input = document.createElement('input');
          input.value = text;
          document.body.appendChild(input);
          input.select();
          document.execCommand('copy');
          document.body.removeChild(input);
          this.$message.success('复制成功');
        },

        // 队列管理 - 已废弃，使用Redis任务管理替代
        /*
        async refreshQueueStatus() {
          this.queueStatusLoading = true;
          try {
            const response = await axios.get('/fzdata/tasks/queue-status', {
              headers: this.getAuthHeaders()
            });

            if (response.data.status === 200 || response.data.code === 200) {
              this.queueStatus = response.data.data;
              this.$message.success('队列状态刷新成功');
            } else {
              this.$message.error('刷新队列状态失败: ' + (response.data.message || '未知错误'));
            }
          } catch (error) {
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
            } else {
              this.$message.error('刷新队列状态失败: ' + error.message);
            }
          } finally {
            this.queueStatusLoading = false;
          }
        },
        */

        /*
        async startQueueProcessing() {
          this.queueActionLoading = true;
          try {
            const response = await axios.post('/fzdata/tasks/start-queue', this.queueProcessForm, {
              headers: this.getAuthHeaders()
            });

            if (response.data.status === 200 || response.data.code === 200) {
              this.$message.success('队列处理已启动');
              this.currentProcessId = response.data.data?.processId;
              // 开始定时刷新状态
              this.startStatusRefresh();
            } else {
              this.$message.error('启动队列处理失败: ' + (response.data.message || '未知错误'));
            }
          } catch (error) {
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
            } else {
              this.$message.error('启动队列处理异常: ' + error.message);
            }
          } finally {
            this.queueActionLoading = false;
          }
        },
        */

        /*
        async pauseProcessing() {
          if (!this.currentProcessId) {
            this.$message.warning('没有正在运行的处理器');
            return;
          }

          this.queueActionLoading = true;
          try {
            const response = await axios.post('/fzdata/tasks/control', {
              processId: this.currentProcessId,
              action: 'pause'
            }, {
              headers: this.getAuthHeaders()
            });

            if (response.data.status === 200 || response.data.code === 200) {
              this.$message.success('队列处理已暂停');
            } else {
              this.$message.error('暂停队列处理失败: ' + (response.data.message || '未知错误'));
            }
          } catch (error) {
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
            } else {
              this.$message.error('暂停队列处理异常: ' + error.message);
            }
          } finally {
            this.queueActionLoading = false;
          }
        },
        */

        /*
        async resumeProcessing() {
          if (!this.currentProcessId) {
            this.$message.warning('没有正在运行的处理器');
            return;
          }

          this.queueActionLoading = true;
          try {
            const response = await axios.post('/fzdata/tasks/control', {
              processId: this.currentProcessId,
              action: 'resume'
            }, {
              headers: this.getAuthHeaders()
            });

            if (response.data.status === 200 || response.data.code === 200) {
              this.$message.success('队列处理已恢复');
            } else {
              this.$message.error('恢复队列处理失败: ' + (response.data.message || '未知错误'));
            }
          } catch (error) {
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
            } else {
              this.$message.error('恢复队列处理异常: ' + error.message);
            }
          } finally {
            this.queueActionLoading = false;
          }
        },

        async stopProcessing() {
          if (!this.currentProcessId) {
            this.$message.warning('没有正在运行的处理器');
            return;
          }

          this.queueActionLoading = true;
          try {
            const response = await axios.post('/fzdata/tasks/control', {
              processId: this.currentProcessId,
              action: 'stop'
            }, {
              headers: this.getAuthHeaders()
            });

            if (response.data.status === 200 || response.data.code === 200) {
              this.$message.success('队列处理已停止');
              this.currentProcessId = null;
              this.stopStatusRefresh();
            } else {
              this.$message.error('停止队列处理失败: ' + (response.data.message || '未知错误'));
            }
          } catch (error) {
            if (error.response?.status === 401) {
              this.$message.error('登录已过期，请重新登录');
              this.handleLogout();
            } else {
              this.$message.error('停止队列处理异常: ' + error.message);
            }
          } finally {
            this.queueActionLoading = false;
          }
        },
        */

        startStatusRefresh() {
          if (this.statusRefreshTimer) {
            clearInterval(this.statusRefreshTimer);
          }
          this.statusRefreshTimer = setInterval(() => {
            this.refreshQueueStatus();
          }, 2000); // 每2秒刷新一次
        },

        stopStatusRefresh() {
          if (this.statusRefreshTimer) {
            clearInterval(this.statusRefreshTimer);
            this.statusRefreshTimer = null;
          }
        },

         // 任务详情相关方法
         getTaskTypeIcon(type) {
           const icons = {
             company: 'el-icon-office-building',
             healthCheck: 'el-icon-first-aid-kit',
             diagnosis: 'el-icon-medical'
           };
           return icons[type] || 'el-icon-files';
         },

         getStatusIcon(status) {
           const icons = {
             pending: 'el-icon-time',
             processing: 'el-icon-loading',
             success: 'el-icon-circle-check',
             failed: 'el-icon-circle-close',
             skipped: 'el-icon-remove',
             retrying: 'el-icon-refresh'
           };
           return icons[status] || 'el-icon-question';
         },

         getDurationColor(duration) {
           if (!duration) return 'info';
           if (duration < 1000) return 'success';
           if (duration < 5000) return 'primary';
           if (duration < 10000) return 'warning';
           return 'danger';
         },

         formatDuration(duration) {
           if (!duration) return '0ms';
           if (duration < 1000) return duration + 'ms';
           if (duration < 60000) return (duration / 1000).toFixed(1) + 's';
           return (duration / 60000).toFixed(1) + 'min';
         },

         getSuccessRateStatus(rate) {
           const numRate = parseFloat(rate) || 0;
           if (numRate >= 95) return 'success';
           if (numRate >= 80) return 'warning';
           return 'exception';
         },

         calculateEfficiency(taskDetail) {
           if (!taskDetail.duration || !taskDetail.totalRecords) {
             return 'N/A';
           }
           const recordsPerSecond = (taskDetail.totalRecords / (taskDetail.duration / 1000)).toFixed(2);
           return recordsPerSecond + ' 条/秒';
         },

         // 检查对象是否为非空对象
         hasNonEmptyObject(obj) {
           if (!obj) return false;
           if (typeof obj !== 'object') return true;
           if (Array.isArray(obj)) return obj.length > 0;
           return Object.keys(obj).length > 0;
         },

         // Redis任务管理相关方法
         showRedisTaskDialog() {
           this.redisTaskDialogVisible = true;
           this.redisActiveTab = 'tasks';
           this.loadRedisTaskList();
           this.loadRedisQueueStats();
         },

         async loadRedisTaskList() {
           this.redisTaskLoading = true;
           try {
             const params = {
               page: this.redisTaskPagination.page,
               pageSize: this.redisTaskPagination.pageSize,
               ...this.redisTaskFilter
             };

             const response = await axios.get('/manage/fzData/redis/tasks', {
               params,
               headers: this.getAuthHeaders()
             });

             if (response.data.status === 200 && response.data.data) {
               // 处理数据，确保每个任务都有必要的字段
               const processedTasks = response.data.data.tasks.map((task, index) => {
                 const processedTask = {
                   ...task,
                   // 确保有taskId字段
                   taskId: task.taskId || (task.taskKey ? task.taskKey.replace('task:', '') : 'unknown'),
                   // 确保有taskType字段
                   taskType: task.taskType || task.dataType || 'unknown',
                   // 确保有status字段
                   status: task.status || 'unknown',
                   // 确保有progress字段
                   progress: task.progress || 0,
                   // 确保有message字段
                   message: task.message || '无消息',
                   // 处理时间字段
                   createdAt: task.createdAt || task.startTime || null,
                   updatedAt: task.updatedAt || task.endTime || null
                 };
                 return processedTask;
               });

               // 使用Vue.set确保响应式更新
               this.$set(this, 'redisTaskList', processedTasks);
               this.$set(this, 'redisTaskPagination', response.data.data.pagination);

               // 强制更新视图
               this.$forceUpdate();

               this.$message.success(`Redis任务列表加载成功，共${this.redisTaskList.length}条任务`);
             } else {
               console.error('Redis任务列表加载失败:', response.data);
               this.$message.error(response.data.message || 'Redis任务列表加载失败');
               this.redisTaskList = [];
               this.redisTaskPagination = { page: 1, pageSize: 20, total: 0, totalPages: 0 };
             }
           } catch (error) {
             console.error('加载Redis任务列表失败:', error);
             this.$message.error('加载Redis任务列表失败: ' + (error.response?.data?.message || error.message));
           } finally {
             this.redisTaskLoading = false;
           }
         },

         async loadRedisQueueStats() {
           this.redisStatsLoading = true;
           try {
             const response = await axios.get('/manage/fzData/redis/stats', {
               headers: this.getAuthHeaders()
             });

             if (response.data.status === 200 && response.data.data) {
               this.redisQueueStats = response.data.data;
               this.$message.success('Redis队列统计加载成功');
             } else {
               this.$message.error(response.data.message || 'Redis队列统计加载失败');
               this.redisQueueStats = {};
             }
           } catch (error) {
             console.error('加载Redis队列统计失败:', error);
             this.$message.error('加载Redis队列统计失败: ' + (error.response?.data?.message || error.message));
           } finally {
             this.redisStatsLoading = false;
           }
         },

         handleRedisTaskPageChange(page) {
           this.redisTaskPagination.page = page;
           this.loadRedisTaskList();
         },

         resetRedisTaskFilter() {
           this.redisTaskFilter = {
             taskType: '',
             status: '',
             pattern: ''
           };
           this.redisTaskPagination.page = 1;
           this.loadRedisTaskList();
         },

         // 继续处理Redis任务
         async continueRedisTask(task) {
           // 设置单个任务的加载状态
           this.$set(task, 'continuing', true);

           try {
             const taskId = task.taskId || task.taskKey.replace('task:', '');
             const response = await axios.post('/manage/fzData/continue-processing', {
               taskId: taskId
             }, {
               headers: this.getAuthHeaders()
             });

             if (response.data.status === 200) {
               const resultData = response.data.data;
               this.$message.success(resultData.message || '任务已重新启动处理');

               // 刷新Redis任务列表
               this.loadRedisTaskList();

               // 如果当前有打开的处理结果，也更新它
               if (this.processResult && this.processResult.taskId === taskId) {
                 this.processResult.status = 'pending';
                 this.processResult.message = resultData.message || '任务已重新启动';
                 this.processResult.totalTasks = resultData.totalTasks;
                 this.processResult.isContinued = resultData.isContinued;

                 // 自动开始监控进度
                 this.startProcessMonitoring(taskId);
               }

               // 刷新其他相关数据
               this.loadLogs();
               this.loadStatistics();
               this.loadRedisQueueStats();
             } else {
               this.$message.error('重新启动任务失败: ' + (response.data.message || '未知错误'));
             }
           } catch (error) {
             if (error.response?.status === 401) {
               this.$message.error('登录已过期，请重新登录');
               this.logout();
             } else {
               this.$message.error('重新启动任务失败: ' + (error.response?.data?.message || error.message));
             }
           } finally {
             this.$set(task, 'continuing', false);
           }
         },

         async deleteRedisTask(task) {
           try {
             await this.$confirm('确定要删除这个Redis任务吗？此操作不可恢复！', '确认删除', {
               confirmButtonText: '确定',
               cancelButtonText: '取消',
               type: 'warning'
             });

             // 这里可以添加删除单个任务的API调用
             this.$message.success('任务删除成功');
             this.loadRedisTaskList();
           } catch (error) {
             if (error !== 'cancel') {
               console.error('删除Redis任务失败:', error);
               this.$message.error('删除Redis任务失败: ' + (error.response?.data?.message || error.message));
             }
           }
         },

         async executeRedisCleanup() {
           this.redisCleanupLoading = true;
           try {
             const response = await axios.post('/manage/fzData/redis/cleanup', this.redisCleanupForm, {
               headers: this.getAuthHeaders()
             });

             if (response.data.status === 200 && response.data.data) {
               this.redisCleanupResult = response.data.data;
               this.$message.success(response.data.message || 'Redis任务清理完成');

               // 如果是实际清理，刷新任务列表
               if (!this.redisCleanupForm.dryRun) {
                 this.loadRedisTaskList();
                 this.loadRedisQueueStats();
               }
             } else {
               this.$message.error(response.data.message || 'Redis任务清理失败');
               this.redisCleanupResult = null;
             }
           } catch (error) {
             console.error('Redis任务清理失败:', error);
             this.$message.error('Redis任务清理失败: ' + (error.response?.data?.message || error.message));
           } finally {
             this.redisCleanupLoading = false;
           }
         },

         // 解密任务ZIP包
         decryptTaskZip(task) {
           this.currentDecryptTask = task;
           this.decryptDialogVisible = true;
           this.decryptResult = null;
           this.decryptResultTab = 'overview';
           this.decryptSearchText = '';
           this.decryptTableColumns = [];
         },

         // 解密任务ZIP文件
         async decryptTaskZipFile() {
           if (!this.currentDecryptTask) {
             this.$message.warning('请选择要解密的任务');
             return;
           }

           this.decryptLoading = true;
           try {
             const response = await axios.post('/manage/fzData/decrypt/task', {
               taskId: this.currentDecryptTask.taskId
             }, {
               headers: this.getAuthHeaders()
             });

             if (response.data.status === 200) {
               this.decryptResult = response.data.data;
               this.generateDecryptTableColumns();
               this.$message.success('任务ZIP文件解密成功');
             } else {
               this.$message.error('解密失败: ' + response.data.message);
             }
           } catch (error) {
             console.error('解密任务ZIP文件失败:', error);
             this.$message.error('解密失败: ' + (error.response?.data?.message || error.message));
           } finally {
             this.decryptLoading = false;
           }
         },

         // 获取任务类型名称
         getTaskTypeName(taskType) {
           const typeMap = {
             company: '企业数据',
             healthCheck: '体检数据',
             diagnosis: '诊断数据'
           };
           return typeMap[taskType] || taskType;
         },

         // 生成解密数据表格列
         generateDecryptTableColumns() {
           if (!this.decryptResult || !this.decryptResult.data || this.decryptResult.data.length === 0) {
             this.decryptTableColumns = [];
             return;
           }

           const firstRecord = this.decryptResult.data[0];
           this.decryptTableColumns = Object.keys(firstRecord).map(key => ({
             prop: key,
             label: key,
             width: key.length > 10 ? 150 : 120
           }));
         },

         // 获取过滤后的解密数据
         getFilteredDecryptData() {
           if (!this.decryptResult || !this.decryptResult.data) {
             return [];
           }

           let data = this.decryptResult.data.slice(0, 100); // 限制显示前100条

           if (this.decryptSearchText) {
             const searchText = this.decryptSearchText.toLowerCase();
             data = data.filter(item => {
               return Object.values(item).some(value =>
                 String(value).toLowerCase().includes(searchText)
               );
             });
           }

           return data;
         }
      },

      // 组件销毁时关闭所有SSE连接
      beforeDestroy() {
        this.sseConnections.forEach((eventSource, taskId) => {
          this.closeSSE(taskId);
        });

        // 关闭串行处理的进度监控连接
        this.stopProcessMonitoring();
      }
    });
  </script>
</body>
</html>
