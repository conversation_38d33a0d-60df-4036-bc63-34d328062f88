

module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  /** 更改
   * 绝大部分字段是直接复制过来，名称没变
   * 但是原jobHelth表的name字段表示机构名称，而adminProjects表的name字段表示项目名称，我就采用了项目表的，机构名改为serviceName
   * 删除了adminProjects表的 corp、adminOrgID两个字段，我发现这个端没人用到
   */
  const jobHealthSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    comment: String, // 备注
    QR_CODE: String,
    // =========== 受检单位相关信息 =============
    EnterpriseID: {
      type: String,
    }, // 企业id
    companyScale: {
      // 企业规模
      type: String,
      enum: [ '大型', '大', '中型', '中', '小型', '小', '微型', '微', '其他' ],
    },
    companyID: {
      type: String,
    }, // 用人单位ID ，取信用代码
    companyName: {
      type: String,
      default: '',
    }, // 用人单位名称
    companyAddress: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        districts: Array, // 营业执照注册地址
        address: String, // 具体地址
      },
    ], // 用人单位地址
    companyContact: {
      type: String,
      default: '',
    }, // 用人单位联系人
    companyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 联系人手机号码
    companyContactEmail: {
      type: String,
      default: '',
    }, // 邮箱
    companyIndustry: {
      type: Array,
      default: [],
    }, // '所属行业',
    riskLevel: {
      type: String,
    }, // 风险等级，从adminorg中的level来
    districtRegAdd: {
      type: Array,
      default: [],
    }, // 工作场所

    // ==================== 委托单位信息
    anthemEnterpriseID: {
      type: String,
    }, // 企业id
    anthemCompanyID: {
      type: String,
      index: true,
    }, // 委托单位ID ，取信用代码
    newAnthemCompany: {
      type: String,
    }, // 新增的委托单位
    anthemCompanyName: {
      type: String,
      default: '',
    }, // 委托单位名称
    anthemCompanyContact: {
      type: String,
      default: '',
    }, // 用人单位联系人
    anthemCompanyContactPhoneNumber: {
      type: String,
      default: '',
    }, // 联系人手机号码
    anthemCompanyContactEmail: {
      type: String,
      default: '',
    }, // 邮箱
    anthemCompanyAddress: [
      {
        districts: Array, // 营业执照注册地址
        address: String, // 具体地址
      },
    ], // 委托单位地址

    // ===================== 机构相关信息  ==============================
    // serviceName: { 讲道理这个更符合，但是为了兼容老数据，只能以这个为准了，毕竟现在机构端还没有真实的数据
    name: {
      type: String,
      default: '',
    }, // 机构名字
    serviceAddress: {
      type: Array,
      default: [],
    }, // 机构地址
    serviceCorp: {
      type: String,
      default: '',
    }, // 机构法人
    serviceCorpPhoneNumber: {
      type: String,
      default: '',
    }, // 机构法人手机号码
    serviceOrgLevel: {
      type: String,
      default: '',
    }, // 机构资质等级
    serviceTerm: {
      type: String,
      default: '',
    }, // 机构资质有效期
    serviceRanges: {
      type: String,
      default: '',
    }, // 机构服务范围
    serviceOrgID: {
      type: String,
    }, // 服务机构ID ， 取信用代码
    serviceID: {
      type: String,
      index: true,
    }, //  服务机构ID - 请和上面区划id的大小写
    serviceUserID: {
      type: String,
      ref: 'ServiceUser',
    }, // 当前操作人员ID。

    // ================== 关于项目的信息 =======================
    projectName: {
      // 对应上面机构名的妥协，这里也妥协
      type: String,
      default: '',
    }, // 项目名称
    serviceType: {
      type: String,
    }, // 技术服务类型
    serviceCon: {
      type: String,
      default: '',
    }, // 服务内容
    date: {
      type: Date,
      default: Date.now,
    }, // 项目创建时间
    expectStartTime: {
      type: Date,
    }, // 预计开始时间
    expectStopTime: {
      type: Date,
    }, // 预计结束时间,
    requiredTime: {
      type: Date,
    }, // 要求完成的时间
    applyTime: {
      type: Date,
    }, // 申报时间
    completeTime: {
      type: Date,
    }, // 实际完成时间
    status: {
      type: Number,
      default: 0,
    }, // 申报状态,0，未报送，1，已报送，
    completeStatus: {
      type: Number,
      default: 0,
    }, // 完成状态，0，未完成； 1，已完成
    projectStop: {
      type: Boolean,
      dafault: false,
    }, // 项目是否暂停，false：未终止，true：终止
    projectCancel: {
      type: Boolean,
      dafault: false,
    }, // 项目是否终止，false：未终止，true：终止
    completeUpdate: {
      type: Number,
      default: 0,
    }, // 上报后更新状态
    InformationChange: [
      {
        content: {
          type: Array,
          default: [],
        }, // 变更内容
        superUser: [
          // 监管端id
          {
            type: String,
            ref: 'SuperUser',
          },
        ],
        nowTime: {
          type: Date,
          default: Date.now,
        }, // 变更内容
      },
    ], // 信息变更 数组
    projectGroup: {
      type: String,
      default: '',
      ref: 'serviceDingtrees',
    }, // '项目检测组',
    EvaluationProjectManager: [
      {
        type: String,
        index: true,
        default: '',
        ref: 'ServiceEmployee',
      },
    ], // '评价项目成员
    EvaluationProjectManagerAdmin: {
      type: String,
      default: '',
      index: true,
      ref: 'ServiceEmployee',
    }, // 评价组经理
    EvaluationGroup: {
      type: String,
      default: '',
      index: true,
      ref: 'serviceDingtrees',
    }, // 评价组
    personInCharge: {
      type: String,
      default: '',
      ref: 'ServiceEmployee',
    }, // '项目负责人ID',
    offlineCompleted: {
      type: Boolean,
      default: false,
    }, // 线下已经完成 如果线下完成的话 需要将项目复制一份到jobhealth（已完成项目）
    personsOfProject: [
      {
        type: String,
        default: '',
        ref: 'ServiceEmployee',
      },
    ], // '项目组成员ID',
    projectNumber: {
      type: String,
      unique: true,
      dropDups: true,
    }, // 项目编号
    declareAdds: {
      type: Array,
      default: [],
    },
    salesman: {
      type: String,
    },
    farmOut: {
      // 是否分包lht+(chenke)
      type: Boolean,
    },
    farmOutParams: {
      // 分包参数
      type: String,
    },
    description: {
      // 项目简介
      type: String,
    },
    vipRequirement: {
      // vip需求
      type: String,
    },
    approvedStartTime: Date, // 合同审批发起时间
    entrustDate: { type: Date, default: Date.now }, // 委托时间
    source: {
      // 项目来源
      type: String,
      default: 'oapi',
    },

    // 申报地址，由于用户可以自行修改，所以和下面的workAdd区别开，
    // 存area_code，该code用于后注册的行政端也能读取申报的项目列表，
    // 存area_code前六位，即区县代码有效位数，其父级单位通过代码即可找到，全国的则需特殊处理
    workPlaces: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        workAddName: String, // 工作场所的中文地址

        name: String, // 工作场所的名称
        workAdd: {
          type: Array,
        }, // 工作场所具体地址，area_code
        checked: {
          type: Boolean,
          default: true,
        }, // 是否被检测，默认一般是检测了
      },
    ], // 检测的工作场所，用于识别
    superUserID: {
      type: Array,
      default: [],
    }, // 机构/行政用户，申报时，如果区域内有行政用户，那就会存进去
    backInfo: [
      {
        superUser: {
          type: String,
          ref: 'SuperUser',
        },
        backTime: {
          type: Date,
        }, // 回执时间
        cname: String, // 监管端
        name: String, // 监管端经办人
      },
    ],

    // =============钱money
    projectPrice: {
      type: Number,
      default: 0,
    }, // 项目价格
    cooperationFee: {
      type: Number,
      default: 0,
    }, // 合作费
    reviewFee: {
      type: Number,
      default: 0,
    }, // 评审费
    otherFee: {
      type: Number,
      default: 0,
    }, // 其他费
    netEarnings: {
      type: Number,
      default: 0,
    }, // 净赚=价格-合作费-评审费-其他费

    // ============== 快递
    mailingAddress: {
      address: String,
      districts: Array,
    }, // 邮寄地址
    recipient: {
      type: String,
      default: '',
    }, // 收件人
    recipientPhoneNum: {
      type: String,
      default: '',
    }, // 收件号码

    // =============银行
    accountsBank: {
      type: String,
      default: '',
    },
    accountsAddress: {
      type: String,
      default: '',
    },
    accountsNumber: {
      type: String,
      default: '',
    },
    accountsPhoneNum: {
      type: String,
      default: '',
    },

    tariffNumber: {
      type: String,
      default: '',
    },

    // ========================= 原jobHelth表数据，除了机构名和项目名=================
    // 所有文件都加来源字段，为了方便不同端读取这个文件，不然不知道文件在哪个端存着
    // 其中默认值在不同端应该放不一样，拷贝过去后应该修改
    formData: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        type: {
          type: String,
        },
        // 工种
        workplace: {
          type: String,
        },
        // 工作地点
        factors: {
          type: String,
        },
        // 危害因素
        number: {
          type: String,
        },
        // 接触人数
        csystem: {
          type: String,
        },
        // 班制
        operation: {
          type: String,
        },
        // 运行情况
        time: {
          type: String,
        },
        // 接触时间
        protectiveUse: String, // 个人防护用品发放及使用情况
      },
    ],
    year: Date, // 年度
    detectionCustom: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        fileName: {
          type: String,
          default: '',
        },
        url: {
          type: String,
          default: '',
        },
        source: {
          type: String,
          default: 'service', // super || enterprise || operate
        }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
        // 一旦这个字段不是enterprise，那么企业端就无权修改
      },
    ], // 自定义机构资质
    contract: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        fileName: {
          type: String,
        },
        url: {
          type: String,
        },
        source: {
          type: String,
          default: 'service', // super || enterprise || operate
        }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
      },
    ], // 合同书
    summary: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        fileName: {
          type: String,
        },
        url: {
          type: String,
        },
        source: {
          type: String,
          default: 'service', // super || enterprise || operate
        }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
      },
    ], // 汇总表
    report: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        fileName: {
          type: String,
        },
        url: {
          type: String,
        },
        source: {
          type: String,
          default: 'oapi', // super || enterprise || operate
        }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
      },
    ], // 报告书
    invoice: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        fileName: {
          type: String,
        },
        url: {
          type: String,
        },
        source: {
          type: String,
          default: 'service', // super || enterprise || operate
        }, // 文件存储来源，不同端字段默认值不一样，如果查出来是null，那么就是企业端存着enterprise，这样为了兼容老数据
      },
    ], // 发票
    reportTime: {
      // 默认当前日期去掉了。有需要的在自己的代码中加吧
      type: Date,
    }, // 检测时间
    checkPointImg: String, // 检测点分布示意图
    isVIP: {
      // 会员类型：战略客户、vip客户、普通客户
      type: String, // svip、vip、ordinary
      default: 'ordinary',
    }, // 是否为会员
    isUrgent: {
      type: Boolean,
      default: false,
    }, // 是否加急
    // 审批=========================
    // 合同审批状态以及审批实例id
    approvedStatus: {
      type: String,
      default: '0',
    }, // , 0 未发起审批 NEW：新创建 RUNNING：审批中 TERMINATED：被终止 COMPLETED：完成 CANCELED：取消 REFUSE:已拒绝
    process_instance_id: String, // 审批编号
    // 报告审批状态以及审批实例id
    reportProcessInstanceId: String, // 报告单审批实例id
    reportApprovedStatus: String, // 报告单审批状态，同上
    reportFinalProcessInstanceId: String, // 报告审批实例id
    reportFinalApprovedStatus: String, // 报告审批状态
    sampleList: { default: false, type: Boolean }, // 交样单是否完成

    reportStatus: {
      // 报告生成状态 0未生成  1已生成  2已上传
      type: String,
      default: '0',
    },
    reportGenerateFileName: {
      // 生成报告文件名
      type: String,
      default: '',
    },
    reportUploadFile: {
      // 上传报告文件
      name: { type: String, default: '' }, // 原文件名
      url: { type: String, default: '' }, // 文件下载地址
    },
    serviceMatters: [[ String ]], // 项目组成员的服务事项
    preparer: String, // 项目对接上报人
    preparerPhone: String, // 项目对接上报人联系电话
    serviceArea: [
      {
        type: String,
        match: /^[0-9]*$/,
      },
    ], // 关于此次项目的企业涉及的技术服务领域，存得是编码，报送信息中要用 [采矿业:'1','化工、石化及医药':'2','冶金、建材':'3','机械制造、电力、纺织、建筑和交通运输等行业领域':'4','核设施':'5','核技术应用':'6']
    investigationTime: Date, // 现场调查时间
    detectionTime: Date, // 现场采样/检测时间
    issuer: {
      // 报告签发人
      name: String,
      employeeId: String, // serviceEmployee id
    },
    issueDate: Date, // 报告签发日期
  });
  return mongoose.model('jobHealth', jobHealthSchema);
};
