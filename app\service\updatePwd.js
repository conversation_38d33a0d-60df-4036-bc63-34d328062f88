const Service = require('egg').Service;

class updatePwdService extends Service {
  async update(selectModel, _id, password, newPsw) {
    const { ctx, app } = this;
    // 加密
    const hashPassword = ctx.helper.hashSha256(password, app.config.salt_sha2_key);
    // 根据传过来的参数查询model里面的数据
    const result = await ctx.model[selectModel].findOne({ _id });
    if (!result) {
      return { errCod: 500, message: '用户不存在' };
    }
    // 判断密码是否正确
    if (result.password !== hashPassword) {
      return { errCod: 500, message: '密码错误' };
    }
    // 更新密码
    const newHashPassword = ctx.helper.hashSha256(newPsw, app.config.salt_sha2_key);
    const updateResult = await ctx.model[selectModel].updateOne({ _id }, { password: newHashPassword });
    if (!updateResult) {
      return { errCod: 500, message: '密码更新失败' };
    }
    return { errCod: 0, message: '密码更新成功' };

  }

}
module.exports = updatePwdService;
