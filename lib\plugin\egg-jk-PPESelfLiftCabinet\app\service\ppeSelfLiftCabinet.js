
const Service = require('egg').Service;
class ppeSelfLiftCabinet extends Service {
  async message(companyId, quantity_k, quantity_e) {
    const { ctx } = this;
    const ppeSelfLiftCabinet = await ctx.model.PpeSelfLiftCabinet.aggregate([
      { $match: { EnterpriseId: companyId } },
      { $unwind: '$specifications' },
      { $match: { 'specifications.surplus': { $ne: 0 } } },
      { $group: { _id: '$specifications.goods', freightLaneSN: { $first: '$specifications.freightLaneSN' }, surplus: { $first: '$specifications.surplus' } } }]);
    // console.log(ppeSelfLiftCabinet, '瞧一瞧，看一看了啊~');
    let m = '1';
    let typeNum = 0;// 出货数量
    for (let i = 0; i < ppeSelfLiftCabinet.length; i++) {
      const ele = ppeSelfLiftCabinet[i];
      typeNum = ele._id === '口罩' ? quantity_k : (ele._id === '耳塞' ? quantity_e : 0);
      if (typeNum) {
        if (ele.surplus >= typeNum) {
          m += `,${ele.freightLaneSN}&${typeNum}`;
          await ctx.model.PpeSelfLiftCabinet.updateOne({ EnterpriseId: companyId }, { $set: { 'specifications.$[i].surplus': ele.surplus - typeNum } }, {
            arrayFilters: [{
              'i.freightLaneSN': ele.freightLaneSN,
            }],
          });
        } else {
          for (let j = typeNum, forSN = 0; j > 0;) { // forSN 第几次循环；第几个货道
            let totality = 15;
            if (ele.freightLaneSN + forSN > 12 && ele.freightLaneSN + forSN < 17) {
              totality = 8;
            } else if (ele.freightLaneSN + forSN >= 17) {
              totality = 5;
            }
            const num = forSN ? (j <= totality ? j : totality) : ele.surplus;
            if (ele.freightLaneSN + forSN > 12 && ele._id === '口罩') {
              i++;
              continue;
            }
            m += `,${ele.freightLaneSN + forSN}&${num}`;
            j = j - num;
            await ctx.model.PpeSelfLiftCabinet.updateOne({ EnterpriseId: companyId }, { $set: { 'specifications.$[i].surplus': forSN ? (totality - num) : 0 } }, {
              arrayFilters: [{
                'i.freightLaneSN': ele.freightLaneSN + forSN,
              }],
            });
            forSN++;
          }
        }
      }
    }
    // console.log('手机端调用（发布消息前）service的逻辑内容');
    return m;
  }
  randomPass(number) {
    const arr = [ '0', '1', '2', '3', '4', '5', '6', '7', '8', '9' ];
    let nonceStr = '';
    let res = '';
    for (let i = 0; i < number; i++) {
      const n = Math.floor(Math.random() * 10);
      arr[i] = arr[n];
      nonceStr += arr[n];
      res = parseInt(nonceStr);
    }
    return res;
  }

}

module.exports = ppeSelfLiftCabinet;
