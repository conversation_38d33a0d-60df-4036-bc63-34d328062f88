const Service = require('egg').Service;
// const { tools } = require('@utils');
const moment = require('moment');
const path = require('path');
const shortid = require('shortid');
const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _safeDelete,
} = require(path.join(process.cwd(), 'app/service/general'));
const _ = require('lodash');

class AdminuserService extends Service {

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {
    const listdata = _list(this.ctx.model.AdminUser, payload, {
      query,
      searchKeys,
      populate,
      files,
    });
    return listdata;
  }

  async count(params = {}) {
    return _count(this.ctx.model.AdminUser, params);
  }

  async create(payload, options) {
    return _create(this.ctx.model.AdminUser, payload, options);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.AdminUser, values, key);
  }

  async safeDelete(res, values) {
    return _safeDelete(res, this.ctx.model.AdminUser, values);
  }

  async update(res, _id, payload, query, options) {
    return _update(res, this.ctx.model.AdminUser, _id, payload, query, options);
  }

  async item(res, {
    query = {},
    populate = [],
    files = null,
    options,
  } = {}) {
    return _item(res, this.ctx.model.AdminUser, {
      files: files ? files : {
        password: 0,
        email: 0,
      },
      query,
      populate,
    }, options);
  }
  // 获取员工当前岗位
  // 获取员工岗位
  async findEmployeeStation(params) {
    try {
      const EnterpriseID = params.companyId ? params.companyId[params.companyId.length - 1] : params.EnterpriseID;
      const res = await this.ctx.model.MillConstruction.aggregate([
        {
          $match: {
            EnterpriseID,
          },
        },
        {
          $unwind: '$children',
        },
        {
          $unwind: '$children.children',
        },
        {
          $match: {
            $or: [{ 'children.children.employees': params.employeeId }, { 'children.children.children.employees': params.employeeId }],
          },
        },
      ]);
      const stations = [];
      res.forEach(item => {
        if (item.category === 'workspaces') {
          item.children.workspace = item.name;
          item.children.workspaceId = item._id;
          item.children.harmFactorsAndSort = item.children.harmFactors;
          item.children.harmFactors = item.children.harmFactors.map(item2 => item2[1]).join('、');
          delete item.children.children;
          stations.push(item.children);
        } else if (item.category === 'mill') {
          item.children.children.workshop = item.name;
          item.children.children.workshopId = item._id;
          item.children.children.workspace = item.children.name;
          item.children.children.workspaceId = item.children._id;
          item.children.children.harmFactorsAndSort = item.children.children.harmFactors;
          item.children.children.harmFactors = item.children.children.harmFactors.map(item2 => item2[1]).join('、');
          delete item.children.children.children;
          stations.push(item.children.children);
        }
      });
      return stations;
    } catch (error) {
      console.log(error);
    }
  }

  // async getCheckResult(params, companyId) { // 没用上
  //   return await this.getStationsCheckResult(params, companyId);
  // }

  // 岗位检测结果
  // 需要参数：岗位
  async getCheckResult(stations, companyId) {
    const { ctx } = this;
    const EnterpriseID = companyId[companyId.length - 1];
    let checkResultItem = [];
    let model = '';
    let matchOrOne = {};
    // let matchOrTwo = {};
    // let matchAnd = {};
    let project = {};
    const checkResult = {};
    let modelName = '';
    let station = {};
    let checkProjectFields = {};

    for (let i = 0; i < stations.length; i++) {
      station = stations[i];
      station.name = station.name.replace('岗位', '').trim();
      station.workspace = station.workspace.replace('车间', '').trim();
      for (let j = 0; j < station.harmFactorsAndSort.length; j++) {
        checkProjectFields = {};
        if (station.harmFactorsAndSort[j][0] === '化学') {
          model = 'chemistryFactors';
          modelName = '化学';
          checkProjectFields[model + '.formData.checkProject'] = station.harmFactorsAndSort[j][1].trim();
        } else if (station.harmFactorsAndSort[j][0] === '粉尘') {
          model = 'dustFactors';
          modelName = '粉尘';
          checkProjectFields[model + '.formData.checkProject'] = { $regex: station.harmFactorsAndSort[j][1].trim() };
        } else if (station.harmFactorsAndSort[j][0] === '生物') {
          model = 'biologicalFactors';
          modelName = '生物';
          checkProjectFields[model + '.formData.checkProject'] = station.harmFactorsAndSort[j][1].trim();
        } else {
          if (station.harmFactorsAndSort[j][1].indexOf('噪声') !== -1) {
            model = 'noiseFactors';
            modelName = '噪声';
          } else if (station.harmFactorsAndSort[j][1].indexOf('高温') !== -1) {
            model = 'heatFactors';
            modelName = '高温';
          } else if (station.harmFactorsAndSort[j][1].indexOf('超高频辐射') !== -1) {
            model = 'ultraHighRadiationFactors';
            modelName = '超高频辐射';
          } else if (station.harmFactorsAndSort[j][1].indexOf('高频电磁场') !== -1) {
            model = 'highFrequencyEleFactors';
            modelName = '高频电磁场';
          } else if (station.harmFactorsAndSort[j][1].indexOf('工频电磁场') !== -1) {
            model = 'powerFrequencyElectric';
            modelName = '工频电场';
          } else if (station.harmFactorsAndSort[j][1].indexOf('激光') !== -1) {
            model = 'laserFactors';
            modelName = '激光辐射';
          } else if (station.harmFactorsAndSort[j][1].indexOf('微波') !== -1) {
            model = 'microwaveFactors';
            modelName = '微博辐射';
          } else if (station.harmFactorsAndSort[j][1].indexOf('紫外线') !== -1) {
            model = 'ultravioletFactors';
            modelName = '紫外线';
          } else if (station.harmFactorsAndSort[j][1].indexOf('振动') !== -1) {
            model = 'handBorneVibrationFactors';
            modelName = '手传振动';
          } else if (station.harmFactorsAndSort[j][1].indexOf('游离二氧化硅') !== -1) {
            model = 'SiO2Factors';
            modelName = '游离二氧化硅';
          }
        }
        if (model) {
          matchOrOne = {};
          // matchOrTwo = {};
          // matchAnd = {};
          project = {};
          matchOrOne[model + '.formData.station'] = { $regex: station.name };
          matchOrOne[model + '.formData.workspace'] = { $regex: station.workspace };
          // matchOrTwo[model + '.formData.checkAddress'] = { $regex: station.workspace.indexOf('车间') ? station.workspace.trim() : station.workspace.trim() + '车间' };
          // matchAnd[model + '.formData.checkAddress'] = { $regex: '车间' + station.name.trim() };
          project[model] = 1;
          project.jobHealthId = 1;
          project['jobHealth.name'] = 1;
          project['jobHealth.reportTime'] = 1;
          checkResultItem = await ctx.model.CheckAssessment.aggregate([
            { $match: { EnterpriseID, process: { $exists: false } } },
            { $unwind: '$' + model + '.formData' },
            {
              $match: {
                ...checkProjectFields,
                $or: [
                  matchOrOne, {
                    $and: [
                      { [model + '.formData.checkAddress']: { $regex: station.name } },
                      { [model + '.formData.checkAddress']: { $regex: station.workspace } },
                    ],
                  },
                ],
              },
            },

            {
              $lookup: {
                from: 'jobhealths',
                localField: 'jobHealthId',
                foreignField: '_id',
                as: 'jobHealth',
              },
            },
            {
              $project: project,
            },
          ]);
          for (let k = 0; k < checkResultItem.length; k++) {
            if (checkResultItem[k] && checkResultItem[k].jobHealth[0] && checkResultItem[k][model].formData) {
              checkResultItem[k][model].formData.checkTime = checkResultItem[k].jobHealth[0].reportTime ? moment(new Date(checkResultItem[k].jobHealth[0].reportTime)).format('YYYY-MM-DD') : '';
              checkResultItem[k][model].formData.checkName = checkResultItem[k].jobHealth[0].name;
              checkResultItem[k][model].formData.checkProject = checkResultItem[k][model].formData.checkProject || modelName;
              checkResultItem[k][model].formData.checkResult = checkResultItem[k][model].formData.checkResult || checkResultItem[k][model].formData.conclusion;
              checkResultItem[k][model].formData.protectiveFacilities = station.protectiveFacilities;
              if (Object.keys(checkResult).indexOf('' + model) === -1) {
                checkResult[model] = { data: [] };
              }
              checkResult[model].model = model;
              checkResult[model].modelName = modelName;
              checkResult[model].data.push(checkResultItem[k][model].formData);
            }
          }
        }
      }
    }
    return Object.values(checkResult);
  }

  // 获取防护用具 - 适配新的数据结构
  async getDefendproducts(params) {
    try {
      const { ctx } = this;
      const EnterpriseID = params.companyId ? params.companyId[0] : params.EnterpriseID;
      const employeeId = params.employeeId;

      // 获取员工信息和分类
      const employee = await ctx.model.Employee.findOne({ _id: employeeId }, { category: 1, departs: 1 }).lean();
      if (!employee) {
        throw new Error('员工不存在');
      }

      // 获取员工所属仓库
      const employeeWarehouse = await this.getEmployeeWarehouse(employeeId, EnterpriseID);

      // 获取配发标准（使用新的扁平化查询）
      const protectionPlans = await this.findProtectionPlanNew(employeeId, EnterpriseID, employee.category);

      // 获取防护用品分类数据（适配新的产品模型）
      const typeList = await this.getProtectiveProductCategories(EnterpriseID, employeeWarehouse.warehouseId);

      // 获取领用记录
      const records = await ctx.model.ReceiveRecord.find({
        EnterpriseID,
        employee: employeeId,
      }).sort({ createdAt: -1 }).lean();

      // 获取申请记录
      const applications = await ctx.model.ApplicationProduct.find({
        EnterpriseID,
        employee: employeeId,
      }).sort({ createdAt: -1 }).lean();

      // 为每个配发标准的每个产品匹配或生成领用记录
      for (let i = 0; i < protectionPlans.length; i++) {
        const plan = protectionPlans[i];
        plan.employee = employeeId;

        for (let j = 0; j < plan.products.length; j++) {
          const product = plan.products[j];
          const targetRecord = records.find(e => {
            if (
              e.planId === plan._id &&
              e.products[0].product === product.product &&
              (!e.sign)
            ) {
              return true;
            }
            return false;
          });

          if (targetRecord) {
            product.todo = targetRecord;
          } else {
            // 生成新的领用记录
            const newReceiveRecord = await this.updateNewReceiveRecord(plan, product);
            product.todo = newReceiveRecord;
          }
        }
      }

      return {
        records,
        typeList,
        applications,
        allProtectionPlan: protectionPlans,
        employeeWarehouse,
      };
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  // 获取员工所属仓库
  async getEmployeeWarehouse(employeeId, EnterpriseID) {
    const { ctx } = this;

    console.log(`[仓库匹配] 开始为员工 ${employeeId} 匹配仓库`);

    // 获取员工岗位信息
    const employeePositions = await this.getEmployeePositions(employeeId, EnterpriseID);
    console.log('[仓库匹配] 员工岗位信息:', employeePositions);

    if (!employeePositions || employeePositions.length === 0) {
      console.log('[仓库匹配] 员工无岗位信息，使用公共仓库');
      // 如果没有岗位信息，返回公共仓库
      const publicWarehouse = await ctx.model.Warehouse.findOne({
        EnterpriseID,
        isPublic: true,
      });

      if (!publicWarehouse) {
        throw new Error('未找到可用仓库');
      }

      console.log('[仓库匹配] 返回公共仓库:', publicWarehouse.name);
      return {
        warehouseId: publicWarehouse._id,
        warehouseName: publicWarehouse.name,
        isPublic: true,
      };
    }

    // 根据岗位匹配仓库（支持跨企业匹配）
    for (const position of employeePositions) {
      console.log(`[仓库匹配] 尝试匹配岗位: ${position.name} (${position.fullId})`);

      // 首先尝试精确匹配
      let warehouse = await ctx.model.Warehouse.findOne({
        'managementScope.fullId': position.fullId,
      });
      console.log(`[仓库匹配] 精确匹配结果: ${warehouse ? '成功' : '失败'}`);

      // 如果没有精确匹配，尝试层级匹配（检查是否有父级权限包含此岗位）
      if (!warehouse) {
        console.log('[仓库匹配] 精确匹配失败，尝试层级匹配');

        // 检查是否有父级fullId能匹配当前岗位
        // 例如：岗位fullId为 "A_child_workspaces_B_child_stations_C"
        // 应该能匹配到管理范围为 "A_child_workspaces_B" 或 "A" 的仓库

        // 构建可能的父级fullId列表
        const possibleParents = [];
        const fullId = position.fullId;

        // 如果是岗位级别，尝试车间级别
        if (fullId.includes('_child_stations_')) {
          const workspaceFullId = fullId.replace(/_child_stations_[^_]+$/, '');
          possibleParents.push(workspaceFullId);

          // 如果车间级别还包含厂房，尝试厂房级别
          if (workspaceFullId.includes('_child_workspaces_')) {
            const millFullId = workspaceFullId.replace(/_child_workspaces_[^_]+$/, '');
            possibleParents.push(millFullId);
          }
        } else if (fullId.includes('_child_workspaces_')) {
          // 如果是车间级别，尝试厂房级别
          const millFullId = fullId.replace(/_child_workspaces_[^_]+$/, '');
          possibleParents.push(millFullId);
        }

        console.log(`[仓库匹配] 可能的父级fullId: ${JSON.stringify(possibleParents)}`);

        for (const parentFullId of possibleParents) {
          console.log(`[仓库匹配] 尝试父级匹配: ${parentFullId}`);

          warehouse = await ctx.model.Warehouse.findOne({
            'managementScope.fullId': parentFullId,
          });

          if (warehouse) {
            console.log(`[仓库匹配] 通过父级权限匹配成功: ${parentFullId}`);
            break;
          } else {
            console.log(`[仓库匹配] 父级匹配失败: ${parentFullId}`);
          }
        }
      }

      if (warehouse) {
        console.log(`[仓库匹配] 找到匹配仓库: ${warehouse.name} (${warehouse._id}) - 企业: ${warehouse.EnterpriseID}`);
        return {
          warehouseId: warehouse._id,
          warehouseName: warehouse.name,
          isPublic: warehouse.isPublic,
          matchedPosition: position,
          warehouseEnterpriseID: warehouse.EnterpriseID,
        };
      }
    }

    console.log('[仓库匹配] 未找到专用仓库，使用公共仓库');
    // 如果没有匹配的仓库，返回公共仓库
    const publicWarehouse = await ctx.model.Warehouse.findOne({
      EnterpriseID,
      isPublic: true,
    });

    console.log('[仓库匹配] 返回公共仓库:', publicWarehouse?.name);
    return {
      warehouseId: publicWarehouse._id,
      warehouseName: publicWarehouse.name,
      isPublic: true,
    };
  }

  // 获取员工岗位信息（使用扁平化结构）
  async getEmployeePositions(employeeId, EnterpriseID) {
    const { ctx } = this;

    // 使用新的扁平化结构查询员工岗位
    const positions = await ctx.model.FlatMillConstructionMaterialized.find({
      EnterpriseID,
      level: { $in: [ 'workspaces', 'stations' ] },
      employees: employeeId,
    }).select('fullId name level millName workspaceName stationName');

    return positions;
  }

  /**
   * 计算产品到期时间
   * @param {Date} productionDate - 生产日期
   * @param {Number} expiryPeriod - 有效期长度
   * @param {String} expiryUnit - 有效期单位
   * @return {Date|null} 到期日期
   */
  calculateProductExpiryDate(productionDate, expiryPeriod, expiryUnit) {
    if (!productionDate || !expiryPeriod || !expiryUnit) {
      return null;
    }

    const date = new Date(productionDate);

    switch (expiryUnit) {
      case 'days':
        date.setDate(date.getDate() + expiryPeriod);
        break;
      case 'months':
        date.setMonth(date.getMonth() + expiryPeriod);
        break;
      case 'years':
        date.setFullYear(date.getFullYear() + expiryPeriod);
        break;
      default:
        return null;
    }

    return date;
  }

  // 领取防护用具（适配新的产品模型）
  async receiveProducts(params) {
    try {
      const { ctx } = this;
      const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : params.EnterpriseID;
      const receiveDate = params.productionDate && params.type === 'receive' ? new Date(params.productionDate) : new Date();
      let res;

      // 查询库存是否充足（使用新的产品模型）
      if (params.products && params.products.length > 0) {
        for (let i = 0; i < params.products.length; i++) {
          const product = params.products[i];

          // 检查并获取正确的产品ID
          let productId = product.productId;
          let targetProduct = null;

          if (productId) {
            // 先尝试直接查询
            targetProduct = await ctx.model.ProtectiveProduct.findOne({
              _id: productId,
              EnterpriseID,
              isActive: true,
            }).select('surplus product productSpec');
          }

          if (!targetProduct) {
            // 如果直接查询失败，根据产品名称查找
            console.log('直接查询失败，尝试根据产品名称查找:', product.product);

            // 尝试多种匹配方式
            const searchQueries = [
              // 精确匹配
              { product: product.product, modelNumber: product.modelNumber || product.product },
              // 只匹配产品名称
              { product: product.product },
              // 只匹配型号
              { modelNumber: product.modelNumber || product.product },
              // 模糊匹配产品名称（包含关系）
              { product: { $regex: product.product, $options: 'i' } },
              // 模糊匹配型号
              { modelNumber: { $regex: product.modelNumber || product.product, $options: 'i' } },
            ];

            for (const query of searchQueries) {
              targetProduct = await ctx.model.ProtectiveProduct.findOne({
                EnterpriseID,
                ...query,
                isActive: true,
              }).select('surplus product productSpec _id');

              if (targetProduct) {
                productId = targetProduct._id;
                console.log(`✅ 库存检查阶段找到产品: ${productId} for ${product.product} (匹配方式: ${JSON.stringify(query)})`);
                break;
              }
            }

            if (!targetProduct) {
              console.error('❌ 库存检查阶段未找到产品:', {
                product: product.product,
                modelNumber: product.modelNumber,
                传入的productId: product.productId,
                EnterpriseID,
              });
              return { message: '产品不存在或已禁用', code: -1 };
            }
          }

          const surplus = targetProduct.surplus || 0;
          const receiveNum = parseFloat(product.receiveNum || product.number) || 0;

          if (isNaN(surplus) || surplus < receiveNum) {
            console.error('库存不足:', { productId: product.productId, surplus, receiveNum });
            return { message: '库存不足', code: -1 };
          }
        }
      }

      if (params.type === 'receive' || params.type === 'reject') {
        // 查找是否有同款用品，是的话报废以前的
        // 🔑 修复：对于主动申请，按产品名称查找；对于配发标准，按planId查找
        const matchCondition = {
          EnterpriseID,
          employee: params.employee,
          receiveDate: { $exists: true }, // 表示已经被领取
          scrap: false,
          isRejected: false,
          _id: { $ne: params._id }, // 排除当前记录
        };

        // 🔑 修复：通过查询当前领用记录的recordSource来判断类型
        const currentRecord = await ctx.model.ReceiveRecord.findOne({
          _id: params._id,
          EnterpriseID,
        }).select('recordSource planId');

        console.log('当前领用记录信息:', {
          recordId: params._id,
          recordSource: currentRecord?.recordSource,
          planId: currentRecord?.planId,
        });

        // 🔑 修复：按分类ID匹配，只报废同一分类的旧记录
        // 获取当前记录的分类信息
        const currentRecordDetail = await ctx.model.ReceiveRecord.findOne({
          _id: params._id,
          EnterpriseID,
        }).select('categoryId categoryPath categoryName');

        if (currentRecordDetail && currentRecordDetail.categoryId) {
          // 按分类ID匹配同分类的旧记录
          matchCondition.categoryId = currentRecordDetail.categoryId;
          console.log('按分类ID报废查询:', {
            categoryId: currentRecordDetail.categoryId,
            categoryName: currentRecordDetail.categoryName,
          });
        } else {
          // 兼容旧数据：如果没有分类ID，则按原逻辑处理
          if (currentRecord && currentRecord.recordSource === 1) {
            // 主动申请：按产品名称查找同款产品
            matchCondition['products.product'] = params.product;
            console.log('主动申请报废查询（兼容模式），按产品名称:', params.product);
          } else {
            // 配发标准：按planId查找
            matchCondition.planId = params.planId;
            console.log('配发标准报废查询（兼容模式），按planId:', params.planId);
          }
        }

        const scraping = await ctx.model.ReceiveRecord.aggregate([
          {
            $match: matchCondition,
          },
        ]);

        console.log('查找到需要报废的记录数量:', scraping.length);
        const date = new Date();
        const currentYear = date.getFullYear(); // 获取当前年份
        const currentMonth = date.getMonth() + 1; // 获取当前月份

        // 若是有计划则创建新的
        const planRes = await ctx.model.ProtectionPlan.aggregate([
          {
            $match: { _id: params.planId },
          },
          {
            $unwind: '$products',
          },
          {
            $match: { 'products.product': params.product },
          },
        ]);
        console.log(88888881111, params, planRes);
        if (planRes.length > 0) {
          const plan = planRes[0];
          const now = new Date(); // 获取当前时间
          const productPlan = plan.products;

          plan.EnterpriseID = EnterpriseID;
          plan.planId = params.planId;
          plan.recordSource = 0;
          plan.receiveStartDate = moment(now).add(productPlan.time, productPlan.timeUnit);
          if (ctx.app.config.branch === 'wh' && params.product === '安全帽') {
            plan.receiveStartDate = moment(receiveDate).add(30, 'months').toDate();
          }
          plan.warningDate = moment(plan.receiveStartDate).add(5, 'd');
          plan.employee = params.employee;
          // 添加工作场所信息获取逻辑
          if (plan.nodeFullId) {
            try {
              const positionInfo = await ctx.model.FlatMillConstructionMaterialized.findOne({
                fullId: plan.nodeFullId,
                EnterpriseID,
              }).select('millName workspaceName stationName parentId _id category level');

              if (positionInfo) {
                // 根据 level 字段判断当前记录的级别
                if (positionInfo.level === 'stations') {
                  // 岗位级别：设置岗位信息，然后向上查找车间和厂房
                  plan.workstationName = positionInfo.stationName || positionInfo.name || '';
                  plan.workstation = positionInfo._id;

                  // 查找上级车间信息
                  if (positionInfo.parentId) {
                    const workspaceInfo = await ctx.model.FlatMillConstructionMaterialized.findOne({
                      _id: positionInfo.parentId,
                      EnterpriseID,
                    }).select('workspaceName name parentId _id level');

                    if (workspaceInfo) {
                      plan.workspacesName = workspaceInfo.workspaceName || workspaceInfo.name || '';
                      plan.workspaces = workspaceInfo._id;

                      // 查找上级厂房信息
                      if (workspaceInfo.parentId) {
                        const workshopInfo = await ctx.model.FlatMillConstructionMaterialized.findOne({
                          _id: workspaceInfo.parentId,
                          EnterpriseID,
                        }).select('millName name _id level');

                        if (workshopInfo) {
                          plan.workshopName = workshopInfo.millName || workshopInfo.name || '';
                          plan.workshop = workshopInfo._id;
                        }
                      }
                    }
                  }
                } else if (positionInfo.level === 'workspaces') {
                  // 车间级别：直接从当前记录获取所有可用的工作场所信息
                  plan.workspacesName = positionInfo.workspaceName || positionInfo.name || '';
                  plan.workspaces = positionInfo._id;

                  // 如果当前记录有厂房信息，直接使用
                  if (positionInfo.millName) {
                    plan.workshopName = positionInfo.millName;
                  }

                  // 如果当前记录有岗位信息，直接使用
                  if (positionInfo.stationName) {
                    plan.workstationName = positionInfo.stationName;
                  }

                  // 查找上级厂房信息（如果当前记录没有厂房信息）
                  if (!plan.workshopName && positionInfo.parentId) {
                    const workshopInfo = await ctx.model.FlatMillConstructionMaterialized.findOne({
                      _id: positionInfo.parentId,
                      EnterpriseID,
                    }).select('millName name _id level');

                    if (workshopInfo) {
                      plan.workshopName = workshopInfo.millName || workshopInfo.name || '';
                      plan.workshop = workshopInfo._id;
                    }
                  }
                } else if (positionInfo.level === 'mill') {
                  // 厂房级别：直接设置厂房信息
                  plan.workshopName = positionInfo.millName || positionInfo.name || '';
                  plan.workshop = positionInfo._id;
                }

                console.log('✅ [调试] 旧方式生成记录-获取工作场所信息成功:', {
                  nodeFullId: plan.nodeFullId,
                  level: positionInfo.level,
                  workshopName: plan.workshopName,
                  workspacesName: plan.workspacesName,
                  workstationName: plan.workstationName,
                  workshop: plan.workshop,
                  workspaces: plan.workspaces,
                  workstation: plan.workstation,
                });
              } else {
                console.warn('⚠️ [调试] 未找到工作场所信息:', {
                  nodeFullId: plan.nodeFullId,
                  EnterpriseID,
                });
              }
            } catch (error) {
              console.error('❌ [调试] 旧方式生成记录-获取工作场所信息失败:', error.message);
            }
          } else {
            console.warn('⚠️ [调试] plan.nodeFullId 为空，无法获取工作场所信息');
          }

          // 🔑 添加分类信息到新生成的记录中
          const planRecordDetail = await ctx.model.ReceiveRecord.findOne({
            _id: params._id,
            EnterpriseID,
          }).select('categoryId categoryPath categoryName');

          if (planRecordDetail) {
            plan.categoryId = planRecordDetail.categoryId;
            plan.categoryPath = planRecordDetail.categoryPath;
            plan.categoryName = planRecordDetail.categoryName;
          }

          delete plan._id;
          console.log('🔍 [调试] 即将创建的 ReceiveRecord 数据:', {
            planId: plan.planId,
            employee: plan.employee,
            grantType: plan.grantType,
            workshopName: plan.workshopName,
            workspacesName: plan.workspacesName,
            workstationName: plan.workstationName,
            workshop: plan.workshop,
            workspaces: plan.workspaces,
            workstation: plan.workstation,
            nodeFullId: plan.nodeFullId,
          });
          new ctx.model.ReceiveRecord(plan).save();
        }
        // 报废以前领取的
        if (scraping.length > 0 && params.type === 'receive') {
          if (scraping.length > 1) console.log('匹配到多个需要报废的了');
          const scrapRecord = scraping[0];

          // 🔑 获取仓库信息：如果领用记录中没有，从产品信息中获取
          let warehouseId = scrapRecord.warehouseId || '';
          let warehouseName = scrapRecord.warehouseName || '';

          if (!warehouseId && params.products && params.products[0] && params.products[0].productId) {
            try {
              const productInfo = await ctx.model.ProtectiveProduct.findOne({
                _id: params.products[0].productId,
                EnterpriseID,
              }).select('warehouseId');

              if (productInfo && productInfo.warehouseId) {
                warehouseId = productInfo.warehouseId;
                // 可以根据warehouseId查询仓库名称，这里暂时使用ID
                warehouseName = productInfo.warehouseId;
                console.log('从产品信息获取仓库ID:', warehouseId);
              }
            } catch (error) {
              console.warn('获取产品仓库信息失败:', error.message);
            }
          }

          const obj = {
            EnterpriseID, // 企业id

            // 工作场所信息（使用名称字段）
            workspaces: scrapRecord.workspacesName || '', // 车间名称
            workstation: scrapRecord.workstationName || '', // 岗位名称
            workshop: scrapRecord.workshopName || '', // 厂房名称

            // 新增分类和仓库信息
            categoryId: scrapRecord.categoryId || '', // 分类ID
            categoryPath: scrapRecord.categoryPath || '', // 分类路径
            categoryName: scrapRecord.categoryName || '', // 分类名称
            warehouseId, // 仓库ID
            warehouseName, // 仓库名称

            products: scrapRecord.products.map(product => ({
              ...product,
              // 确保产品包含分类信息
              categoryId: product.categoryId || scrapRecord.categoryId || '',
              categoryPath: product.categoryPath || scrapRecord.categoryPath || '',
              categoryName: product.categoryName || scrapRecord.categoryName || '',
              productSpec: product.productSpec || '',
              materialCode: product.materialCode || '',
            })),
            employee: params.employee,
            applicationTime: date, // 申请报废时间
            yearNumber: currentYear, // 年份
            mouthNumber: currentMonth, // 月份
            scrapReason: '自动过期报废',
          };

          console.log('创建报废记录:', {
            recordId: scrapRecord._id,
            workshop: obj.workshop,
            workspaces: obj.workspaces,
            workstation: obj.workstation,
            categoryName: obj.categoryName,
          });

          // 修改记录
          await ctx.model.ReceiveRecord.updateOne({ EnterpriseID, _id: scrapRecord._id }, { scrap: true });
          await new ctx.model.ScrapProduct(obj).save();
        }
        // 扣减库存并更新领用记录（使用新的产品模型）
        if (params.products && params.products.length > 0 && params.type === 'receive') {
          for (let i = 0; i < params.products.length; i++) {
            const product = params.products[i];

            // 检查产品ID，如果没有则尝试查找
            let productId = product.productId;
            if (!productId) {
              console.log('产品缺少productId，尝试根据产品名称查找:', product);

              // 根据产品名称和型号查找对应的产品ID（使用多种匹配方式）
              const searchQueries = [
                // 精确匹配
                { product: product.product, modelNumber: product.modelNumber || product.product },
                // 只匹配产品名称
                { product: product.product },
                // 只匹配型号
                { modelNumber: product.modelNumber || product.product },
                // 模糊匹配产品名称（包含关系）
                { product: { $regex: product.product, $options: 'i' } },
                // 模糊匹配型号
                { modelNumber: { $regex: product.modelNumber || product.product, $options: 'i' } },
              ];

              let foundProduct = null;
              for (const query of searchQueries) {
                foundProduct = await ctx.model.ProtectiveProduct.findOne({
                  EnterpriseID,
                  ...query,
                  isActive: true,
                }).select('_id');

                if (foundProduct) {
                  productId = foundProduct._id;
                  console.log(`✅ 库存扣减阶段找到产品: ${productId} for ${product.product} (匹配方式: ${JSON.stringify(query)})`);
                  break;
                }
              }

              if (!foundProduct) {
                console.error('❌ 库存扣减阶段未找到产品:', {
                  product: product.product,
                  modelNumber: product.modelNumber,
                  EnterpriseID,
                  传入的productId: product.productId,
                });
                continue;
              }
            }

            // 查询产品信息
            const targetProduct = await ctx.model.ProtectiveProduct.findOne({
              _id: productId,
              EnterpriseID,
              isActive: true,
            }).select('surplus productSpec product categoryId categoryPath categoryName');

            if (!targetProduct) {
              console.error('扣减库存时未找到产品:', productId);
              continue;
            }

            // 计算新库存
            const receiveNum = parseFloat(product.receiveNum || product.number) || 0;
            const newSurplus = targetProduct.surplus - receiveNum;

            // 更新领用记录中的产品信息
            await ctx.model.ReceiveRecord.updateOne(
              { _id: params._id },
              {
                $set: {
                  'products.$[i].productId': productId,
                  'products.$[i].productSpec': targetProduct.productSpec,
                  'products.$[i].product': targetProduct.product,
                  'products.$[i].categoryId': targetProduct.categoryId,
                  'products.$[i].categoryPath': targetProduct.categoryPath,
                  'products.$[i].categoryName': targetProduct.categoryName,
                  'products.$[i].number': receiveNum,
                },
              },
              { arrayFilters: [{ 'i._id': product.productsOrder }] }
            );

            // 扣减库存
            const updateResult = await ctx.model.ProtectiveProduct.updateOne(
              { _id: productId },
              { $set: { surplus: newSurplus } }
            );

            if (updateResult.matchedCount === 0) {
              console.error(`库存扣减失败: 未找到产品 ${productId}`);
              return { message: `产品${productId}不存在，库存扣减失败`, code: -1 };
            }

            if (updateResult.modifiedCount === 0) {
              console.error(`库存扣减失败: 产品${productId}更新失败`);
              return { message: `产品${productId}库存更新失败`, code: -1 };
            }

            console.log(`库存扣减成功: 产品${productId}, 扣减${receiveNum}, 剩余${newSurplus}`);
          }
        }
        if (params.type === 'receive') {
          // 更新领取状态（签字确认）
          console.log('更新领取记录，签字确认:', params._id);

          // 获取领用记录以检查产品信息
          const record = await ctx.model.ReceiveRecord.findOne({
            _id: params._id,
            EnterpriseID,
          });

          let needProductionDate = false;
          let productExpiryInfo = null;
          let expiryDate = null;

          if (record && record.products && record.products.length > 0) {
            const product = record.products[0];

            // 通过产品名称查询产品信息
            const productInfo = await ctx.model.ProtectiveProduct.findOne({
              EnterpriseID,
              product: product.product,
              modelNumber: product.modelNumber,
            });

            if (productInfo) {
              needProductionDate = productInfo.needProductionDate || false;
              productExpiryInfo = {
                hasExpiry: productInfo.hasExpiry,
                expiryPeriod: productInfo.expiryPeriod,
                expiryUnit: productInfo.expiryUnit,
              };
            }
          }

          // 验证生产日期
          if (needProductionDate && !params.productionDate) {
            throw new Error('该产品需要记录生产日期');
          }

          if (params.productionDate) {
            const prodDate = new Date(params.productionDate);
            const now = new Date();

            if (prodDate > now) {
              throw new Error('生产日期不能晚于当前日期');
            }

            // 检查生产日期是否过于久远
            const oneYearAgo = new Date();
            oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
            if (prodDate < oneYearAgo) {
              ctx.logger.warn(`生产日期较早: ${params.productionDate}, 记录ID: ${params._id}`);
            }

            // 计算到期日期
            if (productExpiryInfo && productExpiryInfo.hasExpiry) {
              expiryDate = this.calculateProductExpiryDate(
                params.productionDate,
                productExpiryInfo.expiryPeriod,
                productExpiryInfo.expiryUnit
              );
            }
          }

          // 🔑 关键修复：更新领用记录中的产品信息
          const updateData = {
            receiveDate,
            sign: params.sign,
            claimType: params.claimType,
            isReceived: true, // 标记为已领取
            receiveStatus: 'completed', // 领取状态：已完成
          };

          // 更新产品信息，保存用户选择的具体产品
          const updatedProducts = [];
          if (params.products && params.products.length > 0) {
            for (const product of params.products) {
              // 验证必要字段
              if (!product.productId) {
                console.error('产品ID缺失:', product);
                return { message: `产品 ${product.product} 缺少有效的产品ID`, code: -1 };
              }

              if (!product.product) {
                console.error('产品名称缺失:', product);
                return { message: '产品名称不能为空', code: -1 };
              }

              // 验证产品是否存在
              const productExists = await ctx.model.ProtectiveProduct.findOne({
                _id: product.productId,
                EnterpriseID,
                isActive: true,
              });

              if (!productExists) {
                console.error('产品不存在:', product.productId);
                return { message: `产品 ${product.product} 不存在或已禁用`, code: -1 };
              }

              updatedProducts.push({
                _id: product.productsOrder || product._id, // 保持原有的领用记录产品ID
                product: product.product,
                modelNumber: product.modelNumber,
                productSpec: product.productSpec || '',
                number: product.number || product.receiveNum || 1,
                receiveNum: product.receiveNum || product.number || 1,
                // 🔑 保存用户选择的具体产品ID
                selectedProductId: product.productId, // 用户选择的ProtectiveProduct._id
                categoryId: product.categoryId,
                categoryPath: product.categoryPath,
                categoryName: product.categoryName,
                // 保存产品验证信息
                productValidated: true,
                productValidatedAt: new Date(),
              });
            }

            updateData.products = updatedProducts;
            console.log('✅ 更新领用记录产品信息:', updatedProducts);
          } else {
            console.error('❌ 产品信息缺失:', params);
            return { message: '产品信息不能为空', code: -1 };
          }

          if (params.productionDate) {
            updateData.productionDate = new Date(params.productionDate);
          }

          if (expiryDate) {
            updateData.expiryDate = expiryDate;
          }

          res = await ctx.model.ReceiveRecord.updateOne(
            { EnterpriseID, _id: params._id },
            updateData
          );

          // 验证更新结果
          if (res.matchedCount === 0) {
            console.error('❌ 领用记录更新失败: 记录不存在', params._id);
            return { message: '领用记录不存在', code: -1 };
          }

          if (res.modifiedCount === 0) {
            console.error('❌ 领用记录更新失败: 无变更', params._id);
            return { message: '领用记录更新失败', code: -1 };
          }

          console.log('✅ 领用记录更新成功:', {
            recordId: params._id,
            matchedCount: res.matchedCount,
            modifiedCount: res.modifiedCount,
            productsCount: updatedProducts.length,
          });
          ctx.auditLog(
            '防护用品签字确认',
            `记录ID: ${params._id}, 员工: ${params.employee}, 生产日期: ${params.productionDate || '无'}, 到期日期: ${expiryDate || '无'}`,
            'info'
          );

        } else if (params.type === 'reject') {
          // 拒绝领取
          console.log('拒绝领取防护用品:', params._id);

          res = await ctx.model.ReceiveRecord.updateOne(
            { EnterpriseID, _id: params._id },
            {
              receiveDate,
              sign: params.sign,
              isRejected: true,
              receiveStatus: 'rejected',
              rejectReason: params.rejectReason || '员工拒绝领取',
            }
          );

          // 记录审计日志
          ctx.auditLog(
            '防护用品拒绝领取',
            `记录ID: ${params._id}, 员工: ${params.employee}`,
            'info'
          );
        }

      } else if (params.type === 'application') {
        // 处理防护用品申请（支持新分类系统）
        console.log('处理防护用品申请，参数:', JSON.stringify(params, null, 2));

        // 从申请数据中获取员工信息
        const firstItem = params.arr[0];
        const employeeId = firstItem.employee || ctx.session.adminUserInfo?.employeeId;
        const employeeName = firstItem.employeeName;

        if (!employeeId) {
          throw new Error('缺少员工ID信息');
        }

        // 获取员工岗位信息
        const station = await this.findEmployeeStation({ employeeId, EnterpriseID });
        const mill = {
          workspace: [], // 所有车间
          workshop: [], // 厂房
          workshopId: [], // 厂房Id
          workspaces: [], // 车间
          workspaceId: [], // 车间id
          workstation: [], // 岗位
          workstationId: [], // 岗位id
        };

        station.forEach(item => {
          if (item.workshop && item.workshopId) {
            mill.workspaces.push(`${item.workshop} > ${item.workspace} > ${item.name}`);
            mill.workshop.push(item.workshop);
            mill.workshopId.push(item.workshopId);
          } else {
            mill.workspaces.push(`${item.workspace} > ${item.name}`);
            mill.workshop.push(0);
            mill.workshopId.push(0);
          }
          mill.workspace.push(item.workspace);
          mill.workspaceId.push(item.workspaceId);
          mill.workstation.push(item.name);
          mill.workstationId.push(item._id);
        });

        const now = new Date();
        const mouth = now.getMonth() + 1;
        const year = now.getFullYear();

        // 存储所有申请记录
        const applicationProducts = [];

        // 处理申请的产品列表（适配新分类系统）
        for (const item of params.arr) {
          console.log('处理申请产品:', item);

          // 构建产品信息（新分类系统格式）
          const productInfo = {
            // 新分类系统字段
            productId: item.productId || item._id,
            categoryId: item.categoryId,
            categoryName: item.categoryName,
            categoryPath: item.categoryPath,

            // 产品基本信息
            product: item.product || item.name,
            productSpec: item.productSpec || '',
            modelNumber: item.modelNumber || '',
            materialCode: item.materialCode || '',
            number: item.number || item.num || 1,

            // 兼容旧字段（保留以防其他地方使用）
            productIds: item.productId || item._id,
            productSpecId: item.productSpecId || '',
          };

          // 创建申请记录（使用新分类系统数据结构）
          const applicationProduct = {
            EnterpriseID,
            // 岗位信息
            workshop: mill.workshop,
            workshopId: mill.workshopId,
            workspaces: mill.workspaces,
            workspace: mill.workspace,
            workspaceId: mill.workspaceId,
            workstation: mill.workstation,
            workstationId: mill.workstationId,

            // 产品信息（新分类系统格式）
            products: [ productInfo ],

            // 申请基本信息
            employee: employeeId,
            employeeName,
            applicationTime: now,
            updateAt: now,
            notes: item.notes || params.arr[0].notes || '',
            claimType: item.claimType || params.arr[0].claimType || '',

            // 时间信息
            yearNumber: year,
            mouthNumber: mouth,

            // 审批状态
            auditStatus: 0, // 初始状态：未审核
            auditLevel: 1, // 初始审批级别：一级审批
            auditRecords: [
              {
                auditLevel: 1,
                auditStatus: 0, // 0-未审核，1-通过，2-驳回
                needToAuditIds: [], // 审批人ID列表，by分支可以为空
                operator: null,
                auditTime: null,
                reason: '',
                _id: shortid.generate(),
              },
            ],

            // 仓库信息
            warehouseId: item.warehouseId || '',
          };
          if (this.config.branch === 'wh' && this.config.whPpeSecondLevelApproval === 'open') {
            // 如果是万华，则寻找一级和二级审批人，一级是HSE管理员，二级是部门经理， 每个层级只要有一个审批通过就通过
            // 查询一级和二级审批人
            const employee = await ctx.model.Employee.findOne({ _id: employeeId });
            const managerUsers = await this.whFindApprovers(employee.departs[0]);
            const firstLevelApprovers = managerUsers.firstLevelApprovers;
            const secondLevelApprovers = managerUsers.secondLevelApprovers;
            if (firstLevelApprovers.length > 0) {
              applicationProduct.auditRecords = [{
                needToAuditIds: firstLevelApprovers,
                auditLevel: 1,
                auditStatus: 0,
                reason: '',
              }];
            }
            if (secondLevelApprovers.length > 0) {
              applicationProduct.auditRecords = [ ...applicationProduct.auditRecords, {
                needToAuditIds: secondLevelApprovers,
                auditLevel: 2,
                auditStatus: 0,
                reason: '',
              }];
            }
            if (applicationProduct.auditRecords.length === 2) {
              applicationProduct.auditLevel = 2;
            }
          }
          applicationProducts.push(applicationProduct);
        }
        if (applicationProducts.length < 1) return 500;
        res = await ctx.model.ApplicationProduct.insertMany(applicationProducts);
        if (this.config.branch === 'wh' && this.config.whPpeSecondLevelApproval === 'open') {
          // 使用新的审批工作流服务来管理多级审批流程
          try {
            const workflowResult = await ctx.service.approvalWorkflow.initializeApproval(
              res,
              employeeId,
              employeeName
            );

            ctx.auditLog(
              '防护用品申请审批流程启动',
              `成功启动${workflowResult.results.length}个申请的审批流程`,
              'info'
            );
          } catch (workflowError) {
            ctx.logger.error('审批工作流启动失败', workflowError);
            // 审批流程启动失败，但申请记录已创建，记录错误但不影响主流程
            ctx.auditLog(
              '防护用品申请审批流程启动失败',
              `错误信息: ${workflowError.message}`,
              'error'
            );
          }
        }
      }
      return res;
    } catch (error) {
      const { ctx } = this;
      console.error('receiveProducts方法执行失败:', error);
      ctx.logger.error('防护用品领用处理失败', error);

      // 记录详细的错误信息
      ctx.auditLog(
        '防护用品领用失败',
        `错误信息: ${error.message}, 参数: ${JSON.stringify(params)}`,
        'error'
      );

      return {
        message: `处理失败: ${error.message}`,
        code: -1,
        error: error.stack,
      };
    }
  }
  async whFindApprovers(nowDingtreeId) {
    const { ctx } = this;
    const nowDingtree = await ctx.model.Dingtree.aggregate([
      // 1. 首先查找HR00003344的完整部门层级路径
      {
        $match: {
          _id: nowDingtreeId,
        },
      },
      {
        $graphLookup: {
          from: 'dingtrees',
          startWith: '$parentid',
          connectFromField: 'parentid',
          connectToField: '_id',
          as: 'ancestors',
          depthField: 'level',
        },
      },
      // 2. 将自身部门与所有祖先部门合并为一个数组，并按照从高到低排序
      {
        $project: {
          departmentPath: {
            $concatArrays: [
              [ '$_id' ],
              {
                $map: {
                  input: {
                    $sortArray: {
                      input: '$ancestors',
                      sortBy: {
                        level: 1,
                      },
                    },
                  },
                  as: 'ancestor',
                  in: '$$ancestor._id',
                },
              },

            ],
          },
        },
      },
      // 3. 展开部门路径，并添加层级信息
      {
        $unwind: {
          path: '$departmentPath',
          includeArrayIndex: 'level',
        },
      },
      // 4. 直接查找相关部门下有权限的用户
      {
        $lookup: {
          from: 'policies',
          let: {
            currentDeptId: '$departmentPath',
          },
          pipeline: [
            {
              $match: {
                $or: [
                  {
                    name: {
                      $regex: /HSE管理员|HSE职能/i,
                    },
                  },
                  {
                    name: {
                      $regex: /部门经理/i,
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: '$dingtree_ids',
                preserveNullAndEmptyArrays: false,
              },
            },
            // 联查这些dingtree_ids对应的部门信息，获取它们的parentid
            {
              $lookup: {
                from: 'dingtrees',
                localField: 'dingtree_ids',
                foreignField: '_id',
                as: 'linkedDepts',
              },
            },
            // 确保找到了对应的部门
            {
              $match: {
                'linkedDepts.0': { $exists: true },
              },
            },
            // 展开联查结果
            {
              $unwind: '$linkedDepts',
            },
            // 筛选出与当前部门有共同父级的部门或者直接包含当前部门的策略
            {
              $match: {
                $expr: {
                  $or: [
                    // 策略直接包含当前部门
                    { $eq: [ '$dingtree_ids', '$$currentDeptId' ] },
                    // 策略的部门与当前部门有共同的父级
                    // { $eq: [ '$linkedDepts.parentid', '$$currentDeptId' ] },
                    // 超级管理员
                    { $eq: [ '$isSuper', true ] },
                  ],
                },
              },
            },
            // 重新分组以恢复原始文档结构
            {
              $group: {
                _id: '$_id',
                name: { $first: '$name' },
                user_ids: { $first: '$user_ids' },
                dingtree_ids: { $push: '$dingtree_ids' },
                isSuper: { $first: '$isSuper' },
                linkedDeptParents: { $addToSet: '$linkedDepts.parentid' },
              },
            },
          ],
          as: 'allPolicies',
        },
      },
      {
        $unwind: {
          path: '$allPolicies',
        },
      },
      // 9. 按部门层级排序(从低到高，即从近到远)
      {
        $sort: {
          level: 1,
          'allPolicies._id': 1,
          'allPolicies.isSuper': 1, // 非超级管理员优先
        },
      },
      // 10. 分组获取HSE管理员和经理
      {
        $group: {
          _id: {
            $cond: {
              if: {
                $regexMatch: {
                  input: '$allPolicies.name',
                  regex: /HSE管理员|HSE职能/i,
                },
              },
              then: 'HSE管理员',
              else: '部门经理',
            },
          },
          firstMatch: {
            $first: '$$ROOT',
          },
        },
      },
      {
        $project: {
          _id: 0,
          policyType: '$_id',
          user_ids: '$firstMatch.allPolicies.user_ids',
          dingtree_ids: '$firstMatch.allPolicies.dingtree_ids',
          name: '$firstMatch.allPolicies.name',
          policyId: '$firstMatch.allPolicies._id',
          isSuper: '$firstMatch.allPolicies.isSuper',
        },
      },
    ]);
    console.log('防护用品查询审批人', nowDingtree);
    if (nowDingtree.length > 0) {
      // 按照一级和二级进行分组
      let firstLevelApprovers = [];
      let secondLevelApprovers = [];
      for (const item of nowDingtree) {
        if (item.policyType === 'HSE管理员') {
          firstLevelApprovers = item.user_ids;
        } else {
          secondLevelApprovers = item.user_ids;
        }
      }
      return {
        firstLevelApprovers,
        secondLevelApprovers,
      };
    }
    return {
      firstLevelApprovers: [],
      secondLevelApprovers: [],
    };
  }
  async ppeSign(params) {
    const { ctx } = this;
    try {
      if (!JSON.parse(params.defendproductsId).length) { // 没有对应的记录的id，
        const num = JSON.parse(params.num);
        const userId = params.userId;
        const employee = await ctx.model.User.findOne({ _id: userId }).populate('employeeId');
        const EnterpriseID = employee.employeeId.EnterpriseID;
        // console.log(EnterpriseID, '?????');
        const list = await ctx.model.Defendproducts.findOne({ EnterpriseID });
        const data = [];
        if (num.quantity_k) {
          data.push({
            acknowledge: true,
            studio: employee.employeeId.station,
            pname: '口罩',
            sku: '3M 9042',
            quantity: num.quantity_k,
            receiveMan: employee.employeeId.name,
            date: new Date(),
            createTime: new Date(),
            sign: params.filename,
          });
        } else if (num.quantity_e) {
          data.push({
            acknowledge: true,
            studio: employee.employeeId.station,
            pname: '耳塞',
            sku: '3M 1110',
            quantity: num.quantity_e,
            receiveMan: employee.employeeId.name,
            date: new Date(),
            createTime: new Date(),
            sign: params.filename,
          });
        }
        if (list) {
          await ctx.model.Defendproducts.updateOne({ EnterpriseID: params.EnterpriseID }, { $addToSet: { formData: { $each: data } } });
        } else {
          await ctx.model.Defendproducts.create({
            updateTime: new Date(),
            invoice: [],
            certifications: [],
            receiveForm: [],
            EnterpriseID,
            formData: data,
          });
        }
      } else {
        await ctx.model.Defendproducts.updateOne({ EnterpriseID: params.EnterpriseID }, { $set: { 'formData.$[i].sign': params.filename, 'formData.$[i].acknowledge': true } }, {
          arrayFilters: [{
            'i._id': { $in: JSON.parse(params.defendproductsId) },
          }],
        });
      }
      // console.log(res, '返回的数据 ');
    } catch (error) {
      console.log(error);
      return 500;
    }

  }

  // 获取转岗信息，从企业端移植过来的
  async getStationChange(params) {
    try {
      const { ctx } = this;
      const { EnterpriseID, employeeId } = params;
      let stationFrom = {};
      let deleteStationFrom = true;
      let deleteStationTo = true;
      const res = await ctx.model.EmployeeStatusChange.aggregate([
        { $match: { employee: employeeId } },
        { $unwind: '$statusChanges' },
        { $match: { 'statusChanges.EnterpriseID': EnterpriseID } },
        { $group: { _id: '$_id', statusChanges: { $push: '$statusChanges' } } },
      ]);
      const employee = await ctx.model.Employee.findOne({ _id: employeeId }, { name: 1 });
      let station = {};
      if (res[0]) {
        res[0].employee = employee;
        for (let i = 0; i < res[0].statusChanges.length; i++) {
          const EnterpriseID = res[0].statusChanges[i].EnterpriseID;
          deleteStationFrom = true;
          deleteStationTo = true;
          res[0].statusChanges[i].EnterpriseID = (await ctx.model.Adminorg.findOne({ _id: res[0].statusChanges[i].EnterpriseID })).cname;
          // 获取岗位信息
          if (res[0].statusChanges[i].stationsTo.length > 0) {
            for (let k = 0; k < res[0].statusChanges[i].stationsTo.length; k++) {
              station = await this.getStationInfo(res[0].statusChanges[i].stationsTo[k], EnterpriseID);
              if (station) {
                res[0].statusChanges[i].stationsTo[k] = station;
                deleteStationTo = false;
              } else {
                res[0].statusChanges[i].stationsTo[k] = '该岗位已被删除';
              }
              // res[0].statusChanges[i].stationsTo[k] = station || '该岗位已删除';
            }
          }
          if (res[0].statusChanges[i].stationFrom) {
            stationFrom = await this.getStationInfo(res[0].statusChanges[i].stationFrom, EnterpriseID);
            if (stationFrom) {
              deleteStationFrom = false;
              res[0].statusChanges[i].stationFrom = stationFrom;
            } else {
              res[0].statusChanges[i].stationFrom = '该岗位已被删除';
            }
            // res[0].statusChanges[i].stationFrom = (await this.getStationInfo(res[0].statusChanges[i].stationFrom, EnterpriseID)) || '该岗位已删除';
          }
          if (res[0].statusChanges[i].changType === 2 && deleteStationFrom && deleteStationTo) {
            // 如果转入岗位和转出岗位都为空 那么删除这条记录
            // await ctx.model.EmployeeStatusChange.updateOne({ employee: params2.employee }, {
            // $pull: {
            // statusChanges: { _id: res[0].statusChanges[i]._id },
            // },
            // });
            await ctx.service.db.updateOne('EmployeeStatusChange', { employee: employeeId }, {
              $pull: {
                statusChanges: { _id: res[0].statusChanges[i]._id },
              },
            });
          }

          // 获取部门信息
          if (res[0].statusChanges[i].departsTo.length > 0) {
            for (let j = 0; j < res[0].statusChanges[i].departsTo.length; j++) {

              for (let k = 0; k < res[0].statusChanges[i].departsTo[j].length; k++) {
                const depart = await ctx.model.Dingtree.findOne({ _id: res[0].statusChanges[i].departsTo[j][k] });
                res[0].statusChanges[i].departsTo[j][k] = depart ? depart.name : '';
                if (depart) {
                  res[0].statusChanges[i].departsTo[j][k] = depart.name;
                  // deleteDepartTo = false
                } else {
                  res[0].statusChanges[i].departsTo[j][k] = '';
                }
              }
            }
            // departTo = res[0].statusChanges[i].departsTo[0];
          }
          if (res[0].statusChanges[i].departFrom.length > 0) {
            for (let j = 0; j < res[0].statusChanges[i].departFrom.length; j++) {
              for (let k = 0; k < res[0].statusChanges[i].departFrom[j].length; k++) {
                const depart = await ctx.model.Dingtree.findOne({ _id: res[0].statusChanges[i].departFrom[j][k] });
                if (depart) {
                  res[0].statusChanges[i].departFrom[j][k] = depart.name;
                  // deleteDepartFrom = false
                } else {
                  res[0].statusChanges[i].departFrom[j][k] = '';
                }
              }
            }
            // departFrom = res[0].statusChanges[i].departFrom[0];
          }
          // if (res[0].statusChanges[i].changType === 3 && !departFrom || departFrom.filter(item => !item)[0] && !departTo || departTo.filter(item => !item)[0]) {
          //   // 如果转入部门和转出部门都为空，那删除这条记录
          //   await ctx.model.employeeStatusChange.update({ employee: params2.employee }, { $pull: { statusChanges: { _id: res[0].statusChanges[i] } } });
          // }
        }
      }
      return res;
    } catch (error) {
      console.log(error);
      return 500;
    }
  }

  // 获取岗位信息
  async getStationInfo(stationId, EnterpriseID) {
    const mills = await this.ctx.model.MillConstruction.aggregate([
      {
        $match: {
          EnterpriseID,
        },
      },
      {
        $unwind: '$children',
      },
      {
        $match: {
          $or: [{ 'children._id': stationId }, { 'children.children._id': stationId }],
        },
      },
    ]);
    if (mills.length > 0) {
      let stationName = '';
      if (mills[0].category === 'mill') {
        mills[0].children.children.forEach(item => {
          if (item._id === stationId) {
            stationName = item.name;
          }
        });
        stationId = `${mills[0].name}/${mills[0].children.name}/${stationName}`;
      } else {
        stationId = `${mills[0].name}/${mills[0].children.name}`;
      }
    } else {
      stationId = '';
    }
    return stationId;
  }

  // 获取消息通知
  async getMessageNotice(payload) {
    const { ctx } = this;
    const { pageSize = 10, pageNum = 1, keywords = [] } = payload;
    const skip = (pageNum - 1) * pageSize;
    const match = {
      reader: {
        $elemMatch: {
          readerID: payload.userId,
        },
      },
      state: 1,
    };
    if (keywords.length > 0) {
      match.keywords = { $all: keywords };
    }
    if (payload.isRead !== null && !isNaN(payload.isRead)) {
      match.reader.$elemMatch.isRead = Number(payload.isRead);
    }
    console.log('🚀那个啥payload', payload, payload.isRead);
    if (payload.isRead !== null && !isNaN(payload.isRead)) {
      match['reader.isRead'] = Number(payload.isRead);
    }
    console.log('🚀那个啥match', match, payload.isRead != null, !isNaN(payload.isRead));
    const res = await ctx.model.MessageNotification.aggregate([
      {
        $match: match,
      },
      {
        $unwind: '$reader',
      },
      {
        $match: {
          'reader.readerID': payload.userId,
        },
      },
      {
        $sort: {
          date: -1,
        },
      },
      {
        $facet: {
          list: [
            { $skip: skip },
            { $limit: pageSize },
          ],
          total: [
            {
              $count: 'total',
            },
          ],
        },
      },
      {
        $addFields: {
          'pageInfo.total': { $arrayElemAt: [ '$total.total', 0 ] },
          'pageInfo.pageSize': pageSize,
          'pageInfo.pageNum': pageNum,
        },
      },
      {
        $project: {
          list: 1,
          pageInfo: 1,
        },
      },
    ]);
    if (res) {
      const upload_http_path = this.config.upload_http_path;
      const docs = res[0].list.map(ele => ({
        ...ele,
        files: ele.files ? ele.files.map(fileName => ({
          url: `/static${upload_http_path}/messageNotification/${ele._id}/${fileName}`,
          name: fileName,
        })) : undefined,
      }));
      res[0].list = docs;
    }
    return res[0] || { list: [], pageInfo: { pageSize, pageNum, total: 0 } };
  }
  // 确认消息通海将消息标记为已读
  async confirmMessageNotice(payload) {
    const { ctx } = this;
    const { messageIds, userId } = payload;
    // 使用updateMany一次性更新所有匹配的消息
    const result = await ctx.model.MessageNotification.updateMany(
      { _id: { $in: messageIds }, 'reader.readerID': userId },
      { $set: { 'reader.$.isRead': 1 } }
    );
    return {
      success: true,
      totalUpdated: result.nModified || 0,
    };
  }
  // 获取违章通知
  async getViolationNotice(payload) {
    const res = await this.ctx.model.ViolationInfo.findById({
      _id: payload.id,
    }).lean();
    const enterprise = await this.ctx.model.Adminorg.findOne({
      _id: res.punishmentUnit,
    }).lean();
    res.cname = enterprise.cname;
    return res;
  }
  // 获取劳动者是否可以编辑个人职业史
  async getLaborIsEdit(params) {
    console.log(params, 'params');
    const res = await this.ctx.model.Adminorg.find({ _id: params.companyId }).select('lobarIsEdit');
    return res;
  }
  async getHistoryList(params) {
    const res = await this.ctx.model.OccupationalHistory.find({ employeeId: params.userId });
    return res;
  }
  async addHistoryList(params) {
    const res = await this.ctx.model.OccupationalHistory.create(params);
    return res;
  }
  async deleteHistoryList(params) {
    const res = await this.ctx.model.OccupationalHistory.deleteOne({ _id: params._id });
    return res;
  }
  async editHistoryList(params) {
    const res = await this.ctx.model.OccupationalHistory.findByIdAndUpdate({ _id: params._id }, params.newData);
    return res;
  }
  // 新的配发标准查询方法（使用扁平化结构）
  async findProtectionPlanNew(employeeId, EnterpriseID, category) {
    const { ctx } = this;
    console.time('findProtectionPlanNew');

    // 获取员工岗位信息（使用扁平化结构）
    const employeePositions = await this.getEmployeePositions(employeeId, EnterpriseID);

    if (!employeePositions || employeePositions.length === 0) {
      console.timeEnd('findProtectionPlanNew');
      return [];
    }

    // 提取所有岗位的fullId
    const positionFullIds = employeePositions.map(pos => pos.fullId);

    // 构建类别匹配条件
    let categoryMatch = {};
    if (category && ctx.app.config.branch === 'wh') {
      categoryMatch = {
        $or: [
          { category },
          { category: { $exists: false } },
          { category: '' },
        ],
      };
    }

    // 查询配发标准（使用新的nodeFullId字段）
    const protectionPlans = await ctx.model.ProtectionPlan.find({
      EnterpriseID,
      nodeFullId: { $in: positionFullIds },
      planStatus: 1, // 启用状态
      configStatus: { $ne: 'no_need' }, // 排除无需配置的
      ...categoryMatch,
    }).lean();

    // 获取员工部门的配发标准
    const employee = await ctx.model.Employee.findById(employeeId).select('departs');
    if (employee && employee.departs && employee.departs.length > 0) {
      const departmentPlans = await ctx.model.ProtectionPlan.find({
        EnterpriseID,
        grantType: 'depart',
        departId: { $in: employee.departs },
        planStatus: 1,
        configStatus: { $ne: 'no_need' },
        ...categoryMatch,
      }).lean();

      // 合并部门配发标准，避免重复
      departmentPlans.forEach(plan => {
        const exists = protectionPlans.find(p => p._id === plan._id);
        if (!exists) {
          protectionPlans.push(plan);
        }
      });
    }

    console.timeEnd('findProtectionPlanNew');
    return protectionPlans;
  }

  // 保留原有方法以兼容旧代码
  async findProtectionPlan(employeeId, EnterpriseID, category) {
    // 调用新方法
    return await this.findProtectionPlanNew(employeeId, EnterpriseID, category);
  }

  // 获取防护用品分类数据（适配新的产品模型）
  async getProtectiveProductCategories(EnterpriseID, warehouseId) {
    const { ctx } = this;

    try {
      console.log(`[产品查询] 查询仓库 ${warehouseId} 的防护用品`);
      // 查询该仓库的所有防护用品
      const products = await ctx.model.ProtectiveProduct.find({
        EnterpriseID,
        warehouseId,
        isActive: true,
        surplus: { $gt: 0 }, // 只返回有库存的产品
      }).populate('categoryId').lean();

      console.log(`[产品查询] 找到 ${products.length} 个有库存的产品`);

      // 按分类组织数据
      const categoryMap = new Map();

      products.forEach(product => {
        const categoryId = product.categoryId?._id || 'uncategorized';
        const categoryName = product.categoryId?.name || '未分类';
        const categoryPath = product.categoryPath || categoryName;

        if (!categoryMap.has(categoryId)) {
          categoryMap.set(categoryId, {
            _id: categoryId,
            name: categoryName,
            categoryPath,
            tableHeader: this.getCategoryIcon(categoryName),
            data: [],
          });
        }

        const category = categoryMap.get(categoryId);
        category.data.push({
          _id: product._id,
          product: product.product,
          productSpec: product.productSpec,
          modelNumber: product.modelNumber,
          surplus: product.surplus,
          materialCode: product.materialCode,
          picture: product.picture,
          // 有效期管理字段
          needProductionDate: product.needProductionDate || false,
          hasExpiry: product.hasExpiry || false,
          expiryPeriod: product.expiryPeriod,
          expiryUnit: product.expiryUnit || 'days',
          // 保持与旧结构兼容的字段
          selectedNum: 0,
        });
      });

      // 转换为数组格式
      const list = Array.from(categoryMap.values());

      return {
        list,
        EnterpriseID,
      };
    } catch (error) {
      console.error('获取防护用品分类数据失败:', error);
      // 如果新模型查询失败，回退到旧模型
      return await this.getProtectiveProductCategoriesLegacy(EnterpriseID);
    }
  }

  // 获取分类图标
  getCategoryIcon(categoryName) {
    const iconMap = {
      头部防护: 'static/images/protection/头部防护.png',
      呼吸防护: 'static/images/protection/呼吸防护.png',
      坠落防护: 'static/images/protection/坠落防护.png',
      听力防护: 'static/images/protection/听力防护.png',
      眼面部防护: 'static/images/protection/眼面部防护.png',
      手部防护: 'static/images/protection/手部防护.png',
      足部防护: 'static/images/protection/足部防护.png',
      防护服装: 'static/images/protection/防护服装.png',
      防护手套: 'static/images/protection/防护手套.png',
      其他防护: 'static/images/protection/其他防护.png',
    };

    return iconMap[categoryName] || 'static/images/none.png';
  }

  // 旧版本的分类数据获取（兼容性）
  async getProtectiveProductCategoriesLegacy(EnterpriseID) {
    const { ctx } = this;

    const typeList = await ctx.model.ProtectiveSuppliesList.findOne({
      EnterpriseID,
    });

    if (!typeList) {
      return { list: [] };
    }

    typeList.list.forEach(item => {
      item.tableHeader = this.getCategoryIcon(item.name);
    });

    return typeList;
  }
  async updateNewReceiveRecord(plan, product) {
    const { ctx } = this;

    try {
      const newPlan = _.cloneDeep(plan);
      newPlan.planId = newPlan._id;
      delete newPlan._id;

      // 处理产品信息，确保包含正确的productId
      const productData = {
        ...product,
        // 保持原有的分类信息（配发标准中已经包含）
        categoryId: product.categoryId || '',
        categoryPath: product.categoryPath || '',
        categoryName: product.categoryName || '',
      };

      // 确保产品有正确的productId
      if (!product.productId) {
        // 尝试从新模型中查找对应的产品
        const targetProduct = await ctx.model.ProtectiveProduct.findOne({
          EnterpriseID: plan.EnterpriseID,
          product: product.product,
          modelNumber: product.modelNumber,
          isActive: true,
        }).select('_id categoryId categoryPath categoryName productSpec');

        if (targetProduct) {
          productData.productId = targetProduct._id;
          // 更新分类信息（以新模型为准）
          productData.categoryId = targetProduct.categoryId || product.categoryId;
          productData.categoryPath = targetProduct.categoryPath || product.categoryPath;
          productData.categoryName = targetProduct.categoryName || product.categoryName;
          productData.productSpec = targetProduct.productSpec || product.productSpec;
          console.log('找到对应产品:', { productId: targetProduct._id, product: product.product });
        } else {
          console.warn('未找到对应的新模型产品:', {
            product: product.product,
            modelNumber: product.modelNumber,
            EnterpriseID: plan.EnterpriseID,
          });
        }
      }

      newPlan.products = [ productData ];

      // 同时在记录级别也设置分类信息
      newPlan.categoryId = productData.categoryId || '';
      newPlan.categoryPath = productData.categoryPath || '';
      newPlan.categoryName = productData.categoryName || '';

      newPlan.receiveStartDate = new Date();
      newPlan.recordSource = 0;

      // 确保工作场所信息完整
      if (newPlan.grantType === 'mill') {
        if (!newPlan.workstation) {
          newPlan.workstation = plan.subRegion[plan.subRegion.length - 1];
        }
        // 确保有工作场所名称信息
        if (!newPlan.workshopName && plan.nodeFullId) {
          try {
            // 从nodeFullId解析工作场所信息
            const positionInfo = await ctx.model.FlatMillConstructionMaterialized.findOne({
              fullId: plan.nodeFullId,
              // EnterpriseID,
            }).select('millName workspaceName stationName parentId _id category level');

            if (positionInfo) {
              // 根据层级设置工作场所信息
              if (positionInfo.category === 'stations') {
                // 如果是岗位，设置岗位信息
                newPlan.workstationName = positionInfo.stationName || positionInfo.name || '';
                newPlan.workstation = positionInfo._id;

                // 查找上级车间信息
                if (positionInfo.parentId) {
                  const workspaceInfo = await ctx.model.FlatMillConstructionMaterialized.findOne({
                    _id: positionInfo.parentId,
                    // EnterpriseID,
                  }).select('workspaceName name parentId _id');

                  if (workspaceInfo) {
                    newPlan.workspacesName = workspaceInfo.workspaceName || workspaceInfo.name || '';
                    newPlan.workspaces = workspaceInfo._id;

                    // 查找上级厂房信息
                    if (workspaceInfo.parentId) {
                      const workshopInfo = await ctx.model.FlatMillConstructionMaterialized.findOne({
                        _id: workspaceInfo.parentId,
                        // EnterpriseID,
                      }).select('millName name _id');

                      if (workshopInfo) {
                        newPlan.workshopName = workshopInfo.millName || workshopInfo.name || '';
                        newPlan.workshop = workshopInfo._id;
                      }
                    }
                  }
                }
              }

              console.log('获取工作场所信息成功:', {
                nodeFullId: plan.nodeFullId,
                category: positionInfo.category,
                workshopName: newPlan.workshopName,
                workspacesName: newPlan.workspacesName,
                workstationName: newPlan.workstationName,
              });
            } else {
              console.warn('未找到工作场所信息:', { nodeFullId: plan.nodeFullId });
            }
          } catch (error) {
            console.warn('获取工作场所信息失败:', error.message);
          }
        }
      } else if (newPlan.grantType === 'depart') {
        if (!newPlan.departId) {
          newPlan.departId = plan.subRegion[plan.subRegion.length - 1];
        }
      }

      const doc = await new ctx.model.ReceiveRecord(newPlan).save();
      console.log('生成领用记录成功:', { recordId: doc._id, productId: productData.productId });
      return doc;
    } catch (error) {
      ctx.logger.error('按需生成领用记录失败', error);
      throw error;
    }
  }


}

module.exports = AdminuserService;
