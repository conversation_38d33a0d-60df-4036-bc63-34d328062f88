const Service = require('egg').Service;
const path = require('path');

const {
  _list,
  _item,
  _count,
  _create,
  _insertMany,
  _update,
  _removes,
  _safeDelete,
} = require(path.join(process.cwd(), 'app/service/general'));


class IndustryCategoryService extends Service {

  // 通过行业分类获取风险分类等级
  async getRiskLevelByIndustryCategory(industryCategory) {
    const { ctx } = this;
    // if (!industryCategory) {
    //   industryCategory = [];
    // }
    let industryCategoryLevel = '一般';
    if (!industryCategory || industryCategory.length < 4) {
      return { errMsg: '行业分类信息不完善' };
    }
    const industryCategoryLevelRes = await ctx.model.IndustryCategory.aggregate([
      { $match: { value: industryCategory[0] } },
      { $unwind: '$children' },
      { $match: { 'children.value': industryCategory[1] } },
      { $project: { children: '$children.children' } },
      { $unwind: '$children' },
      { $match: { 'children.value': industryCategory[2] } },
      { $project: { children: '$children.children' },
      },
      { $unwind: '$children' },
      { $match: { 'children.value': industryCategory[3] } },
    ]);
    console.log(industryCategoryLevelRes, industryCategory, '检测报告对接行业分类编码');
    if (!industryCategoryLevelRes[0]) {
      return { errMsg: '行业分类编码错误' };
    }
    if (industryCategoryLevelRes[0].children.riskType === 2) {
      industryCategoryLevel = '严重';
    } else if (industryCategoryLevelRes[0].children.riskType === 1) {
      industryCategoryLevel = '较重';
    }
    return industryCategoryLevel;
  }
  async find(payload, {
    sort,
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {

    const listdata = _list(this.ctx.model.IndustryCategory, payload, {
      query,
      searchKeys,
      populate,
      files,
      sort,
    });
    return listdata;
  }

  async count(params = {}) {
    return _count(this.ctx.model.IndustryCategory, params);
  }

  async create(payload) {
    return _create(this.ctx.model.IndustryCategory, payload);
  }

  async insertMany(array = [], options = {}) {
    return _insertMany(this.ctx.model.IndustryCategory, array, options);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.IndustryCategory, values, key);
  }

  async safeDelete(res, values) {
    return _safeDelete(res, this.ctx.model.IndustryCategory, values);
  }

  async update(res, _id, payload) {
    return _update(res, this.ctx.model.IndustryCategory, _id, payload);
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.IndustryCategory, params);
  }

}

module.exports = IndustryCategoryService;
