
// 报告对接
const Service = require('egg').Service;
const path = require('path');
// const mkdirp = require('mkdirp');
// const fs = require('fs');
// const awaitWriteStream = require('await-stream-ready').write;
// 管道读入一个虫洞。
const sendToWormhole = require('stream-wormhole');
const moment = require('moment');
const { decrypt } = require('../utils/encryption');
// const { reportData } = require('../utils/testReportData');
class JobhealthService extends Service {
  /**
   * <AUTHOR>
   * @description 处理上传文件时的表单数据 前端文件参数命名：model中的字段名+number！
   * @param {Object} ctx required:true 上下文对象
   * @param {String} pathconfig required:true 配置文件中文件路径
   * @return {Objedct} 返回参数说明:
   *  file:Object<{文件字段名:[files]}> 需要上传的文件
   *  其他字段
   * createdAt 2022-03-17
   */
  async uploadFile(ctx, pathconfig) {
    const parts = ctx.multipart({ autoFields: true });
    let stream;
    const files = [];
    let fileField = '';
    let EnterpriseID = '';
    const errMsgs = [];
    while ((stream = await parts()) != null) {
      if (!stream.filename) { // 注意如果没有传入直接返回
        return;
      }
      // fileFieldNumbers = stream.fieldname.match(/\d+/g);
      fileField = stream.fieldname;

      // fileField = stream.fieldname.replace(fileFieldNumbers[fileFieldNumbers.length - 1], '');
      // 文件名:随机数+时间戳+原文件后缀
      // if (!file[fileField]) {
      //   file[fileField] = [];
      // }
      const filename = fileField + Math.random().toString(36).substr(2) + new Date().getTime() + path.extname(stream.filename).toLocaleLowerCase();
      files.push({ fileType: stream.mimeType, originName: stream.filename, staticName: filename });
      // file[fileField].push();
      // 文件存放在静态资源public/projectFilePath文件夹下
      let target = '';
      const { isAllowCreateOrg = '1' } = ctx.app.config;
      let enterpriseInfo;

      if (isAllowCreateOrg === '1') {
        enterpriseInfo = await ctx.service.report.findEnterpriseInfo(parts.field.empCreditCode);
      } else {
        if (!parts.field.enterpriseId) {
          return { errMsg: '单位唯一编码不能为空' };
        }
        enterpriseInfo = await ctx.model.Adminorg.findById(parts.field.enterpriseId);

        if (!enterpriseInfo && parts.field.empCreditCode) {
          enterpriseInfo = await ctx.service.report.findEnterpriseInfo(parts.field.empCreditCode);
        }

        if (!enterpriseInfo) {
          return { errMsg: '用人单位不存在' };
        }
      }
      if (enterpriseInfo.errMsg) {
        errMsgs.push(enterpriseInfo.errMsg);
        // return {
        //   errMsg: enterpriseInfo.errMsg,
        // };
      }
      if (!parts.field.projectNumber) {
        errMsgs.push('项目编号为空');
      }
      if (errMsgs.length) {
        return { errMsgs };
      }
      EnterpriseID = enterpriseInfo._id;
      const configFilePath = path.resolve(path.join(pathconfig, `/${enterpriseInfo._id}`));
      // await mkdirp(configFilePath);
      target = path.resolve(configFilePath, filename);
      // 生成一个文件写入 文件流
      // const writeStream = fs.createWriteStream(target);
      try {
        // 异步把文件流 写入
        // await awaitWriteStream(stream.pipe(writeStream));
        await ctx.helper.pipe({
          readableStream: stream,
          target,
        });
      } catch (error) {
        console.log('创建文件失败', error);
        // 如果出现错误，关闭管道,防止浏览器响应卡死
        await sendToWormhole(stream);
        // writeStream.destroy();
        return { errMsg: '对接服务器出错' };
      }
    }
    return {
      files,
      EnterpriseID,
      ...parts.field,
    };
  }
  async findEnterpriseInfo(empCreditCode) {
    if (!empCreditCode) {
      return { errMsg: '用人单位社会统一信用代码为空' };
    }
    const enterpriseInfo = await this.ctx.model.Adminorg.findOne({ code: empCreditCode });
    if (!enterpriseInfo) {
      return { errMsg: '用人单位不存在' };
    }
    return enterpriseInfo;
  }
  /**
   * @description 更新检测报告文件/创建检测报告
   * @param {Object} params 参数
   */
  async postReportFileV1_0(params) {
    const { ctx } = this;
    const report = params.files.map(item2 => {
      return {
        fileName: item2.originName,
        url: item2.staticName,
        source: 'report',
      };
    });

    const jobHealthInfo = await ctx.model.JobHealth.findOne({ EnterpriseID: params.EnterpriseID, projectNumber: params.projectNumber });
    if (jobHealthInfo) {
      if (jobHealthInfo.report) { // 删除文件
        for (let i = 0; i < jobHealthInfo.report.length; i++) {
          // await this.ctx.helper.deleteFile(jobHealthInfo.report[i].url, path.resolve(this.app.config.enterpriseUpload_path, params.EnterpriseID));
          const target = path.resolve(path.resolve(this.app.config.enterpriseUpload_path, params.EnterpriseID), jobHealthInfo.report[i].url);
          await ctx.helper.deleteObject(target);
        }
      }
      // 更新
      await ctx.model.JobHealth.updateOne({ projectNumber: params.projectNumber }, { $set: { report } });
    } else {
      await ctx.model.JobHealth.create({
        projectNumber: params.projectNumber,
        EnterpriseID: params.EnterpriseID,
        report,
      });
    }

  }

  /**
   * <AUTHOR>
   * @description 对接报告主方法版本1.0
   * @param {<{isHZ:Boolean,reportData:Array}>} params 检测报告数据
   * createdAt 2022-04-13
   */
  async postReportDataV1_0(params) {
    const reportData = params.reportData;
    // let reportData = require('./test.js');
    // reportData = reportData.reportData;
    const item = JSON.parse(JSON.stringify(reportData));
    // 校验
    const res = await this.checkReportData(item);
    if (res.errMsgs) {
      return { status: 406, data: {}, message: { projectNumber: item.reportInfo.projectNumber, errMsgs: res.errMsgs } };
    }
    // 创建报告相关数据
    const errMsg = await this.createReportData(item, res);
    if (errMsg) {
      return { status: 406, data: {}, message: { projectNumber: item.reportInfo.projectNumber, errMsgs: errMsg instanceof Array ? errMsg : [ errMsg ] } };
    }
    // successReport.push(item.reportInfo.projectNumber);
    return { status: 200, message: '对接成功', data: { successReport: item.reportInfo.projectNumber } };
  }

  // 校验
  postJobhealthValidate(config) {
    const checkResult = [ '符合', '合格', '不符合', '不合格' ];
    return [
      {
        require: true,
        field: 'entrustOrgInfo',
        label: '委托单位信息',
        content: [{
          require: true,
          regex: '',
          field: 'entrustOrgName',
          label: '委托单位名称',
        }, {
          require: true,
          regex: new RegExp('^([0-9A-HJ-NPQRTUWXY]{2}[0-9]{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9][0-9]{14})$'),
          regexMsg: '格式错误',
          field: 'creditCode',
          label: '统一社会信用代码',
        }, {
          require: true,
          regex: '',
          type: 'Date',
          field: 'entrustDate',
          lastField: true,
          parentLabel: '委托单位信息',
          label: '委托时间',
        }],
      },
      {
        label: '受检单位信息',
        field: 'empInfo',
        require: true,
        content: [
          {
            require: true,
            regex: '',
            field: 'empName',
            label: '名称',
          },
          {
            require: true,
            field: 'creditCode',
            regex: new RegExp('^([0-9A-HJ-NPQRTUWXY]{2}[0-9]{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9][0-9]{14})$'),
            regexMsg: '格式错误',
            label: '统一社会信用代码',
          }, {
            require: config.isAllowCreateOrg === '0',
            field: 'enterpriseId',
            regexMsg: '必传',
            label: '单位唯一编码',
          }, {
            require: true,
            regex: new RegExp('^[0-9]{2,6}$'),
            regexMsg: '请输入长度2-6之间的数字字符',
            field: 'economicTypeCode',
            label: '经济类型编码',
          },
          {
            require: true,
            regex: new RegExp('^[A-Z0-9]+$'),
            regexMsg: '请输入大写字母或者数字字符',
            type: 'Array',
            min: 2,
            max: 4,
            dimension: 2, // 二维非对象数组
            field: 'industryCategoryCode',
            label: '行业类别编码',
          },
          {
            require: true,
            enum: [ '大', '中', '小', '微', '不详' ],
            field: 'companyScale',
            label: '企业规模',
          },
          {
            require: true,
            regex: new RegExp('^[0-9]{12}$'),
            regexMsg: '请输入长度为12的数字字符，不够的位数请在数据后面用0补齐',
            type: 'Array',
            max: 4,
            min: 3,
            field: 'districtRegAdd',
            label: '注册地区编码',
          },
          {
            require: true,
            field: 'regAdd',
            // regex: new RegExp('^[\u4E00-\u9FA5\uf900-\ufa2da-zA-Z0-9]{0,}$'),
            // regexMsg: '请输入中文数字和字母',
            label: '注册地区详细地址',
          },
          // {
          //   require: true,
          //   regex: '',
          //   field: 'regAddress',
          //   label: '注册地址',
          // },
          {
            field: 'workAddress',
            require: true,
            label: '技术服务地址',
            regex: '',
            type: 'Array',
            content: [{
              field: 'districts',
              max: 4,
              min: 3,
              label: '技术服务地址编码',
              require: true,
              regex: new RegExp('^[0-9]{12}$'),
              regexMsg: '请输入长度为12的数字字符，不够的位数请在数据后面用0补齐',
              type: 'Array',
            }, {
              field: 'address',
              label: '技术服务地址详细地址',
              require: true,
              regex: '',
              lastField: true,
              parentLabel: '技术服务地址',
            }],
          },
          // serviceAreaList // 待定 技术服务地址与注册地址不一致时可填写
          {
            require: true,
            type: 'Array',
            enum: [ '1', '2', '3', '4', '5', '6' ],
            field: 'serviceAreaCode',
            label: '服务的用人单位技术服务领域编码',
          },
          {
            require: true,
            field: 'legalPerson',
            regex: new RegExp('^[\u4E00-\u9FA5\uf900-\ufa2da-zA-Z]{2,20}$'),
            regexMsg: '请输入长度在2-20的中文、英文字符',
            label: '法人姓名',
          },
          {
            require: true,
            regex: new RegExp('^[\u4E00-\u9FA5\uf900-\ufa2da-zA-Z]{2,20}$'),
            regexMsg: '请输入长度在2-20的中文、英文字符',
            field: 'contactPerson',
            label: '联系人',
          }, {
            require: true,
            regex: new RegExp('^1(3|4|5|6|7|8|9)[0-9]{9}$'),
            regexMsg: '码格式错误',
            field: 'contactPhone',
            label: '联系电话',

          },
          // {
          //   require: false,
          //   regex: '',
          //   field: 'address',
          //   label: '通讯地址',
          // },
          {
            require: false,
            regex: '',
            field: 'postalCode',
            label: '邮政编码',
            lastField: true,
            parentLabel: '受检单位信息',
          },
        ],
      },
      {
        label: '上报机构信息',
        require: true,
        field: 'writeOrgInfo',
        content: [{
          require: true,
          regex: new RegExp('^([0-9A-HJ-NPQRTUWXY]{2}[0-9]{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9][0-9]{14})$'),
          regexMsg: '格式错误',
          field: 'creditCode',
          label: '统一社会信用代码',
        },
        {
          require: true,
          regex: '',
          field: 'orgName',
          lastField: true,
          parentLabel: '上报机构信息',
          label: '机构名称',
        },
        ],
      },
      {
        label: '检测报告信息',
        require: true,
        field: 'reportInfo',
        content: [{
          require: true,
          regex: '',
          field: 'projectNumber',
          label: '检测报告编号',
        }, {
          require: true,
          regex: '',
          field: 'projectName',
          label: '检测项目名称',
        }, {
          require: true,
          enum: [ '10', '20', '30', '31', '32', '33' ],
          field: 'serviceType',
          label: '检测类别编码',
        },
        {
          require: true,
          regex: '',
          type: 'Date',
          field: 'issueDate',
          label: '报告出具日期',
        }, {
          require: true,
          regex: new RegExp('^[\u4E00-\u9FA5\uf900-\ufa2da-zA-Z]{2,20}$'),
          regexMsg: '请输入长度在2-20的中文、英文字符',
          field: 'preparer',
          label: '填写人',
        }, {
          require: true,
          regex: new RegExp('^1(3|4|5|6|7|8|9)[0-9]{9}$'),
          regexMsg: '格式错误',
          field: 'preparerPhone',
          label: '填写人联系电话',
        }, {
          require: true,
          regex: new RegExp('^[\u4E00-\u9FA5\uf900-\ufa2da-zA-Z]{2,20}$'),
          regexMsg: '请输入长度在2-20的中文、英文字符',
          field: 'issuer',
          label: '报告签发人',
        }, {
          require: true,
          regex: '',
          type: 'Date',
          field: 'investigationTime',
          label: '服务的用人单位现场调查日期',
        }, {
          require: true,
          regex: '',
          type: 'Date',
          field: 'reportTime',
          label: '服务的用人单位现场采样、测量日期',
        },
        {
          require: false,
          type: 'Boolean',
          field: 'isPostToSupervise',
          label: '是否需要上报监管',
        },
        {
          require: true,
          field: 'participantList',
          label: '参与人员列表',
          type: 'Array',
          content: [{
            require: true,
            regex: new RegExp('^[\u4E00-\u9FA5\uf900-\ufa2da-zA-Z]{2,20}$'),
            regexMsg: '请输入长度在2-20的中文、英文字符',
            field: 'name',
            label: '姓名',
          }, {
            require: true,
            // regex: '',
            enum: [ '现场调查', '现场采样/检测', '实验室检测', '评价' ],
            type: 'Array',
            field: 'serviceItemList',
            label: '服务事项',
          }],
        }, {
          require: false,
          field: 'personInCharge',
          label: '检测组经理',
          regex: new RegExp('^[\u4E00-\u9FA5\uf900-\ufa2da-zA-Z]{2,20}$'),
          regexMsg: '请输入长度在2-20的中文、英文字符',
        }, {
          require: false,
          field: 'EvaluationProjectManagerAdmin',
          label: '评价组经理',
          regex: new RegExp('^[\u4E00-\u9FA5\uf900-\ufa2da-zA-Z]{2,20}$'),
          regexMsg: '请输入长度在2-20的中文、英文字符',
        }, {
          require: true,
          regex: '',
          type: 'Date',
          field: 'year',
          label: '年份',
          lastField: true,
        },
        // {
        //   field: 'reports',
        //   require: true,
        //   type: 'Array',
        //   regex: '',
        //   label: '报告书文件',
        // }
        ],
      },
      {
        field: 'detectionPointInfo',
        label: '检测结果信息',
        require: true,
        content: [{
          require: false,
          regex: '',
          field: 'chemistryFactors',
          label: '化学检测数据',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'checkProject',
            label: '危害因素名称',
          }, {
            require: true,
            regex: '',
            field: 'projectCode',
            type: 'String',
            label: '检测项目危害因素编码',
          }, {
            require: true,
            enum: checkResult,
            field: 'checkResult',
            label: '判定结果',
          },
          {
            require: true,
            regex: '',
            type: 'Array',
            field: 'checkResults',
            label: '检测结果信息',
            content: [
              {
                require: false,
                type: 'String',
                field: 'RF',
                label: 'RF',
              },
              {
                require: false,
                type: 'String',
                field: 'PCTWAF',
                label: 'PC_TWAf',
              },
              {
                require: false, // 现状评价是必填
                regex: '',
                type: 'Date',
                field: 'date',
                label: '检测日期',
              }, {
                require: false,
                regex: '',
                field: 'MAC',
                label: 'MAC',
              }, {
                require: false,
                regex: '',
                field: 'TWA',
                label: 'TWA',
              }, {
                require: false,
                regex: '',
                field: 'STEL',
                label: 'STEL',
              }, {
                require: false,
                regex: '',
                field: 'PE',
                label: 'PE',
              }, {
                require: false,
                regex: '',
                field: 'excursionLimits',
                label: '超限倍数',
              }, {
                require: true,
                enum: checkResult,
                field: 'checkResultItem',
                label: '判定结果',
              }],
          },
          ],
        }, {
          require: false,
          regex: '',
          field: 'dustFactors',
          label: '粉尘检测数据',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'checkProject',
            label: '危害因素名称',
          }, {
            require: true,
            regex: '',
            field: 'projectCode',
            type: 'String',
            label: '检测项目危害因素编码',
          }, {
            require: true,
            enum: checkResult,
            field: 'checkResult',
            label: '判定结果',
          }, {
            require: true,
            regex: '',
            type: 'Array',
            field: 'checkResults',
            label: '检测结果信息',
            content: [
              {
                require: false,
                regex: 'String',
                field: 'RF',
                label: 'RF',
              },
              {
                require: false,
                regex: 'String',
                field: 'PCTWAF',
                label: 'PC_TWAf',
              },
              {
                require: false, // 现状评价是必填
                regex: '',
                field: 'date',
                type: 'Date',
                label: '检测日期',
              }, {
                require: true,
                regex: '',
                field: 'TWA',
                label: 'TWA',
              }, {
                require: false,
                regex: '',
                field: 'PE',
                label: 'PE',
              }, {
                require: false,
                regex: '',
                field: 'excursionLimits',
                label: '超限倍数',
              }, {
                require: true,
                regex: '',
                enum: checkResult,
                field: 'checkResultItem',
                label: '判定结果',
              }],
          }],
        }, {
          require: false,
          regex: '',
          field: 'biologicalFactors',
          label: '生物检测数据',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'checkProject',
            label: '危害因素名称',
          }, {
            require: true,
            regex: '',
            field: 'projectCode',
            type: 'String',
            label: '检测项目危害因素编码',
          }, {
            require: false,
            regex: '',
            field: 'MAC',
            label: 'MAC',
          }, {
            require: false,
            regex: '',
            field: 'TWA',
            label: 'TWA',
          }, {
            require: false,
            regex: '',
            field: 'STEL',
            label: 'STEL',
          }, {
            require: true,
            field: 'checkResult',
            enum: checkResult,
            label: '判定结果',
          }],
        }, {
          require: false,
          regex: '',
          field: 'noiseFactors',
          label: '噪声检测数据',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'touchTime',
            label: '接触时间',
          }, {
            require: true,
            regex: '',
            field: 'equalLevel',
            label: '8/40h等效声级检测数值[dB(A)]',
          }, {
            require: true,
            regex: '',
            field: 'checkData',
            label: '检测值[dB(A)]',
          }, {
            require: true,
            regex: '',
            field: 'touchLimit',
            label: '职业接触限值',
          }, {
            require: true,
            enum: checkResult,
            field: 'checkResult',
            label: '判定结果',
          }],
        }, {
          require: false,
          regex: '',
          field: 'heatFactors',
          label: '高温检测数据',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'averageValue',
            label: 'WBGT平均值',
          }, {
            require: true,
            regex: '',
            field: 'touchTime',
            label: '接触时间',
          }, {
            require: true,
            regex: '',
            field: 'labourIntensity',
            label: '体力劳动强度',
          }, {
            require: true,
            regex: '',
            field: 'touchLimit',
            label: '职业接触限值（℃）',
          }, {
            require: true,
            enum: checkResult,
            field: 'checkResult',
            label: '判定结果',
          }],
        }, {
          require: false,
          regex: '',
          field: 'handBorneVibrationFactors',
          label: '手传振动',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'dayTouchTime',
            label: '日接触时间',
          }, {
            require: true,
            regex: '',
            field: 'touchLimit',
            label: '接触限值',
          }, {
            require: true,
            regex: '',
            field: 'ahw',
            label: '频率计权振动加速度测量值ahw（m/s2）',
          }, {
            require: true,
            regex: '',
            field: 'fourHoursAccelerated',
            label: '4h等能量频率计权振动加速度值（m/s2）',
          }, {
            require: true,
            enum: checkResult,
            field: 'checkResult',
            label: '判定结果',
          }],
        }, {
          require: false,
          regex: '',
          field: 'highFrequencyEleFactors',
          label: '高频电磁场检测结果',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'radiationHZ',
            label: '辐射频率（MHz）',
          }, {
            require: true,
            regex: '',
            field: 'electricIntensity',
            label: '电场强度限值（V/m）',
          }, {
            require: true,
            regex: '',
            field: 'electricIntensityData',
            label: '电场强度测量值（V/m）/s2）',
          }, {
            require: true,
            regex: '',
            field: 'magneticIntensity',
            label: '磁场强度限值（A/m）',
          }, {
            require: true,
            regex: '',
            field: 'magneticIntensityData',
            label: '磁场强度测量值（A/m）',
          }, {
            require: true,
            enum: checkResult,
            field: 'checkResult',
            label: '判定结果',
          }],
        }, {
          require: false,
          regex: '',
          field: 'laserFactors',
          label: '激光辐射',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'touchLimit',
            label: '接触限值',
          }, {
            require: true,
            regex: '',
            field: 'electricIntensity',
            label: '电场强度限值（V/m）',
          }, {
            require: true,
            regex: '',
            field: 'testAverage',
            label: '平均检测值（W/cm2）',
          }, {
            require: true,
            regex: '',
            field: 'irradiance',
            label: '辐射度',
          }, {
            require: true,
            enum: checkResult,
            field: 'checkResult',
            label: '判定结果',
          }],
        }, {
          require: false,
          regex: '',
          field: 'microwaveFactors',
          label: '微波辐射',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'shortTimeContactPowerDensity',
            label: '短时间接触功率密度最大值（mW/cm²）',
          }, {
            require: true,
            regex: '',
            field: 'shortTimeLimit',
            label: '短时间接触功率密度接触限值（mW/cm²）',
          }, {
            require: true,
            regex: '',
            field: 'average',
            label: '平均值（μW/cm²）',
          }, {
            require: true,
            regex: '',
            field: 'averagePowerDensityLimit',
            label: '8h平均功率密度接触限值（μW/cm²）',
          }, {
            require: true,
            enum: checkResult,
            field: 'checkResult',
            label: '判定结果',
          }],
        }, {
          require: false,
          regex: '',
          field: 'powerFrequencyElectric',
          label: '工频电场',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'electricIntensity',
            label: '电场强度测量值（kV/m）',
          }, {
            require: true,
            regex: '',
            field: 'electricIntensityLimit',
            label: '电场强度职业接触限值（kV/m）',
          }, {
            require: true,
            enum: checkResult,
            field: 'checkResult',
            label: '判定结果',
          }],
        }, {
          require: false,
          regex: '',
          field: 'SiO2Factors',
          label: '游离二氧化硅',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'checkProject',
            label: '危害因素名称',
          }, {
            require: true,
            regex: '',
            field: 'projectCode',
            type: 'String',
            label: '检测项目危害因素编码',
          }, {
            require: true,
            regex: '',
            field: 'checkResult',
            label: '判定结果',
          }],
        }, {
          require: false,
          regex: '',
          field: 'ultraHighRadiationFactors',
          label: '超高频辐射',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'electricAverage',
            label: '脉冲波电场强度平均值（V/m）',
          }, {
            require: true,
            regex: '',
            field: 'eightHoursTouchLimit',
            label: '8h职业接触限值（V/m）',
          }, {
            require: true,
            enum: checkResult,
            field: 'checkResult',
            label: '判定结果',
          }],
        }, {
          require: false,
          regex: '',
          field: 'ultravioletFactors',
          label: '紫外辐射',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'irradiance',
            label: '有效辐照度（μW/cm²）',
          }, {
            require: true,
            regex: '',
            field: 'eightHoursTouchLimit',
            label: '8h职业接触限值（μW/cm²）',
          }, {
            require: true,
            enum: checkResult,
            field: 'checkResult',
            label: '判定结果',
          }],
        }, {
          require: false,
          regex: '',
          field: 'ionizatioSourceFactors',
          label: '电离辐射-含源装置',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'checkProject',
            label: '检测项目',
          }, {
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'device',
            label: '装置名称',
          }, {
            require: true,
            regex: '',
            field: 'sourceNum',
            label: '放射源编号',
          }, {
            require: true,
            regex: '',
            field: 'markNum',
            label: '标号',
          }, {
            require: true,
            regex: '',
            field: 'nuclide',
            label: '核素',
          }, {
            require: true,
            regex: '',
            field: 'installAddress',
            label: '安装位置',
          }, {
            require: true,
            regex: '',
            field: 'curActivity',
            label: '当前活度',
          }, {
            require: true,
            regex: '',
            field: 'deliveryActivity',
            label: '出厂活度',
          },
          {
            require: true,
            regex: '',
            field: 'checkAddress',
            label: '检测点位置',
          }, {
            require: true,
            regex: '',
            field: 'checkResultValue',
            label: '检测结果（μSv/h）',
          }, {
            require: true,
            enum: checkResult,
            field: 'checkResult',
            label: '判定结果',
          }],
        }, {
          require: false,
          regex: '',
          field: 'ionizatioRadialFactors',
          label: '电离辐射-射线装置',
          type: 'Array',
          content: [{
            require: true,
            regex: '',
            field: 'checkProject',
            label: '检测项目',
          }, {
            require: true,
            regex: '',
            field: 'workType',
            label: '工种',
          }, {
            require: true,
            regex: '',
            field: 'workspace',
            label: '车间',
          }, {
            require: true,
            regex: '',
            field: 'station',
            label: '岗位',
          }, {
            require: true,
            regex: '',
            field: 'workTimeDay',
            label: '每天接触时间',
          }, {
            require: true,
            regex: '',
            field: 'workDayWeek',
            label: '每周接触天数',
          }, {
            require: true,
            regex: '',
            field: 'device',
            label: '装置名称',
          }, {
            require: true,
            regex: '',
            field: 'model',
            label: '型号',
          }, {
            require: true,
            regex: '',
            field: 'ratedCapacity',
            label: '额定容器',
          }, {
            require: true,
            regex: '',
            field: 'condition',
            label: '检测条件',
          }, {
            require: true,
            regex: '',
            field: 'checkAddress',
            label: '检测点位置',
          }, {
            require: true,
            regex: '',
            field: 'checkResultValue',
            label: '检测结果（μSv/h）',
          }, {
            require: true,
            enum: checkResult,
            field: 'checkResult',
            label: '判定结果',
            lastField: true,
          }],
        }],
      },
    ];
  }
  /**
   * <AUTHOR>
   * @description 非对象数组校验处理
   * @param {Array} array 校验数据
   * @param {Number} dimension 数组维度
   * @param {Object} fieldItemInfo 校验规则
   * @return {String} 检验结果
   * createdAt 2022-04-11
   */
  handleArrayVaild(array, dimension, fieldItemInfo) {
    let res = '';
    if (dimension === 1) {
      if (!array.length) {
        res = '不能为空';
        return res;
      }
      if (fieldItemInfo.eqLength && array.length !== fieldItemInfo.eqLength) {
        res = '请传入长度为' + fieldItemInfo.eqLength + '的数组';
        return res;
      }
      if (fieldItemInfo.min && fieldItemInfo.max && (fieldItemInfo.min > array.length || fieldItemInfo.max < array.length)) {
        res = '请传入长度为' + fieldItemInfo.min + '到' + fieldItemInfo.max + '的数组';
        return res;
      }
      array.forEach(item => {
        if (fieldItemInfo.enum && fieldItemInfo.enum.indexOf(item) === -1) {
          res = '请输入在' + fieldItemInfo.enum + '中的字符';
          return res;
        }
        if (fieldItemInfo.require && !item) {
          res = '有空元素';
          return res;
        }
        if (fieldItemInfo.regex && !fieldItemInfo.regex.test(item)) {
          res = fieldItemInfo.regexMsg;
        }
      });
    } else {
      array.forEach(item => {
        if (!(item instanceof Array)) {
          res = '数据结构错误';
        } else {
          res = this.handleArrayVaild(item, --dimension, fieldItemInfo);
        }
      });
    }
    return res;
  }

  // 处理校验信息
  handleMessage(item, fields, errMsgs = [], isArray = false) {
    let field = '';
    if (isArray) {
      // 对象数组
      item.forEach(item3 => {
        this.handleMessage(item3, fields, errMsgs);
      });
    } else {
      fields.forEach(fieldItemInfo => {
        field = fieldItemInfo.field; // 字段
        if (fieldItemInfo.enum) {
          if (fieldItemInfo.type !== 'Array') {
            if (fieldItemInfo.enum.indexOf(item[field]) === -1) {
              errMsgs.push(fieldItemInfo.label + '，请输入在' + fieldItemInfo.enum + '中的字符');
            }
          } else {
            if (fieldItemInfo.require && !item[field]) {
              errMsgs.push(fieldItemInfo.label + '不能为空');
            }
          }
        }
        if (!fieldItemInfo.enum) {
          if (fieldItemInfo.require && !item[field]) {
            errMsgs.push(fieldItemInfo.label + '不能为空');
          } else if (item[field] && fieldItemInfo.type) {
            if (fieldItemInfo.type === 'Date') {
              item[field] = new Date(item[field]);
            }
            if (Object.prototype.toString.call(item[field]) !== `[object ${fieldItemInfo.type}]`) {
              errMsgs.push(field + ' require ' + fieldItemInfo.type + ' but ' + typeof item[field] + ' at ' + item[field]);
            }
          }
        }
        if (item[field]) {
          // 非对象数组
          if (fieldItemInfo.type === 'Array') {
            const arrayVaild = this.handleArrayVaild(item[field], fieldItemInfo.dimension || 1, fieldItemInfo);
            if (arrayVaild) {
              errMsgs.push(fieldItemInfo.label + arrayVaild);
            }
          } else if (fieldItemInfo.regex && !fieldItemInfo.regex.test(item[field])) {
            errMsgs.push(fieldItemInfo.label + fieldItemInfo.regexMsg);
          }
        }
        if (item[field] && fieldItemInfo.content) {
          // if (!fieldItemInfo.parentLabel) { // 清空祖先节点
          //   parentField = parentField.replace(new RegExp(`${fieldItemInfo.parentLabel}|-${fieldItemInfo.parentLabel}`),'');
          //   // parentField = ''
          // }
          // parentField += !parentField ? fieldItemInfo.label : '-' + fieldItemInfo.label;
          this.handleMessage(item[field], fieldItemInfo.content, errMsgs, fieldItemInfo.type === 'Array');
        }
      });
    }
    return errMsgs;
  }


  /**
   * <AUTHOR>
   * @param {Object} item 检测报告数据
   * @description 校验其他检测机构平台检测报告数据
   * createAt 2022-03-14
   */
  async checkReportData(item) {
    // let errMsgs = [];
    const { writeOrgInfo } = item;
    if (!writeOrgInfo || !writeOrgInfo.creditCode) {
      return { errMsgs: [ '检测机构统一信用代码为空' ] };
    }
    const { creditCode } = writeOrgInfo;
    let errMsgs = [];
    // 查询机构是否审核通过
    let serviceOrgInfo = {};
    serviceOrgInfo = await this.ctx.model.ServiceOrg.findOne({ organization: creditCode });

    if (!serviceOrgInfo) {
      return { errMsgs: [ '机构暂未注册' ] };
    }
    if (serviceOrgInfo.status !== 3) {
      return { errMsgs: [ '该检测机构暂未通过审核，请联系平台管理员审核' ] };
    }
    const { config } = this.ctx.app;
    const fields = this.postJobhealthValidate(config);
    const res = this.handleMessage(item, fields, []);
    errMsgs = errMsgs.concat(res);
    if (errMsgs.length) {
      return { errMsgs };
    }
    return serviceOrgInfo;
  }

  // 创建报告相关数据数据
  async createReportData(params, serviceOrgInfo) {
    const { ctx } = this;
    let detectionPointInfo = params.detectionPointInfo;
    const { empInfo, writeOrgInfo, reportInfo, entrustOrgInfo } = params;
    const { service } = this;
    // 用人单位
    // 处理地址
    let item = {};
    let item2 = {};
    // jobhealth workPlaces
    const workPlaces = [];
    let workPlaceItem = {};
    const declareAdds = [];
    const superUserID = [];
    for (let i = 0; i < empInfo.workAddress.length; i++) {
      item = empInfo.workAddress[i];
      workPlaceItem = {
        workAdd: [],
        name: item.address,
        checked: true,
      };
      for (let j = 0; j < item.districts.length; j++) {
        item2 = item.districts[j];
        // 编码转中文
        const area_code = await ctx.model.District.findOne({ area_code: item2 });
        if (!area_code) {
          return '企业技术服务地址编码 ' + item2 + ' 错误，请检查';
        }
        workPlaceItem.workAdd.push(item.districts[j]);
        item.districts[j] = area_code.name;
        if (j === 2) {
          const superUserInfo = await ctx.model.SuperUser.findOne({
            area_code: item2.substr(0, 6),
          });
          superUserInfo && superUserID.push(superUserInfo._id);
          declareAdds.push(item2.substr(0, 6));
        }
      }
      workPlaceItem.workAddName = item.districts.join('/');
      workPlaces.push(workPlaceItem);
    }
    for (let i = 0; i < empInfo.districtRegAdd.length; i++) {
      item = empInfo.districtRegAdd[i];
      const area_code = await ctx.model.District.findOne({ area_code: item });
      if (!area_code) {
        return '企业注册地区编码 ' + item2 + ' 错误，请检查';
      }
      empInfo.districtRegAdd[i] = area_code.name;
    }
    if (!await ctx.model.EconomicTypes.findOne({ code: empInfo.economicTypeCode })) {
      return '企业经济类型编码 ' + empInfo.economicTypeCode + ' 不存在，请检查';
    }
    // 创建用人单位
    const adminorgInfo = await service.adminorg.createAdminOrg({
      serviceID: [{
        _id: serviceOrgInfo._id, // 机构id
        EnterpriseContractName: empInfo.contactPerson, // 联系人
        EnterprisePhoneNum: empInfo.contactPhone, // 联系电话
      }],
      isactive: '1',
      leadIn: '4',
      cname: empInfo.empName,
      workAddress: empInfo.workAddress,
      regAdd: empInfo.regAdd,
      code: empInfo.creditCode,
      enterpriseId: empInfo.enterpriseId,
      regType: empInfo.economicTypeCode,
      industryCategory: empInfo.industryCategoryCode[0],
      companyScale: empInfo.companyScale,
      districtRegAdd: empInfo.districtRegAdd,
      corp: empInfo.legalPerson || '', // 法人
      contract: empInfo.contactPerson, // 联系人
      phoneNum: empInfo.contactPhone, // 联系电话
    }, reportInfo.reportTime, serviceOrgInfo._id);

    if (adminorgInfo && adminorgInfo.status) {
      return adminorgInfo.message;
    }

    // 创建jobhealth项目信息
    const serviceType = {
      10: {
        type: '职业病危害因素检测',
        category: '1',
      },
      20: {
        type: '职业病危害因素监督检测',
        category: '1',
      },
      31: {
        type: '职业病危害现状评价',
        category: '2',
      },
      32: {
        type: '职业病危害控制效果评价',
        category: '3',
      },
      33: {
        type: '职业病危害预评价',
        category: '2',
      },
    };
    const serviceMatters = [];
    const personsOfProject = [];

    let personOfProject = {};
    let personOfProjectItem = {};
    for (let i = 0; i < reportInfo.participantList.length; i++) {
      personOfProjectItem = reportInfo.participantList[i];
      personOfProject = await ctx.model.ServiceEmployee.findOne({ EnterpriseID: serviceOrgInfo._id, name: personOfProjectItem.name });
      if (!personOfProject) {
        // 创建ServiceEmployee
        personOfProject = await ctx.model.ServiceEmployee.create({ EnterpriseID: serviceOrgInfo._id, name: personOfProjectItem.name });
      }
      personsOfProject.push(personOfProject._id);
      serviceMatters.push(personOfProjectItem.serviceItemList);
    }
    if (!reportInfo.personInCharge && !reportInfo.EvaluationProjectManagerAdmin) {
      return '项目经理不能为空';
    }
    // 检测组经理
    let personInCharge = reportInfo.personInCharge;
    const personInChargeName = personInCharge;
    if (personInCharge) {

      const personInChargeList = await ctx.model.ServiceEmployee.find({ EnterpriseID: serviceOrgInfo._id, name: personInChargeName });
      if (personInChargeList.length === 1) {
        personInCharge = personInChargeList[0];
      } else if (personInChargeList.length > 1) {
        console.log(personInChargeName + '检测组经理存在多个，请检查!!!');
        personInCharge = personInChargeList.find(item => personsOfProject.includes(item._id));
        if (!personInCharge) {
          console.log(111111, personInChargeList);
          console.log(222222, personsOfProject);
          return '请将检测经理添加到参与人员列表中';
        }
      } else {
        return '参与人员需包括检测经理，请将检测经理添加到参与人员中';
      }
      if (personsOfProject.indexOf(personInCharge._id) === -1) {
        console.log(333333, personInChargeList);
        console.log(444444, personsOfProject);
        return '请将检测经理添加到参与人员中';
      }
      personInCharge = personInCharge._id;
      // if (!personInCharge) {
      // // 创建ServiceEmployee
      //   personInCharge = await ctx.model.ServiceEmployee.create({ name: personInChargeName, EnterpriseID: serviceOrgInfo._id });
      // }
      // personInCharge = personInCharge._id;
      // if (personsOfProject.indexOf(personInCharge) === -1) {
      //   personsOfProject.push(personInCharge);
      //   serviceMatters.push([]);
      // }
    }

    // 评价组经理
    let EvaluationProjectManagerAdmin = reportInfo.EvaluationProjectManagerAdmin;
    const EvaluationProjectManagerAdminName = EvaluationProjectManagerAdmin;
    if (EvaluationProjectManagerAdmin) {

      EvaluationProjectManagerAdmin = await ctx.model.ServiceEmployee.findOne({ EnterpriseID: serviceOrgInfo._id, name: EvaluationProjectManagerAdminName });
      if (EvaluationProjectManagerAdmin) {
        if (personsOfProject.indexOf(EvaluationProjectManagerAdmin) === -1) {
          return '参与人员需包括评价经理，请将评价经理添加到参与人员中';
        }
        EvaluationProjectManagerAdmin = EvaluationProjectManagerAdmin._id;
      } else {
        return '参与人员需包括评价经理，请将评价经理添加到参与人员中';
      }

      // if (!EvaluationProjectManagerAdmin) {
      // // 创建ServiceEmployee
      //   EvaluationProjectManagerAdmin = await ctx.model.ServiceEmployee.create({ name: EvaluationProjectManagerAdminName, EnterpriseID: serviceOrgInfo._id });
      // }
      // EvaluationProjectManagerAdmin = EvaluationProjectManagerAdmin._id;
      // if (personsOfProject.indexOf(EvaluationProjectManagerAdmin) !== -1) {
      //   personsOfProject.push(EvaluationProjectManagerAdmin);
      //   serviceMatters.push([]);
      // }
    }
    const issuer = { name: reportInfo.issuer };
    let issuerInfo = await ctx.model.ServiceEmployee.findOne({ name: reportInfo.issuer, EnterpriseID: serviceOrgInfo._id });
    if (!issuerInfo) {
      issuerInfo = await ctx.model.ServiceEmployee.create({
        name: reportInfo.issuer, EnterpriseID: serviceOrgInfo._id,
      });
    }
    issuer.employeeId = issuerInfo._id;
    const jobHealthInfo = {
      source: 'report',
      // report: reportInfo.reports,
      serviceArea: empInfo.serviceAreaCode,
      workPlaces,
      projectNumber: reportInfo.projectNumber,
      projectName: reportInfo.projectName || '',
      serviceType: serviceType[reportInfo.serviceType].type,
      issueDate: reportInfo.issueDate,
      issuer,
      investigationTime: reportInfo.investigationTime,
      reportTime: reportInfo.reportTime,
      year: reportInfo.year,
      personsOfProject,
      serviceMatters,
      serviceOrgID: writeOrgInfo.creditCode,
      serviceID: serviceOrgInfo._id,
      EnterpriseID: adminorgInfo._id,
      companyID: adminorgInfo.code,
      companyContact: empInfo.contactPerson,
      companyName: adminorgInfo.cname,
      companyAddress: empInfo.workAddress,
      companyScale: empInfo.companyScale,
      companyContactPhoneNumber: empInfo.contactPhone,
      // anthemCompanyContact: empInfo.contactPerson,
      // anthemCompanyContactPhoneNumber: empInfo.contactPhone,
      anthemCompanyID: entrustOrgInfo.creditCode,
      anthemCompanyName: entrustOrgInfo.entrustOrgName,
      entrustDate: entrustOrgInfo.entrustDate,
      preparer: reportInfo.preparer,
      preparerPhone: reportInfo.preparerPhone,
      personInCharge,
      EvaluationProjectManagerAdmin,
      date: new Date(),
      companyIndustry: empInfo.industryCategoryCode,
    };
    let jobhealthInfo = {};
    if (await ctx.model.JobHealth.findOne({ projectNumber: reportInfo.projectNumber })) {
      jobhealthInfo = await ctx.model.JobHealth.findOneAndUpdate({ projectNumber: reportInfo.projectNumber }, { $set: jobHealthInfo }, { new: true });
    } else {
      jobhealthInfo = await ctx.model.JobHealth.create(jobHealthInfo);
    }


    // 创建检测结果信息
    const checkAssessment = {
      chemistryFactors: {
        name: '',
      },
      dustFactors: {
        name: '',
      },
      noiseFactors: {
        name: '噪声',
      },
      biologicalFactors: {
        name: '',
      },
      heatFactors: {
        name: '高温',
      },
      handBorneVibrationFactors: {
        name: '手传振动',
      },
      highFrequencyEleFactors: {
        name: '高频电磁场',
      },
      laserFactors: {
        name: '激光',
      },
      microwaveFactors: {
        name: '微波',
      },
      powerFrequencyElectric: {
        name: '工频电磁场',
      },
      // SiO2Factors: {},
      ultraHighRadiationFactors: {
        name: '超高频辐射',
      },
      ultravioletFactors: {
        name: '紫外线',
      },
      // ionizatioSourceFactors: {
      //   name: '电离辐射',
      //   noWorkspaceAndStation:false,
      // },
      // ionizatioRadialFactors: {
      //   name: '电离辐射',
      //   noWorkspaceAndStation:false,
      // },
    };
    detectionPointInfo = JSON.parse(JSON.stringify(detectionPointInfo));
    detectionPointInfo = await this.parseCheckAssessmentsData(detectionPointInfo);
    if (detectionPointInfo.errMsgs.length) {
      return detectionPointInfo.errMsgs;
    }
    detectionPointInfo = detectionPointInfo.data;
    Object.keys(detectionPointInfo).forEach(item => {
      detectionPointInfo[item] = {
        formData: detectionPointInfo[item],
      };
    });
    detectionPointInfo.category = serviceType[reportInfo.serviceType].category;
    detectionPointInfo.jobHealthId = jobhealthInfo._id;
    detectionPointInfo.EnterpriseID = adminorgInfo._id;
    detectionPointInfo.source = 'report';
    if (await ctx.model.CheckAssessment.findOne({ jobHealthId: jobhealthInfo._id })) {
      await ctx.model.CheckAssessment.updateOne({ jobHealthId: jobhealthInfo._id }, { $set: detectionPointInfo });
    } else {
      await ctx.model.CheckAssessment.create(detectionPointInfo);
    }
    const EnterpriseID = adminorgInfo._id;
    // 更新车间岗位
    const checkAssessmentKeys = Object.keys(checkAssessment);
    for (let i = 0; i < checkAssessmentKeys.length; i++) {
      item = checkAssessmentKeys[i];
      if (detectionPointInfo[item]) {
        await this.updateMill(detectionPointInfo[item].formData, EnterpriseID, item.name);
      }
    }
    // 如果是控评的话 标记这一次危害检测已完成
    if (serviceType[reportInfo.serviceType].category === '32') {
      const jobHealth = await ctx.model.JobHealth.findOne({ _id: params.jobHealthId });
      await ctx.model.JobHealth.updateOne({ _id: params.jobHealthId }, { $set: { completeStatus: 1, completeTime: jobHealth.reportTime } });
    }
    if (params.reportInfo.isPostToSupervise) {
      // 上报到监管
      const jobHealthInfo = await ctx.model.JobHealth.findOneAndUpdate({ projectNumber: params.reportInfo.projectNumber }, {
        $set: {
          superUserID,
          status: 1,
          applyTime: Date.now(),
          declareAdds,
        },
      });
      await this.projectBack(superUserID, [ jobHealthInfo._id ]);
    }
  }

  // 报送回执
  async projectBack(orgIds, projectIds) {
    const { ctx } = this;
    let projectBackType = {};
    let projects = [];
    let superUserInfo = [];
    let projectInfo = {};
    for (let i = 0; i < orgIds.length; i++) {
      projectBackType = await ctx.model.ProjectBackType.findOne({
        superUserID: orgIds[i],
      });
      superUserInfo = await ctx.model.SuperUser.findOne({
        _id: orgIds[i],
      });
      // 获取所有的项目
      projects = await ctx.model.JobHealth.find({
        _id: {
          $in: projectIds,
        },
      });
      for (let j = 0; j < projects.length; j++) {
        if (!projectBackType || (projectBackType && projectBackType.backType === '自动')) { // 该监管部门未设置回执方式，则按手动回执方式回执
          projectInfo = await ctx.model.JobHealth.findOne({
            _id: projects[j]._id,
            'backInfo.superUser': orgIds[i],
          });
          if (!projectInfo) {
            await ctx.model.JobHealth.updateOne({
              _id: projects[j]._id,
            }, {
              $push: {
                backInfo: {
                  superUser: orgIds[i],
                  backTime: new Date(),
                  cname: superUserInfo.cname,
                  name: superUserInfo.name,
                },
              },
            });
          } else {
            await ctx.model.JobHealth.updateOne({
              _id: projects[j]._id,
              'backInfo.superUser': orgIds[i],
            }, {
              $set: {
                'backInfo.$': {
                  superUser: orgIds[i],
                  cname: superUserInfo.cname,
                  name: superUserInfo.name,
                  backTime: new Date(),
                },
              },
            });
          }
          await ctx.model.JobHealth.updateOne({
            _id: projects[j]._id,
          }, {
            $addToSet: {
              'InformationChange.$[].superUser': orgIds[i],
            },
          });
        }
      }
    }
  }

  // 更新或创建工作场所
  async updateMill(data, EnterpriseID, harm) { // 添加检测数据时同步工作场所
    const { ctx } = this;
    let item = {};
    let workspaces = [];
    let stations = [];
    let harmFactor = {};
    let checkProject = '';

    for (let i = 0; i < data.length; i++) {
      item = data[i];
      if (item.workspace && item.station) {
        checkProject = harm ? [ harm ] : [ item.checkProject ];
        // 查询危害因素
        harmFactor = await this.matchHarmFactors(checkProject || []);
        // harmFactor = JSON.parse(JSON.stringify(harmFactor))
        const harmFactors = harmFactor.harmFactors[0] || null;
        const harmFactorName = harmFactor.harmFactors[0] ? harmFactor.harmFactors[0][1] : '';
        workspaces = await ctx.model.MillConstruction.aggregate([
          {
            $match: { EnterpriseID },
          },
          {
            $unwind: {
              path: '$children',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $match: {
              $or: [
                { name: item.workspace, category: 'workspaces' },
                { 'children.name': item.workspace, category: 'mill' },
              ],
            },
          },
        ]);
        if (!workspaces || workspaces.length === 0) { // 不存在该车间
          await ctx.model.MillConstruction.create({
            name: item.workspace,
            EnterpriseID,
            status: '0',
            category: 'workspaces',
            children: [{
              name: item.station.replace(/(\d+)$/g, ''), status: '0', category: 'stations', workType: item.workType,
              harmFactors: harmFactor.harmFactors,
              workTimeDay: item.workTimeDay,
              workDayWeek: item.workDayWeek,
              customizeHarm: harmFactor.customHamrs,
            }],
          });
        } else { // 存在该车间
          for (let j = 0; j < workspaces.length; j++) {
            if (workspaces[j].category === 'mill') {
              stations = await ctx.model.MillConstruction.aggregate([
                { $match: { EnterpriseID } },
                { $unwind: '$children' },
                { $match: { 'children._id': workspaces[j].children._id } },
                { $unwind: '$children.children' },
                { $match: { 'children.children.name': item.station.replace(/(\d+)$/g, '') } },
              ]);
              if (!stations.length) { // 不存在岗位
                await ctx.model.MillConstruction.updateOne({
                  _id: workspaces[j]._id,
                  'children._id': workspaces[j].children._id,
                }, {
                  $addToSet: {
                    'children.$.children': {
                      name: item.station.replace(/(\d+)$/g, ''),
                      category: 'stations',
                      workType: item.workType,
                      status: '0',
                      workTimeDay: item.workTimeDay,
                      workDayWeek: item.workDayWeek,
                      harmFactors: harmFactor.harmFactors,
                      customizeHarm: harmFactor.customHamrs,
                    },
                  },
                });
              } else { // 存在岗位
                await ctx.model.MillConstruction.updateOne({
                  EnterpriseID,
                  _id: workspaces[j]._id,
                  'children._id': workspaces[j].children._id,
                }, {
                  $set: {
                    'children.$[i].children.$[j].workTimeDay': item.workTimeDay,
                    'children.$[i].children.$[j].workDayWeek': item.workDayWeek,
                  },
                }, {
                  arrayFilters: [
                    { 'i._id': workspaces[j].children._id },
                    { 'j.name': item.station.replace(/(\d+)$/g, '') },
                  ],
                });
                if (harmFactors && stations[0].children.children.harmFactors.filter(item2 => item2[1] === harmFactorName).length === 0) {
                  await ctx.model.MillConstruction.updateOne({
                    EnterpriseID,
                    _id: workspaces[j]._id,
                    'children._id': workspaces[j].children._id,
                  }, {
                    $addToSet: {
                      'children.$[i].children.$[j].harmFactors': harmFactors,
                    },
                  }, {
                    arrayFilters: [
                      { 'i._id': workspaces[j].children._id },
                      { 'j.name': item.station.replace(/(\d+)$/g, '') },
                    ],
                  });
                }
              }
            } else if (workspaces[j].category === 'workspaces') {
              stations = await ctx.model.MillConstruction.aggregate([
                { $match: { EnterpriseID } },
                { $match: { _id: workspaces[j]._id } },
                { $unwind: '$children' },
                { $match: { 'children.name': item.station.replace(/(\d+)$/g, '') } },
              ]);
              stations = JSON.parse(JSON.stringify(stations));
              if (!stations || stations.length === 0) { // 不存在岗位
                await ctx.model.MillConstruction.findOneAndUpdate({ EnterpriseID, _id: workspaces[j]._id }, { $addToSet: { children: {
                  name: item.station.replace(/(\d+)$/g, ''),
                  category: 'stations',
                  status: '0',
                  workType: item.workType,
                  workTimeDay: item.workTimeDay,
                  workDayWeek: item.workDayWeek,
                  harmFactors: harmFactor.harmFactors,
                  customizeHarm: harmFactor.customHamrs,
                } } }, { new: true });
              } else { // 存在岗位
                await ctx.model.MillConstruction.updateOne({
                  EnterpriseID,
                  _id: workspaces[j]._id,
                  'children._id': workspaces[j].children._id,
                }, {
                  $set: {
                    'children.$[i].workTimeDay': item.workTimeDay,
                    'children.$[i].workDayWeek': item.workDayWeek,
                  },
                }, {
                  arrayFilters: [
                    { 'i._id': workspaces[j].children._id },
                    { 'j.name': item.station.replace(/(\d+)$/g, '') },
                  ],
                });
                // 插入危害因素
                if (harmFactors && stations[0].children.harmFactors.filter(item2 => item2[1] === harmFactorName).length === 0) {
                  await ctx.model.MillConstruction.updateOne({
                    EnterpriseID,
                    _id: workspaces[j]._id,
                    'children._id': workspaces[j].children._id,
                  }, {
                    $addToSet: {
                      'children.$[i].harmFactors': harmFactors,
                    },
                  }, {
                    arrayFilters: [
                      { 'i._id': workspaces[j].children._id },
                    ],
                  });
                }

              }
            }
          }
        }
      }
    }
  }

  // 根据危害因素名称数组匹配危害因素
  async matchHarmFactors(harmFactors) {
    const arr = [];// 匹配到的结果
    const matchHarms = [];
    let customHamrs = [];
    if (harmFactors[0]) {
      if (harmFactors[0] instanceof Array) {
        harmFactors = harmFactors.map(item => item[1]);
      } else {
        harmFactors = harmFactors.map(item => {
          item = item.replace(/（总尘）|（总）|\(总\)|（呼）|\(呼\)|（总粉尘）|\(总粉尘\)|（呼吸性粉尘）|\(呼吸性粉尘\)|（呼尘）|\(总尘\)|\(呼尘\)/gi, '');
          return item;
        });
      }
      const res = await this.ctx.model.OccupationalexposureLimits.aggregate([
        { $unwind: '$chineseName' },
        { $match: { chineseName: { $in: harmFactors } } },
      ]);
      if (harmFactors[0] instanceof Array) {
        res.forEach(item => {
          arr.push([ item.catetory, item.showName ]);
        });
      } else {
        res.forEach(item => {
          matchHarms.push(item.showName);
          arr.push([ item.catetory, item.showName ]);
        });
        // 差集
        customHamrs = harmFactors.filter(item => !(new Set(matchHarms)).has(item));
      }
    }
    return { harmFactors: arr, customHamrs: customHamrs.join('、') };
  }

  // 处理检测结果数据
  async parseCheckAssessmentsData(params) {
    const errMsgs = [];
    const { ctx } = this;
    const checkResultTxts = [ '符合', '合格' ];
    let noExceedCount = 0; // 一条检测结果中符合数量
    let percent = 0;
    const categorys = [
      { category: 'chemistryFactors' },
      { category: 'dustFactors' },
      { category: 'biologicalFactors' },
      { category: 'noiseFactors' },
      { category: 'handBorneVibrationFactors' },
      { category: 'heatFactors' },
      { category: 'highFrequencyEleFactors' },
      { category: 'laserFactors' },
      { category: 'microwaveFactors' },
      { category: 'powerFrequencyElectric' },
      { category: 'ultraHighRadiationFactors' },
      { category: 'ionizatioSourceFactors' },
      { category: 'ionizatioRadialFactors' },
    ];
    let item = {};
    let item2 = {};
    let item3 = {};
    for (let i = 0; i < categorys.length; i++) {
      item = categorys[i];
      if (!params[item.category]) {
        continue;
      }
      if (item.category === 'chemistryFactors') {
        let touchLimit = {};
        let checkData = 0;
        let checkItem = 'MAC';
        let limitCheckItem = 'MAC';

        if (!params.dustFactors) {
          params.dustFactors = [];
        }
        // params.dustFactors =
        //   params.dustFactors.concat(params.chemistryFactors.filter(item2 => item2.checkProject.search(/总尘|呼尘|呼吸性粉尘|总粉尘|（总）|（呼）|\(总\)|\(呼\)/) !== -1));
        // params.chemistryFactors = params.chemistryFactors.filter(item2 => item2.checkProject.search(/总尘|呼尘|呼吸性粉尘|总粉尘|（总）|（呼）|\(总\)|\(呼\)/) === -1);
        for (let j = 0; j < params.chemistryFactors.length; j++) {
          item2 = params.chemistryFactors[j];
          item2.projectCode = item2.projectCode + '';
          if (await ctx.model.OccupationalexposureLimits.findOne({ catetory: '粉尘', projectCode: item2.projectCode })) {
            params.dustFactors.push(item2);
            params.chemistryFactors.splice(j--, 1);
          } else {
            touchLimit = await ctx.model.OccupationalexposureLimits.findOne({
              projectCode: item2.projectCode,
            });
            percent = 0;
            item2.percent = 0;
            noExceedCount = 0;
            if (touchLimit) {
              item2.touchLimitMAC = touchLimit.MAC || '';
              item2.touchLimitTWA = touchLimit.PC_TWA || '';
              item2.touchLimitSTEL = touchLimit.PC_STEL || '';
              item2.touchLimitPE = touchLimit.PE || '';
              const touchLimitMACNumber = this.getNumber(item2.touchLimitMAC);
              const touchLimitTWANumber = this.getNumber(item2.touchLimitTWA);

              if (touchLimitMACNumber) {
                checkItem = 'MAC';
                limitCheckItem = Number(touchLimitMACNumber);
              } else {
                checkItem = 'TWA';
                limitCheckItem = Number(touchLimitTWANumber);
              }
            } else {
              errMsgs.push(`检测项目危害因素编码${item2.projectCode}错误`);
            }
            for (let j = 0; j < item2.checkResults.length; j++) {
              item3 = item2.checkResults[j];
              if (touchLimit) {
                checkItem = item3[checkItem];
                if (checkItem) {
                  if ((checkItem + '').search(/<|＜/) !== -1) {
                    checkData = Number(this.handleFE(this.getNumber(checkItem)).data) / 2;
                  } else {
                    checkData = Number(this.handleFE(this.getNumber(checkItem)).data);
                  }
                } else {
                  checkData = 0;
                }
                percent = limitCheckItem ? (checkData / limitCheckItem).toFixed(4) : 0;
                percent = +percent;
                item2.percent = Math.max(item2.percent, percent);
              }
              if (checkResultTxts.indexOf(item3.checkResultItem) !== -1) {
                noExceedCount++;
              }
            }
            item2.checkResult = noExceedCount === item2.checkResults.length ? '符合' : '不符合';
            if (item2.percent <= 0.01) {
              item2.level = '0';
            } else if (item2.percent <= 0.1) {
              item2.level = 'I';
            } else if (item2.percent <= 0.5) {
              item2.level = 'II';
            } else if (item2.percent <= 1) {
              item2.level = 'III';
            } else {
              item2.level = 'IV';
            }
          }
        }
        if (!params.chemistryFactors.length) delete params.chemistryFactors;
      }
      if (item.category === 'SiO2Factors') {
        for (let j = 0; j < params.SiO2Factors.length; j++) {
          item2 = params.SiO2Factors[j];
          item2.projectCode = item2.projectCode + '';
          const touchLimit = await ctx.model.OccupationalexposureLimits.findOne({ projectCode: item2.projectCode });
          if (touchLimit) {
            item2.checkType = Number(this.getNumber(item2.checkResult)) >= 0.1 ? '矽尘' : (item2.checkProject || '');
          } else {
            errMsgs.push(`检测项目危害因素编码${item2.projectCode}错误`);
          }
        }
      }
      if (item.category === 'dustFactors') {
        let touchLimit = {};
        let checkData = 0;
        let item2 = {};
        // let checkProject = '';
        for (let j = 0; j < params.dustFactors.length; j++) {
          item2 = params.dustFactors[j];
          item2.projectCode = item2.projectCode + '';
          // checkProject = item2.checkProject || '';
          // checkProject = checkProject.replace(/（总尘）|（总）|\(总\)|（呼）|\(呼\)|（总粉尘）|\(总粉尘\)|（呼吸性粉尘）|\(呼吸性粉尘\)|（呼尘）|\(总尘\)|\(呼尘\)/gi, '');
          touchLimit = await ctx.model.OccupationalexposureLimits.findOne({ projectCode: item2.projectCode });
          percent = 0;
          item2.percent = 0;
          noExceedCount = 0;
          if (touchLimit) {
            if (item2.checkProject.search(/呼尘|呼吸性粉尘|（呼）|\(呼\)/) === -1) {
              item2.TWALimit = touchLimit.PC_TWA;
              item2.PELimit = touchLimit.PE || '';
            } else {
              item2.TWALimit = touchLimit.respirableDust_TWA;
              item2.PELimit = touchLimit.respirableDust_PE || '';
            }
          } else {
            errMsgs.push(`检测项目危害因素编码${item2.projectCode}错误`);
          }
          for (let j = 0; j < item2.checkResults.length; j++) {
            item3 = item2.checkResults[j];
            if (item3.TWA) {
              if ((item3.TWA + '').search(/<|＜/) !== -1) {
                checkData = Number(this.handleFE(this.getNumber(item3.TWA + '')).data) / 2;
              } else {
                checkData = Number(this.handleFE((item3.TWA + '')).data);
              }
            } else {
              checkData = 0;
            }
            if (checkResultTxts.indexOf(item3.checkResultItem) !== -1) {
              noExceedCount++;
            }
            percent = Number(this.getNumber(item2.TWALimit)) ? (checkData / Number(this.getNumber(item2.TWALimit))).toFixed(4) : 0;
            percent = +percent;
            item2.percent = Math.max(percent, item2.percent);
          }
          item2.checkResult = noExceedCount === item2.checkResults.length ? '符合' : '不符合';
          if (item2.percent <= 0.01) {
            item2.level = '0';
          } else if (item2.percent <= 0.1) {
            item2.level = 'I';
          } else if (item2.percent <= 0.5) {
            item2.level = 'II';
          } else if (item2.percent <= 1) {
            item2.level = 'III';
          } else {
            item2.level = 'IV';
          }
        }
      }
      if (item.category === 'biologicalFactors') {
        for (let j = 0; j < params.biologicalFactors.length; j++) {
          item2 = params.biologicalFactors[j];
          item2.projectCode = item2.projectCode + '';
          if (!await ctx.model.OccupationalexposureLimits.findOne({ projectCode: item2.projectCode })) {
            errMsgs.push(`检测项目危害因素编码${item2.projectCode}错误`);
          }
        }

      }
      params[item.category].forEach(item2 => {
        item2.checkAddress = this.getCheckAddress(item2.workspace, item2.station, item2.checkAddress);
      });
    }
    return { data: params, errMsgs };
  }

  // 拼接车间岗位得到检测点位置
  getCheckAddress(workspace, station, checkAddress) {
    if (checkAddress) {
      return checkAddress;
    }
    if (workspace.indexOf('车间') === -1) {
      workspace = workspace + '车间';
    }
    return workspace + station;
  }

  // 处理科学计数法
  handleFE(data) {
    data = data + '';
    data = this.getNumber(data) + '';
    const originData = data;
    let splitOne = '';
    let splitTwo = '';
    if (data.search(/\*|x|×/) !== -1) { // 如果存在*
      splitOne = data.split(/\*|x|×/);
      if (/^10/.test(splitOne[1])) {
        splitTwo = splitOne[1].split(/^10/);
        data = Number(splitOne[0]) * Math.pow(10, Number(splitTwo[1].replace('^', '')));
      } else {
        data = Number(splitOne[0]) * Number(splitOne[1]);
      }
      return { data, originData };
    }
    return { data: Number(data), originData };
  }

  // 字符串中截取数字
  getNumber(str) {
    if (!str) return '';
    str = str.match(/[-+]?[0-9]*\.?[0-9\*x×Ee^-]+/);
    if (str) {
      str = str[0];
    } else {
      str = '';
    }
    return str;
  }

  // 更新预警
  async updateHealthCheck(data) { // data 传healthCheckId或者projectDetail都行
    const { ctx } = this;
    let newProject,
      healthCheckId;
    if (typeof data === 'string') { // 传的是 healthCheckId
      healthCheckId = data;
      newProject = await ctx.model.Healthcheck.findOne({ _id: healthCheckId });
    } else if (typeof data === 'object') { // 传的是 newProject
      newProject = data;
      healthCheckId = newProject._id;
    } else {
      return '体检项目预警判断传的数据类型不对';
    }
    if (newProject && newProject.EnterpriseID) {
      // 体检项目更新之后,同步处理企业表中的healcheckInfo字段
      ctx.service.healthcheck.dealWithHealcheckInfo(newProject);
    }
    if (!newProject._id || !newProject.reportStatus) return;
    if (newProject.checkType === '0') return '岗前体检不生成预警';
    const checkDate = newProject.checkDate ? moment(newProject.checkDate).format('YYYY年MM月DD日') : newProject.year + '年'; // 体检日期
    const year = new Date((newProject.year + '') || newProject.checkDate).getFullYear();
    if (year && (year < 2021)) return '2021年以前的数据不生成预警';

    const checkType = [ '上岗前', '在岗', '离岗', '复查', '应急' ][+newProject.checkType] || ''; // 体检类型
    let warningLever = 4; // 预警等级，假设是四级
    const desArr = []; // 预警描述

    if (+newProject.re_examination >= 1) { // 复查
      desArr.push(`复查者${newProject.re_examination}名，请及时安排复查`);
    }
    if (+newProject.forbid >= 1) { // 禁忌症
      warningLever = 3;
      desArr.push(`禁忌证${newProject.forbid}名，请及时安排调岗`);
    }
    if (+newProject.suspected >= 1) { // 有疑似职业病案例1例及以上
      warningLever = 2;
      desArr.push(`疑似职业病患者${newProject.suspected}名，请及时安排诊断`);
    }
    const warningList = desArr.length ? [ `${checkDate}的${checkType}职业健康检查结果中发现 ${desArr.join('；')}。` ] : []; // 预警内容

    const oldWarning = await ctx.model.Warning.findOne({
      jobHealthId: healthCheckId,
      delete: false,
      status: { $ne: 3 }, // 已解除的预警不能修改
    });

    const workAddress = await this.getWorkAddress(newProject);
    if (oldWarning) {
      // 修改预警
      return await ctx.service.warning.update(oldWarning._id, warningLever, warningList, workAddress, newProject.physicalExaminationOrgID);
    }
    if (warningList.length) {
      // 添加预警
      const companyId = newProject.EnterpriseID;
      const orgName = await ctx.service.warning.findCurUserName(newProject.physicalExaminationOrgID);
      await this.add(companyId, warningLever, warningList, healthCheckId, workAddress, newProject.projectNumber || '', orgName || '');
    }
  }

  // 添加体检预警
  async add(companyId, warningLever, warningList, jobHealthId, workAddress, projectNumber, orgName, modelName = 'Healthcheck') {
    const { ctx, config } = this;
    // 查找企业详情
    const companyDetail = await ctx.model.Adminorg.findOne({ _id: companyId }, { cname: 1 });
    if (!companyDetail) return '未找到相关企业';
    // 创建预警
    const res = await ctx.model.Warning.create({
      workAddress,
      companyId,
      companyName: companyDetail.cname,
      lever: +warningLever,
      content: warningList,
      jobHealthId,
      type: +warningLever === 1 ? 3 : 2,
      modelName,
      process: [{
        time: Date.now(),
        thing: '生成预警',
        remark: `由体检机构 - ${orgName}上传的检测报告${projectNumber ? '（编号：' + projectNumber + '）' : ''}生成`,
      }],
    });
    if (res) {
      ctx.auditLog('添加一条预警记录成功', `机构${orgName}为企业 - ${companyDetail.cname}新添一条预警记录，id为${res._id}`, 'info');
      // 预警短信提醒
      if (config.branch === 'hz') {
        const sendRes = await ctx.curl(`${config.iServiceHost}/api/sendWarningSMS`, {
          method: 'POST',
          dataType: 'json',
          data: { warningId: res._id },
        });
        if (sendRes.status !== 200) {
          ctx.auditLog('预警短信发送失败', `预警id为${res._id}的短信发送失败：${sendRes.data.message}`, 'error');
        }
      }
      return res;
    }
    ctx.auditLog('添加一条预警记录失败', `机构${orgName}为企业 - ${companyDetail.cname}新添一条预警记录失败`, 'warn');
    return `名为${companyDetail.cname}的企业新添一条预警记录失败`;
  }

  // 体检项目 根据项目详情或者companyDetail中的工作场所获取预警的展示区域
  async getWorkAddress(projectDetail) {
    let workAddress = [];
    if (projectDetail && typeof projectDetail === 'object') {
      if (projectDetail.workAddress && projectDetail.workAddress.length) {
        workAddress = projectDetail.workAddress.map(ele => ele.districts);
      } else { // 没有工作场所就拿企业注册地址
        if (projectDetail.enterpriseAddr && projectDetail.enterpriseAddr.length) {
          workAddress = [ projectDetail.enterpriseAddr ];
        } else if (projectDetail.districtRegAdd && projectDetail.districtRegAdd.length) {
          workAddress = [ projectDetail.districtRegAdd ];
        }
      }
    }
    return workAddress; // 比如 [["浙江省", "衢州市", "龙游县","湖镇镇"]]
  }

  // 根据条件获取检测项目列表
  async getProjectsV1_0(params) {
    const reg = new RegExp(params.keyWords, 'i'); // 不区分大小写
    const query = {
      projectStop: { $ne: true },
      status: 1,
      $or: [
        { name: reg },
        { companyName: reg },
        { projectName: reg },
        { projectNumber: reg },
      ],
    };
    // console.log(query, params.regAddr, '1111111111111111111');
    // if (params.status) query.status = +params.status;
    if (params.company) query.companyID = params.company; // 实际用的是公司统一社会信用代码
    if (params.orgCode) query.serviceOrgID = params.orgCode; // 实际用的是机构统一社会信用代码
    if (params.org && !params.orgCode) {
      const org = await this.ctx.model.ServiceOrg.findOne({ _id: params.org }, { organization: 1 });
      if (org)query.serviceOrgID = org.organization;
    }
    // if (params.superUserID) query.superUserID = params.superUserID; // 监管端的id
    if (params.regAddr) query.workPlaces = { $elemMatch: { workAdd: { $all: params.regAddr } } }; // 工作场所
    // if (params.industry && params.industry.length) query.companyIndustry = { $all: params.industry }; // 所属行业
    if (params.serviceAreas && params.serviceAreas.length) query.serviceArea = { $in: params.serviceAreas }; // 技术服务类型
    if (params.dateType && params.dates && params.dates.length === 2) {
      query[params.dateType] = {
        $lt: new Date(new Date(params.dates[1]).getTime() + 24 * 3600000),
        $gte: new Date(params.dates[0]),
      };
    } else if (params.year && params.year !== 'NaN') {
      query[params.dateType] = {
        $lt: new Date(`${+params.year + 1}-1-1`),
        $gte: new Date(`${params.year}-1-1`),
      };
    }
    // console.log(11111111111, query[params.dateType]);

    if (!query.companyIndustry) { // 没有传行业分类
      // return await this.ctx.model.JobHealth.find(query)
      //   .populate('personInCharge personsOfProject EnterpriseID', 'name education station phoneNum age workYears certificate qualificationImg headImg status _id ')
      //   .skip((params.curPage - 1) * (+params.limit))
      //   .limit(+params.limit || 10)
      //   .sort({ applyTime: -1 });
      return await this.ctx.model.JobHealth.aggregate([
        { $match: query },
        {
          $lookup: {
            from: 'serviceEmployee', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
            localField: 'personInCharge', // 本地关联的字段
            foreignField: '_id', // user中用的关联字段
            as: 'personInCharge', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
          },
        },
        {
          $lookup: {
            from: 'serviceEmployee', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
            localField: 'personsOfProject', // 本地关联的字段
            foreignField: '_id', // user中用的关联字段
            as: 'personsOfProject', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
          },
        },
        {
          $lookup: {
            from: 'riskAssessmentReport',
            localField: '_id',
            foreignField: 'jobHealthId',
            as: 'riskAssessmentReport',
          },
        },
        // {
        //   $unwind: {
        //     path: '$preventAssess',
        //     preserveNullAndEmptyArrays: true },
        // },
        // { $sort: { 'preventAssess.assessDate': -1 } },
        // {
        //   $group: {
        //     _id: '$_id',
        //     info: { $first: '$$ROOT' },
        //   },
        // },
        // { $replaceRoot: { newRoot: '$info' } },
        {
          $sort: { applyTime: -1 },
        },
        {
          $skip: (params.curPage - 1) * (+params.limit),
        },
        {
          $limit: +params.limit || 10,
        },
      ]);
    }
    return await this.ctx.model.JobHealth.aggregate([
      { $unwind: '$companyIndustry' },
      { $match: query },
      { $group: {
        _id: '$_id',
        companyIndustry1: { $push: '$companyIndustry' },
        info: { $first: '$$ROOT' },
      } },
      { $addFields: { 'info.companyIndustry': '$companyIndustry1' } },
      { $replaceRoot: { newRoot: '$info' } },
      {
        $lookup: {
          from: 'serviceEmployee', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
          localField: 'personInCharge', // 本地关联的字段
          foreignField: '_id', // user中用的关联字段
          as: 'personInCharge', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
        },
      },
      {
        $lookup: {
          from: 'serviceEmployee', // 从哪个Schema中查询（一般需要复数，除非声明Schema的时候专门有处理）
          localField: 'personsOfProject', // 本地关联的字段
          foreignField: '_id', // user中用的关联字段
          as: 'personsOfProject', // 查询到所有user后放入的字段名，这个是自定义的，是个数组类型。
        },
      },
      {
        $lookup: {
          from: 'riskAssessmentReport',
          localField: '_id',
          foreignField: 'jobHealthId',
          as: 'riskAssessmentReport',
        },
      },
      // {
      //   $unwind: {
      //     path: '$preventAssess',
      //     preserveNullAndEmptyArrays: true },
      // },
      // { $sort: { 'preventAssess.assessDate': -1 } },
      // {
      //   $group: {
      //     _id: '$_id',
      //     info: { $first: '$$ROOT' },
      //   },
      // },
      // { $replaceRoot: { newRoot: '$info' } },
      {
        $sort: { applyTime: -1 },
      },
      {
        $skip: (params.curPage - 1) * (+params.limit),
      },
      {
        $limit: +params.limit || 10,
      },
    ]);


  }

  async getCount(params) {
    const reg = new RegExp(params.keyWords, 'i'); // 不区分大小写
    const query = {
      projectStop: { $ne: true },
      status: 1,
      $or: [
        { name: { $regex: reg } },
        { companyName: { $regex: reg } },
        { projectName: { $regex: reg } },
      ],
    };
    // if (params.status) query.status = +params.status;
    if (params.company) query.companyID = params.company; // 实际用的是公司统一社会信用代码
    if (params.orgCode) query.serviceOrgID = params.orgCode; // 实际用的是机构统一社会信用代码
    if (params.serviceAreas && params.serviceAreas.length) query.serviceArea = { $in: params.serviceAreas }; // 技术服务类型
    if (params.org && !params.orgCode) {
      const org = await this.ctx.model.ServiceOrg.findOne({ _id: params.org }, { organization: 1 });
      if (org)query.serviceOrgID = org.organization;
    }
    // if (params.superUserID) query.superUserID = params.superUserID; // 监管端的id
    if (params.industry && params.industry.length) query.companyIndustry = { $all: params.industry }; // 行业分类

    if (params.regAddr) query.workPlaces = { $elemMatch: { workAdd: { $all: params.regAddr } } }; // 工作场所

    if (params.completeStatus) query.completeStatus = params.completeStatus;
    if (params.completeUpdate) query.completeUpdate = params.completeUpdate;
    if (params.dateType && params.dates && params.dates.length === 2) {
      query[params.dateType] = {
        $lte: new Date(new Date(params.dates[1]).getTime() + 24 * 3600000),
        $gte: new Date(params.dates[0]),
      };
    } else if (params.year && params.year !== 'NaN') {
      query[params.dateType] = {
        $lt: new Date(`${+params.year + 1}-1-1`),
        $gte: new Date(`${params.year}-1-1`),
      };
    }

    if (!query.companyIndustry) {
      const count = await this.ctx.model.JobHealth.count(query);
      return new Promise(res => res(count));
    }
    const res = await this.ctx.model.JobHealth.aggregate([
      { $unwind: '$companyIndustry' },
      { $match: query },
      { $group: {
        _id: '$_id',
      } },
      { $count: 'count' },
    ]);
    const count2 = res && res[0] ? res[0].count : 0;
    // const count = await this.ctx.model.JobHealth.count(query);
    // console.log(33333, count);
    return new Promise(res => res(count2));
  }

  async getCheckAssessmentV1_0(query = {}) {
    return await this.ctx.model.CheckAssessment.findOne(query);
  }
  async getPhysicalExamOrgV1_0(query = {}) {
    const list = await this.ctx.model.PhysicalExamOrg.find(query, { name: 1, organization: 1, regAddr: 1, address: 1, status: 1, ctime: 1 }).sort({ ctime: -1 });
    return { list, count: list.length };
  }
  async getPhysicalExamInfoV1_0(query = {}, page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    const list = await this.ctx.model.Suspect.aggregate([
      { $match: query },
      { $sort: { createTime: -1 } },
      { $skip: skip },
      { $limit: +limit },
      {
        $lookup: {
          from: 'healthchecks',
          localField: 'batch',
          foreignField: '_id',
          as: 'healthCheck',
        },
      },
      {
        $lookup: {
          from: 'physicalExamOrgs',
          localField: 'healthCheck.physicalExaminationOrgID',
          foreignField: '_id',
          as: 'physicalExamOrg',
        },
      },
      {
        $unwind: {
          path: '$physicalExamOrg',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          physicalExamOrg: {
            name: '$physicalExamOrg.name',
            organization: '$physicalExamOrg.organization',
          },
          createTime: 1,
          name: 1,
          IDCard: 1,
          checkType: 1,
          checkDate: 1,
          diseaseName: 1,
          gender: 1,
          age: 1,
          organization: 1,
          harmFactors: 1,
          CwithO: 1,
          opinion: 1,
          riskFactorsOfPhysicalExaminations: 1, // 体检危害因素
          bhkSubList: 1, // 体检结果
          mhkRstList: 1,
          exmdDataList: 1,
          symptomList: 1,
          bhkAnamnesisList: 1,
          supoccdiseList: 1,
          contraindList: 1,
          encryptionAlgorithm: 1,
          nameForStore: 1,
          IDCardForStore: 1,
        },
      },
    ]);
    for (let i = 0; i < list.length; i++) {
      const item = list[i];
      if (item.encryptionAlgorithm) {
        if (item.IDCard && item.IDCard.includes('********')) {
          item.IDCard = await decrypt(item.IDCardForStore, item.encryptionAlgorithm);
          delete item.IDCardForStore;
        }
        if (item.name && item.name.includes('*')) {
          item.name = await decrypt(item.nameForStore, item.encryptionAlgorithm);
          delete item.nameForStore;
        }
        delete item.encryptionAlgorithm;
      }
    }
    const count = await this.ctx.model.Suspect.count(query);
    const totalPage = Math.ceil(count / Number(limit));
    return { list, count, totalPage };
  }
  async getOdiseaseV1_0(query = {}) {
    const options = {
      returnOptions: {
        IDNum: {
          returnPlaintext: true, // 返回明文密码
        },
        name: {
          returnPlaintext: true, // 返回明文密码
        },
      },
    };
    const list = await this.ctx.model.Odisease.find(query, {
      name: 1,
      nameForStore: 1,
      IDNum: 1,
      IDNumForStore: 1,
      decideDate: 1,
      diseaseName: 1,
      hospital: 1,
      updatedAt: 1,
    })
      .sort({ updatedAt: -1 })
      .setOptions(options);
    return { list, count: list.length };
  }
}

module.exports = JobhealthService;
