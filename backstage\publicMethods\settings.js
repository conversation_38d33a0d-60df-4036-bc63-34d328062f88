module.exports = {

  title: '职业健康信息化平台',

  /**
   * @type {boolean} true | false
   * @description Whether fix the header
   */
  fixedHeader: true,

  /**
   * @type {boolean} true | false
   * @description Whether show the logo in sidebar
   */
  sidebarLogo: true,

  server_api: '',
  // 加密key
  token_key: 'admin_frame',
  // 中台tokenkey
  admin_token_key: 'admin_frameapi',
  // 检索代号：#0001 宿主后台管理根目录 在每一个Vue模块的router使用到
  admin_base_path: '/admin',
  qy_base_path: '/qy',
  user_base_path: '/user',

  imageType: [ '.png', '.jpeg', '.jpg', '.bmp', '.jfif' ],
  imagePDFType: [ '.png', '.jpeg', '.jpg', '.bmp', '.jfif', '.pdf' ],
  imagePDFWordType: [ '.png', '.jpeg', '.jpg', '.bmp', '.jfif', '.pdf', '.docx', '.doc' ],

  // 宿主工程目录
  host_project_path: '/Users/<USER>/Documents/frame/coding.net/egg-cms',
  // 七牛文件上传目录
  qiniuStaticPath: 'cms/plugins/static/admin/',
};
