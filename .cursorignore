# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)
logs/
npm-debug.log
yarn-error.log
node_modules/
**/package-lock.json
!/package-lock.json
yarn.lock
coverage/
.idea/
run/
.DS_Store
*.sw*
*.un~
typings/
.nyc_output/
.history/
app/public/upload/images/
app/public/upload/caseCard/
!/app/public/upload/images/notificationcard
app/public/enterprise/
app/public/certificate/
app/public/upload/enterprise/
databak/
.vs/
backstage/*/dist
