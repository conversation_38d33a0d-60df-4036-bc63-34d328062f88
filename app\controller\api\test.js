const Controller = require('egg').Controller;
class TestController extends Controller {
  // 全量对接测试接口
  async addTest(ctx) {
    try {
      // 组织拿取
      console.log('🚀 开始组织增量更新');
      // const companyList = await ctx.service.byData.getOrganizationList({
      //   // dt: '2024-01-01 13:30:39',
      // });
      // const orgResult = await ctx.service.byData.clearOrganizationList(
      //   companyList
      // );
      // await ctx.service.byData.incrementOrgs({});
      console.log('🚀 结束组织');
      // // 部门拿洗
      console.log('🚀 开始部门');
      // const departmentList = await ctx.service.byData.getDepartmentList({});
      // const departmentResult = await ctx.service.byData.clearDepartmentList(
      //   departmentList
      // );
      // await ctx.service.byData.incrementDepart({});
      console.log('🚀 结束部门');
      // // 岗位拿洗
      console.log('🚀 开始岗位');
      // const postList = await ctx.service.byData.getPostList({});
      // const postResult = await ctx.service.byData.clearPostList(postList);
      // await ctx.service.byData.incrementPost({});
      console.log('🚀 结束岗位');
      // // 人员拿洗
      console.log('🚀 开始人员');
      // const peoList = await ctx.service.byData.getPeopleList({});
      // const peoResult = await ctx.service.byData.clearPeopleList(peoList);
      // await ctx.service.byData.incrementPeople({});
      console.log('🚀 结束人员');
      console.log('🚀 开始人员工作信息');
      // const peoWorkList = await ctx.service.byData.getPeopleWorkList({});
      // const peoWorkResult = await ctx.service.byData.clearPeopleWorkList(
      //   peoWorkList
      // );
      // let result = false;
      // result = await ctx.service.byData.byAxios('getZzjg', {
      //   access_token,
      //   dt: '2023-10-30',
      // });
      // if (!result) {
      //   console.log(result);
      //   const tokenRest = await ctx.service.byData.byAxios('getVerifyInfo');
      //   access_token = tokenRest.access_token;
      //   console.log('🚀 access_token:', access_token);
      //   result = await ctx.service.byData.byAxios('getZzjg', {
      //     access_token,
      //     dt: '2023-10-30',
      //   });
      // }
      // 1、
      // const trainPLanList = await ctx.service.byData.getTrainPlanList({});
      // await ctx.service.byData.clearTrainList(trainPLanList);
      // 2、忘了，好像是加人的
      // const details = await ctx.service.byData.getTrainPlanDetails({});
      // await ctx.service.byData.clearTrainDetailList(
      //   // details
      // );
      // 3、personing
      // const progresList = await ctx.service.byData.getTrainPersonProgress();
      // await ctx.service.byData.clearTrainPersonProgress(
      //   // progresList
      // );
      const testList = await ctx.service.byData.getTrainKsxx();
      console.log('🚀 结束人员考试信息', testList.length);
      const tesjcjList = await ctx.service.byData.getTrainKscj();
      console.log('🚀 结束人员考试成绩', tesjcjList.length);
      // const testlist =
      await ctx.service.byData.clearTrainKsList(
        tesjcjList,
        testList
      );

      ctx.helper.renderSuccess(ctx, {
        data: {
          // trainPLanList,
          // trainPLanLists,
          // result,
          // companyList: orgResult,
          // departmentList: departmentResult,
          // postList: postResult,
          // peoList: peoResult,
          // companyList,
          // departmentList,
          // peoList,
        },
      });
    } catch (error) {
      console.log(error);
    }
  }
  async test(ctx) {
    try {
      // 组织拿取
      // // console.log('🚀 开始组织')
      // const companyList = await ctx.service.byData.getOrganizationList({
      //   dt: '2023-07-06',
      // });
      // const orgResult = await ctx.service.byData.clearOrganizationList(
      //   companyList
      // );
      // // console.log('🚀 结束组织');
      // // 部门拿洗
      // console.log('🚀 开始部门');
      // const departmentList = await ctx.service.byData.getDepartmentList({});
      // const departmentResult = await ctx.service.byData.clearDepartmentList(
      //   departmentList
      // );
      // console.log('🚀 结束部门');
      // // 岗位拿洗
      // console.log('🚀 开始岗位');
      // const postList = await ctx.service.byData.getPostList({});
      // const postResult = await ctx.service.byData.clearPostList(postList);
      console.log('🚀 结束岗位');
      // // 人员拿洗
      console.log('🚀 开始人员');
      // const peoList = await ctx.service.byData.getPeopleList({});
      // const peoResult = await ctx.service.byData.clearPeopleList(peoList);
      console.log('🚀 结束人员');
      // let result = false;
      // result = await ctx.service.byData.byAxios('getZzjg', {
      //   access_token,
      //   dt: '2023-10-30',
      // });
      // if (!result) {
      //   console.log(result);
      //   const tokenRest = await ctx.service.byData.byAxios('getVerifyInfo');
      //   access_token = tokenRest.access_token;
      //   console.log('🚀 access_token:', access_token);
      //   result = await ctx.service.byData.byAxios('getZzjg', {
      //     access_token,
      //     dt: '2023-10-30',
      //   });
      // }
      // await ctx.service.deviceMonitorFactors.create({
      //   key: 'dust_001', // 因子key
      //   name: '粉尘浓度', // 因子名称
      //   code: 'DC001', // 因子编码
      //   unit: 'mg/m³', // 单位
      //   description: '用于测量空气中的粉尘浓度', // 描述
      // });
      // const getNoiseDustEquipmentList =
      //   await ctx.service.byData.getEquipmentRealTimeDataH2SPH3();
      ctx.helper.renderSuccess(ctx, {
        data: {
        },
      });
    } catch (error) {
      console.log(error);
    }
  }
  // 山西焦煤体检对接测试接口
  async sxccTest(ctx) {
    try {
      console.log('🚀 开始组织');
      console.log('🚀 结束组织');
      const res = {
        // list: [
        //   {
        //     HEALTH_EXAM_RECORD_LIST: {
        //       ID: '**********',
        //       ENTERPRISE_INFO_EMPLOYER: {
        //         ENTERPRISE_NAME_EMPLOYER: '山西焦煤集团有限责任公司',
        //         CREDIT_CODE_EMPLOYER: '91110000700000000A',
        //         INDUSTRY_CATEGORY_CODE_EMPLOYER: '[]',
        //         BUSINESS_SCALE_CODE_EMPLOYER: '',
        //         ADDRESS_CODE_EMPLOYER: '',
        //       },
        //       WORKER_INFO: {
        //         WORKER_NAME: '胡国顺',
        //         ID_CARD_TYPE_CODE: '1',
        //         ID_CARD: '149001196204124711',
        //         GENDER_CODE: 1,
        //         WORKER_TELPHONE: '***********',
        //         BIRTH_DATE: '1962-04-12',
        //         PLAN_ID: 'qvYXBgp0-v',
        //       },
        //       JC_TYPE: '01',
        //       EXAM_TYPE_CODE: '01',
        //       EXAM_DATE: '20240925',
        //       CONTACT_FACTOR_CODE: '',
        //       FACTOR_CODE: '',
        //       AREA_CODE: '',
        //       ORG_CODE: '',
        //       REPORT_DATE: '20240925',
        //       EXAM_CONCLUSION_LIST: [
        //         {
        //           ITAM_CODE: '',
        //           ITAM_NAME: '',
        //           EXAM_CONCLUSION_CODE: '',
        //         },
        //       ],
        //       EXAM_ITEM_RESULT_LIST: [
        //         {
        //           EXAM_ITEM_PNAME: '',
        //           EXAM_ITEM_NAME: '',
        //           EXAM_ITEM_CODE: '',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: '',
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: '身高',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '167',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: '体重',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '68',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: '脉搏',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '102',
        //           ABNORMAL: 1,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: 'BMI',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '24.38',
        //           ABNORMAL: 1,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: '舒张压',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '89',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: '收缩压',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '89',
        //           ABNORMAL: 1,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: '早餐票',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '11',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '红细胞计数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '44',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血红蛋白',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '33',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板计数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '12',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜酸细胞绝对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '32',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '淋巴细胞绝对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '32',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '单核细胞绝对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '33',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '中性粒细胞绝对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '>100',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板平均体积',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '>20',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板分布宽度',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '<10',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '红细胞分布宽度CV',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '10-220',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜碱细胞绝对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '23',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板比容',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '1',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '平均红细胞血红蛋白浓度(MCHC)',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '3',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '平均红细胞血红蛋白量',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '2',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '平均红细胞容积(MCV)',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '红细胞比容',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜碱性粒细胞计数相对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜酸性粒细胞计数相对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '白细胞计数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '32',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '单核细胞计数相对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '淋巴细胞计数相对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '中性粒细胞计数相对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '中性粒细胞比例',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '淋巴细胞比例',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '单核细胞比例',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '白细胞',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜酸性粒细胞比例',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜碱性粒细胞比例',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜酸性粒细胞数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '淋巴细胞数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '中性粒细胞数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '单核细胞数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜碱性粒细胞数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: 'RBC平均血红蛋白',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '红细胞比积',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '红细胞平均体积',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血红蛋白浓度',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '红细胞',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: 'RBC体积分布宽度CV',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: 'RBC血红蛋白浓度',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: 'RBC体积分布宽度SD',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板体积分布宽度',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板平均体积',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板比积',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '大血小板比例',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板比率',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '有核红细胞百分比',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '有核红细胞计数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '异常提示',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '报告评价',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //       ],
        //     },
        //   },
        //   {
        //     HEALTH_EXAM_RECORD_LIST: {
        //       ID: '**********',
        //       ENTERPRISE_INFO_EMPLOYER: {
        //         ENTERPRISE_NAME_EMPLOYER: '山西焦煤集团有限责任公司',
        //         CREDIT_CODE_EMPLOYER: '91110000700000000A',
        //         INDUSTRY_CATEGORY_CODE_EMPLOYER: '[]',
        //         BUSINESS_SCALE_CODE_EMPLOYER: '',
        //         ADDRESS_CODE_EMPLOYER: '',
        //       },
        //       WORKER_INFO: {
        //         WORKER_NAME: '庞琳荟',
        //         ID_CARD_TYPE_CODE: '1',
        //         ID_CARD: '140104198207152721',
        //         GENDER_CODE: 2,
        //         WORKER_TELPHONE: '***********',
        //         BIRTH_DATE: '1982-07-15',
        //         PLAN_ID: 'T5r43_Fd6O',
        //       },
        //       JC_TYPE: '01',
        //       EXAM_TYPE_CODE: '01',
        //       EXAM_DATE: '20240925',
        //       CONTACT_FACTOR_CODE: '',
        //       FACTOR_CODE: '',
        //       AREA_CODE: '',
        //       ORG_CODE: '',
        //       REPORT_DATE: '20240925',
        //       EXAM_CONCLUSION_LIST: [
        //         {
        //           ITAM_CODE: '',
        //           ITAM_NAME: '',
        //           EXAM_CONCLUSION_CODE: '',
        //         },
        //       ],
        //       EXAM_ITEM_RESULT_LIST: [
        //         {
        //           EXAM_ITEM_PNAME: '',
        //           EXAM_ITEM_NAME: '',
        //           EXAM_ITEM_CODE: '',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: '',
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: '身高',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '167',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: '体重',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '68',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: '脉搏',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '102',
        //           ABNORMAL: 1,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: 'BMI',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '24.38',
        //           ABNORMAL: 1,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: '舒张压',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '89',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: '收缩压',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '89',
        //           ABNORMAL: 1,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '身高、体重、血压、脉搏（含早餐）',
        //           EXAM_ITEM_NAME: '早餐票',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '11',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '红细胞计数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '44',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血红蛋白',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '33',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板计数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '12',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜酸细胞绝对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '32',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '淋巴细胞绝对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '32',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '单核细胞绝对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '33',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '中性粒细胞绝对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '>100',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板平均体积',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '>20',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板分布宽度',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '<10',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '红细胞分布宽度CV',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '10-220',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜碱细胞绝对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '23',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板比容',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '1',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '平均红细胞血红蛋白浓度(MCHC)',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '3',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '平均红细胞血红蛋白量',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '2',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '平均红细胞容积(MCV)',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '红细胞比容',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜碱性粒细胞计数相对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜酸性粒细胞计数相对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '白细胞计数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '32',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '单核细胞计数相对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '淋巴细胞计数相对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '中性粒细胞计数相对值',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '中性粒细胞比例',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '淋巴细胞比例',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '单核细胞比例',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '白细胞',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜酸性粒细胞比例',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜碱性粒细胞比例',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜酸性粒细胞数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '淋巴细胞数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '中性粒细胞数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '单核细胞数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '嗜碱性粒细胞数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: 'RBC平均血红蛋白',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '红细胞比积',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '红细胞平均体积',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血红蛋白浓度',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '红细胞',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: 'RBC体积分布宽度CV',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: 'RBC血红蛋白浓度',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: 'RBC体积分布宽度SD',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板体积分布宽度',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板平均体积',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板比积',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '大血小板比例',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '血小板比率',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '有核红细胞百分比',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '有核红细胞计数',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '异常提示',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //         {
        //           EXAM_ITEM_PNAME: '血常规（五分类）',
        //           EXAM_ITEM_NAME: '报告评价',
        //           EXAM_ITEM_CODE: '02',
        //           EXAM_RESULT_TYPE: '',
        //           EXAM_RESULT: '',
        //           ABNORMAL: 0,
        //         },
        //       ],
        //     },
        //   },
        // ],
        // total: '2',
        // page: 1,
        // page_size: 15,
      };
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (error) {
      console.log(error);
    }
  }
  async sxccTestFile(ctx) {
    try {
      const res = {
        file: '',
      };
      ctx.helper.renderSuccess(ctx, {
        data: res,
      });
    } catch (error) {
      console.log(error);
    }
  }
}
module.exports = TestController;
