const Service = require('egg').Service;
const path = require('path');
const {
  _unionQuery,
  _list,
  _item,
  _count,
  _create,
  _update,
  _removes,
  _removeAll,
} = require(path.join(process.cwd(), 'app/service/general'));

class HealthcheckService extends Service {
  async unionQuery(payload, {
    collections = [],
    unwindArray = [],
    query = {},
    searchKeys = [],
    files = null,
    sort = {},
    statisticsFiles = [],
  } = {}) {
    const listdata = _unionQuery(this.ctx.model.Healthcheck, payload, {
      collections,
      unwindArray,
      query,
      searchKeys,
      files,
      sort,
      statisticsFiles,
    });
    return listdata;
  }

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
    sort = {},
  } = {}) {
    const listdata = _list(this.ctx.model.Healthcheck, payload, {
      query,
      searchKeys,
      populate,
      files,
      sort,
    });
    return listdata;
  }

  async count(params = {}) {
    return _count(this.ctx.model.Healthcheck, params);
  }

  async create(payload, options) {
    return _create(this.ctx.model.Healthcheck, payload, options);
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.Healthcheck, params);
  }

  async update(res, _id, payload) {
    return _update(res, this.ctx.model.Healthcheck, _id, payload);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.Healthcheck, values, key);
  }

  async removeAll() {
    return _removeAll(this.ctx.model.Healthcheck);
  }
  // 新增/删除/修改 体检项目或职业病信息之后,同步处理企业表中的healcheckInfo字段
  async dealWithHealcheckInfo(project, n = 1) { // 1是体检 2诊断  project代表项目ID或者项目详情
    const { ctx } = this;
    try {
      let EnterpriseID;
      if (typeof project === 'string') {
        const modelName = n === 1 ? 'Healthcheck' : 'Odisease';
        const projectDetail = await ctx.model[modelName].findOne({ _id: project }, { EnterpriseID: 1 });
        if (projectDetail && projectDetail.EnterpriseID) EnterpriseID = projectDetail.EnterpriseID;
      } else if (typeof project === 'object') {
        if (project && project.EnterpriseID) EnterpriseID = project.EnterpriseID;
      }
      if (!EnterpriseID) return;
      const lastYear = this.getLastYear();
      const occupationalNum = await ctx.model.Odisease.count({ // 职业病人数
        EnterpriseID,
        decideDate: { $gte: lastYear },
        diseaseName: { $exists: true, $nin: [ '无', '' ] },
      });
      if (n === 1) { // 体检
        const healthCheck = await this.ctx.model.Healthcheck.find({
          EnterpriseID,
          checkDate: { $gte: lastYear },
          $or: [{ source: 'qy' }, { reportStatus: true }],
        }, { actuallNum: 1, re_examination: 1, suspected: 1, forbid: 1, checkDate: 1 })
          .sort({ checkDate: -1 });
        const healcheckInfo = {
          actuallNum: 0, // 实检人数
          recheck: 0, // 复查
          suspected: 0, // 疑似
          forbid: 0, // 禁忌证
          occupational: occupationalNum, // 职业病
        };
        if (healthCheck.length) {
          healcheckInfo.recentDay = healthCheck[0].checkDate;
          healthCheck.forEach(ele => {
            healcheckInfo.actuallNum += ele.actuallNum;
            healcheckInfo.recheck += ele.re_examination;
            healcheckInfo.suspected += ele.suspected;
            healcheckInfo.forbid += ele.forbid;
          });
        }
        // 更新企业表
        await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { $set: { healcheckInfo: {
          actuallNum: String(healcheckInfo.actuallNum),
          recheck: String(healcheckInfo.recheck),
          suspected: String(healcheckInfo.suspected),
          forbid: String(healcheckInfo.forbid),
          occupational: String(healcheckInfo.occupational),
          recentDay: healcheckInfo.recentDay || '',
        } } });
      } else { // 诊断
        await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { $set: { 'healcheckInfo.occupational': occupationalNum + '' } });
      }
    } catch (err) {
      ctx.auditLog('同步处理企业表中的healcheckInfo字段', JSON.stringify(err), 'error');
    }
  }
  // 周期年计算，获取几年前的今天，默认一年前的今天
  getLastYear(yearNum = 1) {
    const today = new Date(); // 当天
    today.setFullYear(today.getFullYear() - yearNum);
    return today;
  }
}

module.exports = HealthcheckService;
