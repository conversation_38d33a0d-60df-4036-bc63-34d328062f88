/*
 * @Author: 汪周强
 * @Date: 2022-03-11 14:29:27
 * @LastEditors: 汪周强
 * @LastEditTime: 2022-03-25 09:16:26
 * @Description:
 *
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const trainingRecordsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: String, // 受训企业id
    EnterpriseName: String, // 受训企业名称
    pxOrgId: {
      type: String,
      ref: 'PxOrg',
    }, // 培训机构id

    trainingClassId: String, // 培训班id
    examSyllabusId: String, // 大纲id

    trainingPlaceArea_code: [ String ], // 地区编码
    trainingPlace: String, // 培训地点

    trainingStartDate: { // 培训开始时间
      type: Date,
      require: true,
    },
    trainingEndDate: { // 培训结束时间
      type: Date,
      require: true,
    },
    conclusion: String, // 培训结语
    proofMaterial: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      name: String, // 原始文件名
      staticName: String, // 服务器静态文件名
    }], // 佐证材料

    employees: [{ // 培训人员
      type: String,
    }],

    reviewStatus: { // 监管审核状态
      type: Number,
      default: 0,
      enum: [ 0, 1, 2, 3, 4 ], // 0: 草稿 1: 已上报 2: 审核通过 3: 审核未通过
    },
    reviewRecord: [{ // 审核记录
      _id: {
        type: String,
        default: shortid.generate,
      },
      reviewResult: Boolean, // 审核结果 true：通过。false：拒绝
      reviewerId: {
        type: String,
        ref: 'SuperUser',
      },
      reviewTime: { // 审核时间
        type: Date,
      },
      reviewComments: { // 审核意见
        type: String,
      },
    }],

  }, { timestamps: true });
  return mongoose.model('trainingRecords', trainingRecordsSchema, 'trainingRecords');
};

