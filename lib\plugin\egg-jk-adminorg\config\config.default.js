

/**
 * egg-dora-adminorg default config
 * @member Config#eggDoraAdminorg
 * @property {String} SOME_KEY - some description
 */

const pkgInfo = require('../package.json');
exports.jk_adminorg = {
  alias: 'adminorg', // 插件目录，必须为英文
  pkgName: 'egg-jk-adminorg', // 插件包名
  enName: 'jk_adminorg', // 插件名
  name: '企业管理', // 插件名称
  description: '企业管理', // 插件描述
  isadm: 1, // 是否有后台管理，1：有，0：没有，入口地址:'/ext/devteam/admin/index'
  isindex: 0, // 是否需要前台访问，1：需要，0：不需要,入口地址:'/ext/devteam/index/index'
  version: pkgInfo.version, // 版本号
  iconName: 'icon_service', // 主菜单图标名称
  adminUrl: '/adminorg/js/app.js',
  adminApi: [
    {
      url: 'adminorg/getWorkPlace',
      method: 'get',
      controllerName: 'getWorkPlace',
      details: '获取工作场所',
    },
    {
      url: 'adminorg/getList',
      method: 'get',
      controllerName: 'list',
      details: '获取企业管理列表',
    },
    {
      url: 'adminorg/getListApp',
      method: 'get',
      controllerName: 'listApp',
      details: '获取企业管理列表',
    },
    {
      url: 'adminorg/getOne',
      method: 'get',
      controllerName: 'getOne',
      details: '获取单条企业管理信息',
    },
    {
      url: 'adminorg/addOne',
      method: 'post',
      controllerName: 'create',
      details: '添加单个企业管理',
    },
    {
      url: 'adminorg/updateOne',
      method: 'post',
      controllerName: 'update',
      details: '更新企业管理信息',
    },
    {
      url: 'adminorg/delete',
      method: 'get',
      controllerName: 'removes',
      details: '删除企业管理',
    },
  ],
  fontApi: [
    // {
    // url: 'adminorg/getList',
    // method: 'get',
    // noPower: true,
    // controllerName: 'list',
    // details: '获取企业列表',
    // },
    {
      url: 'adminorg/getWorkPlace',
      method: 'get',
      noPower: true,
      controllerName: 'getWorkPlace',
      details: '获取工作场所',
    },
    {
      url: 'adminorg/getOne',
      method: 'get',
      noPower: true,
      controllerName: 'getOne',
      details: '获取单个企业',
    },
    {
      url: 'adminorg/upload/files',
      method: 'post',
      controllerName: 'upload',
      details: '上传文件',
    },
    {
      url: 'adminorg/addOne',
      method: 'post',
      controllerName: 'create',
      details: '新企业申请',
    },
    {
      url: 'adminorg/updateOne',
      method: 'post',
      controllerName: 'update',
      details: '新企业申请',
    },
    {
      url: 'adminorg/userInfo',
      method: 'post',
      controllerName: 'userInfo',
      details: '获取用户基本信息',
    },
    {
      url: 'adminorg/uploadLicensePic',
      method: 'post',
      controllerName: 'uploadLicensePic',
      details: '上传营业执照',
    },
    {
      url: 'adminorg/getIndustryCategory',
      method: 'get',
      controllerName: 'getIndustryCategory',
      details: '获取行业分类信息',
    },
    {
      url: 'adminorg/enterpriseList',
      method: 'get',
      controllerName: 'enterpriseList',
      details: '获取企业列表',
    },
  ],

  initData: '', // 初始化数据脚本
  pluginsConfig: ` 
    exports.jk_adminorg = {\n
        enable: true,\n
         \n
    };\n
    `, // 插入到 plugins.js 中的配置
  defaultConfig: `
    adminorgRouter:{\n
        match: [ctx => ctx.path.startsWith('/manage/adminorg'), ctx => ctx.path.startsWith('/api/adminorg')],\n
    },\n
    `, // 插入到 config.default.js 中的配置
};
