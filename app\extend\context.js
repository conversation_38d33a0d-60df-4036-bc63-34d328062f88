module.exports = {
  auditLog(title = '警告：未输入任何记录！', message = '警告：未输入任何记录！', level = 'debug') {
    const { session, request, logger, app } = this;
    const loggerTitle = '操作记录：',
      userInfo = session.user,
      header = request.header,
      json = {
        title,
        userID: (userInfo && userInfo._id) || '暂未获取到当前用户ID！',
        message,
        version: app.config.version || '无',
        userAgent: {
          IP: header['x-real-ip'] || header.host,
          AGENT: header['user-agent'],
        },
      };

    switch (level) {
      case 'info':
        logger.info(loggerTitle, json);
        break;
      case 'debug':
        logger.debug(loggerTitle, json);
        break;
      case 'warn':
        logger.warn(loggerTitle, json);
        break;
      case 'error':
        if (typeof json.message === 'string') {
          json.message = json.message.replace(/\n/g, '');
        }
        // 如果 message 本身是 Error 对象，保留其堆栈信息
        if (message instanceof Error) {
          json.stack = message.stack;
        }
        logger.error(loggerTitle, json);
        break;
      default:
        break;
    }
  },
};
