
module.exports = app => {
  const shortid = require('shortid');
  // 用户反馈信息表
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const commentSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    userId: {
      type: String,
      ref: 'User',
    },
    replyStatus: { // 回复状态
      type: Boolean,
      default: false,
    },
    comments: [{ // 留言/对话
      role: { // 角色
        type: Number,
        default: 1, // 0代表客服  1代表用户
        enum: [ 0, 1 ],
      },
      time: {
        type: Date,
        default: Date.now,
        set() {
          return Date.now();
        },
      },
      img: String, // 图片
      msg: String, // 文字信息
    }],
    status: {
      type: Boolean,
      default: true,
    },

  }, { timestamps: true });
  return mongoose.model('Comment', commentSchema, 'comment');
};
