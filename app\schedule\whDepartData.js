// const moment = require('moment');
module.exports = app => {
  return {
    schedule: app.config.whDepartData,

    async task(ctx) {
      const redisTopDepartKey = 'whOrgData:topDepartCache';

      try {
        // 从 Redis 中读取缓存的顶级部门
        const cachedTopDepart = await ctx.app.redis.lrange(
          redisTopDepartKey,
          0,
          -1
        );

        if (cachedTopDepart.length > 0) {
          ctx.auditLog(`万华iam定时任务开始处理 ${cachedTopDepart.length} 个顶级部门`, '', 'info');

          // 执行批量处理逻辑
          await ctx.service.whData.processNoTopCompany();
          await ctx.service.whData.fetchCompanyData(cachedTopDepart);

          // 清空缓存
          await ctx.app.redis.del(redisTopDepartKey);

          ctx.auditLog('万华iam定时任务处理完成，缓存已清空', '', 'info');
        } else {
          ctx.auditLog('万华iam定时任务未发现需要处理的顶级部门', '', 'info');
        }
        await ctx.service.whData.fetchDepartmentData();
      } catch (error) {
        ctx.auditLog('定时任务处理失败', error.message, 'error');
      }
    },
  };
};
