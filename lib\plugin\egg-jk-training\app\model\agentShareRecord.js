module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  const agentShareRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    shareBy: { // 分享人 ID
      type: String,
      ref: 'User',
    },
    shareTrain: [// 分享的培训IDS
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        adminTrainingId: { type: String, ref: 'AdminTraining' }, // 培训id
      },
    ],
    clickCount: { // 点击次数
      type: Number,
      default: 0,
    },
    deadlineDate: { // 链接有效期
      type: Date,
    },
  });
  return mongoose.model('AgentShareRecord', agentShareRecordSchema, 'agentShareRecords');
};
