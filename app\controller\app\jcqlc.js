const Controller = require('egg').Controller;
const jwt = require('jsonwebtoken'); // 生成token

class IndexController extends Controller {
// 用户名密码登录
  async accountLogin() {
    const { ctx, service, config, app } = this;
    const params = ctx.request.body;
    const result = await ctx.model.ServiceUser.find({ userName: params.account }); // 查找该userName是否存在
    if (result && result.length) {
      const user = result[0];
      if (!user.enable || !user.state) {
        ctx.body = {
          status: false,
          msg: '该账户已被禁用，如有疑问请联系管理员',
        };
        return;
      }
      if (!user.isQLC) {
        ctx.body = {
          status: false,
          isVIP: '0',
          msg: '对不起！您并非VIP用户，请访问：<a href="https://service.zyws.cn/">技术服务机构信息报送平台</a> 登录！',
        };
        return;
      }
      const hashPassword = ctx.helper.hashSha256(params.password, config.salt_sha2_key); // 解密
      if (user.password === hashPassword) { // 密码正确
        const user_id = user._id;
        ctx.helper.setCache('currentUser', user_id || '', 1000 * 60 * 60 * 12); // 缓存用户信息12h
        // 通过用户ID查找对应的机构信息
        const orgs = await ctx.model.ServiceOrg.find({
          managers: user_id,
        }, { managers: 0 });
        let org = null;
        if (orgs && orgs.length > 0) {
          org = orgs[0];
          // 判断用户的org_id存在与否，没有就插入到用户信息
          if (!user.org_id || !user.org) {
            await service.serviceUser.inserOrgToUser({
              _id: user_id,
              org_id: org._id,
              org: org.name,
            })
              .then(res => {
                console.log('用户org_id插入成功', res);
              }).catch(err => {
                console.log('用户org_id插入失败', err);
              });
          }
        }
        // 返回token
        const newToken = jwt.sign({
          EnterpriseID: org ? org._id : '',
          type: 1, // 用于区别用户类型 0 为服务机构员工账户 1 为服务机构用户
          _id: user_id,
          regAddr: org ? org.regAddr : [],
          userName: result[0].userName,
        }, app.config.encrypt_key, {
          expiresIn: 3600 * 24 * 30, // token缓存30天
        });
        // 存入cookie
        ctx.cookies.set('admin_' + app.config.auth_jcqlccookie_name, newToken, {
          path: '/',
          maxAge: 1000 * 60 * 60 * 24 * 30,
          signed: true,
          httpOnly: true,
        });
        ctx.auditLog('登录操作', `未知用户正在通过用户名 ${result[0].userName} 执行登录。`, 'info');
        // 返回信息
        ctx.body = {
          status: true,
          data: {
            token: 'Bearer ' + newToken,
            org,
          },
          msg: '登录成功',
        };
      } else { // 密码不对
        ctx.body = {
          status: false,
          msg: '账户名或者密码错误，请重新确认',
        };
      }
    } else {
      ctx.body = {
        status: false,
        msg: '账户不存在',
      };
    }

  }
}

module.exports = IndexController;
