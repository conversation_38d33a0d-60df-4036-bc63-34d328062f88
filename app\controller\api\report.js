const Controller = require('egg').Controller;
const moment = require('moment');
const xlsx = require('node-xlsx');
// const test = require('./testData.js');
class JobhealthController extends Controller {
  async postReportFile(ctx) {
    try {
      const params = await ctx.service.report.uploadFile(ctx, this.app.config.enterpriseUpload_path);
      if (params.errMsgs) {
        ctx.helper.renderCustom(ctx, {
          status: 406,
          message: { errMsgs: params.errMsgs },
        });
        return;
      }
      const accept = ctx.header.accept;
      const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
      const versions = [ '1_0' ];
      if (!version || version.indexOf(versions) === -1) {
        ctx.helper.renderCustom(ctx, {
          status: 406,
          message: { errMsgs: [ '版本号错误，请检查' ] },
          data: {},
        });
        return;
      }
      await ctx.service.report['postReportFileV' + version](params);
      ctx.helper.renderCustom(ctx, {
        status: 200,
        message: '上传成功',
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderCustom(ctx, {
        status: 500,
        message: error,
      });
    }
  }
  async postReportData(ctx) {
    try {
      const params = ctx.request.body;
      if (!params.reportData) {
        ctx.helper.renderCustom(ctx, {
          status: 406,
          message: { errMsgs: [ '报告数据为空' ] },
          data: {},
        });
        return;
      }
      if (Object.prototype.toString.call(params.reportData) !== '[object Object]') {
        ctx.helper.renderCustom(ctx, {
          status: 406,
          message: { errMsgs: [ '报告数据类型错误，请传入对象类型' ] },
          data: {},
        });
        return;
      }
      if (JSON.stringify(params.reportData) === '{}') {
        ctx.helper.renderCustom(ctx, {
          status: 406,
          message: { errMsgs: [ '报告数据为空' ] },
          data: {},
        });
        return;
      }
      // params.reportData = JSON.parse(params.reportData);
      // let accept = 'application/oapi.zyws.v1.0+json';
      if (params.reportData.empInfo) {
        const checkArea = await ctx.service.district.checkArea(params.reportData.empInfo.workAddress);
        if (!checkArea) {
          ctx.auditLog('导入检测项目', '该项目不隶属于杭州地区', 'error');
        }
      }
      const accept = ctx.header.accept;
      const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
      const versions = [ '1_0' ];
      if (!version || version.indexOf(versions) === -1) {
        ctx.helper.renderCustom(ctx, {
          status: 406,
          message: { errMsgs: [ '版本号错误，请检查' ] },
          data: {},
        });
        return;
      }
      const res = await ctx.service.report['postReportDataV' + version](params);
      ctx.helper.renderCustom(ctx, {
        status: res.status,
        message: res.message,
        data: res.data || {},
      });
    } catch (error) {
      console.log(error);
      ctx.helper.renderCustom(ctx, {
        status: 500,
        message: error,
      });
    }
  }

  async test() {
    // 体检报告接口测试用
    // const axios = require('axios');
    // const FormData = require('form-data');
    // const fs = require('fs');
    // const filepath = 'E:/oapi/app/public/upload/1.docx';
    // const filepath1 = 'E:/oapi/app/public/upload/2.docx';
    // const filepath2 = 'E:/oapi/app/public/upload/城东医院在岗职业报告.pdf';
    // const newData = new FormData();
    // const headers = newData.getHeaders();
    // headers.Accept = 'application/oapi.zyws.v1.0+json';
    // headers.token = '{"agentId":"IE68NT0y6","appKey":"43ecc01ed3146EN6641I","appSecret":"e1bbc3573f598096bcdbe61a971662acb2787e415429a4e9009a99b9eceac49b"}';
    // const stream = await fs.createReadStream(filepath);
    // const stream1 = await fs.createReadStream(filepath1);
    // const stream2 = await fs.createReadStream(filepath2);
    // newData.append('info', JSON.stringify({ creditCode: '91330108676781732W', fileList: [{ projectNumber: '测试接口1', name: '1.docx' }, { projectNumber: '123131231', name: '2.docx' }, { projectNumber: '手动新建测试', name: '城东医院在岗职业报告.pdf' }] }));
    // newData.append('files', stream);
    // newData.append('files', stream1);
    // newData.append('files', stream2);
    // const res = await axios.post('http://127.0.0.1:7009/api/physical/uploadReportFile', newData, { headers });
    // console.log(res.data);
    // const headers = newData.getHeaders();
    // headers.Accept = 'application/oapi.zyws.v1.0+json';
    // headers.token = '{"agentId":"IE68NT0y6","appKey":"43ecc01ed3146EN6641I","appSecret":"e1bbc3573f598096bcdbe61a971662acb2787e415429a4e9009a99b9eceac49b"}';
    // await axios.post('http://127.0.0.1:7009/api/tjReportFile', newData, { headers });
    // const res = await axios({
    //   method: 'post',
    //   url: 'http://127.0.0.1:7009/api/tjReportFile',
    //   headers: { Accept: 'application/oapi.zyws.v1.0+json', token: '{"agentId":"IE68NT0y6","appKey":"43ecc01ed3146EN6641I","appSecret":"e1bbc3573f598096bcdbe61a971662acb2787e415429a4e9009a99b9eceac49b"}', 'content-Type': 'multipart/form-data' },
    //   data: newData,
    // });
    // const rstream = fs.createReadStream(filepath);
    // rstream.on('data', function(chunk) {
    //   console.log(chunk.toString())
    // });
    // await axios({
    //   method: 'post',
    //   url: 'http://127.0.0.1:7009/api/tjreport',
    //   headers: { Accept: 'application/oapi.zyws.v1.0+json', token: '{"agentId":"IE68NT0y6","appKey":"43ecc01ed3146EN6641I","appSecret":"e1bbc3573f598096bcdbe61a971662acb2787e415429a4e9009a99b9eceac49b"}' },
    //   data: { data: JSON.stringify({ creditCode: '91330108676781732W', // 机构信用代码,
    //     list: [
    //       {
    //         isUp: true, // 是否直接上报
    //         // info: { // 机构信息
    //         //   creditCode: '91330108676781732W', // 机构信用代码
    //         // },
    //         project: { // 项目信息
    //           // 企业信息
    //           // (必填部分*)
    //           enterpriseName: '浙江华采科技(杭州）有限公司', // 企业名称
    //           enterpriseContactsPhoneNumber: '17730229511', // 企业联系方式
    //           workAddress: '安徽省合肥市蜀山区黄山路与潜山路交口西环中心广场14层', // 工作场所
    //           // (非必填部分*)
    //           enterpriseContactsName: '张三', // 企业联系人
    //           // 体检报告信息
    //           // (必填部分*)
    //           projectNumber: '测试接口1333', // 项目编号
    //           recheck: false, // 是否复查
    //           checkDate: new Date('2021-12-24T16:00:00.000Z').getTime().toString(), // 体检开始日期
    //           shouldCheckNum: 9, // 应检人数
    //           actuallNum: 9, // 实检人数
    //           checkType: '2', // 体检类别
    //           // (非必填部分*)
    //           checkEndDate: new Date('2022-01-14T16:00:00.000Z').getTime().toString(), // 体检结束日期
    //           approvalDate: new Date('2022-01-19T16:00:00.000Z').getTime().toString(), // 批准日期
    //           checkPlace: '浙江艾博医学保健中心', // 体检地点
    //           // enterpriseName: '', // 企业名称
    //           // enterpriseContactsPhoneNumber: '9511', // 企业联系方式
    //           // workAddress: '', // 工作场所
    //           // // (非必填部分*)
    //           // enterpriseContactsName: '', // 企业联系人
    //           // // 体检报告信息
    //           // // (必填部分*)
    //           // projectNumber: '', // 项目编号
    //           // recheck: 9, // 是否复查
    //           // checkDate: 22, // 体检开始日期
    //           // shouldCheckNum: '', // 应检人数
    //           // actuallNum: 9, // 实检人数
    //           // checkType: '8', // 体检类别
    //           // // (非必填部分*)
    //           // checkEndDate: 'fff', // 体检结束日期
    //           // approvalDate: new Date('2022-01-19T16:00:00.000Z').getTime(), // 批准日期
    //           // checkPlace: '', // 体检地点
    //         },
    //         peopleInfoList: [ // 体检人员信息
    //           {
    //             name: '嵇汉文',
    //             gender: '1',
    //             age: '36',
    //             workType: '中间体羟化工', // 工种
    //             workYears: '7年6个月', // 工龄
    //             harmFactors: '高温、环氧乙烷', // 危害因素
    //             abnormalIndex: '2021.10.19首次结果：血压:136/106mmHg2022.02.21第一次复查结果:血压:142/85mmHg2022.02.25第二次复查结果:血压:139/85mmHg2022.02.26第三次复查结果:血压:129/83mmHg2021.10.19首次结果：空腹血糖:8.31mmol/L↑2022.02.19复查结果：葡萄糖:12.70mmol/L↑糖化血红蛋白:9.6%↑', // 异常指标
    //             dedicalAdvice: '1.糖尿病，内分泌科诊治；2.血压偏高，内科定期复查。', // 医学建议
    //             opinion: '职业禁忌证：未控制的糖尿病，属高温作业在岗职业禁忌证，目前不宜从事高温危害作业。', // 意见
    //             CwithO: '禁忌证', // 结论
    //           },
    //           {
    //             name: '嵇汉文',
    //             gender: '1',
    //             age: '36',
    //             workType: '中间体羟化工',
    //             workYears: '7年6个月',
    //             harmFactors: '44444',
    //             abnormalIndex: '333333333↑',
    //             dedicalAdvice: '1.22222',
    //             opinion: '111111。', // 不一定有
    //             CwithO: '疑似职业病',
    //           },
    //           {
    //             workType: '测试工种',
    //             name: '鲍承志',
    //             gender: '0',
    //             age: '36',
    //             harmFactors: '粉尘',
    //             abnormalIndex: '测试',
    //             opinion: '意见',
    //             dedicalAdvice: '医学建议',
    //             CwithO: '禁忌证',
    //             workYears: '11',
    //           },
    //         ],
    //       },

    //       {
    //         // info: { // 机构信息
    //         //   creditCode: '91330108676781732W', // 机构信用代码
    //         // },
    //         project: { // 项目信息
    //           // 企业信息
    //           // (必填部分*)
    //           enterpriseName: '浙江华采科技有限公司', // 企业名称
    //           enterpriseContactsPhoneNumber: '***********', // 企业联系方式
    //           workAddress: '安徽省合肥市蜀山区黄山路与潜山路交口西环中心广场14层', // 工作场所
    //           // (非必填部分*)
    //           enterpriseContactsName: '张三', // 企业联系人
    //           // 体检报告信息
    //           // (必填部分*)
    //           projectNumber: '测试接口1', // 项目编号
    //           recheck: true, // 是否复查
    //           checkDate: new Date('2021-12-24T16:00:00.000Z').getTime().toString(), // 体检开始日期
    //           shouldCheckNum: 9, // 应检人数
    //           actuallNum: 9, // 实检人数
    //           checkType: '4', // 体检类别
    //           // (非必填部分*)
    //           checkEndDate: new Date('2022-01-14T16:00:00.000Z').getTime().toString(), // 体检结束日期
    //           approvalDate: new Date('2022-01-19T16:00:00.000Z').getTime().toString(), // 批准日期
    //           checkPlace: '浙江艾博医学保健中心', // 体检地点
    //         },
    //         peopleInfoList: [ // 体检人员信息
    //           {
    //             name: '嵇汉文',
    //             gender: '1',
    //             age: '36',
    //             workType: '中间体羟化工', // 工种
    //             workYears: '7年6个月', // 工龄
    //             harmFactors: '高温、环氧乙烷', // 危害因素
    //             abnormalIndex: '2021.10.19首次结果：血压:136/106mmHg2022.02.21第一次复查结果:血压:142/85mmHg2022.02.25第二次复查结果:血压:139/85mmHg2022.02.26第三次复查结果:血压:129/83mmHg2021.10.19首次结果：空腹血糖:8.31mmol/L↑2022.02.19复查结果：葡萄糖:12.70mmol/L↑糖化血红蛋白:9.6%↑', // 异常指标
    //             dedicalAdvice: '1.糖尿病，内分泌科诊治；2.血压偏高，内科定期复查。', // 医学建议
    //             opinion: '职业禁忌证：未控制的糖尿病，属高温作业在岗职业禁忌证，目前不宜从事高温危害作业。', // 意见
    //             CwithO: '禁忌证', // 结论
    //           },
    //           {
    //             name: '嵇汉文',
    //             gender: '1',
    //             age: '36',
    //             workType: '中间体羟化工',
    //             workYears: '7年6个月',
    //             harmFactors: '44444',
    //             abnormalIndex: '333333333↑',
    //             dedicalAdvice: '1.22222',
    //             opinion: '111111。', // 不一定有
    //             CwithO: '疑似职业病',
    //           },
    //           {
    //             workType: '测试工种',
    //             name: '鲍承志',
    //             gender: '0',
    //             age: '36',
    //             harmFactors: '粉尘',
    //             abnormalIndex: '测试',
    //             opinion: '意见',
    //             dedicalAdvice: '医学建议',
    //             CwithO: '禁忌证',
    //             workYears: '11',
    //           },
    //           {
    //             workType: '测试工种',
    //             name: '鲍承志',
    //             gender: '0',
    //             age: '36',
    //             harmFactors: '粉尘',
    //             abnormalIndex: '测试',
    //             opinion: '意见',
    //             dedicalAdvice: '医学建议',
    //             CwithO: '疑似职业病aa',
    //             workYears: '11',
    //           },
    //         ],
    //       },

    //     ] }
    //   ) },
    // });
  }

  // 体检报告批量上报 暂不开通 后期开通需维护
  // async tjReportedUp(ctx) {
  //   try {

  //     const params = ctx.request.body;
  //     const accept = ctx.header.accept;
  //     const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
  //     console.log(`tjReportedUpV${version}`);
  //     const result = await this[`tjReportedUpV${version}`](params);
  //     if (result) {
  //       ctx.helper.renderCustom(ctx, {
  //         status: result.status,
  //         message: result.message,
  //       });
  //     }

  //   } catch (error) {
  //     console.log(error);
  //     ctx.helper.renderCustom(ctx, {
  //       status: 500,
  //       message: '未找到对应版本',
  //     });
  //   }
  // }

  // async tjReportedUpV1_0(params) {
  //   const { ctx } = this;
  //   try {

  //     const { projectList, PhysicalExamOrgCode } = params;
  //     console.log(projectList, PhysicalExamOrgCode);
  //     const PhysicalExamOrg = await ctx.model.PhysicalExamOrg.find({ organization: PhysicalExamOrgCode });
  //     const { _id } = PhysicalExamOrg[0] || {}; // 委托单位id

  //     if (PhysicalExamOrg.length === 0) {
  //       ctx.helper.renderCustom(ctx, {
  //         status: 415,
  //         message: '该委托单位还未注册, 请尽快注册',
  //       });
  //       return;
  //     }


  //     for (let index = 0; index < projectList.length; index++) {
  //       const { projectNumber, reportStatus } = projectList[index];
  //       await ctx.model.Healthcheck.update({ projectNumber, physicalExaminationOrgID: _id }, {
  //         reportStatus,
  //         applyTime: new Date(),
  //       });
  //     }

  //     return { status: 200, message: '上报成功' };

  //   } catch (error) {
  //     ctx.logger.error(error);
  //     console.error(error);
  //     ctx.helper.renderFail(ctx, {
  //       message: 'error',
  //     });
  //   }
  // }

  // 获取检测项目列表
  async projectList() {
    const { ctx, config } = this;
    // 版本号检查
    const accept = ctx.header.accept;
    const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
    const versions = [ '1_0' ];
    if (!version || version.indexOf(versions) === -1) {
      return ctx.helper.renderCustom(ctx, {
        status: 406,
        message: { errMsgs: [ '版本号错误，请检查' ] },
        data: null,
      });
    }

    // 获取数据
    const params = ctx.request.body;
    if (typeof params.regAddr === 'string' || typeof params.dates === 'string') {
      return ctx.helper.renderCustom(ctx, { status: 400, message: 'regAddr和dates数据类型必须为数组' });
    }
    params.curPage = params.curPage ? +params.curPage : 1;
    params.limit = params.limit ? +params.limit : 10;

    const img_static_path = config.static.prefix + config.upload_http_path;
    if (params.regAddr && params.regAddr.length) { // 传了选择区域
      const tempArr = [];
      params.regAddr.forEach(ele => {
        tempArr.push(ele.area_code);
      });
      params.regAddr = tempArr;
    } else { // 没有传选择区域
      params.regAddr = null;
    }

    await ctx.service.report['getProjectsV' + version](params).then(async res => {
      const data = [];
      for (const ele of res) {
        const newItem = JSON.parse(JSON.stringify(ele));
        // console.log(newItem.EnterpriseID, 'ssssssssssssssssssssssssss');
        newItem.applyTime = ele.applyTime ? moment(ele.applyTime).format('YYYY-MM-DD') : '';
        newItem.reportTime = ele.reportTime ? moment(ele.reportTime).format('YYYY-MM-DD') : '';
        newItem.investigationTime = ele.investigationTime ? moment(ele.investigationTime).format('YYYY-MM-DD') : '';
        newItem.completeTime = ele.completeTime ? moment(ele.completeTime).format('YYYY-MM-DD') : '';
        // 文件前缀
        newItem.file_static_path = img_static_path;
        if (newItem.preventAssess) {
          newItem.preventAssess.level = newItem.preventAssess.level ? newItem.preventAssess.level + '级' : newItem.preventAssess.level;
        }
        // 识别行业分类的中文名
        const newCompanyIndustry = [];
        const industryArr = ele.companyIndustry;
        if (industryArr && industryArr.length) {
          // 每一个行业
          for (let j = 0; j < industryArr.length; j++) {
            const oneIndustry = industryArr[j];
            const chineseName = [];

            const len = oneIndustry.length;
            let industryCategory = await ctx.model.IndustryCategory.findOne({ value: oneIndustry[0] });
            for (let i = 0; i < len; i++) {
              if (industryCategory) {
                chineseName.push(industryCategory.label);
                if (industryCategory.children) industryCategory = industryCategory.children.filter(ele => ele.value === oneIndustry[i + 1])[0];
              }
            }

            newCompanyIndustry.push(chineseName);
          }
        }

        newItem.companyIndustry = newCompanyIndustry;

        data.push(newItem);
      }
      // 查询总数
      const total = await ctx.service.report.getCount(params) || 0;
      const completed = await ctx.service.report.getCount({
        ...params,
        completeStatus: 1,
      }) || 0;
      const updated = await ctx.service.report.getCount({
        ...params,
        completeUpdate: 1,
      }) || 0;
      ctx.helper.renderCustom(ctx, {
        message: '数据获取成功', data: {
          data,
          count: {
            total,
            completed,
            updated,
          },
        },
      });
    })
      .catch(err => {
        // console.log(**********, err);
        ctx.helper.renderCustom(ctx, { status: 400, message: '系统错误', data: err });
      });
  }

  // 获取检测结果数据
  async getCheckAssessment(ctx) {
    const jobHealthId = ctx.query.jobHealthId;
    const accept = ctx.header.accept;
    const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
    const versions = [ '1_0' ];
    if (!version || version.indexOf(versions) === -1) {
      return ctx.helper.renderCustom(ctx, {
        status: 406,
        message: { errMsgs: [ '版本号错误，请检查' ] },
        data: null,
      });
    }
    const data = await ctx.service.report['getCheckAssessmentV' + version]({ jobHealthId });
    if (data) {
      ctx.helper.renderCustom(ctx, { data });
    } else {
      ctx.helper.renderCustom(ctx, { status: 404, message: `找不到id为 ${jobHealthId} 的检测项目`, data: null });
    }
  }

  // 获取体检机构数据
  async getPhysicalExamOrg(ctx) {
    ctx.auditLog('/api/physical/physicalExamOrg: 获取体检机构数据', ctx, 'info');
    console.log(ctx);
    const accept = ctx.header.accept;
    const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
    const versions = [ '1_0' ];
    if (!version || version.indexOf(versions) === -1) {
      return ctx.helper.renderCustom(ctx, {
        status: 406,
        message: '版本号错误，请检查',
        data: null,
      });
    }
    // 参数校验
    const startTime = ctx.query.startTime ? new Date(ctx.query.startTime) : null;
    const endTime = ctx.query.endTime ? new Date(ctx.query.endTime) : null;
    const query = { ctime: { $exists: true } }; // 机构创建时间
    if (startTime) query.ctime.$gte = startTime;
    if (endTime) query.ctime.$lte = endTime;
    const data = await ctx.service.report['getPhysicalExamOrgV' + version](query);
    if (data && data.count) {
      ctx.helper.renderCustom(ctx, { message: '体检机构获取成功', data });
    } else {
      ctx.helper.renderCustom(ctx, { status: 404, message: '找不到该时间段内创建的体检机构', data: null });
    }
  }
  // 获取个人体检信息
  async getPhysicalExamInfo(ctx) {
    ctx.auditLog('/api/physical/physicalExamInfo: 获取个人体检信息参数', ctx.query, 'info');
    console.log(ctx);
    const accept = ctx.header.accept;
    const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
    const versions = [ '1_0' ];
    if (!version || version.indexOf(versions) === -1) {
      return ctx.helper.renderCustom(ctx, {
        status: 406,
        message: '版本号错误，请检查',
        data: null,
      });
    }
    // 参数校验
    const startTime = ctx.query.startTime ? new Date(ctx.query.startTime) : null;
    const endTime = ctx.query.endTime ? new Date(ctx.query.endTime) : null;
    const { page = 1, limit = 10 } = ctx.query;
    const query = { createTime: { $exists: true } }; // 体检创建时间
    if (startTime) query.createTime.$gte = startTime;
    if (endTime) query.createTime.$lte = endTime;
    const data = await ctx.service.report['getPhysicalExamInfoV' + version](query, page, limit);
    if (data && data.count) {
      ctx.helper.renderCustom(ctx, { message: '体检数据获取成功', data });
    } else {
      ctx.helper.renderCustom(ctx, { status: 404, message: '找不到该时间段内更新的体检数据', data: null });
    }
  }
  // 获取职业病诊断数据
  async getOdisease(ctx) {
    ctx.auditLog('/api/physical/diagnosis: 获取诊断信息', ctx, 'info');
    console.log(ctx);
    const accept = ctx.header.accept;
    const version = ctx.helper.getAPIVersion(accept, 'application/oapi.zyws.v', '+json');
    const versions = [ '1_0' ];
    if (!version || version.indexOf(versions) === -1) {
      return ctx.helper.renderCustom(ctx, {
        status: 406,
        message: '版本号错误，请检查',
        data: null,
      });
    }
    // 参数校验
    const startTime = ctx.query.startTime ? new Date(ctx.query.startTime) : null;
    const endTime = ctx.query.endTime ? new Date(ctx.query.endTime) : null;
    const query = { updatedAt: { $exists: true } }; // 诊断报告更新时间
    if (startTime) query.updatedAt.$gte = startTime;
    if (endTime) query.updatedAt.$lte = endTime;
    const data = await ctx.service.report['getOdiseaseV' + version](query);
    if (data && data.count) {
      ctx.helper.renderCustom(ctx, { message: '诊断数据获取成功', data });
    } else {
      ctx.helper.renderCustom(ctx, { status: 404, message: '找不到该时间段内更新的诊断数据', data: null });
    }
  }
  async createPhy2(ctx) {
    try {
      const fs = require('fs');
      const res = await xlsx.parse(fs.readFileSync('./fzPhy.xlsx'));
      const reg = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;
      res[0].data = res[0].data.slice(0);
      const result = {
        success: [],
        error: [],
        hasExist: [],
      };
      for (let i = 1; i < res[0].data.length; i++) {
        const item = res[0].data[i];
        const regAddr = item[0] ? item[0].split('_') : [];
        const organization = reg.test(item[1]) ? item[1] + '' : '';
        const unitCode = reg.test(item[1]) ? '' : item[1] + '';
        console.log(organization, unitCode, 'organization, unitCode');
        const name = item[2] || '';
        // 查询库中是否有该体检公司
        const inOurPhysicalExamOrg = await ctx.service.importToDb.getOnePhysicalExamOrgBySearchKey({
          searchKey: organization || unitCode,
        });
        if (!inOurPhysicalExamOrg) {
          const physicalExamUser = {
            password: organization
              ? organization.slice(-6)
              : unitCode.slice(-6),
          };
          const physicalExamOrg = {
            name,
            shortName: name,
            organization,
            unitCode,
            regAddr,
          };
          // 没有的话创建
          const params = {
            physicalExamUser,
            physicalExamOrg,
          };
          try {
            const res = await ctx.service.importToDb.createPhysicalExamOrgFlow(
              params
            );
            result.success.push(res);
          } catch (error) {
            result.error.push(params);
            ctx.auditLog('/api/physical/createPhy2: 创建体检机构失败', `${error}`, 'error');
          }
        } else {
          result.hasExist.push(inOurPhysicalExamOrg._id);
        }
      }
      ctx.helper.renderCustom(ctx, {
        status: 200,
        message: result,
      });
    } catch (error) {
      console.log(error, 'wwwwwwwww');
    }

  }
  async createPhyFz(ctx) {
    try {
      // const ZoneCompanyListResultObj = await this.ctx.service.fzData.getOldDataFromZip('baseCompanyData');
      // const { crptList } = ZoneCompanyListResultObj;
      // console.log('共有企业数量', crptList.length);
      // await this.ctx.service.fzData.clearOurCompanyData(crptList);
      // const ZoneTjDataListResultObj = await this.ctx.service.fzData.getOldDataFromZip('baseTjData');
      // const { bhkList } = ZoneTjDataListResultObj;
      // console.log('共有体检数据数量', bhkList.length);
      // await this.ctx.service.fzData.clearHealthyCheckData(bhkList);
      // const ZoneOccdiscasetDataListResultObj = await this.ctx.service.fzData.getOldDataFromZip('baseOccdiscasetData');
      // const { occdiscasetList } = ZoneOccdiscasetDataListResultObj;
      // console.log('共有诊断数据数量', occdiscasetList.length);
      // const res = await this.ctx.service.fzData.createDiagnosis(occdiscasetList);

      try {
        console.log('FZDATA获取往年任务开始');
        console.time('FZDATA获取往年任务耗时');
        const zoneCodeArray = [
          '**********',
          '**********',
          '**********',
          '**********',
          '**********',
          '**********',
          '**********',
          '**********',
          '**********',
          '**********',
          '**********',
          '**********',
          '**********',
          '**********',
          '**********',
          '**********',
          '**********',
          '3501030600',
          '3501030700',
          '3501030800',
          '3501030900',
          '3501031000',
          '3501031100',
          '3501040000',
          '3501040100',
          '3501040200',
          '3501040300',
          '3501040400',
          '3501040500',
          '3501040600',
          '3501040700',
          '3501040800',
          '3501040900',
          '3501041000',
          '3501041100',
          '3501041200',
          '3501041300',
          '3501050000',
          '3501050100',
          '3501050200',
          '3501050300',
          '3501050400',
          '3501110000',
          '3501110100',
          '3501110200',
          '3501110300',
          '3501110400',
          '3501110500',
          '3501110600',
          '3501110700',
          '3501110900',
          '3501111000',
          '3501210000',
          '3501210100',
          '3501210200',
          '3501210300',
          '3501210400',
          '3501210500',
          '3501210600',
          '3501210700',
          '3501210800',
          '3501210900',
          '3501211100',
          '3501211200',
          '3501211300',
          '3501211400',
          '3501211500',
          '3501211700',
          '3501220000',
          '3501220100',
          '3501220200',
          '3501220300',
          '3501220400',
          '3501220500',
          '3501220600',
          '3501220700',
          '3501220800',
          '3501220900',
          '3501221000',
          '3501221100',
          '3501221200',
          '3501221300',
          '3501221400',
          '3501221500',
          '3501221600',
          '3501221700',
          '3501221800',
          '3501221900',
          '3501222000',
          '3501222100',
          '3501222200',
          '3501230000',
          '3501230100',
          '3501230200',
          '3501230300',
          '3501230400',
          '3501230500',
          '3501230600',
          '3501230700',
          '3501230800',
          '3501230900',
          '3501231000',
          '3501231100',
          '3501240000',
          '3501240100',
          '3501240200',
          '3501240300',
          '3501240400',
          '3501240500',
          '3501240600',
          '3501240700',
          '3501240800',
          '3501241000',
          '3501241100',
          '3501241200',
          '3501241300',
          '3501241400',
          '3501241500',
          '3501241600',
          '3501241700',
          '3501250000',
          '3501250100',
          '3501250200',
          '3501250300',
          '3501250400',
          '3501250500',
          '3501250600',
          '3501250700',
          '3501250800',
          '3501250900',
          '3501251000',
          '3501251100',
          '3501251200',
          '3501251300',
          '3501251400',
          '3501251500',
          '3501251600',
          '3501251700',
          '3501251800',
          '3501251900',
          '3501252000',
          '3501252100',
          '3501810000',
          '3501810100',
          '3501810200',
          '3501810300',
          '3501810400',
          '3501810500',
          '3501810600',
          '3501810700',
          '3501810800',
          '3501810900',
          '3501811000',
          '3501811100',
          '3501811200',
          '3501811300',
          '3501811400',
          '3501811500',
          '3501811600',
          '3501811700',
          '3501811800',
          '3501811900',
          '3501812000',
          '3501812100',
          '3501812200',
          '3501812300',
          '3501812400',
          '3501820000',
          '3501820100',
          '3501820200',
          '3501820300',
          '3501820400',
          '3501820500',
          '3501820600',
          '3501820700',
          '3501820800',
          '3501820900',
          '3501821000',
          '3501821100',
          '3501821200',
          '3501821300',
          '3501821400',
          '3501821500',
          '3501821600',
          '3501821700',
          '3501821800',
        ];
        // 1. 首先获取tokenid
        const authObj = {
          unitCode: '35010899',
          regCode: 'a8e32a5d9a724293a71f200c23c0f7fc',
        };
        const { tokenId } = await this.ctx.service.fzData.getVerifyInfo(authObj);
        for (const zoneCode of zoneCodeArray) {
          try {
            // 根据tokenId bhkType  bhkYear zoneCode ifNoDownCrptAndOrg 获取体检数据
            for (const nowYear of [ 2021, 2022 ]) {
              const toGetBhkData = {
                tokenId,
                bhkType: '3,4',
                bhkYear: nowYear,
                zoneCode,
                ifNoDownCrptAndOrg: 1,
              };
              // 结束标识 当获取到的数据为null的话 {"mess":"未查询到相关数据！","type":"03"}
              let finish = false;
              do {
                try {
                  const tjData = await this.ctx.service.fzData.getBhkData(
                    toGetBhkData
                  );
                  if (tjData === null) {
                    finish = true;
                  } else {
                    await this.ctx.service.fzData.clearHealthyCheckData(tjData);
                  }
                } catch (error) {
                  this.ctx.auditLog(
                    '获取往年任务里清除体检信息失败',
                    JSON.stringify(error),
                    'error'
                  );
                }
              } while (!finish);
            }
          } catch (error) {
            this.ctx.auditLog(
              `${zoneCode}同步福州数据失败`,
              JSON.stringify(error),
              'error'
            );
          }
        }
        console.timeEnd('FZDATA获取往年任务耗时');
      } catch (error) {
        this.ctx.auditLog('同步福州数据失败', JSON.stringify(error), 'error');
      }
      ctx.helper.renderSuccess(ctx, {
        data: '成功',
      });
    } catch (error) {
      ctx.helper.renderFail(ctx, {
        message: error,
      });
    }
  }
}

module.exports = JobhealthController;
