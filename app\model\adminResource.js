/**
 * Created by Zhanglc on 2022/3/16.
 * 所有端内部资源
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const AdminResourceSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    label: String, // 标题 检查重复用
    type: String, // 0为普通菜单 1为功能菜单
    routePath: String, // 路由路径
    icon: String, // icon图标样式
    componentPath: String, // 模板路径
    api: String, // 资源路径
    parentId: String, // 父级ID
    isExt: { // 是否由插件安装而来
      type: Boolean,
      default: false,
    },
    enable: { // 是否可见
      type: Boolean,
      default: true,
    },
    sortId: { // 排序
      type: Number,
      default: 0,
    },
    date: { // 创建时间
      type: Date,
      default: Date.now,
    },
    comments: String, // 描述
  });

  return mongoose.model('AdminResource', AdminResourceSchema, 'adminresources');
};
