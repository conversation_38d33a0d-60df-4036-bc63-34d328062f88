const Service = require('egg').Service;
const moment = require('moment');
const shortid = require('shortid');
class WhDataService extends Service {
  // 接收万华人员数据
  async getPersonData(item) {
    const { ctx } = this;

    // 递归地替换对象中的键名
    const sanitizeKeys = obj => {
      if (Array.isArray(obj)) {
        return obj.map(sanitizeKeys);
      } else if (obj !== null && typeof obj === 'object') {
        return Object.keys(obj).reduce((acc, key) => {
          const sanitizedKey = key.startsWith('$') ? 'departName' : key;
          acc[sanitizedKey] = sanitizeKeys(obj[key]);
          return acc;
        }, {});
      }
      return obj;
    };

    const sanitizedItem = sanitizeKeys(item);
    await ctx.model.WhPerson.insertMany(sanitizedItem);
    // const personData = require('./whdatas.json');
    // await this.processPersonInfo(personData);
    await this.processPersonInfo(sanitizedItem);
    // await this.personInfoSend(sanitizedItem);
  }

  async personInfoSend(data) {
    await this.ctx.service.rabbitmq.produce({
      message: data,
      exchange: 'whohs',
      routingKey: 'whPersonInfo',
    });
  }

  // 处理万华人员详细数据
  async processPersonInfo(personData) {
    const { ctx } = this;
    if (personData.length) {
      for (const item of personData) {
        const {
          cn: name,
          employeeNumber: unitCode,
          mobile: phoneNum,
          mail: email,
          sex,
          birthday,
          destinationindicator: IDNum,
          workStartDate,
          posDn,
          uid: _id,
        } = item;
        const isExistEmployee = await ctx.model.Employee.findOne({
          _id,
        });
        if (isExistEmployee) {
          const birth = birthday && moment(birthday, 'YYYYMMDD').toDate();
          const workStart =
            workStartDate && moment(workStartDate, 'YYYYMMDD').toDate();
          const age = birth && moment().diff(birth, 'years');
          const station = this.formatStationName(posDn);
          await ctx.model.Employee.updateOne(
            {
              _id,
            },
            {
              $set: {
                name,
                gender: sex === '1' ? '0' : (sex === '0' ? '1' : '无'),
                IDNum,
                station,
                phoneNum,
                workStart,
                unitCode,
                age,
              },
            }
          );
          await ctx.model.User.updateOne(
            {
              _id,
            },
            {
              $set: {
                name,
                gender: sex === '1' ? '0' : (sex === '0' ? '1' : '无'),
                idNo: IDNum,
                phoneNum,
                email,
                birth,
              },
            }
          );
          const existingRecord = await ctx.model.EmployeeStatusChange.findOne({
            employee: _id,
            'statusChanges.changType': 4, // 查询是否存在 changType 为 4 的记录
          });

          if (!existingRecord) {
            // 如果不存在 changType 为 4 的记录，则在数组头部插入
            await ctx.model.EmployeeStatusChange.findOneAndUpdate(
              { employee: _id }, // 匹配员工
              {
                $setOnInsert: { _id: shortid.generate() }, // 如果文档不存在，则设置 _id
                $push: {
                  statusChanges: {
                    $each: [
                      {
                        changType: 4,
                        EnterpriseID: isExistEmployee.EnterpriseID,
                        timestamp: workStart,
                        message: '入职',
                      },
                    ],
                    $position: 0, // 插入到数组的第一个位置
                  },
                },
              },
              { upsert: true, new: true }
            );
          }
        }
      }
    }
  }

  // 接收万华组织数据
  async getOrgData(data) {
    const { ctx } = this;
    await ctx.model.WhOrg.insertMany(data);
    // const orgData = require('./whorgs.json');
    // await this.processOrgInfo(orgData);
    await this.orgDataSend(data);
  }

  async orgDataSend(data) {
    await this.ctx.service.rabbitmq.produce({
      message: data,
      exchange: 'whohs',
      routingKey: 'whOrgData',
    });
  }

  // 处理万华组织数据
  async processOrgInfo(
    orgData
  ) {
    const { ctx } = this;

    // 检查是否需要处理顶级公司
    // await this.processNoTopCompany();

    const { uid, parentDn, changeType, orgType } = orgData;
    const name = this.formatOrgName(orgData.ou);
    const dingtreeInfo = {
      parentid: parentDn,
      topLevelOfTheEnterprise: orgType === '1',
      isDelete: changeType === '0',
      name,
    };

    try {
      const dingtree = await ctx.model.Dingtree.findOne({ _id: uid });

      if (dingtree) {
        // 如果记录存在，更新Dingtree和Adminorg
        await ctx.model.Dingtree.updateOne({ _id: uid }, { $set: dingtreeInfo });
        await ctx.model.Adminorg.updateOne(
          { _id: uid },
          {
            $set: {
              cname: name,
              productionStatus: changeType === '1' ? '2' : '0',
            },
          }
        );
        if (orgType === '1') {
          // 如果是顶级公司，记录到缓存
          ctx.app.redis.rpush('whOrgData:topDepartCache', uid);
        }
      } else {
        // 如果记录不存在，创建新记录
        dingtreeInfo._id = uid;
        await ctx.model.Dingtree.create(dingtreeInfo);

        if (orgType === '1') {
          const adminorgInfo = await ctx.model.Adminorg.findOne({ _id: uid });
          if (!adminorgInfo) {
            await ctx.model.Adminorg.create({
              _id: uid,
              cname: name,
              isactive: '1',
              productionStatus: changeType === '1' ? '2' : '0',
            });
          } else {
            await ctx.model.Adminorg.updateOne(
              {
                _id: uid,
              },
              {
                $set: {
                  cname: name,
                  productionStatus: changeType === '1' ? '2' : '0',
                },
              }
            );
          }
          ctx.app.redis.rpush('whOrgData:topDepartCache', uid);
        }
      }
    } catch (error) {
      ctx.auditLog('组织信息处理失败', error.message, 'error');
      throw error;
    }
  }

  formatOrgName(ou) {
    let name = '';
    for (const item of ou) {
      if (item.lang === 'ZH') {
        name = item.desc;
        break;
      }
    }
    return name;
  }

  formatStationName(posDs) {
    let station = '';
    if (posDs) {
      for (const item of posDs) {
        if (item['@lang'] === 'ZH') {
          station = item.departName;
          break;
        }
      }
    }
    return station;
  }

  async fetchDepartmentData() {
    const { ctx } = this;
    try {
      const firstPipeline = [
        {
          $match: {
            EnterpriseID: { $exists: false },
          },
        },
        {
          $graphLookup: {
            from: 'dingtrees',
            startWith: '$_id',
            connectFromField: 'parentid',
            connectToField: '_id',
            as: 'bumeninfo',
            depthField: 'depth',
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $match: {
            'bumeninfo.topLevelOfTheEnterprise': true,
          },
        },
        {
          $group: {
            _id: '$_id',
            minDepth: { $min: '$bumeninfo.depth' },
            bumeninfo: { $push: '$bumeninfo' },
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $match: {
            $expr: {
              $eq: [ '$bumeninfo.depth', '$minDepth' ],
            },
          },
        },
        {
          $addFields: {
            EnterpriseID: '$bumeninfo._id',
          },
        },
        {
          $project: {
            _id: 1,
            EnterpriseID: '$bumeninfo._id',
          },
        },
      ];

      const firstResult = await ctx.model.Dingtree.aggregate(firstPipeline);
      if (firstResult.length) {
        for (const item of firstResult) {
          await ctx.model.Dingtree.updateOne(
            { _id: item._id },
            { $set: { EnterpriseID: item.EnterpriseID } }
          );
        }
      }

      // 处理集团本部的情况
      const secondPipeline = [
        {
          $match: { EnterpriseID: { $exists: false } },
        },
        {
          $graphLookup: {
            from: 'dingtrees',
            startWith: '$_id',
            connectFromField: 'parentid',
            connectToField: '_id',
            as: 'bumeninfo',
            depthField: 'depth',
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $group: {
            _id: '$_id',
            maxDepth: { $max: '$bumeninfo.depth' },
            bumeninfo: { $push: '$bumeninfo' },
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $match: {
            $expr: {
              $eq: [ '$bumeninfo.depth', '$maxDepth' ],
            },
          },
        },
        {
          $match: {
            'bumeninfo._id': '10000001',
          },
        },
        {
          $addFields: {
            EnterpriseID: '10000000',
          },
        },
        {
          $project: {
            _id: 1,
            EnterpriseID: 1,
          },
        },
      ];

      const secondResult = await ctx.model.Dingtree.aggregate(secondPipeline);
      if (secondResult.length) {
        for (const item of secondResult) {
          await ctx.model.Dingtree.updateOne(
            { _id: item._id },
            { $set: { EnterpriseID: item.EnterpriseID } }
          );
        }
      }
      ctx.auditLog(
        '处理部门，公司数据，添加各部门对应的EnterpriseID成功',
        '',
        'info'
      );
    } catch (error) {
      ctx.auditLog(
        '处理部门，公司数据，添加各部门对应的EnterpriseID失败',
        error.message,
        'error'
      );
    }
  }

  /*
  处理公司上下层级，添加adminorgs里的parentId，childrenId
  */
  async fetchCompanyData(topDepart) {
    const { ctx } = this;
    try {
      topDepart.push('HCM');
      // groupEnterprisesRecords
      const res = await ctx.model.Dingtree.aggregate([
        {
          $match: {
            _id: { $in: topDepart },
            topLevelOfTheEnterprise: true,
          },
        },
        {
          $graphLookup: {
            from: 'dingtrees',
            startWith: '$_id',
            connectFromField: 'parentid',
            connectToField: '_id',
            as: 'bumeninfo',
            depthField: 'depth',
          },
        },
        {
          $project: {
            bumeninfo: {
              $filter: {
                input: '$bumeninfo',
                as: 'item',
                cond: { $ne: [ '$$item._id', '$_id' ] },
              },
            },
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $match: {
            'bumeninfo.topLevelOfTheEnterprise': true,
          },
        },
        {
          $group: {
            _id: '$_id',
            minDepth: { $min: '$bumeninfo.depth' },
            bumeninfo: { $push: '$bumeninfo' },
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $match: {
            $expr: {
              $eq: [ '$bumeninfo.depth', '$minDepth' ],
            },
          },
        },
        {
          $addFields: {
            targetEnterpriseID: '$bumeninfo.EnterpriseID',
            EnterpriseID: '$_id',
            messageStatus: 2,
            isUp: true,
          },
        },
        {
          $project: {
            _id: 0,
            minDepth: 0,
            bumeninfo: 0,
          },
        },
        // {
        //   $out: 'groupEnterprisesRecords',
        // },
      ]);
      // 创建groupEnterprisesRecords文档，用create
      // await this.groupEnterprisesRecordsRepository.insertMany(res);
      for (const item of res) {
        const groupEnterprisesRecords =
          await ctx.model.GroupEnterprisesRecord.findOne({
            EnterpriseID: item.EnterpriseID,
          });
        if (!groupEnterprisesRecords) {
          await ctx.model.GroupEnterprisesRecord.create(item);
        } else {
          delete item._id;
          delete item.EnterpriseID;
          await ctx.model.GroupEnterprisesRecord.updateOne(
            { EnterpriseID: item.EnterpriseID },
            { $set: item }
          );
        }
      }
      // console.log(res);
      // parentId
      const parentIdLine = [
        {
          $match: {
            _id: { $in: topDepart },
            topLevelOfTheEnterprise: true,
          },
        },
        {
          $graphLookup: {
            from: 'dingtrees',
            startWith: '$_id',
            connectFromField: 'parentid',
            connectToField: '_id',
            as: 'bumeninfo',
            depthField: 'depth',
          },
        },
        {
          $project: {
            bumeninfo: {
              $filter: {
                input: '$bumeninfo',
                as: 'item',
                cond: { $ne: [ '$$item._id', '$_id' ] },
              },
            },
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $match: {
            'bumeninfo.topLevelOfTheEnterprise': true,
          },
        },
        {
          $group: {
            _id: '$_id',
            minDepth: { $min: '$bumeninfo.depth' },
            bumeninfo: { $push: '$bumeninfo' },
          },
        },
        {
          $unwind: '$bumeninfo',
        },
        {
          $match: {
            $expr: {
              $eq: [ '$bumeninfo.depth', '$minDepth' ],
            },
          },
        },
        {
          $addFields: {
            targetEnterpriseID: '$bumeninfo.EnterpriseID',
            EnterpriseID: '$_id',
            messageStatus: 2,
            isUp: true,
            parentId: [ '$bumeninfo.EnterpriseID' ], // Ensure parentId is an array
          },
        },
        {
          $project: {
            _id: 0,
            minDepth: 0,
            bumeninfo: 0,
          },
        },
        {
          $lookup: {
            from: 'adminorgs',
            localField: 'EnterpriseID',
            foreignField: '_id',
            as: 'adminorgs',
          },
        },
        {
          $unwind: {
            path: '$adminorgs',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            'adminorgs.parentId': {
              $cond: {
                if: { $isArray: '$adminorgs.parentId' },
                then: '$adminorgs.parentId',
                else: {
                  $cond: {
                    if: { $ne: [ '$adminorgs.parentId', null ] },
                    then: [ '$adminorgs.parentId' ],
                    else: [],
                  },
                },
              },
            },
            parentId: {
              $cond: {
                if: { $isArray: '$parentId' },
                then: '$parentId',
                else: {
                  $cond: {
                    if: { $ne: [ '$parentId', null ] },
                    then: [ '$parentId' ],
                    else: [],
                  },
                },
              },
            },
          },
        },
        {
          $addFields: {
            'adminorgs.parentId': {
              $concatArrays: [ '$parentId' ],
            },
            _id: '$EnterpriseID',
            parentId: '$parentId',
          },
        },
        {
          $project: {
            _id: 1,
            parentId: 1,
          },
        },
      ];

      const parentIdResult = await ctx.model.Dingtree.aggregate(parentIdLine);

      for (const item of parentIdResult) {
        await ctx.model.Adminorg.updateOne(
          { _id: item._id },
          { $set: { parentId: item.parentId } },
          { upsert: true }
        );
      }
      // childrenId
      const childrenIdLine = [
        {
          $match: {
            _id: { $in: topDepart },
          },
        },
        {
          $unwind: {
            path: '$parentId',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'adminorgs',
            localField: '_id',
            foreignField: 'parentId',
            as: 'adminorgChild',
          },
        },
        {
          $group: {
            _id: '$_id',
            adminorgChild: {
              $push: '$adminorgChild',
            },
            doc: {
              $first: '$$ROOT',
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: {
              $mergeObjects: [
                '$doc',
                {
                  adminorgChild: {
                    $reduce: {
                      input: '$adminorgChild',
                      initialValue: [],
                      in: {
                        $concatArrays: [ '$$value', '$$this' ],
                      },
                    },
                  },
                },
              ],
            },
          },
        },
        {
          $addFields: {
            childrenId: {
              $cond: {
                if: {
                  $gt: [{ $size: '$adminorgChild' }, 0 ],
                },
                then: {
                  $map: {
                    input: '$adminorgChild',
                    as: 'child',
                    in: '$$child._id',
                  },
                },
                else: [],
              },
            },
          },
        },
        {
          $project: {
            adminorgChild: 0, // 可以移除不需要的字段
          },
        },
      ];
      const childrenIdResult = await ctx.model.Adminorg.aggregate(
        childrenIdLine
      );

      for (const item of childrenIdResult) {
        await ctx.model.Adminorg.updateOne(
          { _id: item._id },
          { $set: { childrenId: item.childrenId } },
          { upsert: true }
        );
      }
      ctx.auditLog(
        `处理公司上下层级，添加adminorgs里的parentId，childrenId成功:code:${topDepart}`,
        '',
        'info'
      );
    } catch (error) {
      ctx.auditLog(
        `处理公司上下层级，添加adminorgs里的parentId，childrenId失败:code:${topDepart}`,
        error.message,
        'error'
      );
    }
  }

  /**
   * @description 处理万华不存在顶级企业的情况
   */
  async processNoTopCompany() {
    const { ctx } = this;
    try {
      const topWhAdminorg = await ctx.model.Adminorg.findOne({
        _id: 'HCM',
      });
      if (!topWhAdminorg) {
        await ctx.model.Adminorg.create({
          _id: 'HCM',
          cname: '万华化学集团股份有限公司',
          code: '91370000163044841F',
          regAdd: '山东省烟台市经济技术开发区重庆大街59号',
          corp: '廖增太',
          createTime: new Date('2001-01-05'),
          productionStatus: '2',
          parentId: [],
          isactive: '1',
          companyCategory: 'group',
        });
      }
      const whAdminUser = await ctx.model.AdminUser.findOne({
        newAddEnterpriseID: 'HCM',
      });
      if (!whAdminUser) {
        const whAdminuser = await ctx.model.AdminUser.create({
          name: '万华化学集团股份有限公司',
          userName: 'wh10000000',
          password: 'Tc666888.',
          group: this.adminGroup,
          newAddEnterpriseID: 'HCM',
          EnterpriseID: 'HCM',
        });
        await ctx.model.Adminorg.updateOne(
          { _id: 'HCM' },
          {
            $set: {
              adminUserId: whAdminuser._id,
              adminArray: [ whAdminuser._id ],
            },
          }
        );
        await this.processSuperAdmin(whAdminuser._id);
      }
      const topWhDingtree = await ctx.model.Dingtree.findOne({
        _id: 'HCM',
      });
      if (!topWhDingtree) {
        await ctx.model.Dingtree.create({
          _id: 'HCM',
          EnterpriseID: 'HCM',
          name: '万华化学集团股份有限公司',
          staff: [],
          isDelete: false,
          topLevelOfTheEnterprise: true,
          createTime: new Date('2001-01-05'),
        });
      }
      ctx.auditLog('处理万华不存在顶级企业的情况成功', '', 'info');
      return true;
    } catch (error) {
      ctx.auditLog('处理万华不存在顶级企业的情况失败', error.message, 'error');
      return false;
    }
  }

  /**
   * @param suerUserId
   * @description 处理超级管理员
   */
  async processSuperAdmin(suerUserId) {
    try {
      const superUser = await this.ctx.model.Policy.findOne({
        isSuper: true,
        user_ids: suerUserId,
      });
      if (!superUser) {
        await this.ctx.model.Policy.create({
          sortId: 0,
          user_ids: [ suerUserId ],
          enable: false,
          name: '超级管理员',
          scope_type: '',
          lastOperator: '系统',
          enterprise_ids: [],
          dingtree_ids: [],
          millConstruction_ids: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          isSuper: true,
          group_ids: [],
        });
        this.ctx.auditLog('处理超级管理员成功', '', 'info');
      }
    } catch (error) {
      this.ctx.auditLog('处理超级管理员失败', error.message, 'error');
    }
  }

  async processRedisData(message) {
    const { ctx } = this;
    try {
      const data = message;
      await this.processOrgInfo(data);
    } catch (error) {
      ctx.auditLog('队列消费处理失败', error.message, 'error');
    }
  }

  // 接收万华账号数据
  async getAccountData(data) {
    const { ctx } = this;
    await ctx.model.WhAccount.insertMany(data);
    // const accountData = require('./whaccounts.json');
    // if (accountData.length) {
    //   for (const item of accountData) {
    //     await this.processPersonAndDingtree(item);
    //   }
    // }
    await this.accountDataSend(data);
  }

  accountDataSend(data) {
    this.ctx.service.rabbitmq.produce({
      message: data,
      exchange: 'whohs',
      routingKey: 'whAccountData',
    });
  }

  // 处理万华人员部门数据
  async processPersonAndDingtree(item) {
    const { ctx } = this;
    const { uid, isBlock, cn, orgs } = item;

    try {
      if (orgs && orgs.length) {
        const dingtree = orgs[0].unitCode;
        const dingtreeInfo = await ctx.model.Dingtree.findOne({
          _id: dingtree,
        });
        if (dingtreeInfo) {
          await ctx.model.Dingtree.updateOne(
            { _id: dingtree },
            { $addToSet: { staff: uid } }
          );
          const EnterpriseID = dingtreeInfo.EnterpriseID;
          const employeeInfo = {
            enable: isBlock === '1',
            name: cn,
            EnterpriseID,
            status: isBlock === '1' ? 1 : 0,
            departs: [ dingtree ],
            _id: uid,
          };
          let ourEmployeeInfo = await ctx.model.Employee.findOne({
            _id: uid,
          }).lean();
          if (!ourEmployeeInfo) {
            ourEmployeeInfo = await ctx.model.Employee.create(employeeInfo);
            await ctx.model.User.create({
              enable: isBlock === '1',
              userName: uid,
              name: cn,
              companyId: [ EnterpriseID ],
              password: 'Tc666888.',
              employeeId: uid,
              _id: uid,
            });
            // 生成新上岗审核记录
            const nowStationIds = await ctx.service.byData.millSearch({
              departId: dingtree,
              EnterpriseID,
            });
            if (nowStationIds.length === 0) return;
            const auditStationChangeInfo = {
              _id: shortid.generate(),
              employeeId: ourEmployeeInfo._id,
              nowEnterpriseID: EnterpriseID,
              status: 0,
              nowDingtreeIds: dingtree,
              nowStationIds,
              stationStatus: 4,
              employeeName: ourEmployeeInfo.name,
              files: null,
            };
            await this.ctx.model.AuditStationChange.create(
              auditStationChangeInfo
            );
          } else {
            await ctx.model.Employee.updateOne(
              {
                _id: uid,
              },
              {
                $set: employeeInfo,
              }
            );
            await ctx.model.User.updateOne(
              {
                employeeId: uid,
              },
              {
                $set: {
                  enable: isBlock === '1',
                  name: cn,
                  userName: uid,
                  companyId: [ EnterpriseID ],
                },
              }
            );
            // 如果部门变更
            if (dingtree !== ourEmployeeInfo.departs[0]) {
              // 原部门进行删除
              await ctx.model.Dingtree.updateOne(
                { _id: ourEmployeeInfo.departs[0] },
                { $pull: { staff: uid } }
              );
              // 更新员工状态变更表
              const message = '转入部门';
              const needToIndo = {
                changType: 3,
                EnterpriseID,
                departsTo: [[ dingtree ]],
                message,
              };
              await ctx.model.EmployeeStatusChange.findOneAndUpdate(
                { employee: uid },
                {
                  $setOnInsert: { _id: shortid.generate() },
                  $push: {
                    statusChanges: needToIndo, // 将新的变更记录 push 到数组末尾
                  },
                },
                { upsert: true, new: true }
              );
              // 计算是否需要审核车间岗位信息：转岗、离岗
              const stationStatus = 2;
              await ctx.service.byData.calculateIsNeedReview(
                ourEmployeeInfo,
                employeeInfo,
                uid,
                stationStatus
              );
            }
          }
        }
        ctx.auditLog('处理万华人员部门数据成功', uid, 'info');
      }
    } catch (error) {
      ctx.auditLog(`处理万华人员部门:${uid}数据错误`, error.message, 'error');
      throw new Error(error);
    }
  }

  // 接收万华万华岗位(管理组)数据
  async getPostData(data) {
    const { ctx } = this;
    if (data.length) {
      // 1. 批量插入数据到 WhPostInfo 表
      await ctx.model.WhPostInfo.insertMany(data);
      ctx.auditLog('万华岗位推送成功', `${data.length}条岗位直接插入`, 'info');
      this.postInfoSend(data);
    } else {
      throw new Error('数据为空');
    }
  }

  postInfoSend(data) {
    this.ctx.service.rabbitmq.produce({
      message: data,
      exchange: 'whohs',
      routingKey: 'whPostInfo',
    });
  }

  // 处理万华岗位(管理组)数据
  async postInfoReceive(data) {
    const { ctx, config } = this;

    try {

      // 2. 数据分类：删除和更新/插入
      const toDelete = [];
      const toUpdateOrInsert = [];

      for (const item of data) {
        const { positionId: _id, texts, deleteFlag, mdgDepartId } = item;

        if (deleteFlag === 'X') {
          // 收集删除的 ID
          toDelete.push(_id);
        } else {
          // 收集需要更新或插入的记录
          const name = this.formateTexts(texts);
          toUpdateOrInsert.push({ _id, name, lastOperator: 'supos', mdgDepartId });
        }
      }

      // 3. 批量删除记录
      if (toDelete.length > 0) {
        const deleteResult = await ctx.model.Policy.deleteMany({
          _id: { $in: toDelete },
        });
        ctx.auditLog(
          '万华岗位推送成功',
          `删除${deleteResult.deletedCount}条岗位`,
          'info'
        );
      }

      // 4. 批量更新或插入记录
      if (toUpdateOrInsert.length > 0) {
        const { groupID } = config;
        const { groupBaseRoleID } = groupID;
        const bulkOps = toUpdateOrInsert.map(({ _id, name, lastOperator, mdgDepartId }) => ({
          updateOne: {
            filter: { _id },
            update: {
              $set: {
                name,
                lastOperator,
                dingtree_ids: [ mdgDepartId ],
                group_ids: [ groupBaseRoleID ],
                scope_type: 'Dingtree',
                enable: true,
                isSuper: false,
              },
            },
            upsert: true, // 如果文档不存在，则创建
          },
        }));

        const bulkResult = await ctx.model.Policy.bulkWrite(bulkOps);
        ctx.auditLog(
          '万华岗位推送成功',
          `更新或插入${bulkResult.upsertedCount}条岗位`,
          'info'
        );
      }

      ctx.auditLog('万华岗位推送成功', '数据处理完成', 'info');
    } catch (error) {
      // 捕获并记录错误
      ctx.auditLog('万华岗位推送失败', error.message, 'error');
      throw new Error(`万华岗位推送失败: ${error.message}`);
    }
  }

  async getPostAndPersonData(params) {
    const { ctx } = this;
    const { data = [] } = params;
    await ctx.model.AcceptData.insertMany({
      params,
      model: 'WhPostAndPersonInfo',
    });
    if (data.length) {
      await ctx.model.WhPostAndPersonInfo.insertMany(data);
      ctx.auditLog(
        '万华岗位人员推送成功',
        `${data.length}条岗位直接插入`,
        'info'
      );
      // this.postAndPersonInfoSend(params);
      await this.postAndPersonInfoReceive(params);
    }
  }

  postAndPersonInfoSend(data) {
    this.ctx.service.rabbitmq.produce({
      message: data,
      exchange: 'whohs',
      routingKey: 'whPostAndPersonInfo',
    });
  }

  // 处理万华岗位人员(管理组)数据
  async postAndPersonInfoReceive(params) {
    const { ctx, config } = this;
    try {
      const { taskBillNo, data, authFlag } = params;
      const returnInfo = [];
      for (const item of data) {
        const res = await this.processPolicy(item, taskBillNo, authFlag);
        returnInfo.push(res);
      }
      ctx.auditLog(
        '万华岗位人员推送返回信息',
        JSON.stringify(returnInfo),
        'info'
      );
      await ctx.curl(config.whRequest.userPositionPush, {
        method: 'POST',
        dataType: 'json',
        data: {
          data: returnInfo,
        },
        headers: {
          'content-type': 'application/json',
          Authorization: `Basic ${Buffer.from(
            `${config.whRequest.username}:${config.whRequest.password}`
          ).toString('base64')}`,
        },
      });
    } catch (error) {
      ctx.auditLog('万华岗位人员推送失败', error.message, 'error');
      throw new Error(`万华岗位人员推送失败: ${error.message}`);
    }
  }

  async processPolicy(item, taskBillNo, authFlag) {
    const { ctx } = this;
    const {
      userId,
      positionId,
      timestamp,
      deleteFlag,
      validFlag,
      taskBillLine,
    } = item;

    try {
      if (authFlag === 'X') {
        // 查找台账记录
        const record = await ctx.model.WhPostAndPersonInfo.findOne({
          userId,
          positionId,
        }, { sort: { createdAt: -1 } });

        if (!record) {
          ctx.auditLog(
            '万华岗位人员推送失败',
            `未找到台账记录，userId: ${userId}, positionId: ${positionId}`,
            'error'
          );
          return {
            errorMessage: '未找到台账记录',
            taskBillNo: taskBillNo + '',
            taskBillLine,
            returnType: 'E',
          };
        }

        // 比较台账的timestamp和推送的timestamp
        if (timestamp < record.timestamp) {
        // 检查不通过
          ctx.auditLog(
            '万华岗位人员推送失败',
            `版本号已过期，taskBillNo: ${taskBillNo}, taskBillLine: ${taskBillLine}`,
            'error'
          );
          return {
            errorMessage: '版本号已过期',
            taskBillNo: taskBillNo + '',
            taskBillLine,
            returnType: 'E',
          };
        }

        // 查找岗位信息
        const policyInfo = await ctx.model.Policy.findOne({
          _id: positionId,
        });
        if (!policyInfo) {
          ctx.auditLog(
            '万华岗位人员推送失败',
            `未找到岗位信息，positionId: ${positionId}`,
            'error'
          );
          return {
            errorMessage: '岗位信息未推送',
            taskBillNo: taskBillNo + '',
            taskBillLine,
            returnType: 'E',
          };
        }

        // 执行相应的操作
        if (deleteFlag === 'X') {
          await ctx.model.Policy.updateOne(
            {
              _id: positionId,
            },
            {
              $pull: { user_ids: userId },
            }
          );
          ctx.auditLog(
            '万华岗位人员推送成功',
            `成功从岗位 ${positionId} 删除人员 ${userId}`,
            'info'
          );
        } else if (!deleteFlag) {
          if (validFlag === 'X') {
            await ctx.model.Policy.updateOne(
              {
                _id: positionId,
              },
              {
                $addToSet: { user_ids: userId },
              }
            );
            await this.addAdminUser(userId);
            ctx.auditLog(
              '万华岗位人员推送成功',
              `成功将人员 ${userId} 添加到岗位 ${positionId}`,
              'info'
            );
          } else {
            await ctx.model.Policy.updateOne(
              {
                _id: positionId,
              },
              {
                $pull: { user_ids: userId },
              }
            );
            ctx.auditLog(
              '万华岗位人员推送成功',
              `成功从岗位 ${positionId} 删除人员 ${userId}`,
              'info'
            );
          }
        }
      } else {
        const policyInfo = await ctx.model.Policy.findOne({
          _id: positionId,
        });
        if (policyInfo) {
          if (deleteFlag === 'X') {
            await ctx.model.Policy.updateOne(
              { _id: positionId },
              { $pull: { user_ids: userId } }
            );
            ctx.auditLog(
              '万华岗位人员推送成功',
              `成功从岗位 ${positionId} 删除人员 ${userId}`,
              'info'
            );
          } else if (!deleteFlag) {
            if (validFlag === 'X') {
              await ctx.model.Policy.updateOne(
                { _id: positionId },
                { $addToSet: { user_ids: userId } }
              );
              await this.addAdminUser(userId);
              ctx.auditLog(
                '万华岗位人员推送成功',
                `成功将人员 ${userId} 添加到岗位 ${positionId}`,
                'info'
              );
            } else {
              await ctx.model.Policy.updateOne(
                { _id: positionId },
                { $pull: { user_ids: userId } }
              );
              ctx.auditLog(
                '万华岗位人员推送成功',
                `成功从岗位 ${positionId} 删除人员 ${userId}`,
                'info'
              );
            }
          }
        } else {
          ctx.auditLog(
            '万华岗位人员推送失败',
            `未找到岗位信息，positionId: ${positionId}`,
            'error'
          );
        }
        return {
          errorMessage: '',
          taskBillNo: taskBillNo + '',
          taskBillLine,
          returnType: 'S',
        };
      }

      return {
        errorMessage: '',
        taskBillNo: taskBillNo + '',
        taskBillLine,
        returnType: 'S',
      };
    } catch (error) {
      // 捕获错误
      ctx.auditLog(
        '万华岗位人员推送失败',
        `处理失败，错误信息: ${error.message}`,
        'error'
      );
      return {
        errorMessage: '处理失败',
        taskBillNo: taskBillNo + '',
        taskBillLine,
        returnType: 'E',
      };
    }
  }

  formateTexts(texts) {
    let name = '';
    for (const item of texts) {
      if (item.language === 'ZH') {
        name = item.text;
        break;
      }
    }
    return name;
  }

  async addAdminUser(user_id) {
    const { ctx } = this;
    const userExist = await ctx.model.AdminUser.findOne({
      employees: { $in: [ user_id ] },
    });
    if (userExist) {
      return;
    }
    const employeeInfo = await ctx.model.Employee.findOne({
      _id: user_id,
    });
    const adminUserInfo = {
      _id: employeeInfo._id,
      userName: employeeInfo._id,
      name: employeeInfo.name || '',
      phoneNum: employeeInfo.phoneNum || '',
      unitCode: employeeInfo.unitCode || '',
      IDcard: employeeInfo.IDNum,
      newAddEnterpriseID: employeeInfo.EnterpriseID,
      password: 'Tc666888.',
      employees: [ employeeInfo._id ],
      userId: employeeInfo._id,
      source: 'qy',
      enable: true,
      state: '1',
      group: ctx.app.config.groupID.adminGroupID,
    };
    const adminUser = await ctx.model.AdminUser.findOneAndUpdate(
      { _id: employeeInfo._id },
      adminUserInfo,
      {
        upsert: true,
        new: true,
        setDefaultsOnInsert: true,
      }
    );
    await ctx.model.User.updateOne({ _id: user_id }, { $set: { group: ctx.app.config.adminGroupId } });
    const adminOrg = await ctx.model.Adminorg.findOne({
      _id: employeeInfo.EnterpriseID,
    });
    if (adminOrg) {
      if (adminOrg.adminUserId) {
        await ctx.model.Adminorg.updateOne(
          { _id: employeeInfo.EnterpriseID },
          {
            $addToSet: { adminArray: adminUser._id },
          }
        );
      } else {
        await ctx.model.Adminorg.updateOne(
          { _id: employeeInfo.EnterpriseID },
          {
            $set: {
              adminUserId: adminUser._id,
              phoneNum: adminUser.phoneNum,
              contract: adminUser.name,
            },
            $addToSet: { adminArray: adminUser._id },
          }
        );
      }
    }
  }

  // 定时任务处理万华已存组织权限范围
  async processOrgExistInfo() {
    const { ctx } = this;
    try {
      const orgExistInfo = await ctx.model.WhPostInfo.find({
        deleteFlag: { $ne: 'X' },
      }).lean();
      const bukOps = [];
      for (const item of orgExistInfo) {
        const { mdgDepartId, positionId } = item;
        const policyExist = await ctx.model.Policy.findOne({
          _id: positionId,
        }).lean();
        if (policyExist) {
          const updateOps = {
            updateOne: {
              filter: { _id: positionId },
              update: {
                $set: {
                  dingtree_ids: [ mdgDepartId ],
                  scope_type: 'Dingtree',
                },
              },
            },
          };
          bukOps.push(updateOps);
        }
      }
      if (bukOps.length) {
        await ctx.model.Policy.bulkWrite(bukOps);
      }
      ctx.auditLog('处理万华已存组织权限范围成功', '', 'info');
    } catch (error) {
      ctx.auditLog('处理万华已存组织权限范围失败', error.message, 'error');
    }
  }
  // 上传职业健康档案(单个)
  async itemParameterVerification(item) {
    if (!item.WORKER_INFO || !item.WORKER_INFO.ID_CARD_TYPE_CODE) {
      throw new Error('WORKER_INFO和ID_CARD_TYPE_CODE不能为空');
    }
    if (
      !item.ENTERPRISE_INFO_EMPLOYER ||
      !item.ENTERPRISE_INFO_EMPLOYER.CREDIT_CODE_EMPLOYER
    ) {
      throw new Error('ENTERPRISE_INFO_EMPLOYER和CREDIT_CODE_EMPLOYER不能为空');
    }
    if (!item.EXAM_CONCLUSION_LIST || !item.EXAM_CONCLUSION_LIST.length) {
      throw new Error('EXAM_CONCLUSION_LIST不能为空');
    }
    if (!item.WORKER_INFO.WORKER_TELPHONE) {
      throw new Error('WORKER_TELPHONE不能为空');
    }
    if (!item.EXAM_DATE) {
      throw new Error('EXAM_DATE不能为空');
    }
    if (!item.REPORT_DATE) {
      throw new Error('REPORT_DATE不能为空');
    }
    if (!item.EXAM_TYPE_CODE) {
      throw new Error('EXAM_TYPE_CODE不能为空');
    }
    if (
      [ '01', '02', '03', '04', '05', '06' ].indexOf(item.EXAM_TYPE_CODE) === -1
    ) {
      throw new Error('EXAM_TYPE_CODE参数错误');
    }
    if (!item.EXAM_PROJECT_NUM) {
      throw new Error('EXAM_PROJECT_NUM不能为空');
    }
    if (!item.WORKER_INFO.BHK_CODE) {
      throw new Error('BHK_CODE不能为空');
    }
  }
  // 上传职业健康档案(国家标准)
  async healthExamRecordV1_0(physicalExamOrgId, item) {
    try {
      await this.itemParameterVerification(item);
      if (item.EXAM_DATE && item.EXAM_DATE.length === 8) {
        item.EXAM_DATE = this.getTrueTime(item.EXAM_DATE);
      }
      if (item.REPORT_DATE && item.REPORT_DATE.length === 8) {
        item.REPORT_DATE = this.getTrueTime(item.REPORT_DATE);
      }
      const { ctx } = this;
      const physicalExamOrg = await ctx.model.PhysicalExamOrg.findOne({
        _id: physicalExamOrgId,
      });
      if (item.ORG_CODE && item.ORG_CODE !== physicalExamOrg.organization) {
        throw new Error('您的token跟上传的创建机构代码不匹配');
      }
      // 1、通过用工单位统一信用代码判断是否预约体检
      const EnterpriseCode = item.ENTERPRISE_INFO_EMPLOYER.CREDIT_CODE_EMPLOYER;
      const cname = item.ENTERPRISE_INFO_EMPLOYER.ENTERPRISE_NAME_EMPLOYER;
      const checkType = item.EXAM_TYPE_CODE;
      //   let appointment = await this.ctx.model.HealthCheckAppointment.findOne({ EnterpriseCode, physicalExamOrgId, checkType });
      //   if (appointment) {
      //     const checkDate = new Date(item.EXAM_DATE);
      //     if (checkDate < appointment.startTime || checkDate > appointment.endTime) {
      //       appointment = null;
      //     }
      //   }
      // 2、处理用工单位信息
      const Enterprise = await this.handleEnterprise(EnterpriseCode, cname);
      // 3、处理用人单位信息
      //   if (item.ENTERPRISE_INFO && item.ENTERPRISE_INFO.CREDIT_CODE) {
      //     await this.handleEnterprise(item.ENTERPRISE_INFO.CREDIT_CODE, item.ENTERPRISE_INFO, physicalExamOrg);
      //   }
      // 4、人员信息
      const workerInfo = item.WORKER_INFO;
      const personInfo = await this.handlePersonInfo({
        EnterpriseID: Enterprise._id,
        name: workerInfo.WORKER_NAME,
        IDNum: workerInfo.ID_CARD,
        laborDispatching: !!(
          item.ENTERPRISE_INFO && item.ENTERPRISE_INFO.CREDIT_CODE
        ),
        gender: workerInfo.GENDER_CODE
          ? String(workerInfo.GENDER_CODE - 1)
          : '',
        phoneNum: workerInfo.WORKER_TELPHONE,
        reportCode: workerInfo.BHK_CODE,
        userId: '',
        source: 'oapi',
        status: [ '03', '05' ].includes(checkType) ? 0 : 1,
      });
      if (item.EXAM_TYPE_CODE === '06') {
        return {
          status: 200,
          message: '普通体检不创建体检项目',
        };
      }
      // 5、体检项目
      const healthcheck = await this.handleHealthCheck(
        physicalExamOrg,
        item,
        Enterprise
      );
      // 6、体检信息
      await this.handleSuspect(healthcheck, personInfo, item);
      // 7、更新预约单
      //   if (appointment) await this.updateAppointment(appointment, healthcheck._id, personInfo);
      // 8、更新体检项目中的人数以及企业表中的healcheckInfo
      await this.updateHealthCheck(healthcheck, '', Enterprise);
      return {
        status: 200,
        message: `用工单位：${item.ENTERPRISE_INFO_EMPLOYER.ENTERPRISE_NAME_EMPLOYER ||
          Enterprise.cname
        } - ${item.WORKER_INFO.WORKER_NAME}的职业健康档案上传成功`,
      };
    } catch (e) {
      this.ctx.auditLog(
        '体检系统对接 上传职业健康档案失败',
        e.message,
        'error'
      );
      return {
        status: 500,
        message: (item.id || item.ID) + '上传失败：' + e.message,
      };
    }
  }
  // 更新HealthCheck中的人数以及企业表中的体检信息
  async updateHealthCheck(healthcheck, appointmentId, Enterprise) {
    // const appointment = appointmentId ? await this.ctx.model.HealthCheckAppointment.findOne({ _id: appointmentId }) : {};
    // const appointmentPeopleNum = (appointment.employeeIds ? appointment.employeeIds.length : appointment.peopleNum) || 0;
    const suspectList = await this.ctx.model.Suspect.find({
      batch: healthcheck._id,
    });
    const updateData = {
      shouldCheckNum: suspectList.length,
      actuallNum: suspectList.length,
      recheckNum: suspectList.filter(ele => ele.recheck === '是').length,
      normal: suspectList.filter(ele => ele.CwithO === '目前未见异常').length,
      suspected: suspectList.filter(ele => ele.CwithO === '疑似职业病')
        .length,
      forbid: suspectList.filter(ele => ele.CwithO === '禁忌证').length,
      otherDisease: suspectList.filter(ele => ele.CwithO === '其他疾病或异常')
        .length,
    };
    await this.ctx.model.Healthcheck.updateOne(
      { _id: healthcheck._id },
      updateData
    );
    await this.ctx.model.Adminorg.updateOne(
      { _id: Enterprise._id },
      {
        $set: {
          healcheckInfo: {
            actuallNum: String(updateData.actuallNum), // 实检人数
            recheck: String(updateData.recheckNum), // 复查
            suspected: String(updateData.suspected), // 疑似
            forbid: String(updateData.forbid), // 禁忌证
            occupational: String(Enterprise.occupational || 0), // 职业病
            recentDay: healthcheck.checkDate, // 体检时间
          },
        },
      }
    );
  }
  // 处理企业信息(用工单位、用人单位)
  async handleEnterprise(EnterpriseCode, cname) {
    const Enterprise = await this.ctx.model.Adminorg.findOne({
      code: EnterpriseCode,
      isDelete: false,
      cname,
    });
    if (!Enterprise) {
      throw new Error(`${EnterpriseCode}用工单位不存在`);
    }
    return Enterprise;
  }
  // 按照体检结果更新预约单
  async updateAppointment(appointment, healthcheckId, personInfo) {
    let updateData = {};
    if (appointment.employeeIds.indexOf(personInfo._id) === -1) {
      updateData = {
        $push: { employeeIds: personInfo._id },
        $inc: { peopleNum: 1 },
      };
    }
    updateData.healthcheckId = healthcheckId;
    let jcRes = true;
    for (let i = 0; i < appointment.employeeIds.length; i++) {
      const employeeId = appointment.employeeIds[i];
      const res = await this.ctx.model.Suspect.findOne(
        { employeeId, batch: healthcheckId },
        { name: 1 }
      );
      if (!res) {
        jcRes = false;
        break;
      }
    }
    updateData.status = jcRes ? 6 : 5;
    const result = await this.ctx.model.HealthCheckAppointment.updateOne(
      { _id: appointment._id },
      updateData
    );
    if (result.nModified) {
      this.ctx.auditLog(
        '体检系统对接 上传体检报告-更新预约单成功',
        updateData,
        'info'
      );
      this.synchronizeAppointment(appointment._id); // 同步mongodb中的预约单到sqlserver中
    } else {
      this.ctx.auditLog(
        '体检系统对接 上传体检报告-更新预约单失败',
        result,
        'error'
      );
    }
  }
  // 获取体检结果中的CwithO
  getCwithO(EXAM_CONCLUSION_LIST = [], EXAM_ITEM_RESULT_LIST = []) {
    const data = EXAM_CONCLUSION_LIST[0];
    if (data.YSZYB_CODE) return '疑似职业病';
    if (data.ZYJJZ_NAME) return '禁忌证';
    if (data.QTJB_NAME) return '其他疾病或异常';
    if (EXAM_ITEM_RESULT_LIST[0] && EXAM_ITEM_RESULT_LIST[0].ABNORMAL === '1') {
      return '目前未见异常';
    }
    return EXAM_ITEM_RESULT_LIST[0]
      ? EXAM_ITEM_RESULT_LIST[0].EXAM_RESULT
      : '目前未见异常';
  }
  // 创建/更新suspect
  async handleSuspect(healthcheck, personInfo, item) {
    const age = item.WORKER_INFO.BIRTH_DATE
      ? moment().diff(item.WORKER_INFO.BIRTH_DATE, 'years')
      : ''; // 根据出生日期计算年龄
    const workType = await this.getWorkTypeCode(
      item.WORK_TYPE_CODE || item.OTHER_WORK_TYPE
    );
    const reportCode = item.WORKER_INFO.BHK_CODE;
    let suspect = await this.ctx.model.Suspect.findOne({
      employeeId: personInfo._id,
      batch: healthcheck._id,
      reportCode,
    });
    const newData = {
      name: personInfo.name,
      age,
      gender: item.WORKER_INFO.GENDER_CODE
        ? item.WORKER_INFO.GENDER_CODE - 1 + ''
        : '',
      workType,
      reportCode,
      harmFactors:
        item.EXAM_CONCLUSION_LIST[0].ITAM_NAME ||
        item.EXAM_CONCLUSION_LIST[0].ITAM_CODE ||
        item.CONTACT_FACTOR_CODE,
      otherHarmFactors: item.CONTACT_FACTOR_OTHER || item.FACTOR_OTHER || '',
      opinion: item.EXAM_CONCLUSION_LIST.map(
        ele => ele.EXAM_CONCLUSION_CODE
      ).join('；'), // 意见
      CwithO: this.getCwithO(
        item.EXAM_CONCLUSION_LIST,
        item.EXAM_ITEM_RESULT_LIST
      ),
      dedicalAdvice: '',
      batch: healthcheck._id,
      employeeId: personInfo._id,
      IDCard: item.WORKER_INFO.ID_CARD,
      EnterpriseID: personInfo.EnterpriseID,
      checkType: healthcheck.checkType,
      checkDate: new Date(item.EXAM_DATE),
      recheck: item.IS_REVIEW === '1' ? '是' : '否',
      riskFactorsOfPhysicalExaminations: item.EXAM_CONCLUSION_LIST.map(
        ele => {
          return {
            harmFactor: ele.ITAM_NAME || '',
            examConclusion: ele.EXAM_CONCLUSION_CODE || '',
            suspectedOccupationalDisease: ele.YSZYB_CODE || '',
            occupationalContraindications: ele.ZYJJZ_NAME || '',
            otherOrDes: ele.QTJB_NAME || '',
          };
        }
      ),
      bhkSubList: item.EXAM_ITEM_RESULT_LIST.map(ele => {
        return {
          itmcod: ele.EXAM_ITEM_PNAME || '',
          name: ele.EXAM_ITEM_NAME || '',
          classify: ele.EXAM_RESULT_TYPE || '',
          msrunt: ele.EXAM_ITEM_UNIT_CODE || '', // 计量单位
          itemStdValue: `${ele.REFERENCE_RANGE_MIN || ''} - ${ele.REFERENCE_RANGE_MAX || ''
          }`,
          result: ele.EXAM_RESULT || '',
          chkdat: new Date(item.EXAM_DATE),
          jdgptn: +ele.EXAM_RESULT_TYPE || 1,
          minVal: ele.REFERENCE_RANGE_MIN || '',
          maxVal: ele.REFERENCE_RANGE_MAX || '',
          diagRest: ele.EXAM_RESULT || '',
          rstFlag: ele.ABNORMAL || '',
        };
      }),
    };
    if (suspect) {
      await this.ctx.model.Suspect.updateOne({ _id: suspect._id }, newData);
    } else {
      suspect = await this.ctx.model.Suspect.create(newData);
    }
  }
  // 创建/更新体检项目(体检机构已存在)
  async handleHealthCheck(physicalExamOrgDetail, item, EnterpriseDetail) {
    const { ctx } = this;
    const physicalExaminationOrgID = physicalExamOrgDetail._id,
      EnterpriseID = EnterpriseDetail._id,
      year = new Date(item.EXAM_DATE).getFullYear() + '';
    const healthcheckeCode = {
      '01': '0',
      '02': '1',
      '03': '2',
      '04': '4',
      '05': '5',
    };
    const checkType = healthcheckeCode[item.EXAM_TYPE_CODE];
    const projectNumber = item.EXAM_PROJECT_NUM;
    const recheck = !!(item.IS_REVIEW && item.IS_REVIEW === '1');
    let healthchecke = await ctx.model.Healthcheck.findOne({
      physicalExaminationOrgID,
      EnterpriseID,
      projectNumber,
      checkType,
      recheck,
    });
    if (!healthchecke) {
      const newData = {
        organization:
          item.REPORT_ORGAN_CREDIT_CODE || physicalExamOrgDetail.name, // 检查机构名称
        physicalExaminationOrgID,
        enterpriseName:
          item.ENTERPRISE_INFO_EMPLOYER.ENTERPRISE_NAME_EMPLOYER ||
          EnterpriseDetail.cname,
        EnterpriseID,
        enterpriseContactsName: EnterpriseDetail.contract || '',
        enterpriseContactsPhonNumber: EnterpriseDetail.phoneNum || '',
        enterpriseAddr: EnterpriseDetail.districtRegAdd || [],
        workAddress: EnterpriseDetail.workAddress || [],
        year,
        projectNumber,
        checkDate: new Date(item.EXAM_DATE),
        checkEndDate: new Date(item.EXAM_DATE),
        checkPlace:
          (physicalExamOrgDetail.regAddr
            ? physicalExamOrgDetail.regAddr.join('')
            : '') + physicalExamOrgDetail.address || '',
        approvalDate: new Date(item.REPORT_DATE),
        applyTime: new Date(),
        checkType,
        shouldCheckNum: 0,
        actuallNum: 0,
        recheck: !!(item.IS_REVIEW && item.IS_REVIEW === '1'),
        reportStatus: true,
      };
      healthchecke = await ctx.model.Healthcheck.create(newData);
    } else {
      const updateData = {
        checkDate:
          new Date(item.EXAM_DATE) < healthchecke.checkDate
            ? new Date(item.EXAM_DATE)
            : healthchecke.checkDate,
        checkEndDate:
          new Date(item.EXAM_DATE) > healthchecke.checkEndDate
            ? new Date(item.EXAM_DATE)
            : healthchecke.checkEndDate,
      };
      await ctx.model.Healthcheck.updateOne(
        { _id: healthchecke._id },
        updateData
      );
    }
    return healthchecke;
  }
  // 创建/更新人员信息employee、user
  async handlePersonInfo(personInfo) {
    const { ctx } = this;
    const { EnterpriseID, IDNum, phoneNum } = personInfo;
    if (!IDNum) throw new Error('身份证号不能为空');
    let employeeInfo = await ctx.model.Employee.findOne({
      EnterpriseID,
      IDNum,
    });
    if (!employeeInfo) {
      employeeInfo = await ctx.model.Employee.findOne({
        EnterpriseID,
        phoneNum,
      });
    }
    if (!employeeInfo) {
      ctx.auditLog(
        '体检机构对接 上传用人单位信息失败',
        `用工单位ID为${EnterpriseID}的人员信息${IDNum}不存在`,
        'error'
      );
      throw new Error(`用工单位ID为${EnterpriseID}的人员信息${IDNum}不存在`);
    }
    return employeeInfo;
    // if (phoneNum) {
    //   const flag = /^1[3-9][0-9]{9}$/.test(phoneNum); // 手机号校验
    //   if (!flag) throw new Error('手机号格式错误');
    //   let user = await ctx.model.User.findOne({ phoneNum });
    //   if (!user) {
    //     user = await ctx.model.User.create({ name: employeeInfo.name, phoneNum, employeeId: employeeInfo._id, idNo: IDNum, companyId: [ EnterpriseID ], companyStatus: 2, birth: personInfo.birthDate });
    //   }
    //   if (user && user._id) {
    //     await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { userId: user._id });
    //   }
    // }
    // if (employeeInfo.departs.length === 0) {
    //   let dingtrees = await ctx.model.Dingtree.findOne({ EnterpriseID });
    //   if (!dingtrees) {
    //     dingtrees = await ctx.model.Dingtree.create({ EnterpriseID, name: '企业部门', type: '1' });
    //   }
    //   await ctx.model.Employee.updateOne({ _id: employeeInfo._id }, { departs: [[ dingtrees._id ]] });
    //   await ctx.model.Dingtree.updateOne({ _id: dingtrees._id }, { $addToSet: { staff: employeeInfo._id } });
    // }
    // return ctx.model.Employee.findOne({ _id: employeeInfo._id });
  }

  // 根据token获取体检机构id并校验
  async getOrgId(token = '') {
    token = token.replace(/\\\"/g, '"');
    const agentId = token ? JSON.parse(token).agentId : '';
    if (!agentId) throw new Error('token数据错误');
    const apiUser = await this.ctx.model.ApiUser.findOne(
      { _id: agentId, status: 1 },
      { orgId: 1 }
    );
    if (apiUser) {
      const physicalExamOrgId = apiUser.orgId;
      if (!physicalExamOrgId) {
        throw new Error('token错误或者权限不足');
      }
      const physicalExamOrg = await this.ctx.model.PhysicalExamOrg.findOne({
        _id: physicalExamOrgId,
      });
      if (!physicalExamOrg) {
        throw new Error('体检机构不存在');
      }
      return physicalExamOrgId;
    }
    throw new Error('体检机构找不到');
  }

  // 数组中时间格式化
  perseTime(arr = []) {
    const newArr = JSON.parse(JSON.stringify(arr));
    return newArr.map(item => {
      item.startTime = moment(item.startTime).format('YYYY-MM-DD HH:mm');
      item.endTime = moment(item.endTime).format('YYYY-MM-DD HH:mm');
      item.createdAt = moment(item.createdAt).format('YYYY-MM-DD HH:mm');
      item.updatedAt = moment(item.updatedAt).format('YYYY-MM-DD HH:mm');
      return item;
    });
  }

  // 根据code获取工种代码名称
  async getWorkTypeCode(code) {
    if (!code) return '';
    const data = await this.ctx.model.ZjWorkTypeCode.findOne({ code });
    return data ? data.name : code;
  }

  // 时间转化
  getTrueTime(dateString) {
    // dateString = '20200101'
    if (!dateString || dateString.length !== 8) return '';
    const year = dateString.substring(0, 4);
    const month = dateString.substring(4, 6);
    const day = dateString.substring(6, 8);
    return new Date(year, month - 1, day);
  }

  // 处理未处理的mdg数据
  async processNoMdgData() {
    const { ctx } = this;
    try {
      const employeeInfo = await ctx.model.Employee.find({
        $or: [
          { phoneNum: { $exists: false } },
          { IDNum: { $exists: false } },
        ],
      }).select('_id EnterpriseID');
      if (employeeInfo.length === 0) return;
      // 处理更新员工mdg信息
      const employeebulkOps = [];
      const userbulkOps = [];
      for (const item of employeeInfo) {
        const result = await this.handleEmployeeMdg({
          EnterpriseID: item.EnterpriseID,
          employeeId: item._id,
        });
        if (result) {
          const { employeeUpdateOp, userUpdateOp } = result;
          employeebulkOps.push(employeeUpdateOp);
          userbulkOps.push(userUpdateOp);
        }
      }
      await ctx.model.Employee.bulkWrite(employeebulkOps);
      await ctx.model.User.bulkWrite(userbulkOps);
    } catch (error) {
      ctx.auditLog('处理未处理的mdg数据失败', error.message, 'error');
      throw new Error(`处理未处理的mdg数据失败: ${error.message}`);
    }
  }

  // 更新员工mdg信息
  async handleEmployeeMdg(data) {
    const { ctx } = this;
    const { EnterpriseID, employeeId } = data;
    try {
      const mdgInfo = await ctx.model.WhPerson.findOne({
        uid: employeeId,
      }).sort({ createdAt: -1 }).lean();
      if (!mdgInfo) return;
      const {
        cn: name,
        employeeNumber: unitCode,
        mobile: phoneNum,
        mail: email,
        sex,
        birthday,
        destinationindicator: IDNum,
        workStartDate,
        posDn,
      } = mdgInfo;
      const birth = birthday && moment(birthday, 'YYYYMMDD').toDate();
      const workStart =
        workStartDate && moment(workStartDate, 'YYYYMMDD').toDate();
      const age = birth && moment().diff(birth, 'years');
      const station = this.formatStationName(posDn);
      const employeeUpdateOp = this.createUpdateOperation(employeeId, {
        name,
        gender: sex === '1' ? '0' : (sex === '0' ? '1' : '无'),
        IDNum,
        station,
        phoneNum,
        workStart,
        unitCode,
        age,
      });

      const userUpdateOp = this.createUpdateOperation(employeeId, {
        name,
        gender: sex === '1' ? '0' : (sex === '0' ? '1' : '无'),
        idNo: IDNum,
        phoneNum,
        email,
        birth,
      });
      await ctx.model.EmployeeStatusChange.findOneAndUpdate(
        {
          employee: employeeId,
          'statusChanges.changType': { $not: { $eq: 4 } },
        },
        {
          $setOnInsert: { _id: shortid.generate() },
          $push: {
            statusChanges: {
              $each: [
                {
                  changType: 4,
                  EnterpriseID,
                  timestamp: workStart,
                  message: '入职',
                },
              ],
              $position: 0, // 插入到数组的第一个位置
            },
          },
        },
        { upsert: true, new: true }
      );
      return { employeeUpdateOp, userUpdateOp };
    } catch (error) {
      ctx.auditLog(`员工:${employeeId}信息更新失败`, error.message, 'error');
      throw new Error(`员工:${employeeId}信息更新失败: ${error.message}`);
    }
  }

  /**
 * 生成 updateOne 操作，并去除值为空的字段
 * @param {string} id - 目标文档的 _id
 * @param {Object} data - 需要更新的字段对象
 * @return {Object} MongoDB bulkWrite updateOne 结构
 */
  createUpdateOperation(id, data) {
  // 过滤掉值为 undefined、null、空字符串的字段
    const filteredData = Object.fromEntries(
      Object.entries(data).filter(([ , value ]) => value !== undefined && value !== null && value !== '')
    );

    return {
      updateOne: {
        filter: { _id: id },
        update: { $set: filteredData },
      },
    };
  }
}

module.exports = WhDataService;
