'use strict';

const Service = require('egg').Service;

/**
 * 任务处理器服务
 */
class TaskProcessorService extends Service {

  constructor(ctx) {
    super(ctx);
    this.isProcessing = false;
    this.processingTasks = new Map();
  }

  /**
   * 启动任务处理器
   * @param {String} taskType - 任务类型
   * @param {Object} options - 处理选项
   */
  async startProcessor(taskType, options = {}) {
    if (this.isProcessing) {
      this.ctx.logger.warn(`任务处理器已在运行: ${taskType}`);
      return;
    }

    this.isProcessing = true;
    this.ctx.logger.info(`启动任务处理器: ${taskType}`);

    // 在后台持续处理任务
    setImmediate(() => {
      this._processLoop(taskType, options);
    });
  }

  /**
   * 停止任务处理器
   */
  async stopProcessor() {
    this.isProcessing = false;
    this.ctx.logger.info('任务处理器已停止');
  }

  /**
   * 处理循环
   * @param {String} taskType - 任务类型
   * @param {Object} options - 处理选项
   */
  async _processLoop(taskType, options) {
    console.log(`开始处理任务类型: ${options}`);
    while (this.isProcessing) {
      try {
        // 从队列中获取任务
        const taskId = await this.service.taskQueue.popTask(taskType, 5);

        if (taskId) {
          // 处理任务
          await this._processTask(taskId);
        }
      } catch (error) {
        this.ctx.logger.error('任务处理循环错误', error);
        // 短暂等待后继续
        await this._sleep(1000);
      }
    }
  }

  /**
   * 处理单个任务
   * @param {String} taskId - 任务ID
   */
  async _processTask(taskId) {
    try {
      // 获取任务信息
      const task = await this.service.taskQueue.getTask(taskId);
      if (!task) {
        this.ctx.logger.warn(`任务不存在: ${taskId}`);
        return;
      }

      this.ctx.logger.info(`开始处理任务: ${taskId}`, { taskType: task.taskType });

      // 标记任务为处理中
      this.processingTasks.set(taskId, Date.now());
      await this.service.taskQueue.updateTask(taskId, {
        status: 'processing',
        progress: 10,
        message: '任务处理中...',
        startTime: new Date().toISOString(),
      });

      // 根据任务类型调用相应的处理方法
      let result;
      switch (task.taskType) {
        case 'company':
          result = await this.service.fzData.processCompanyData(task.payload);
          break;
        case 'healthCheck':
          result = await this.service.fzData.processHealthCheckData(task.payload);
          break;
        case 'diagnosis':
          result = await this.service.fzData.processDiagnosisData(task.payload);
          break;
        default:
          throw new Error(`不支持的任务类型: ${task.taskType}`);
      }

      // 更新进度
      await this.service.taskQueue.updateTask(taskId, {
        status: 'processing',
        progress: 80,
        message: '数据处理完成，正在保存结果...',
      });

      // 标记任务完成
      await this.service.taskQueue.updateTask(taskId, {
        status: 'success',
        progress: 100,
        message: result.message || '任务处理完成',
        endTime: new Date().toISOString(),
        result: JSON.stringify(result),
        processed: result.processed || 0,
        duration: Date.now() - this.processingTasks.get(taskId),
      });

      this.ctx.logger.info(`任务处理完成: ${taskId}`, {
        processed: result.processed,
        duration: Date.now() - this.processingTasks.get(taskId),
      });

    } catch (error) {
      this.ctx.logger.error(`任务处理失败: ${taskId}`, error);

      // 标记任务失败
      await this.service.taskQueue.updateTask(taskId, {
        status: 'failed',
        progress: 0,
        message: `任务处理失败: ${error.message}`,
        endTime: new Date().toISOString(),
        error: error.message,
        duration: this.processingTasks.has(taskId) ? Date.now() - this.processingTasks.get(taskId) : 0,
      });
    } finally {
      // 清理处理中的任务记录
      this.processingTasks.delete(taskId);
    }
  }

  /**
   * 获取处理器状态
   * @return {Object} 处理器状态
   */
  getProcessorStatus() {
    return {
      isProcessing: this.isProcessing,
      processingTasksCount: this.processingTasks.size,
      processingTasks: Array.from(this.processingTasks.keys()),
    };
  }

  /**
   * 处理指定任务（立即处理，不通过队列）
   * @param {String} taskType - 任务类型
   * @param {Object} payload - 任务数据
   * @return {Promise<String>} 任务ID
   */
  async processTaskImmediately(taskType, payload) {
    // 创建任务
    const taskId = await this.service.taskQueue.createTask(taskType, payload, {
      priority: 'high',
    });

    // 立即处理任务
    setImmediate(() => {
      this._processTask(taskId);
    });

    return taskId;
  }

  /**
   * 批量处理任务
   * @param {String} taskType - 任务类型
   * @param {Array} payloads - 任务数据数组
   * @return {Promise<Array>} 任务ID数组
   */
  async processBatchTasks(taskType, payloads) {
    const taskIds = [];

    for (const payload of payloads) {
      const taskId = await this.service.taskQueue.createTask(taskType, payload);
      taskIds.push(taskId);
    }

    // 启动处理器（如果还没启动）
    if (!this.isProcessing) {
      this.startProcessor(taskType);
    }

    return taskIds;
  }

  /**
   * 暂停处理指定任务类型
   * @param {String} taskType - 任务类型
   */
  async pauseTaskType(taskType) {
    // 这里可以实现更复杂的暂停逻辑
    // 比如将任务类型加入暂停列表
    this.ctx.logger.info(`暂停任务类型: ${taskType}`);
  }

  /**
   * 恢复处理指定任务类型
   * @param {String} taskType - 任务类型
   */
  async resumeTaskType(taskType) {
    // 这里可以实现恢复逻辑
    this.ctx.logger.info(`恢复任务类型: ${taskType}`);
  }

  /**
   * 获取任务处理统计
   * @return {Promise<Object>} 统计信息
   */
  async getProcessingStats() {
    const stats = {
      processor: this.getProcessorStatus(),
      queues: {},
    };

    // 获取各类型队列长度
    const taskTypes = [ 'company', 'healthCheck', 'diagnosis' ];
    for (const taskType of taskTypes) {
      stats.queues[taskType] = await this.service.taskQueue.getQueueLength(taskType);
    }

    return stats;
  }

  /**
   * 睡眠函数
   * @param {Number} ms - 毫秒数
   */
  _sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = TaskProcessorService;
