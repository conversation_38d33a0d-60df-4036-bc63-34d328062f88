const httpProxy = require('http-proxy');


module.exports = () => {

  return async function proxyMiddleware(ctx, next) {
    const { oss } = ctx.app.config;
    // 定义代理规则
    const proxyRules = [
      {
        path: '/oss/',
        target: `${oss.endPoint}/`,
        appendQueryString: true,
        pathRewrite: {
          '^/oss/': '',
        },
      },
    ];
    const oldPath = ctx.request.url.split('?')[0];
    // 匹配代理规则
    const rule = proxyRules.find(rule => oldPath.startsWith(rule.path));
    if (rule) {
      // console.log('匹配到代理规则', rule, oldPath);
      // 设置目标 URL
      const target = rule.target;
      const appendQueryString = rule.appendQueryString || false;
      const queryString = rule.queryString || '';

      let proxyTarget = target;
      let rewitePath = '';
      // 处理路径 按照rules中的pathRewrite规则重写路径
      if (rule.pathRewrite) {
        const pathRewrite = rule.pathRewrite;
        for (const key in pathRewrite) {
          const reg = new RegExp(key);
          rewitePath = ctx.request.url.replace(reg, pathRewrite[key]);
        }
      }
      proxyTarget = proxyTarget + rewitePath;
      // 拼接参数的
      if (appendQueryString) {
        const delimiter = target.includes('?') ? '&' : '?';
        proxyTarget = proxyTarget + delimiter + queryString;
      }
      // 转发请求
      try {
        // 处理body参数
        // 创建代理服务器
        // console.log('proxyTarget', proxyTarget);
        // 正则替换掉最后一个?为空
        proxyTarget = proxyTarget.replace(/\?$/, '');
        const proxyServer = httpProxy.createProxyServer({
          changeOrigin: true,
          ignorePath: true,
          secure: false,
          logLevel: 'debug',
          // headers: ctx.request.headers,
          target: proxyTarget,
        });
        proxyServer.on('proxyReq', function(proxyReq) {
          proxyReq.setHeader('Cookie', '');
          if (ctx.request.rawBody) {
            //   let bodyData = JSON.stringify(ctx.request.rawBody)
            const bodyData = ctx.request.rawBody;
            // incase if content-type is application/x-www-form-urlencoded -> we need to change to application/json
            //   proxyReq.setHeader('Content-Type', 'application/x-www-form-urlencoded')
            proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
            // stream the content
            proxyReq.write(bodyData);
            // Add this line to set the Content-Disposition header to inline
            proxyReq.setHeader('Content-Disposition', 'inline');
          }
        });
        proxyServer.on('proxyRes', function(proxyRes) {
          // console.log(req, res);
          // Set the Content-Disposition header to inline for image files
          if (
            proxyRes.headers['content-type'] &&
            proxyRes.headers['content-type'].startsWith('image/')
          ) {
            proxyRes.headers['content-disposition'] = 'inline';
          }
        });
        await new Promise((resolve, reject) => {
          proxyServer.web(ctx.req, ctx.res, { target: proxyTarget }, err => {
            if (err) {
              reject(err);
            } else {
              resolve();
            }
          });
        });
      } catch (error) {
        // ctx.auditLog('代理错误', `${error} 。`, 'error');
      }
    } else {
      // 如果没有匹配的代理规则，继续下一个中间件处理
      await next();
    }
  };
};
