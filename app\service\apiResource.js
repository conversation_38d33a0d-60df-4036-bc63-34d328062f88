const Service = require('egg').Service;
const path = require('path');

// general是一个公共库，可用可不用
const {
  _list,
  _item,
  _count,
  _create,
  _update,
  _safeDelete,
  _removes,
} = require(path.join(process.cwd(), 'app/service/general'));


class ApiResourceService extends Service {

  async find(payload, {
    query = {},
    searchKeys = [],
    populate = [],
    files = null,
  } = {}) {
    const listdata = _list(this.ctx.model.ApiResource, payload, {
      query,
      searchKeys,
      populate,
      files,
      sort: {
        sortId: 1,
      },
    });
    return listdata;
  }

  async count(params = {}) {
    return _count(this.ctx.model.ApiResource, params);
  }

  async create(payload) {
    return _create(this.ctx.model.ApiResource, payload);
  }

  async removes(res, values, key = '_id') {
    return _removes(res, this.ctx.model.ApiResource, values, key);
  }

  async safeDelete(res, values) {
    return _safeDelete(res, this.ctx.model.ApiResource, values);
  }

  async update(res, _id, payload) {
    return _update(res, this.ctx.model.ApiResource, _id, payload);
  }

  async item(res, params = {}) {
    return _item(res, this.ctx.model.ApiResource, params);
  }

}

module.exports = ApiResourceService;
