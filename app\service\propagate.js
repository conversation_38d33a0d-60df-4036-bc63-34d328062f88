
const Service = require('egg').Service;

class PropagateService extends Service {
  // 创建一条新纪录
  async create(data) {
    const ctx = this.ctx;
    const oneData = new ctx.model.Propagate(data);
    return await oneData.save();
  }
  async update(query, data) {
    return await this.ctx.model.Propagate.updateOne(query, data, { new: true });
  }
  async findOne(query) {
    return await this.ctx.model.Propagate.findOne(query);
  }
  async deleteOne(query) {
    return await this.ctx.model.Propagate.deleteOne(query);
  }

}

module.exports = PropagateService;
