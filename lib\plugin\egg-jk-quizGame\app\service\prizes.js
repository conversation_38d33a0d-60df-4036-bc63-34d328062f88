const Service = require('egg').Service;
const path = require('path');
class PrizesService extends Service {
  // 抽奖功能
  async drawPrize(gameRecordId) {
    const { ctx, app } = this;

    if (!gameRecordId) throw new Error('gameRecordId不能为空');

    // 1. 查询游戏记录
    const cacheKeyRecord = `gameRecord:${gameRecordId}`;
    let gameRecord = await app.redis.get(cacheKeyRecord);
    if (!gameRecord) {
      // 使用 lean() 获取纯数据对象
      const record = await ctx.model.GameRecords.findById(gameRecordId).lean();
      if (!record) throw new Error('未找到游戏记录: ' + gameRecordId);
      gameRecord = record;
      await app.redis.set(cacheKeyRecord, JSON.stringify(gameRecord), 'EX', 300);
    } else {
      gameRecord = JSON.parse(gameRecord);
    }

    if (!gameRecord) throw new Error('未找到游戏记录: ' + gameRecordId);

    const { userId, gameEventId, Enterprise } = gameRecord;
    if (!gameEventId) throw new Error('未找到用户信息中的gameEventId, userId:' + userId);
    if (!Enterprise) throw new Error('未找到用户信息中的Enterprise, userId:' + userId);

    // 2. 判断用户是否有抽奖资格
    const flag = await this.getChance(userId);
    if (!flag) {
      const cacheKeyGameEvent = `gameEvent:${gameEventId}`;
      let gameEvent = await app.redis.get(cacheKeyGameEvent);
      if (!gameEvent) {
        gameEvent = await ctx.model.GameEvents.findOne({ _id: gameEventId, enable: true }, { getPrizeChanceNum: 1 }).lean();
        if (!gameEvent) throw new Error('未找到活动信息, gameEventId:' + gameEventId);
        await app.redis.set(cacheKeyGameEvent, JSON.stringify(gameEvent), 'EX', 300); // 缓存 300 秒
      } else {
        gameEvent = JSON.parse(gameEvent);
      }
      throw new Error(`您暂无抽奖资格，请先完成游戏通关至少${gameEvent.getPrizeChanceNum}题及以上`);
    }

    // 3. 判断是否已经抽中过奖品
    const winningGameRecord = await ctx.model.GameRecords.find({
      userId,
      prizeId: { $ne: null },
      Enterprise,
    });
    if (winningGameRecord.length) {
      const winningGameRecordIds = winningGameRecord.map(item => item._id);
      if (winningGameRecordIds.includes(gameRecordId)) {
        throw new Error('您此次已经抽过奖品了，请勿重复抽奖');
      } else {
        await ctx.model.GameRecords.updateOne({ _id: gameRecordId }, { drawTime: new Date() }); // 更新游戏记录
        return '谢谢参与';
      }
    }

    // 4. 开始抽奖（使用分布式锁避免并发问题）
    const lockKey = `drawPrizeLock:${gameEventId}`;
    const lock = await app.redis.set(lockKey, 'locked', 'NX', 'EX', 5); // 设置 5 秒锁
    if (!lock) throw new Error('系统繁忙，请稍后再试');

    try {
      // 查询奖品信息
      const cacheKeyPrizes = `prizes:${gameEventId}`;
      let prizes = await app.redis.get(cacheKeyPrizes);
      if (!prizes) {
        prizes = await ctx.model.Prizes.find({ gameEventId, enable: true, quantity: { $gt: 0 } }).lean();
        await app.redis.set(cacheKeyPrizes, JSON.stringify(prizes), 'EX', 300); // 缓存 300 秒
      } else {
        prizes = JSON.parse(prizes);
      }

      if (!prizes.length) {
        await ctx.model.GameRecords.updateOne({ _id: gameRecordId }, { drawTime: new Date() }); // 更新游戏记录
        return '谢谢参与';
      }

      // 抽奖逻辑
      const random = Math.random() * 100; // 生成随机数
      let cumulativeRate = 0; // 累计中奖率
      for (const prize of prizes) {
        cumulativeRate += prize.winningRate;
        if (random <= cumulativeRate) {
          // 更新奖品数量
          const updateResult = await ctx.model.Prizes.updateOne(
            { _id: prize._id, quantity: { $gt: 0 } },
            { $inc: { quantity: -1 } }
          );
          if (updateResult.nModified === 0) {
            throw new Error('奖品库存不足，请重试');
          }

          // 更新游戏记录
          await ctx.model.GameRecords.updateOne(
            { _id: gameRecordId },
            { prizeId: prize._id, drawTime: new Date() }
          );

          // 返回中奖奖品
          prize.picture = await ctx.helper.concatenatePath({
            path: `${app.config.upload_http_path}/prizes/${prize.picture}`,
          });
          return prize;
        }
      }

      // 未中奖
      await ctx.model.GameRecords.updateOne({ _id: gameRecordId }, { drawTime: new Date() });
      return '谢谢参与';
    } finally {
      // 释放锁
      await app.redis.del(lockKey);
    }
  }
  async drawPrize2(gameRecordId) {
    const { ctx, app } = this;
    if (!gameRecordId) throw new Error('gameRecordId不能为空');
    const gameRecord = await ctx.model.GameRecords.findOne({ _id: gameRecordId });
    if (!gameRecord) throw new Error('未找到游戏记录: ' + gameRecordId);

    const { userId, gameEventId, Enterprise } = gameRecord;
    if (!gameEventId) throw new Error('未找到用户信息中的gameEventId, userId:' + userId);
    if (!Enterprise) throw new Error('未找到用户信息中的Enterprise, userId:' + userId);

    // 判断用户是否有抽奖资格
    const flag = await this.getChance(userId);
    if (!flag) {
      const gameEvent = await ctx.model.GameEvents.findOne({ _id: gameEventId, enable: true }, { getPrizeChanceNum: 1 });
      if (!gameEvent) throw new Error('未找到活动信息, gameEventId:' + gameEventId);
      throw new Error(`您暂无抽奖资格，请先完成游戏通关至少${gameEvent.getPrizeChanceNum}题及以上`);
    }

    // 判断是否已经抽中过奖品
    const winningGameRecord = await ctx.model.GameRecords.find({
      userId,
      prizeId: { $ne: null },
      Enterprise,
    });
    if (winningGameRecord.length) {
      const winningGameRecordIds = winningGameRecord.map(item => item._id);
      if (winningGameRecordIds.includes(gameRecordId)) {
        throw new Error('您此次已经抽过奖品了，请勿重复抽奖');
      } else {
        await ctx.model.GameRecords.updateOne({ _id: gameRecordId }, { drawTime: new Date() }); // 更新游戏记录
        return '谢谢参与';
      }
    }

    // 开始抽奖
    const prizes = await ctx.model.Prizes.find({ gameEventId, enable: true, quantity: { $gt: 0 } }); // 查询所有启用的奖品
    const random = Math.random() * 100; // 生成随机数

    let cumulativeRate = 0; // 累计中奖率
    for (const prize of prizes) {
      cumulativeRate += prize.winningRate;
      if (random <= cumulativeRate) {
        await this.updatePrizeQuantity(prize._id); // 更新奖品数量
        await ctx.model.GameRecords.updateOne({ _id: gameRecordId }, { prizeId: prize._id, drawTime: new Date() }); // 更新游戏记录
        prize.picture = await ctx.helper.concatenatePath({
          path: `${app.config.upload_http_path}/prizes/${prize.picture}`,
        });
        return prize; // 返回中奖奖品
      }
    }
    await ctx.model.GameRecords.updateOne({ _id: gameRecordId }, { drawTime: new Date() }); // 更新游戏记录
    return '谢谢参与';
  }

  // 判断用户有没有抽奖资格
  async getChance(userId) {
    const { ctx } = this;
    const chance = await ctx.model.GameRecords
      .findOne({ userId, status: 2, drawTime: null })
      .populate('userId', 'status');
    if (!chance || !chance.userId) return { hasChance: false };
    if (chance.userId.status === 0) {
      throw new Error('您的账户已被限制，暂时无法参与抽奖，如有疑问请联系管理员。');
    }
    return { hasChance: true, _id: chance._id };
  }

  // 更新奖品数量
  async updatePrizeQuantity(prizeId) {
    await this.ctx.model.Prizes.findByIdAndUpdate(prizeId, { $inc: { quantity: -1 } });
  }

  // 查询我已获得的奖品
  async getMyPrize(userId) {
    const { ctx, app } = this;
    if (!userId) throw new Error('参数错误，用户userId不能为空');
    const myGameRecord = await ctx.model.GameRecords.findOne({ userId, prizeId: { $ne: null } }, 'prizeId');
    if (!myGameRecord) return null;
    const prizeId = myGameRecord.prizeId;
    const urlImg = await ctx.model.Prizes.findById(prizeId, 'picture name');
    if (urlImg) {
      urlImg.picture = await ctx.helper.concatenatePath({
        path: `${app.config.upload_http_path}/prizes/${urlImg.picture}`,
      });
    }
    return urlImg;
  }

  async getOne(query = {}) {
    return await this.ctx.model.Prizes.findOne(query);
  }

  // 获取活动奖品
  async getList(gameEventId) {
    const match = { gameEventId, enable: true };
    const list = await this.ctx.model.Prizes.find(match, { picture: 1, name: 1 });
    const filePath = path.join('/static', this.config.upload_http_path, 'prizes');
    return list.map(item => {
      const data = item.toObject();
      if (data.picture) data.picture = path.join(filePath, data.picture);
      return data;
    });
  }
}
module.exports = PrizesService;
