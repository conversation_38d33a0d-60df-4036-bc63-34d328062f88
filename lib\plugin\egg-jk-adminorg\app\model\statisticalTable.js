module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const StatisticalTableSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: {
      type: String,
    },
    updateTime: Date,
    regTime: Date,
    regAdd: Array,
    completeList: {
      type: Object,
      default: {},
    },
    healthList: {
      type: Object,
      default: {},
    },
    recordList: {
      type: Object,
      default: {},
    }, // 档案完成列表
  });

  return mongoose.model('StatisticalTable', StatisticalTableSchema, 'statisticaltables');
};
