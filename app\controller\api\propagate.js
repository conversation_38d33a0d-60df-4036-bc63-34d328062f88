const Controller = require('egg').Controller;
class PropagateController extends Controller {
  async add(ctx) {
    const params = ctx.request.body;
    const { id, Enterprise, name, IDnum, phoneNum, partakeDepartent, type, annex, hours, createTime, implementDate, content, lecturer, year } = params;
    const code = params.code ? String(params.code).toUpperCase() : '';
    // 1、必传参校验
    if (!id || !code || !name || !type || !hours || !Enterprise || !IDnum || !phoneNum) {
      ctx.helper.renderFail(ctx, {
        message: '培训记录添加失败，参数不全。',
      });
      return;
    }
    // 2、根据code查询企业信息
    const company = await ctx.service.adminorg.findOne({ code });
    // console.log(22222, company);
    let EnterpriseID = company && company._id ? company._id : '';
    const isManage = (Number(type) === 1 || Number(type) === 2);
    if (!EnterpriseID) {
      let newAdminUser;
      // 3. 创建adminUser
      if (isManage) {
        newAdminUser = await ctx.service.adminorg.createAdminUser({
          phoneNum,
          name,
          userName: phoneNum,
          IDcard: IDnum,
          enable: true,
        });
      }
      // 4、创建adminOrg
      const companyData = {
        cname: Enterprise || '',
        code,
        createTime: new Date(),
        message: '衢州职业卫生培训中新增的企业',
        isactive: '1',
      };
      const detail = await ctx.service.adminorg.findFirmsByCode(code);
      // console.log(333333, detail);
      if (detail && detail._id) {
        companyData.cname = detail.jgmc || Enterprise || ''; // 企业名称
        companyData.code = detail.jgxydm || detail.zch; // 统一社会信用代码或者组织机构代码
        companyData.corp = detail.fddbr || ''; // 法人
        companyData.introduce = detail.jyfw || ''; // 经营范围
        companyData.regAdd = detail.jgdz || ''; // 注册地址
        companyData.adminUserId = newAdminUser ? newAdminUser._id : '';
        if (companyData.regAdd) {
          companyData.workAddress = [{
            address: detail.jgdz || '',
          }];
        }
      }
      const newCompany = await ctx.service.adminorg.create(companyData);
      // console.log(44444, newCompany);
      EnterpriseID = newCompany._id;
    } else {
      // 更新企业的adminUser
      if (!company.adminUserId && isManage) {
        const newAdminUser = await ctx.service.adminorg.createAdminUser({
          phoneNum,
          name,
          userName: name,
          IDcard: IDnum,
        });
        ctx.service.adminorg.update2({ _id: EnterpriseID }, { adminUserId: newAdminUser._id });
      }
    }
    // 5、添加培训
    const typeName = [ '负责人培训', '管理人员培训', '劳动者培训' ];
    const res = await ctx.service.propagate.create({
      othersId: id,
      EnterpriseID,
      createTime: createTime || Date.now(),
      type: typeName[+type - 1],
      hours: String(hours),
      partakeDepartment: [[ partakeDepartent || '/' ]],
      personnel: [ name ],
      content: content || '衢州市职业健康培训',
      organizeDepartment: '衢州市职业健康培训中心',
      implementData: implementDate || Date.now(),
      annex: annex ? String(annex) : '1', // 也就是课程数量
      year: String(year || new Date().getFullYear()),
      allPeopleNumber: '1',
      lecturer: lecturer || '',
      source: 'qzpx',
    });
    if (res && res._id) {
      ctx.auditLog('衢州市职业健康培训-外部系统添加企业培训记录操作', JSON.stringify(res), 'info');
      ctx.helper.renderSuccess(ctx, {
        message: '培训记录添加成功',
        data: res,
      });
    } else {
      ctx.helper.renderFail(ctx, {
        message: '培训记录添加失败。',
      });
    }
  }

  // 更新
  async update(ctx) {
    const params = ctx.request.body;
    // console.log(55555, params);
    const { id, organizeDepartment, name, partakeDepartent, type, annex, hours, year, createTime, implementDate, content, lecturer } = params;
    if (!id) {
      ctx.helper.renderFail(ctx, {
        message: '数据更新失败，参数id必须传。',
      });
      return;
    }
    const oldData = await ctx.service.propagate.findOne({ othersId: id });
    // console.log(66666, oldData);
    if (!oldData) {
      ctx.helper.renderFail(ctx, {
        message: '数据更新失败，因为原id找不到。',
      });
      return;
    }
    try {
      const typeName = [ '负责人培训', '管理人员培训', '劳动者培训' ];
      const newData = {
        createTime: createTime || oldData.createTime,
        type: type ? typeName[+type - 1] : oldData.type,
        hours: hours ? String(hours) : oldData.hours,
        partakeDepartment: partakeDepartent ? [[ partakeDepartent ]] : oldData.partakeDepartment,
        personnel: name ? [ name ] : oldData.name,
        content: content || oldData.content,
        organizeDepartment: organizeDepartment || oldData.organizeDepartment,
        implementData: implementDate || oldData.implementDate,
        annex: annex ? String(annex) : oldData.annex, // 也就是课程数量
        year: year ? String(year || new Date().getFullYear()) : oldData.year,
        lecturer: lecturer || oldData.lecturer,
      };
      const res = await ctx.service.propagate.update({ othersId: id }, newData);
      // console.log(888888, res);
      if (res && res.ok === 1) {
        params.othersId = params.id;
        ctx.auditLog('衢州市职业健康培训-外部系统修改企业培训记录操作', '修改的参数：' + JSON.stringify(params), 'info');
        ctx.helper.renderSuccess(ctx, {
          message: '培训记录修改成功',
          data: newData,
        });
      }
    } catch (error) {
      console.log(error);
      ctx.helper.renderFail(ctx, {
        message: '培训记录修改失败',
        data: error,
      });
    }

  }
  // 删除记录
  async delete(ctx) {
    const othersId = ctx.request.body.id;
    if (!othersId) {
      ctx.helper.renderFail(ctx, {
        message: '培训记录删除失败，参数id必须传。',
      });
      return;
    }
    const res = await ctx.service.propagate.deleteOne({ othersId });
    // console.log(44444, res);
    if (res.ok === 1 && res.deletedCount === 1) {
      ctx.auditLog('衢州市职业健康培训-外部系统删除企业培训记录操作', '删除的othersId为' + othersId, 'info');
      ctx.helper.renderSuccess(ctx, {
        message: '培训记录删除成功',
      });
    } else {
      ctx.helper.renderFail(ctx, {
        message: '培训记录删除失败',
        data: res,
      });
    }
  }

}

module.exports = PropagateController;
