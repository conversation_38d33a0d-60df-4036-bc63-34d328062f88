// 防护用品自提柜
// const { promisify } = require('util');
// const sleep = promisify(setTimeout);
const PPESelfLiftCabinetController = {
  async issueOrder(ctx) {
    try {
      // const array = [{ id: '863488050210916', value: { aa: 1 } }];
      // ctx.helper.setCache('cache', array, 1000 * 60 * 15);
      const data = JSON.parse(ctx.query.data);
      const id = ctx.query.id;
      console.log(data, id, 'app传过来的信息');
      const cache = ctx.helper.getCache('cache');
      let cacheArr = [];
      if (cache) {
        cacheArr = cache.filter(item => {
          return item.id === id;
        });
        console.log(cacheArr, '符合条件的仪器');
      }
      if (cacheArr.length > 0) {
        const client = cacheArr[0].value;
        console.log(client, '客户端信息');
        const saleId = await this.rondomPass(10);
        const messageId = await this.rondomPass(7);
        console.log(`saleId:${saleId},messageId:${messageId},topic:ZHZN/${id}`);
        // for (let i = 0; i < data.length; i++) {
        // const item = data[i];
        // if (item.pname === '口罩') {
        client.subscribe({ topic: `ZHZN/${id}`, qos: 2 }, error => {
          if (error) {
            console.log('订阅出错了，。。。。。', error);
          } else {
            console.log('订阅成功了。。。。。。');
            client.publish({
              cmd: 'publish',
              messageId: 43,
              qos: 2,
              dup: false,
              topic: `ZHZN/${id}`,
              payload: `{"c":22,"f":"DuopuOapi","t":${id},"m":"1,1&2","s":${saleId},"mi":${messageId}}`,
            },
            function(err) {
              if (err) {
                console.log('字符串传参报错了11111===========', err);
              } else {
                console.log('我成功了11111。。。。。。。。。。。。');
              }
            });
          }
        });
      } else {
        console.log('仪器数组中没有东西');
      }
      // 告诉机器出货
      // 1.成功：更改库的确认领取状态
      // 2.失败：返回信息，输出错误原因
      // }
      // }
      // 通过app二维码获取自提柜信息。并发送信息给自提柜（几货道几个物品）
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: '成功',
      });
    } catch (err) {
      console.log(err, '错误');
      ctx.helper.renderCustom(ctx, {
        status: 500,
        data: err,
        message: '后端错误',
      });
    }
  },
  rondomPass(number) {
    const arr = [ '0', '1', '2', '3', '4', '5', '6', '7', '8', '9' ];
    let nonceStr = '';
    let res = '';
    for (let i = 0; i < number; i++) {
      const n = Math.floor(Math.random() * 10);
      arr[i] = arr[n];
      nonceStr += arr[n];
      res = parseInt(nonceStr);
    }
    return res;
  },
};

module.exports = PPESelfLiftCabinetController;
