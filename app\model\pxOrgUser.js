/**
 * 机构端用户对象
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const CryptoJS = require('crypto-js');
  const pxGroupID = app.config.groupID.pxGroupID || ''; // 机构用户角色ID

  require('./adminGroup');

  const PxOrgUserSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    userName: { // 用户名
      type: String,
      default: '',
    },
    name: { // 姓名
      type: String,
      default: '',
    },
    phoneNum: {
      type: String,
      index: true, // 普通索引
      require: true,
    },
    email: {
      type: String,
      default: '',
    },
    password: {
      type: String,
      set(val) {
        return CryptoJS.SHA256(val + app.config.salt_sha2_key).toString();
      },
    },
    // 存储密码有效期 val为new Date()
    passwordExpiresAt: {
      type: Date,
    },
    loginAttempts: Number, // 记录登录尝试次数
    loginAttemptsTimestamp: Array, // 记录登录尝试时间
    org_id: { // 所属机构id
      type: String,
      default: '',
      ref: 'PxOrg', // 关联机构表
    },
    department_id: { // 所属部门id
      type: String,
      default: '',
    },
    department: { // 所属部门名称
      type: String,
      default: '',
    },
    group: {
      type: String,
      default: pxGroupID,
      ref: 'AdminGroup',
    },
    countryCode: { // 手机号前国家代码
      type: String,
      default: '86',
    },
    logo: { // 头像
      type: String,
      default: '/static/upload/images/defaultlogo.png',
    },
    state: { // 在职状态
      type: Boolean,
      default: true,
    },
    enable: { // 是否能用
      type: Boolean,
      default: true,
    },
    comments: String, // 备注
  }, { timestamps: true });

  return mongoose.model('PxOrgUser', PxOrgUserSchema, 'pxOrgUsers');


};

