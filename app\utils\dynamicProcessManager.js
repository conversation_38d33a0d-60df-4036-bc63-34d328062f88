'use strict';

const { fork } = require('child_process');
const path = require('path');
const os = require('os');

/**
 * 动态进程管理器
 * 按需创建Node.js子进程处理大批量任务，用完即销毁
 */
class DynamicProcessManager {
  constructor(options = {}) {
    this.processorScript = options.processorScript || path.join(__dirname, '../workers/fzDataProcessor.js');
    this.maxProcesses = options.maxProcesses || os.cpus().length;
    this.taskTimeout = options.taskTimeout || 300000; // 5分钟超时
    this.processes = new Map(); // 进程ID -> 进程信息
    this.taskCounter = 0;
  }

  /**
   * 处理文件批次
   * @param {Array} filesToProcess - 要处理的文件列表
   * @param {Object} options - 处理选项
   * @return {Promise<Object>} 处理结果
   */
  async processFiles(filesToProcess, options = {}) {
    const {
      taskType = 'company',
      batchSize = 10, // 增加默认批次大小，提高单个进程处理效率
      maxConcurrentProcesses = this.maxProcesses,
    } = options;

    if (!filesToProcess || filesToProcess.length === 0) {
      return {
        success: true,
        message: '没有文件需要处理',
        results: [],
        processStats: {},
      };
    }

    console.log('\n=== 动态进程管理器启动 ===');
    console.log(`[DynamicProcessManager] 开始处理 ${filesToProcess.length} 个文件`);
    console.log(`[DynamicProcessManager] 批次大小: ${batchSize}, 最大并发进程: ${maxConcurrentProcesses}`);
    console.log(`[DynamicProcessManager] 系统CPU核心数: ${os.cpus().length}, 当前主进程PID: ${process.pid}`);

    try {
      // 将文件分组到批次中
      const batches = this.createBatches(filesToProcess, batchSize);
      console.log(`[DynamicProcessManager] 创建了 ${batches.length} 个批次`);

      // 限制并发进程数量
      const results = [];
      const processStats = {};

      // 分批并发处理 - 优化并发策略
      const concurrentBatches = Math.min(maxConcurrentProcesses, batches.length);
      for (let i = 0; i < batches.length; i += concurrentBatches) {
        const currentBatches = batches.slice(i, i + concurrentBatches);
        console.log(`[DynamicProcessManager] 处理批次组 ${Math.floor(i / concurrentBatches) + 1}/${Math.ceil(batches.length / concurrentBatches)}, 包含 ${currentBatches.length} 个批次`);

        // 为当前批次组创建进程并处理
        const batchPromises = currentBatches.map(batch =>
          this.processBatchWithNewProcess(batch, taskType)
        );

        const batchResults = await Promise.all(batchPromises);

        batchResults.forEach(result => {
          results.push(...result.results);
          Object.assign(processStats, result.processStats);
        });

        console.log(`[DynamicProcessManager] 批次组完成，当前总处理文件: ${results.length}`);

        // 批次组间短暂休息，避免系统过载
        if (i + concurrentBatches < batches.length) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }

      const successCount = results.filter(r => r.success === true).length;
      const failCount = results.filter(r => r.success === false && !r.skipped).length;
      const skippedCount = results.filter(r => r.skipped === true).length;

      console.log(`[DynamicProcessManager] 所有处理完成: 成功 ${successCount}, 失败 ${failCount}, 跳过 ${skippedCount}`);

      return {
        success: failCount === 0,
        message: `动态进程处理完成: 成功 ${successCount}, 失败 ${failCount}, 跳过 ${skippedCount}`,
        totalFiles: filesToProcess.length,
        processedFiles: successCount,
        failedFiles: failCount,
        skippedFiles: skippedCount,
        results,
        processStats,
      };

    } catch (error) {
      console.error('[DynamicProcessManager] 处理失败:', error);
      throw error;
    }
  }

  /**
   * 使用新进程处理单个批次
   * @param {Array} batch - 文件批次
   * @param {string} taskType - 任务类型
   * @return {Promise<Object>} 批次处理结果
   */
  async processBatchWithNewProcess(batch, taskType) {
    const processId = `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return new Promise((resolve, reject) => {
      console.log(`[Process ${processId}] 启动子进程处理 ${batch.length} 个文件`);

      // 创建子进程
      const childProcess = fork(this.processorScript, [], {
        stdio: [ 'pipe', 'pipe', 'pipe', 'ipc' ],
        env: { ...process.env, PROCESS_ID: processId },
      });

      const startTime = Date.now();
      let isCompleted = false;

      // 存储进程信息
      this.processes.set(processId, {
        process: childProcess,
        startTime,
        batch,
        taskType,
      });

      // 设置超时
      const timeoutHandle = setTimeout(() => {
        if (!isCompleted) {
          console.error(`[Process ${processId}] 处理超时，强制终止`);
          this.forceKillProcess(processId);
          reject(new Error(`进程 ${processId} 处理超时`));
        }
      }, this.taskTimeout);

      // 监听进程消息
      childProcess.on('message', message => {
        if (message.type === 'result') {
          isCompleted = true;
          clearTimeout(timeoutHandle);

          const duration = Date.now() - startTime;
          console.log(`[Process ${processId}] 处理完成，耗时: ${duration}ms`);

          // 统计结果 - 支持新的status字段
          const results = message.results || [];
          const processStats = {
            [processId]: {
              duration,
              filesProcessed: batch.length,
              // 优先使用新的status字段，向后兼容旧的success/skipped字段
              successCount: results.filter(r => r.status === 'success' || (r.status === undefined && r.success === true)).length,
              failCount: results.filter(r => r.status === 'failed' || (r.status === undefined && r.success === false && !r.skipped)).length,
              skippedCount: results.filter(r => r.status === 'skipped' || (r.status === undefined && r.skipped === true)).length,
            },
          };

          const result = {
            results: message.results,
            processStats,
            summary: message.summary || null, // 包含子进程提供的汇总信息
          };

          // 优雅地终止进程
          this.terminateProcess(processId);
          resolve(result);
        } else if (message.type === 'error') {
          // 处理子进程发送的错误信息
          console.error(`[Process ${processId}] 收到错误信息:`, message.error);
          // 这里不立即reject，等待进程退出事件
        }
      });

      // 监听进程错误
      childProcess.on('error', error => {
        isCompleted = true;
        clearTimeout(timeoutHandle);
        console.error(`[Process ${processId}] 进程错误:`, error);
        this.terminateProcess(processId);
        reject(error);
      });

      // 监听进程退出
      childProcess.on('exit', (code, signal) => {
        if (!isCompleted) {
          const errorMsg = `进程 ${processId} 意外退出: code=${code}, signal=${signal}`;
          console.error(`[Process ${processId}] ${errorMsg}`);
          clearTimeout(timeoutHandle);
          this.processes.delete(processId);

          // 创建包含错误信息的结果
          const errorResult = {
            results: [{
              status: 'failed',
              success: false,
              fileName: 'unknown',
              message: errorMsg,
              error: '进程意外退出',
              exitCode: code,
              signal,
              workerId: processId,
            }],
            processStats: {
              [processId]: {
                duration: Date.now() - startTime,
                filesProcessed: 0,
                successCount: 0,
                failCount: 1,
                skippedCount: 0,
                exitCode: code,
                signal,
              },
            },
            error: new Error(errorMsg),
          };

          reject(errorResult.error);
        } else {
          console.log(`[Process ${processId}] 正常退出`);
        }
      });

      // 发送任务到子进程
      childProcess.send({
        type: 'task',
        batch,
        taskType,
        processId,
      });
    });
  }

  /**
   * 创建文件批次
   * @param {Array} files - 文件列表
   * @param {number} batchSize - 批次大小
   * @return {Array} 批次数组
   */
  createBatches(files, batchSize) {
    const batches = [];
    for (let i = 0; i < files.length; i += batchSize) {
      batches.push(files.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 优雅地终止进程
   * @param {string} processId - 进程ID
   */
  terminateProcess(processId) {
    const processInfo = this.processes.get(processId);
    if (processInfo) {
      try {
        processInfo.process.kill('SIGTERM');
        setTimeout(() => {
          if (!processInfo.process.killed) {
            processInfo.process.kill('SIGKILL');
          }
        }, 5000); // 5秒后强制杀死
      } catch (error) {
        console.error(`[Process ${processId}] 终止进程失败:`, error);
      }
      this.processes.delete(processId);
    }
  }

  /**
   * 强制杀死进程
   * @param {string} processId - 进程ID
   */
  forceKillProcess(processId) {
    const processInfo = this.processes.get(processId);
    if (processInfo) {
      try {
        processInfo.process.kill('SIGKILL');
      } catch (error) {
        console.error(`[Process ${processId}] 强制杀死进程失败:`, error);
      }
      this.processes.delete(processId);
    }
  }

  /**
   * 获取管理器状态
   * @return {Object} 状态信息
   */
  getStatus() {
    const processes = Array.from(this.processes.values()).map(info => ({
      processId: `proc_${info.process.pid}`,
      pid: info.process.pid,
      startTime: info.startTime,
      runningTime: Date.now() - info.startTime,
      taskType: info.taskType,
      filesCount: info.batch.length,
      status: 'active',
    }));

    return {
      processes,
      totalProcesses: this.processes.size,
      activeProcesses: this.processes.size,
      maxProcesses: this.maxProcesses,
      taskTimeout: this.taskTimeout,
    };
  }

  /**
   * 清理所有进程
   */
  async cleanup() {
    console.log(`[DynamicProcessManager] 清理 ${this.processes.size} 个活跃进程`);

    const processIds = Array.from(this.processes.keys());
    await Promise.all(
      processIds.map(processId => {
        return new Promise(resolve => {
          this.terminateProcess(processId);
          setTimeout(resolve, 1000); // 给进程1秒时间优雅退出
        });
      })
    );

    console.log('[DynamicProcessManager] 所有进程已清理');
  }
}

module.exports = DynamicProcessManager;
