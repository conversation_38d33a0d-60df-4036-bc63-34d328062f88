/*
 * @Author: 汪周强
 * @Date: 2022-01-10 15:07:59
 * @LastEditors: 汪周强
 * @LastEditTime: 2023-03-08 13:23:04
 * @Description: 修改申请记录表
 * 全流程
 */

/*
  1. 申请人发起修改审批
  校验该项目是否存在status: 1 进行中的修改，若有则不需要再创建审批
  || 若无 创建新的审批
  ||
  \/
  2. 审核人钉钉处理审批后，修改表数据。
  设置 approvalResultTime: new Date(), approvalResult: 1|2,
  approvalResult：1 同意修改
  approvalResult：2 禁止用户修改 ==> 设置 modifyAccomplishTime: new Date(), status: 0。结束该审核
  ||
  ||
  \/
  3.申请人修改后完成后要 本次修改审核办结
  || 设置 modifyAccomplishTime: new Date(), status: 0
  ||
  \/
  4. 向审核人发送 approvalResultTime - modifyAccomplishTime 时间段内的修改内容
  ||
  \/
*/

module.exports = app => {
  // 修改审批model
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const modifyApprovalRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    modifyTarget: { // 发起审批的模块，计划单samplingPlan，现场记录单spotRecord。
      type: String,
      require: true,
    },
    serviceOrgId: { type: String, ref: 'ServiceOrg' }, // 机构id
    projectId: { // 项目id
      type: String,
      require: true,
    },
    reviewer: [{ // 审核人
      _id: {
        type: String,
        default: shortid.generate,
      },
      serviceEmployeeId: {
        type: String,
        ref: 'ServiceEmployee',
      },
      dingUserId: {
        type: String,
      },
      type: { // firstTrial 初审 secondTrial 二审
        type: String,
      },
    }],
    sponsor: { // 发起人
      type: String,
      ref: 'ServiceEmployee',
    },
    proposeTime: { // 发起修改审批的时间
      type: Date,
      require: true,
    },
    approvalResultTime: { // 审核人处理审批的时间
      type: Date,
    },
    approvalResult: { // 审批结果
      type: Number,
      enum: [ 0, 1, 2 ], // 0 未审批，1 审批通过，2 审批拒绝
      default: 0,
    },
    modifyAccomplishTime: { // 本次修改的结束时间
      type: Date,
    },
    status: { // 该修改审批的状态
      type: Number,
      enum: [ 0, 1 ], // 0 已办结 1 进行中
      default: 1,
    },
    reportProcessInstanceId: { // 审批单id
      type: String,
    },
    createAt: { // 创建日期 - 数据库会自动生成
      type: Date,
    },
    updateAt: { // 更新时间 - 数据库会自动生成
      type: Date,
    },
  }, { timestamps: true });

  return mongoose.model('modifyApprovalRecord', modifyApprovalRecordSchema, 'modifyApprovalRecord');
};
