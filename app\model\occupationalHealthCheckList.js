module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const occupationalHealthCheckListSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      year: {
        // 年份
        type: String,
        require: true,
      },
      employeeId: {
        // 人员id
        type: String,
        required: true,
        ref: 'Employees',
      },
      EnterpriseID: {
        type: String,
        required: true,
        ref: 'Adminorg',
      },
      // 清单状态
      status: {
        type: String,
        default: '0',
        enum: [ '0', '1' ], // 0.未提交 1.已提交
      },
      // 审核状态
      reviewStatus: {
        type: String,
        default: '0',
        enum: [ '0', '1', '2' ], // 0.未审核 1.审核通过 2.审核不通过
      },
      // // 是否是新建状态（提交审核状态）
      // isNewStatus: {
      //   type: Boolean,
      //   default: false,
      // },
      // // 是否通过审核状态
      // hasReview: {
      //   type: Boolean,
      //   default: false,
      // },
      // 绑定的体检类型表
      medicalExamInfo: [
        {
          type: String,
          ref: 'MedicalExamInfo',
        },
      ],
      // 确定的体检医院
      hospitalConfirm: {
        _id: {
          type: String,
          default: shortid.generate,
        },
        orgId: {
          type: String,
          ref: 'PhysicalExamOrg',
        },
        orgName: {
          type: String,
        },
      },
      guideForm: {
        // 引导单
        _id: {
          type: String,
          default: shortid.generate,
        },
        originName: {
          // 用户上传文件名称
          type: String,
          default: '',
        },
        staticName: {
          // 后台处理后存储到public静态资源的文件名
          type: String,
          default: '',
        },
      },
      confirmStatus: {
        // 确认状态 0未确认 1已确认
        type: Boolean,
        default: false,
      },
      // 是否提交引导单（是否体检）
      isSubmitGuideForm: {
        type: Boolean,
        default: false,
      },
    },
    { timestamps: true }
  );
  return mongoose.model('occupationalHealthCheckList', occupationalHealthCheckListSchema);
};
