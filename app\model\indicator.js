
module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const IndicatorSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 指标名称
      type: String,
    },
    code: { // 指标itemCode
      type: String,
    },
    category: { // 所属项目
      type: String,
    },
  });

  return mongoose.model('indicator', IndicatorSchema, 'indicator');

};
