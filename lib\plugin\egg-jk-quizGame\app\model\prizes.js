// creator: xxn 2024-8-30
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  // 抽奖奖品
  const PrizesSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    name: { // 奖品名称
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    picture: { // 奖品图片 path.join('/static', config.upload_http_path, 'prizes',  picture)
      type: String,
      default: '',
    },
    quantity: { // 奖品数量
      type: Number,
      default: 0,
    },
    winningRate: { // 中奖概率
      type: Number,
      default: 0,
      max: 100,
    },
    gameEventId: { // 活动id
      type: String,
      required: true,
      index: true,
      ref: 'GameEvents',
    },
    hidden: { // 是否对外隐藏
      type: Boolean,
      default: false,
    },
    enable: { // 是否启用
      type: Boolean,
      default: true,
    },
    creator: { // 创建者
      type: String,
      default: '',
    },
  }, { timestamps: true });

  return mongoose.model('Prizes', PrizesSchema, 'prizes');
};
