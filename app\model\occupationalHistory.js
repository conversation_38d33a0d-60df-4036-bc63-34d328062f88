module.exports = app => {
  const shortid = require('shortid');
  // 职业病史
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const occupationalHistory = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    employeeId: {
      type: String,
      ref: 'Employees',
    },
    entryTime: String, // 入职时间
    leaveTime: String, // 离职时间
    workUnit: String, // 工作单位
    workshop: String, // 车间
    station: String, // 岗位
    workType: String, // 工种
    HarmfulFactors: String, // 接触有害因素
    protect: String, // 防护措施
    hisType: {
      // hisType
      type: String,
      enum: [ '1', '2' ], // 1.放射/2.非放射
    },
    exposureToHarmfulFactors: {
      // 接触有害因素	S..50	--
      type: String,
    },
    prfwrklod: {
      // (放射)每日工作时数或工作量	S..50	类型编码为：1时，必填；
      type: String,
    },
    prfshnvlu: {
      // (放射)职业史累积受照剂量	S..50	类型编码为：1时，必填；
      type: String,
    },
    prfexcshn: {
      // (放射)职业史过量照射史	S..50	类型编码为：1时，必填；
      type: String,
    },
    prfraysrt2: {
      //	(放射)职业照射种类	S..250	类型编码为：1时，必填；
      type: String,
    },
    prfraysrtcods: {
      // (放射)职业照射种类代码	S..100	类型编码为：1时，必填；
      type: String,
    },
    fsszl: {
      // (放射)放射线种类	S..100	类型编码为：1时，必填；
      type: String,
    },
    defendStep: {
      //	(非放射)防护措施	S..25	类型编码为：2时，必填；
      type: String,
    },
    chkdat: {
      //	检查日期	D10	必填
      type: String,
    },
    chkdoct: {
      //	检查医生	S..25	--
      type: String,
    },
  });
  return mongoose.model(
    'OccupationalHistory',
    occupationalHistory,
    'occupationalHistories'
  );
};
