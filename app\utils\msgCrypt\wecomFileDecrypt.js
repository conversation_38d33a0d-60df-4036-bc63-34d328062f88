const CryptoJS = require('crypto-js');
const fs = require('fs');
const crypto = require('crypto');

/**
 * 解密数据
 * @param {Buffer} encryptedBytes 加密的字节数组
 * @param {string} encodingAesKey Base64 编码的 AES 密钥
 * @return {string} 解密后的明文数据
 */
function decryptData(encryptedBytes, encodingAesKey) {
  // 从 Base64 字符串还原 AES 密钥
  const key = CryptoJS.enc.Base64.parse(encodingAesKey);
  // 截取前16个字节作为 IV
  const iv = CryptoJS.enc.Hex.parse(
    key.toString(CryptoJS.enc.Hex).slice(0, 32)
  );

  // 将加密的字节数组转换为 WordArray
  const encryptedWordArray = CryptoJS.lib.WordArray.create(encryptedBytes);

  // 解密数据
  const decryptedWordArray = CryptoJS.AES.decrypt(
    { ciphertext: encryptedWordArray },
    key,
    {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.NoPadding,
    }
  );

  // 将解密后的数据转换为 Buffer
  const decryptedBytes = CryptoJS.enc.Utf8.parse(
    decryptedWordArray.toString(CryptoJS.enc.Utf8)
  );
  let decryptedBuffer = Buffer.from(
    decryptedBytes.toString(CryptoJS.enc.Hex),
    'hex'
  );

  // 去除 PKCS#7 填充
  decryptedBuffer = PKCS7Decoder(decryptedBuffer);

  // 将解密后的数据转换为 UTF-8 字符串
  const decryptedText = decryptedBuffer.toString('utf8');

  return decryptedText;
}

/**
 * PKCS#7 解码
 * @param {Buffer} byteBuffer 带填充的解密数据
 * @return {Buffer} 去掉填充后的数据
 */
function PKCS7Decoder(byteBuffer) {
  const padding = byteBuffer[byteBuffer.length - 1];
  if (padding < 1 || padding > 16) return byteBuffer;
  return byteBuffer.slice(0, -padding);
}

/**
 * 处理解密后的数据，去除控制字符
 * @param {string} data 解密后的数据
 * @return {string} 处理后的数据
 */
function processDecryptedData(data) {
  // 去除 \u001d 控制字符
  // eslint-disable-next-line no-control-regex
  return data.replace(/\u001d/g, '');
}

/**
 * 计算 MD5 校验和
 * @param {string} data 明文数据
 * @return {string} 计算出的 MD5 校验和
 */
function calculateMD5(data) {
  return crypto.createHash('md5').update(data).digest('hex');
}

/**
 * 将 JSON 数据写入文件
 * @param {string} filePath 文件路径
 * @param {Object} jsonData 要写入的 JSON 数据
 */
function writeJSONToFile(filePath, jsonData) {
  fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2), 'utf8');
  console.log(`JSON 数据已写入到 ${filePath}`);
}

// 示例使用
const encryptedFilePath = './data_0.json'; // 加密文件的路径
const outputFilePath = './data_0_decrypted.json'; // 解密后 JSON 数据文件的路径
const encodingAesKey = 'Cq4o7hvFX0SXaPK9MB2D5DVgnmFnUjxGRoAYsx5wQn2'; // Base64 编码的 AES 密钥

// 读取加密的文件内容
const encryptedData = fs.readFileSync(encryptedFilePath);

// 解密文件数据
const decryptedText = decryptData(encryptedData, encodingAesKey);

// 处理解密后的数据
const processedText = processDecryptedData(decryptedText);

// 打印解密后的数据
console.log('处理后的数据:', processedText);

// 计算 MD5 校验和
const md5Checksum = calculateMD5(processedText);
console.log('计算出的 MD5 校验和:', md5Checksum);

// 比对 MD5 校验和
const expectedMd5Checksum = 'cb52607b310d1c9a674ec98a3b70f5ef';
console.log('MD5 校验和是否匹配:', md5Checksum === expectedMd5Checksum);

// 解析解密后的 JSON 数据
try {
  const jsonData = JSON.parse(processedText);
  // 将 JSON 数据写入到文件
  writeJSONToFile(outputFilePath, jsonData);
} catch (error) {
  console.error('处理后的数据不是有效的 JSON 格式:', error.message);
}
