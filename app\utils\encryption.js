const crypto = require('crypto');
// const dotenv = require('dotenv');
// const sm3 = require('sm-crypto').sm3;
const sm4 = require('sm-crypto').sm4;
// const key = 'd0123456789abcdeffedcba9876543210';
const axios = require('axios');
const { hashSm3 } = require('./hmacSign');
const moment = require('moment');
// const { getFzGMToken } = require('./fzGMToken');
// dotenv.config();

const ENCRYPTION_KEY = Buffer.from(
  '0906d014e19db439bb45cc6d5b674e9519640e97a7273350d54a90b74142d891',
  'hex'
);
const INITIAL_VECTOR = Buffer.from('ef62567e3a16fbaa0c2c58a4', 'hex');

// 加密和解密算法
const ALGORITHM_OPENSSL = 'aes-256-gcm';
const ALGORITHM_GM = 'GM';
const ALGORITHM_FZGM = 'FZGM';
const fzgmBaseUrl = process.env.FZGM_BASE_URL || '';
const fzgmKey = process.env.FZGM_KEY || '';
const passwordEncryptionAlgorithm =
  process.env.PASSWORD_ENCRYPTION_ALGORITHM || '';
const hmacAlgorithmEnv = process.env.HMAC_SIGN_ALGORITHM || '';
const encryptDataAlgorithm = process.env.ENCRYPTION_ALGORITHM || '';
let fzgmToken = null;

// 加密函数
async function encrypt(
  plainText,
  algorithm = process.env.ENCRYPTION_ALGORITHM
) {
  if (algorithm === ALGORITHM_OPENSSL) {
    const cipher = crypto.createCipheriv(
      ALGORITHM_OPENSSL,
      ENCRYPTION_KEY,
      INITIAL_VECTOR
    );
    let encrypted = cipher.update(plainText, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    const authTag = cipher.getAuthTag().toString('hex');
    return `${encrypted}:${authTag}`;
  } else if (algorithm === ALGORITHM_GM) {
    const encryptData = sm4.encrypt(
      plainText,
      '0123456789abcdeffedcba9876543210',
      {
        mode: 'cbc',
        iv: 'fedcba98765432100123456789abcdef',
      }
    ); // 加密，cbc 模式
    return encryptData;
  } else if (algorithm === ALGORITHM_FZGM) {
    if (!fzgmBaseUrl || !fzgmKey) {
      throw new Error('当前环境未配置福州GM相关信息');
    }
    if (!fzgmToken) {
      try {
        const response = await axios({
          method: 'get',
          url: `${fzgmBaseUrl}/apisix/plugin/jwt/sign`,
          params: {
            key: fzgmKey,
          },
        });
        fzgmToken = response.data;
      } catch (error) {
        throw new Error('获取福州GMtoken失败', error);
      }
    }
    const base64Data = Buffer.from(plainText).toString('base64');
    const encryptData = await axios({
      method: 'post',
      url: `${fzgmBaseUrl}/csmp/v3/paas/hsm/sm4-encrypt`,
      data: {
        mode: 1,
        plain: base64Data,
        appid: fzgmKey,
        keyid: '',
      },
      headers: {
        Authorization: fzgmToken,
      },
    });
    return encryptData.data.cipher;
  }
  throw new Error('不支持的加密算法');
}

// 解密函数
async function decrypt(combinedText, algorithm = process.env.ENCRYPTION_ALGORITHM) {
  if (algorithm === ALGORITHM_OPENSSL) {
    if (!combinedText.includes(':')) return '';
    const [ encrypted, authTag ] = combinedText.split(':');
    const decipher = crypto.createDecipheriv(
      ALGORITHM_OPENSSL,
      ENCRYPTION_KEY,
      INITIAL_VECTOR
    );
    decipher.setAuthTag(Buffer.from(authTag, 'hex'));
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } else if (algorithm === ALGORITHM_GM) {
    const decryptData = sm4.decrypt(
      combinedText,
      '0123456789abcdeffedcba9876543210',
      {
        mode: 'cbc',
        iv: 'fedcba98765432100123456789abcdef',
      }
    ); // 解密，cbc 模式
    return decryptData;
  } else if (algorithm === ALGORITHM_FZGM) {
    if (!fzgmBaseUrl || !fzgmKey) {
      throw new Error('当前环境未配置福州GM相关信息');
    }
    if (!fzgmToken) {
      try {
        const response = await axios({
          method: 'get',
          url: `${fzgmBaseUrl}/apisix/plugin/jwt/sign`,
          params: {
            key: fzgmKey,
          },
        });
        fzgmToken = response.data;
      } catch (error) {
        throw new Error('获取福州GMtoken失败', error);
      }
    }
    const decryptData = await axios({
      url: `${fzgmBaseUrl}/csmp/v3/paas/hsm/sm4-decrypt`,
      method: 'post',
      data: {
        mode: 1,
        cipher: combinedText,
        appid: fzgmKey,
      },
      headers: {
        Authorization: fzgmToken,
      },
    });
    const base64Data = Buffer.from(decryptData.data.plain, 'base64').toString();
    return base64Data;
  }
  throw new Error('不支持的加密算法');
}

async function splitAndEncrypt(
  value,
  algorithm = process.env.ENCRYPTION_ALGORITHM
) {
  // 根据字符串长度动态设置segmentLength
  const segmentLength = 4;
  const segments = [];

  for (let i = 0; i <= value.length - segmentLength; i++) {
    const segment = value.substring(i, i + segmentLength);
    const encryptedSegment = await encrypt(segment, algorithm);
    segments.push(encryptedSegment);
  }

  // 对最后一部分（如果有的话）进行加密
  if (value.length % segmentLength !== 0) {
    const lastSegment = value.substring(
      value.length - (value.length % segmentLength)
    );
    const encryptedSegment = await encrypt(lastSegment, algorithm);
    segments.push(encryptedSegment);
  }

  return segments.join(''); // 通过 '|' 拼接加密后的分词部分
}
// 更新加密算法并返回更新后的数据
async function updateEncryption(data, currentAlgorithm, newAlgorithm) {
  // 解密数据
  const decryptedData = await decrypt(data, currentAlgorithm);
  console.log('新的加密方式:', newAlgorithm);
  console.log('解密后的原数据:', decryptedData);

  return {
    // 使用新算法加密数据
    updatedData: await encrypt(decryptedData, newAlgorithm),
    // 分词加密处理后的数据
    splitEncryptedData: await splitAndEncrypt(decryptedData),
  };
}
async function processUpdate(doc, fields, model) {
  if (!doc.encryptDataAlgorithm && !encryptDataAlgorithm) return;
  doc.encryptionAlgorithm = doc.encryptDataAlgorithm || encryptDataAlgorithm;
  for (let i = 0; i < fields.length; i++) {
    const field = fields[i];
    if (doc[field] && !doc[field].includes('*')) {
      doc[`${field}ForStore`] = await encrypt(
        doc[field],
        doc.encryptionAlgorithm
      );
      doc[`${field}SplitEncrypted`] = await splitAndEncrypt(
        doc[field],
        doc.encryptionAlgorithm
      );
      doc[field] = mask(field, doc[field]);
    }
  }
  if (model === 'SuperUser') {
    if (doc.members && doc.members.length > 0) {
      for (let j = 0; j < doc.members.length; j++) {
        const member = doc.members[j];
        for (let i = 0; i < fields.length; i++) {
          const field = fields[i];
          if (member[field] && !member[field].includes('*')) {
            member[`${field}ForStore`] = await encrypt(
              member[field],
              doc.encryptionAlgorithm
            );
            member[`${field}SplitEncrypted`] = await splitAndEncrypt(
              member[field],
              doc.encryptionAlgorithm
            );
            member[field] = mask(field, member[field]);
          }
        }
        if (member.password && member.password.length < 13) {
          const passwordEncrypt =
            member.passwordEncryptionAlgorithm || passwordEncryptionAlgorithm;
          member.password = await hashSm3(member.password, passwordEncrypt);
          member.passwordEncryptionAlgorithm = passwordEncrypt;
        }
      }
    }
  }
  if (
    !doc.passwordEncryptionAlgorithm &&
    doc.password &&
    passwordEncryptionAlgorithm &&
    doc.password.length < 13
  ) {
    doc.password = await hashSm3(doc.password, passwordEncryptionAlgorithm);
    doc.passwordEncryptionAlgorithm = passwordEncryptionAlgorithm;
  } else if (
    doc.passwordEncryptionAlgorithm &&
    doc.password &&
    doc.password.length < 13
  ) {
    doc.password = await hashSm3(doc.password, doc.passwordEncryptionAlgorithm);
  }
  // await processUpdateHmac(doc, model, conditions, adminGroup);
}
async function processUpdateHmac(doc, model, conditions, adminGroup) {
  const hmacSignAlgorithm =
    doc.user_role_power_hmac_algorithm || hmacAlgorithmEnv;
  if (!hmacSignAlgorithm) {
    return;
  }

  // 将 group 从对象转换为字符串（如果是对象）
  const originalGroup =
    typeof doc.group === 'object' ? doc.group._id : doc.group;

  if (model === 'SuperUser') {
    if (!conditions) return;
    const { _id } = conditions;
    if (!_id) return;
    if (!originalGroup && !doc.date && !doc.powerStatus && !doc.power) return;

    const { date = new Date(), powerStatus = false, members = [] } = doc;

    let power = doc.power || [];
    if (!powerStatus) {
      power = adminGroup.power;
    }

    const primarySign =
      _id +
      originalGroup +
      moment(date).format('YYYY-MM-DD HH:mm:ss') +
      powerStatus +
      power.join('');

    const primarySignHmac = await hashSm3(primarySign, hmacSignAlgorithm);
    doc.user_role_power_hmac = primarySignHmac;

    if (members.length > 0) {
      for (let i = 0; i < members.length; i++) {
        const member = members[i];
        const { _id } = member;
        if (!_id) continue;

        const secondarySign =
          _id +
          originalGroup +
          moment(date).format('YYYY-MM-DD HH:mm:ss') +
          powerStatus +
          power.join('');

        const secondarySignHmac = await hashSm3(
          secondarySign,
          hmacSignAlgorithm
        );
        member.user_role_power_hmac = secondarySignHmac;
      }
    }

    doc.members = members;
    doc.user_role_power_hmac_algorithm = hmacSignAlgorithm;
    doc.date = date;
  } else if (
    model === 'AdminUser' ||
    model === 'OperateUser' ||
    model === 'User' ||
    model === 'PhysicalExamUser' ||
    model === 'ServiceUser'
  ) {
    // 更新qy端用户组权限hmac
    if (!conditions) return;
    const { _id = '' } = conditions;
    if (!_id) return;
    if (!originalGroup && !doc.date) return;

    const { date = new Date() } = doc;

    const primarySign =
      _id +
      originalGroup +
      moment(date).format('YYYY-MM-DD HH:mm:ss') +
      adminGroup.power.join('');

    const primarySignHmac = await hashSm3(primarySign, hmacSignAlgorithm);
    doc.user_role_power_hmac = primarySignHmac;
    doc.user_role_power_hmac_algorithm = hmacSignAlgorithm;
    doc.date = date;
  }

  // 最后确保 group 是字符串而不是对象
  doc.group = originalGroup;
}
// 脱敏函数
function mask(field, data) {
  if (field === 'phoneNum') {
    return `${data.substring(0, 3)}****${data.substring(7, 11)}`;
  } else if (
    field === 'IDcard' ||
    field === 'idNo' ||
    field === 'IDCard' ||
    field === 'IDNum'
  ) {
    return `${data.substring(0, 6)}********${data.substring(14, 18)}`;
  } else if (field === 'name') {
    if (data.length === 2) {
      return `${data[0]}*`;
    } else if (data.length === 3) {
      return `${data[0]}*${data[2]}`;
    } else if (data.length > 3) {
      return `${data.substring(0, 1)}**${data.substring(data.length - 1)}`;
    }
  }
  return data;
}
module.exports = {
  encrypt,
  decrypt,
  updateEncryption,
  processUpdate,
  processUpdateHmac,
  splitAndEncrypt,
};
