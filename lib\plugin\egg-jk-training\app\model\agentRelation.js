module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 代理等级及抽佣比例--代理方案 wx 2023/3/23
  const agentRelationSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    userID: { // user_id
      type: String,
      ref: 'User',
      require: true,
      unique: true, // 添加唯一索引
    },
    parentUserID: { // 上级代理唯一标识符
      type: String,
      default: '',
      ref: 'User',
      require: true,
    },
    // 收益
    profit: {
      type: Number,
      default: 0,
      require: true,
      // set: v => {
      //   console.log(999999, Math.round(v * 100) / 100);
      //   return Math.round(v * 100) / 100;
      // },
    },
    // 代理方案
    agentScheme: {
      type: String,
      default: '',
      ref: 'AgentCommissionRate',
      require: true,
    },
    childUserIDs: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        childUserID: {
          type: String,
          ref: 'User',
          require: true,
        },
        regDate: {
          type: Date,
          require: true,
          default: Date.now,
        },
        source: {
          type: String,
          default: 'pc',
          require: true,
        },
        effectivePayed: { // 是否产生有效支付
          type: Boolean,
          default: false,
          require: true,
        },
      },
    ], // 下级代理唯一标识符列表
    profitRecords: [
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        payInfoID: {
          type: String,
          ref: 'PayInfo',
        },
        amount: { // 金额
          type: Number,
          default: 0,
          require: true,
        },
        type: {
          type: Number,
          enum: [ 0, 1 ], // 类型 收入,支出
          require: true,
        },
        remark: { // 备注
          type: String,
          default: '',
        },
        triggerUserID: { // 触发者
          type: String,
          ref: 'User',
          require: true,
        },
        date: {
          type: Date,
          require: true,
          default: Date.now,
        },
      },
    ], // 收益记录
  }, {
    timestamps: true,
  });
  // 在进行 $inc 操作之后对 profit 字段进行四舍五入处理 只对findOneAndUpdate有效
  agentRelationSchema.post('findOneAndUpdate', function(doc) {
    if (doc && 'profit' in doc) {
      doc.profit = Math.round(doc.profit * 100) / 100;
      console.log(11111, doc.profit);
      doc.save();
    }
  });
  agentRelationSchema.index({ userID: 1 }, { unique: true }); // 添加唯一索引
  return mongoose.model('AgentRelation', agentRelationSchema, 'agentRelation');
};
