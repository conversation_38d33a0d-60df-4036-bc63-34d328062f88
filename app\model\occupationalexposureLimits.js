
/*
 * @Author: 黄婷婷
 * @Date: 2021-04-16 09:00
 * @LastEditors: 黄婷婷
 * @LastEditTime: 2021-04-16 09:00
 * @Description: 危害因素信息表
 *
 */
module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const occupationalexposureLimitsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    serviceOrgId: String, // 机构id
    projectCode: String, // 检测项目编号
    standardName2_1: String, // 2.1标准名称
    standardName2_1FullName: String, // 2.1标准全名
    hasRespirableDust: Boolean, // 是否有呼尘
    initial: { // 首字母
      type: String,
      trim: true,
    },
    pinyin: { // 搜索用的lht+ 拼音检索
      type: Array,
    },
    chineseName: [{
      type: String,
      trim: true,
    }], // 别名
    showName: {
      type: String,
      trim: true,
    }, // 中文名
    englishName: {
      type: String,
      trim: true,
    }, // 英文名
    catetory: String, // 危害因素分类
    casNum: {
      type: String,
      trim: true,
    }, // 化学文摘号
    MAC: {
      type: String,
      trim: true,
    }, // MAC限值
    PC_TWA: {
      type: String,
      trim: true,
    }, // TWA限值 如果是粉尘的话是总尘
    respirableDust_TWA: {
      type: String,
      trim: true,
    }, // 粉尘-呼尘
    FSiO2: {
      type: Boolean,
      default: false,
    }, // 是否需要做游离SiO2检测
    PC_STEL: {
      type: String,
      trim: true,
    }, // STEL限值
    PE: {
      type: String,
      trim: true,
    }, // PE限值
    respirableDust_PE: {
      type: String,
      trim: true,
    }, // 呼尘的PE限值
    healthEffect: {
      type: String,
      trim: true,
    }, // 临界不良健康效应
    note: String, // 备注
    protectiveEquipment: {
      type: String,
      trim: true,
    }, // 防护用品
    highHarm: String, // 是否高毒 '1'表示高毒,‘0’表示不是高毒
    seriousHarm: String, // 是否是严重危害因素 '1':严重 '2':一般
    samplingMedium: {
      type: String,
      trim: true,
    }, // 采样介质
    fixedPointDetection: { // 定点检测
      instrument: {
        type: String,
        trim: true,
      }, // 仪器
      flow: {
        type: String,
        trim: true,
      }, // 流量 总尘流量
      respirableDust_flow: {
        type: String,
        trim: true,
      }, // 呼尘流量
      samplingTime: {
        type: String,
        trim: true,
      }, // 采样时间
    },
    individualDetection: { // 个体检测
      instrument: {
        type: String,
        trim: true,
      }, // 仪器
      flow: {
        type: String,
        trim: true,
      }, // 流量
      samplingTime: {

      }, // 采样时间
    },
    storageTime: {
      type: String,
      trim: true,
    }, // 储存时间
    standard: {
      type: String,
      trim: true,
    }, // 标准 总尘
    standardInfo: { // 标准信息
      name: {
        type: String,
        trim: true,
      }, // 标准名称
      num: {
        type: String,
        trim: true,
      }, // 标准号
    },
    respirableDust_standard: {
      type: String,
      trim: true,
    }, // 呼尘标准
    respirableDust_standardInfo: { // 呼尘标准信息
      name: {
        type: String,
        trim: true,
      }, // 标准名称
      num: {
        type: String,
        trim: true,
      }, // 标准号
    },
    alias: String, // 物理别名


    // ----------------物理因素限值信息----------------------------
    // 超高频辐射---------------
    ultraHighRadiationTouch: [{
      type: new Schema({
        touchTime: String, // 接触时间
        seriesWavePowerDensity: Number, // 连续波 功率密度
        seriesWaveElectricIntensity: Number, // 连续波 电场强度
        pulseWavePowerDensity: Number, // 脉冲波 功率密度
        pulseWaveElectricIntensity: Number, // 脉冲波 电场强度
      }),
      default: undefined,
    }],

    // 高频电磁场-----------------
    highFrequencyElectromagnetic: {
      type: [ new Schema({
        magneticIntensity: String, // 磁场强度
        frequency: String, // 频率
        electricIntensity: String, // 电场强度
      }) ],
      default: undefined,
    },

    // 工频电场----------------------
    powerFrequencyElectric: {
      type: [ new Schema({
        frequency: String, // 频率
        electricIntensity: String, // 电场强度
      }) ],
      default: undefined,
    },

    // 激光辐射-------------------------
    laser: {
      type: [ new Schema({
        spectralRange: String, // 光谱范围
        wavelength: String, // 波长
        irradiationTime: String, // 照射时间
        irradiationDose: String, // 照射量
        irradiance: String, // 辐照度
      }) ],
      default: undefined,
    },
    // 微波辐射----------------------------
    microwave: {
      type: [
        new Schema({
          type: String, // 类型
          subType: String, // 子类型
          dailyDose: String, // 日剂量
          averagePowerDensity: String, // 8h平均功率密度
          noAveragePowerDensity: String, // 非8h平均功率密度
          shortTimeContactPowerDensity: String, // 短时间接触功率密度（mW/cm²）
        }),
      ],
      default: undefined,
    },

    // 紫外辐射--------------------------------
    ultravioletRadiation: {
      type: [
        new Schema({
          category: String, // 紫外光谱分类
          irradiance: String, // 辐照度
          exposureDose: String, // 辐射量
        }),
      ],
      default: undefined,
    },

    // 高温--------------------------------
    heat: {
      type: [ new Schema({
        contactTimeRate: String, // 接触时间率
        laborIntensity: String, // 体力劳动强度
        touchLimit: String, // 限值
      }) ],
      default: undefined,
    },
    // 体力劳动强度分级表--------------------------
    labourIntensityLevel: {
      type: [ new Schema({
        level: String, // 体力劳动强度级别
        index: String, // 劳动强度指数（n）
      }) ],
      default: undefined,
    },
    // 噪声------------------------------
    noise: {
      type: [ new Schema({
        touchTime: String, // 接触时间
        touchLimit: String, // 接触限值[dB(A)]
        note: String, // 备注
      }) ],
      default: undefined,
    },

    // 手传振动---------------------
    handBorneVibration: {
      type: [ new Schema({
        touchTime: String, // 接触时间
        acceleration: String, // 等能量频率计权振动加速度（m/s²）
      }) ],
      default: undefined,
    },
  });
  occupationalexposureLimitsSchema.index({ initial: 1 });
  return mongoose.model('occupationalExposureLimits', occupationalexposureLimitsSchema);
};
