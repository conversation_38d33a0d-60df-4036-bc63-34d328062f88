/*
 * @Author: 黄婷婷
 * @Date: 2021-07-13 09:00
 * @LastEditors: 黄婷婷
 * @LastEditTime: 2021-07-13 09:00
 * @Description: 集团公司绑定操作记录表
 *
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const GroupEnterprisesRecordSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    EnterpriseID: String, // 企业id 发起企业
    targetEnterpriseID: String, // 目标企业id
    isUp: { // 用于扩展的申请类型，true为添加上级单位，false为添加下级单位
      type: Boolean,
      default: true,
    },
    messageStatus: { // 消息状态 0 无申请 1 待审核 2 通过 3 拒绝
      type: Number,
      default: 0,
    },
    date: { // 操作时间
      type: Date,
      default: Date.now,
    },
  });
  return mongoose.model('GroupEnterprisesRecord', GroupEnterprisesRecordSchema, 'groupEnterprisesRecords');
};
