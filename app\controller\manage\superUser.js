const Controller = require('egg').Controller;
const validator = require('validator');

// const moment = require('moment');

class SuperManageController extends Controller {
  // 用于前端判断用户是否已登录
  async loginVerification() {
    const { ctx } = this;
    try {
      const _id = ctx.session.superUser._id;
      const user = await ctx.service.superUser.item(ctx, {
        query: { _id },
        populate: 'none',
      });

      console.log(user.name || user.userName, '========刷新页面展示的用户信息===============');
      if (user) {
        await ctx.helper.renderSuccess(ctx, {
          data: user,
          // modelPath: `${this.app.config.static.prefix}/${this.app.config.upload_http_path}/${user.companyId[user.companyId.length - 1]}`,
          message: '校验成功',
        });
      } else {
        await ctx.helper.renderFail(ctx, {
          message: '校验失败',
        });
      }
    } catch (error) {
      ctx.helper.renderFail(error);
    }

  }
  // 退出登录
  async logOutAction() {
    const { ctx, app } = this;
    ctx.auditLog('退出登录', '当前用户进行了退出登录操作。', 'info');
    ctx.session = null;
    ctx.cookies.set('admin_' + app.config.auth_govcookie_name, null);
    ctx.helper.renderSuccess(ctx, { message: '退出登录成功' });
  }
  // 修改密码或者手机号
  async updateInfo() {
    const { ctx, app } = this;
    let { password, phoneNum, countryCode, messageCode } = ctx.request.body;
    let errMsg = '';
    const _id = ctx.session.superUser._id;
    if (password) { // 修改密码
      password = password.trim();
      if (password.indexOf('$') === -1) {
        await ctx.service.superUser.update(ctx, _id, {
          password,
        });
        ctx.helper.renderSuccess(ctx, {
          message: `修改 ${ctx.__('restful_api_response_success', [ ctx.__('lc_password') ])}`,
        });
      } else {
        ctx.helper.renderFail(ctx, {
          message: ctx.__('validate_error_params'),
        });
      }
    } else if (phoneNum && messageCode) { // 修改手机
      phoneNum = phoneNum.trim();
      messageCode = messageCode.trim();
      countryCode = countryCode.trim() || '86';
      const cacheKey = '_sendMessage_update_';
      if (!phoneNum || !validator.isNumeric(phoneNum.toString())) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_phoneNum') ]);
      }
      if (!countryCode) {
        errMsg = ctx.__('validate_selectNull', [ ctx.__('label_user_countryCode') ]);
      }
      if (!messageCode || !validator.isNumeric((messageCode).toString()) || (messageCode).length !== 6) {
        errMsg = ctx.__('validate_inputCorrect', [ ctx.__('label_user_imageCode') ]);
      }
      const queryUserObj = {
        $or: [{
          phoneNum,
        }, {
          phoneNum: '0' + phoneNum,
        }],
        countryCode,
      };
      const userCount = await ctx.service.superUser.count(queryUserObj);
      if (userCount > 0) {
        errMsg = '该手机号已被使用！';
      }
      const params = countryCode + phoneNum;
      const currentCode = ctx.helper.getCache(app.config.session_secret + cacheKey + params);
      if (!currentCode || !validator.isNumeric((currentCode).toString()) || (currentCode).length !== 6) {
        errMsg = '对不起！手机号错误！';
      }
      if (errMsg) {
        ctx.helper.renderFail(ctx, {
          message: errMsg,
        });
        return;
      }
      if (currentCode !== messageCode) {
        // 依照需求决定是否使用严格的校验方式 现方式允许用户手机号或验证码写错后不用重新发送验证码
        // ctx.helper.clearCache(params, cacheKey);
        ctx.helper.renderFail(ctx, {
          message: '对不起！验证码错误！看看是不是验证码写错了？',
        });
        return;
      }
      await ctx.service.superUser.update(ctx, _id, {
        phoneNum,
      });
      ctx.helper.renderSuccess(ctx, {
        status: 200,
        message: `修改 ${ctx.__('restful_api_response_success', [ ctx.__('label_user_phoneNum') ])}`,
      });

    } else {
      ctx.helper.renderFail(ctx, {
        message: ctx.__('validate_error_params'),
      });
    }
  }

  async getGuideMap(ctx) {
    const res = await this.ctx.model.GuideMap.findOne({ superID: ctx.session.superUser._id });
    ctx.helper.renderSuccess(ctx, {
      data: res,
      message: res ? '数据获取成功' : '数据获取失败',
      status: 200,
    });
  }

}

module.exports = SuperManageController;
