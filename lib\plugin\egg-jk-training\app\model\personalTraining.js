module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 个人培训记录

  //              管理员培训  公开课培训   员工培训
  // employeesId                           有
  // EnterpriseID                          有
  // userId           有        有         有
  // adminUserId      有

  // 查询的时候通过userId可以查询到该用户所有的培训，
  // 再加上EnterpriseID可以帅选在不同企业中的员工培训，
  // 加上trainingType可以帅选不同类型的培训。

  const PersonalTrainingSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      trainingType: {
        type: Number,
        enum: [ 1, 2, 3, 4 ], // 1管理员培训 2自主培训 3 员工培训 4 负责人培训（目前只有安徽培训有）
        default: 1,
      },
      roles: [
        {
          // 角色,对应的是roles表的alisa，这个角色身份是静态的，也就是创建个人培训记录时用户的身份，以后是有可能会变的
          type: String,
        },
      ],
      adminUserId: {
        // 管理员培训肯定有adminUserId   如果是企业负责人或者企业管理人一定有adminUserId
        type: String,
        ref: 'AdminUser',
      },
      userId: {
        // 这个必须有
        type: String,
        ref: 'User',
        require: true,
      },
      unitTrain: String, // 第三培训id 目前用户propagates
      employeesId: {
        // 员工培训培训肯定有employeesId
        type: String,
        ref: 'Employees',
        default: '',
      },
      adminTrainingId: {
        // trainingType == 1/2时才有
        type: String,
        ref: 'AdminTraining',
        default: '',
      },
      employeesTrainingPlanId: {
        // trainingType == 3时才有
        type: String,
        ref: 'employeesTrainingPlan',
        default: '',
      },
      examSyllabusId: {
        // 考试大纲
        type: String,
        ref: 'ExamSyllabus',
      },
      trainingClassId: {
        // 培训班id
        type: String,
        ref: 'trainingClass',
      },
      trainingRecordId: {
        // 培训记录id, 目前只有安徽培训有
        type: String,
        ref: 'trainingRecords',
      },
      EnterpriseID: {
        // 企业id，员工培训肯定有EnterpriseID
        type: String,
        ref: 'Adminorg',
        default: '',
      },
      courses: [
        {
          // 所有课程, 这里有个问题，就是课程有可能是会被修改的，但是这个问题暂时不考虑
          _id: {
            type: String,
            default: shortid.generate,
          },
          courseType: {
            type: Number,
            default: 1,
          }, // 1必修  2选修
          coursesId: {
            type: String,
            default: '',
            ref: 'Courses',
          },
          coursePlayProgress: [
            {
              _id: {
                type: String,
                default: shortid.generate,
              },
              classHours: {
                // 视频学时
                type: Number,
                default: 0,
                set(val) {
                  return Number(val);
                },
              },
              chapterID: String, // 章节ID，存的是courses表里的sort里的_id
              videoProgress: {
                type: Number, // 视频播放的位置
                default: 0, // 非视频也可以是0，反正用不到
                set(val) {
                  return Number(val);
                },
              },
              totalTime: {
                type: Number, // 观看的总时间，如果重复播放的话就会超过视频原本的总时长
                default: 0, // 非视频也可以是0，反正用不到
                set(val) {
                  return Number(val);
                },
              }, // 这节观看总时间，及格线为视频总时长的80%，允许超过视频总时长
              completeState: {
                type: Boolean,
                default: false,
              }, // 章节完成状态
              duration: { // 视频原本总时长
                type: Number,
                require: true,
                set(val) {
                  return Number(val);
                },
              },
            },
          ], // 课程学习进度

          completeState: {
            // 课程完成状态
            type: Boolean,
            default: false,
          },
          completeDate: Date, // 课程完成时间

          chapterPosition: {
            type: String, // 章节ID，存的是courses表里的sort里的_id
            default: '', // 就是上面数组的_id
          }, // 上次播放的章节位置， 空就为没开始学习。

          testStatus: {
            // 考试是否通过
            type: Boolean,
            default: false,
          },
          completeTime: Date, // 考试通过时间,也就是这个课程的完成时间
          testList: [
            {
              type: String,
              ref: 'TestRecord',
            },
          ], // 考试记录（记录里面包含了每次考试的结果），最后一个就是最终的考试结果

          like: {
            type: Boolean,
            default: false,
          }, // 是否点赞
        },
      ],
      completeState: {
        // 培训完成状态,不包括领取证书,另外员工培训是没有证书
        type: Boolean,
        default: false,
      },
      bigTestList: [
        {
          type: String,
          ref: 'TestRecord',
        },
      ], // 大考考试记录（记录里面包含了每次考试的结果），最后一个就是最终的考试结果
      certificateID: {
        // 证书id
        type: String,
        ref: 'Certificate',
      },
      status: {
        // 是否可用
        type: Boolean,
        default: true,
      },
      payInfoID: { // 支付信息
        type: String,
        ref: 'PayInfo',
      },
      sign: { // 签名
        type: String,
        default: '',
      },
      verifyImage: { // 人脸识别的图片
        type: String,
        default: '',
      },
      exmaVerifyImage: [
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          times: {
            type: Number,
            default: 1,
          }, // 第几次考试
          url: {
            type: String,
            default: '',
          }, // 路径
          name: {
            type: String,
            default: '',
          }, // 名字
        },
      ],
      // 北元培训
      duration: {
        // 培训时长
        type: Number,
      },
      // STUDYTIME
      studyTime: {
        // 学习时长
        type: Number,
      },
      // VALIDSTUDYTIME
      validStudyTime: {
        // 有效学习时长
        type: Number,
      },
      // 合格分数
      validScore: {
        type: Number,
      },
      // 实际分数
      actualScore: {
        type: Number,
      },
      // 考试开始和结束时间
      examStartTime: {
        type: Date,
      },
      examEndTime: {
        type: Date,
      },
      source: { // 来源，指哪个端创建的
        type: String,
        default: 'oapi',
        enum: [ 'oapi', 'qy', 'pxOrg', 'super' ],
      },
    },
    {
      timestamps: true,
    }
  );

  PersonalTrainingSchema.index({ EnterpriseID: 1 });
  PersonalTrainingSchema.index({ updateAt: -1 });
  PersonalTrainingSchema.index({ unitTrain: 1 });
  return mongoose.model('PersonalTraining', PersonalTrainingSchema, 'personalTraining');
};
