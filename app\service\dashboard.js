

const Service = require('egg').Service;

class DashboardService extends Service {
  // 根据adcode获取adname
  async getAdCodeName(adcode) {
    if (adcode === '100000') return [ '中国' ];
    const res = await this.ctx.model.District.findOne({
      area_code: adcode + '',
    });
    if (res && res.name) {
      if (res.parent_code && res.parent_code.length === 12) {
        const parent = await this.ctx.model.District.findOne({ area_code: res.parent_code });
        if (parent) return [ parent.name, res.name ];
      }
      return [ res.name ];
    }
    return null;
  }

  // 获取监管首页 企业列表、体检、检测、培训等数据
  async EnterpriseListV1_0(adcode, adname) { // adcode:'330100000000', adname: ["浙江省", "杭州市"]
    const { ctx } = this;
    // 1、获取管辖区域 查询条件
    const adnameQuery = adcode === '100000' ? { $exists: true } : { $all: adname };
    // console.log(111111111, typeof adcode, adcode, adname, adnameQuery);
    // 2、按企业工作场所来
    const list1 = await ctx.model.Adminorg.find({
      isactive: { $ne: '2' },
      $or: [
        { isDelete: false },
        { isDelete: null },
      ],
      'workAddress.districts': adnameQuery,
      productionStatus: { $ne: '0' },
    }, { id: 1, level: 1 });

    // 3、按照检测项目来
    const query = { projectStop: { $ne: true } };
    if (adcode !== '100000') query.workPlaces = { $elemMatch: { workAdd: adcode } };
    const list2 = await ctx.model.JobHealth.find(query, { EnterpriseID: 1, status: 1, name: 1, serviceOrgID: 1, completeStatus: 1, completeUpdate: 1 });
    // 4、已上报的项目
    const reportJobHealth = list2.filter(ele => ele.status === 1).map(ele => ele.EnterpriseID);
    const reportJobHealthCount = [ ...new Set(reportJobHealth) ].length;
    // 5、计算超标的项目(已上报)
    const JobHealthIds = list2.filter(ele => ele.status === 1).map(ele => ele._id);
    const checkDataList = await ctx.model.CheckAssessment.find( // 检测数据
      { jobHealthId: { $in: JobHealthIds } }
    );
    const abnormalJobHealth = checkDataList.filter(ele => JSON.stringify(ele).includes('不符合') || JSON.stringify(ele).includes('不合格'));
    // 6、统计检测项目：机构名称、项目数量（个）、已申报（个）、已完成（个）、已更新（个）
    const jobHealthOrgs = {};
    list2.forEach(ele => {
      const serviceOrgID = ele.serviceOrgID || ele.name;
      if (!jobHealthOrgs[serviceOrgID]) jobHealthOrgs[serviceOrgID] = { name: ele.name, projectNum: 0, completed: 0, updated: 0 };
      jobHealthOrgs[serviceOrgID].projectNum += 1;
      if (ele.completeStatus) jobHealthOrgs[serviceOrgID].completed += 1;
      if (ele.completeUpdate) jobHealthOrgs[serviceOrgID].updated += 1;
    });
    // 获取注册在此没有项目的检测机构
    const jobHealthOrgIds = Object.keys(jobHealthOrgs);
    const jobHealthOrgsList = await ctx.model.ServiceOrg.find(
      { _id: { $nin: jobHealthOrgIds }, regAddr: adnameQuery, status: 3, name: { $exists: true } },
      { name: 1 }
    );
    jobHealthOrgsList.forEach(ele => {
      if (ele.name !== 'null') {
        jobHealthOrgs[ele._id] = { name: ele.name, projectNum: 0, completed: 0, updated: 0 };
      }
    });

    // 7、体检项目数据
    const list3 = await ctx.model.Healthcheck.find({
      reportStatus: true,
      workAddress: {
        $elemMatch: { districts: adnameQuery },
      },
    }, { EnterpriseID: 1, suspected: 1, forbid: 1, physicalExaminationOrgID: 1, organization: 1, workAddress: 1 });
    // 8、计算疑似职业病（人数）、禁忌症（人数）
    const suspectedCount = list3.reduce((c, R) => c + R.suspected, 0);
    const forbidCount = list3.reduce((c, R) => c + R.forbid, 0);
    // 9、统计体检项目数量
    const healthCheckOrgs = {};
    list3.forEach(ele => {
      const serviceOrgID = ele.physicalExaminationOrgID || ele.organization; // 防止有的项目没有机构ID
      if (!healthCheckOrgs[serviceOrgID]) healthCheckOrgs[serviceOrgID] = { name: ele.organization, projectNum: 0, workAddress: ele.workAddress[0] || null };
      healthCheckOrgs[serviceOrgID].projectNum += 1;
    });
    // 获取注册在此没有项目的体检机构
    const healthCheckOrgIds = Object.keys(healthCheckOrgs);
    const healthCheckOrgsList = await ctx.model.PhysicalExamOrg.find(
      { _id: { $nin: healthCheckOrgIds }, regAddr: adnameQuery, status: 3, name: { $exists: true } },
      { name: 1, address: 1, regAddr: 1 }
    );
    healthCheckOrgsList.forEach(ele => {
      healthCheckOrgs[ele._id] = { name: ele.name, projectNum: 0, workAddress: { districts: ele.regAddr, address: ele.address || '' } };
    });

    // 10、统计分析
    const list1Ids = list1.map(ele => ele._id); // 注册的企业
    const list2Ids = list2.map(ele => ele.EnterpriseID); // 检测的企业
    const list3Ids = list3.map(ele => ele.EnterpriseID); // 体检的企业
    const result = [ ...new Set(list1Ids.concat(list2Ids).concat(list3Ids)) ]; // 所有企业id
    // 11、统计严重危害企业数量
    const seriouHarmList = list1.filter(ele => ele.level === '严重');
    // 12、获取培训数据
    const trainingList = await ctx.model.Propagate.find({ EnterpriseID: { $in: result } }, { EnterpriseID: 1 });
    const trainingCount = new Set(trainingList.map(ele => ele.EnterpriseID)).size;
    // 13、职业病人数
    const odiseaseCount = await ctx.model.Odisease.count({ EnterpriseID: { $in: result } });
    // 14、返回数据
    return {
      totle: result.length, // 总数量
      regEnterprise: [ ...new Set(list1Ids) ].length, // 注册企业数量
      seriouHarmEnterprise: seriouHarmList.length, // 严重危害企业数
      jobHealth: [ ...new Set(list2Ids) ].length, // 检测的企业数量
      reportJobHealth: reportJobHealthCount, // 已申报的项目数量
      abnormalJobHealth: abnormalJobHealth.length, // 超标的项目数量
      healthCheck: [ ...new Set(list3Ids) ].length, // 体检的企业数量
      suspectedCount, // 疑似职业病人数
      forbidCount, // 禁忌症人数
      odiseaseCount, // 职业病人数
      trainingCount, // 培训企业数量
      healthCheckOrgs: Object.values(healthCheckOrgs).sort((a, b) => b.projectNum - a.projectNum), // 体检机构 项目数量统计
      jobHealthOrgs: Object.values(jobHealthOrgs).sort((a, b) => b.projectNum - a.projectNum), // 检测机构 项目数量统计
    };
  }

  // 获取首页预警数据 xxn add
  async countWarningDataV1_0(adname) { // adname: ["杭州市"]
    const { ctx } = this;
    const query = { delete: false, status: { $in: [ 0, 1, 2, 4 ] } };
    if (adname[0] !== '中国') {
      query.workAddress = { $elemMatch: { $elemMatch: { $all: [ adname[adname.length - 1] ] } } };
    }
    return await ctx.model.Warning.count(query);
  }

}

module.exports = DashboardService;
