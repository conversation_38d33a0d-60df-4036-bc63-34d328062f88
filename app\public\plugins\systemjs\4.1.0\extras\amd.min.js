!function(e){const t=System.constructor.prototype,r=[[],function(){return{}}];function n(){throw Error("AMD require not supported.")}function o(){}const s=["require","exports","module"];function i(e,t){const r={},o={exports:r},s=[],i=[];let u=0;for(let t=0;t<e.length;t++){const f=e[t],c=i.length;if("require"===f)s[t]=n,u++;else if("module"===f)s[t]=o,u++;else if("exports"===f)s[t]=r,u++;else{const e=t;i.push(function(t){s[e]=t.__useDefault?t.default:t})}u&&(e[c]=f)}u&&(e.length-=u);const f=t;return[e,function(e){return e({default:r,__useDefault:!0}),{setters:i,execute:function(){o.exports=f.apply(r,s)||o.exports,r!==o.exports&&e("default",o.exports)}}}]}let u;const f=t.register;3===f.length?t.register=function(e,t,r){"string"!=typeof e&&(u=t),f.apply(this,arguments)}:t.register=function(e,t){u=t,f.apply(this,arguments)};const c=t.getRegister;let l,p;t.getRegister=function(){const e=c.call(this);if(e&&e[1]===u)return e;if(!l)return e||r;const t=i(l,p);return l=null,t},e.define=function(e,t,r){if("string"==typeof e){if(l){if(!System.registerRegistry)throw Error("Include the named register extension for SystemJS named AMD support.");return System.registerRegistry[e]=i(t,r),l=[],void(p=o)}System.registerRegistry&&(System.registerRegistry[e]=i([].concat(t),r)),e=t,t=r}e instanceof Array?(l=e,p=t):"object"==typeof e?(l=[],p=function(){return e}):"function"==typeof e&&(l=s,p=e)},e.define.amd={}}("undefined"!=typeof self?self:global);