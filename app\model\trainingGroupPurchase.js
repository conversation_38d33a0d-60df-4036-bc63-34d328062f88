module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 团购兑换码 wx 2023/2/14
  const TrainingGroupPurchaseSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    buyerUserID: { // 购买者userID
      type: String,
      ref: 'User',
      require: true,
    },
    quota: { // 购买的名额数量
      type: Number,
      default: 0,
      set: val => Number(val),
    },
    useInfo: [// 使用信息
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        userID: { // 使用者id
          type: String,
          ref: 'User',
          require: true,
        },
        personalTrainingId: { // 个人培训id
          type: String,
          ref: 'PersonalTraining',
        },
        usedTime: Date, // 使用时间
      },
    ],
    conversionCode: { // 兑换码
      type: String,
      require: true,
    },
    status: {
      type: Number,
      default: 0,
      enum: [ 0, 1, 2, 3 ], // 0 创建一条记录并没有支付，1 未用完,2 已经用完了 3 作废
    },
    payInfoID: { // 支付信息
      type: String,
      ref: 'PayInfo',
    },
    usable: { // 是否可用,假删备用的字段
      type: Boolean,
      default: true,
    },
  }, {
    timestamps: true,
  });


  return mongoose.model('TrainingGroupPurchase', TrainingGroupPurchaseSchema, 'trainingGroupPurchase');
};
