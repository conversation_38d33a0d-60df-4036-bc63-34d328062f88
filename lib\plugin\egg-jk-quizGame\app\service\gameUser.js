const Service = require('egg').Service;
const moment = require('moment');
class GameUserService extends Service {
  // 更新游戏用户信息
  async editGameUser(payload) {
    const { ctx } = this;
    await this.createGameUserValidate(payload);
    const { _id } = payload;
    if (!_id) throw new Error('用户_id不能为空');
    const user = await ctx.model.GameUser.findOne({ _id });
    if (user) {
      const user = await ctx.model.GameUser.findOneAndUpdate({ _id }, payload, { new: true });
      if (user) {
        ctx.auditLog('更新游戏用户信息', JSON.stringify(payload), 'info');
        return user;
      }
      throw new Error('更新用户信息失败');
    }
    throw new Error('用户信息不存在, _id: ' + _id);
  }

  // 根据 unionId 获取/创建游戏用户信息
  async getGameUser(gameEventId, wxUnionId) {
    if (!wxUnionId) throw new Error('参数错误，wxUnionId不能为空');
    if (!gameEventId) throw new Error('参数错误，gameEventId不能为空');
    const { ctx } = this;
    const user = await ctx.model.GameUser.findOne({ wxUnionId, gameEventId });
    if (!user) { // 创建并返回
      return await ctx.model.GameUser.create({ wxUnionId, gameEventId });
    }
    return user;
  }

  async getOne(query = {}) {
    return await this.ctx.model.GameUser.findOne(query);
  }

  // 用户的字段校验
  async createGameUserValidate(payload) {
    const { _id, name, phoneNum, Enterprise, gender } = payload;
    if (!_id) throw new Error('用户_id不能为空');
    if (!name) throw new Error('姓名不能为空');
    if (!phoneNum) throw new Error('手机号不能为空');
    if (!Enterprise) throw new Error('所在企业不能为空');
    if (!gender) throw new Error('性别不能为空');
    if (![ 'male', 'female' ].includes(gender)) throw new Error('性别格式不正确, 请填写male或female');
    if (!/^1[3-9]\d{9}$/.test(phoneNum)) throw new Error('手机号格式不正确');
    const EnterpriseInfo = await this.ctx.service.gameEnterprise.getOne({ _id: Enterprise });
    if (!EnterpriseInfo) throw new Error('所选企业不存在');
  }

  // 获取已获奖用户列表
  async getWinningUsers(gameEventId, limit = 30) {
    const { ctx } = this;
    if (!gameEventId) throw new Error('参数错误，gameEventId不能为空');
    const prizes = await ctx.model.Prizes.find({ gameEventId, enable: true, hidden: false }, { _id: 1 });
    const showPrizes = prizes.map(item => item._id);
    // 查询获奖用户列表
    let list = await ctx.model.GameRecords.find(
      { gameEventId, prizeId: { $in: showPrizes }, status: 2, userId: { $ne: null } },
      { userId: 1, prizeId: 1, drawTime: 1 }
    )
      .sort({ drawTime: -1 })
      .limit(limit)
      .populate('prizeId', 'name')
      .populate('userId', 'name phoneNum');
    list = list.filter(item => item.userId);
    // 格式化结果
    const result = list.map(({ userId, prizeId, drawTime }) => ({
      name: userId.name,
      phoneNum: userId.phoneNum,
      prize: prizeId.name,
      drawTime: moment(drawTime).format('YYYY-MM-DD HH:mm'),
    }));
    return result;
  }

  // 抽奖后更新个人信息
  async addWinnerInfo(winnerInfo) {
    const { ctx } = this;
    const { userId, EnterpriseName, name, phoneNum, address, idCard, idType, phoneCarrier } = winnerInfo;

    const user = await ctx.model.GameUser.findOne({ _id: userId });
    if (!user) throw new Error('用户不存在');

    // 检查活动是否存在且启用
    const { gameEventId } = user;
    const gameEvent = await ctx.model.GameEvents.findOne({ _id: gameEventId, enable: true });
    if (!gameEvent) throw new Error(`id为${gameEventId}的活动不存在或已被禁用`);
    if (!gameEvent.needWinnerInfo) throw new Error('该活动不需要填写中奖者信息');

    const winnerInfoKeys = gameEvent.winnerInfo || [];
    const updateData = {};

    // 验证并赋值中奖者信息
    winnerInfoKeys.forEach(item => {
      if (!winnerInfo[item]) throw new Error(`缺少参数: ${item}`);
      if (item === 'EnterpriseName') {
        updateData.EnterpriseName = EnterpriseName;
      } else if (item === 'name') {
        updateData.name = name;
      } else if (item === 'phoneNum') {
        if (!/^1[3-9]\d{9}$/.test(phoneNum)) throw new Error('手机号格式不正确');
        updateData.phoneNum = phoneNum;
        updateData.countryCode = '86';
      } else if (item === 'address') {
        updateData.address = address;
      } else if (item === 'phoneCarrier') {
        updateData.phoneCarrier = phoneCarrier;
      } else if (item === 'idCard') {
        if (!idType || idType === '身份证') {
          if (!/^\d{15}|\d{18}$/.test(idCard)) throw new Error('身份证号码格式不正确');
        }
        updateData.idCard = idCard;
        updateData.idType = idType;
      } else {
        console.log('未知的字段:', item);
      }
    });

    // 更新用户信息并返回
    const result = await ctx.model.GameUser.updateOne({ _id: userId }, { winnerInfo: updateData });
    if (result.nModified === 0) {
      throw new Error('中奖者收获信息添加失败');
    }
    const res = await ctx.model.GameUser.findOne({ _id: userId }, { winnerInfo: 1 });
    return res.winnerInfo;
  }
}
module.exports = GameUserService;
