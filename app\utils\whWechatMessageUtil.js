'use strict';

const axios = require('axios');
const moment = require('moment');
const fs = require('fs').promises;
const path = require('path');

class whWechatMessageUtil {
  constructor(ctx) {
    this.ctx = ctx;
    this.config = ctx.app.config.whRequest;
    this.baseUrl = this.config.whWechatUrl || 'https://apigwqas.whchem.com:643/moa_api_msg';
  }

  /**
   * 验证消息数据格式
   * @param {Object|Array} data - 消息数据
   * @return {Object} 验证结果
   */
  validateMessageData(data) {
    const messages = Array.isArray(data) ? data : [ data ];
    const errors = [];

    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i];
      const prefix = `消息[${i}]`;

      // 验证必填字段
      if (!msg.bpdname) {
        errors.push(`${prefix}: bpdname（流程名称）为必填字段`);
      } else {
        const bpdnameValidation = this.validateBilingualFormat(msg.bpdname, 'bpdname');
        if (!bpdnameValidation.valid) {
          errors.push(`${prefix}: ${bpdnameValidation.error}`);
        }
      }

      if (!msg.activityname) {
        errors.push(`${prefix}: activityname（环节名称）为必填字段`);
      } else {
        const activitynameValidation = this.validateBilingualFormat(msg.activityname, 'activityname');
        if (!activitynameValidation.valid) {
          errors.push(`${prefix}: ${activitynameValidation.error}`);
        }
      }

      if (!msg.fromid) {
        errors.push(`${prefix}: fromid（发起人itcode）为必填字段`);
      }

      if (!msg.fromname) {
        errors.push(`${prefix}: fromname（发起人姓名）为必填字段`);
      }

      if (!msg.toid) {
        errors.push(`${prefix}: toid（接收人itcode）为必填字段`);
      }

      if (!msg.toname) {
        errors.push(`${prefix}: toname（接收人姓名）为必填字段`);
      }

      if (!msg.taskid) {
        errors.push(`${prefix}: taskid（任务URL）为必填字段`);
      }

      // 验证action字段值
      if (msg.action && ![ '1', '2', '3', '4', '99' ].includes(msg.action.toString())) {
        errors.push(`${prefix}: action 字段值无效，应为 1(提交), 2(驳回), 3(加签), 4(转办), 99(未知)`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 验证双语格式（中文@英文）
   * @param {string} text - 待验证文本
   * @param {string} fieldName - 字段名称
   * @return {Object} 验证结果
   */
  validateBilingualFormat(text, fieldName) {
    if (!text || typeof text !== 'string') {
      return {
        valid: false,
        error: `${fieldName} 必须为字符串类型`,
      };
    }

    if (!text.includes('@')) {
      return {
        valid: false,
        error: `${fieldName} 必须包含双语格式：中文@英文`,
      };
    }

    const parts = text.split('@');
    if (parts.length !== 2) {
      return {
        valid: false,
        error: `${fieldName} 格式错误，应为：中文@英文`,
      };
    }

    const [ zhPart, enPart ] = parts;
    if (zhPart.length > 20) {
      return {
        valid: false,
        error: `${fieldName} 中文部分不能超过20个字符`,
      };
    }

    if (enPart.length > 20) {
      return {
        valid: false,
        error: `${fieldName} 英文部分不能超过20个字符`,
      };
    }

    return { valid: true };
  }

  /**
   * 构造双语描述
   * @param {string} zhName - 中文名称
   * @param {string} enName - 英文名称
   * @return {string} 双语格式字符串
   */
  buildBilingualName(zhName, enName) {
    // 截断超长文本
    const maxLength = 20;
    const trimmedZh = zhName.length > maxLength ? zhName.substring(0, maxLength) : zhName;
    const trimmedEn = enName.length > maxLength ? enName.substring(0, maxLength) : enName;

    return `${trimmedZh}@${trimmedEn}`;
  }

  /**
   * 格式化时间为标准格式
   * @param {Date|string} date - 时间对象或字符串
   * @return {string} 格式化后的时间字符串
   */
  formatDateTime(date = new Date()) {
    return moment(date).format('YYYY-M-D HH:mm:ss');
  }

  /**
   * 构造微信消息推送请求负载
   * @param {string} sid - 系统标识，如 HSE、OHS 等
   * @param {Object|Array} messageData - 消息数据
   * @return {Object} 请求负载
   */
  buildMessagePayload(sid, messageData) {
    const messages = Array.isArray(messageData) ? messageData : [ messageData ];

    // 为消息添加默认值和格式化
    const formattedMessages = messages.map(msg => ({
      piid: msg.piid || '0', // 第三方流程固定填写0
      taskid: msg.taskid,
      bpdid: msg.bpdid || '',
      bpdname: msg.bpdname,
      activityid: msg.activityid || '',
      activityname: msg.activityname,
      fromid: msg.fromid,
      fromname: msg.fromname,
      ctime: msg.ctime || this.formatDateTime(),
      toid: msg.toid,
      toname: msg.toname,
      action: msg.action || '1', // 默认为提交
      tstaskid: msg.tstaskid || '',
      tspiid: msg.tspiid || '',
      mpappid: msg.mpappid || '', // 小程序appid
      mppagepath: msg.mppagepath || '', // 小程序页面路径
    }));

    return {
      sid,
      data: formattedMessages,
    };
  }

  /**
   * 发送微信消息推送
   * @param {string} sid - 系统标识
   * @param {Object|Array} messageData - 消息数据
   * @return {Promise<Object>} 发送结果
   */
  async sendMessage(sid, messageData) {
    try {
      // 验证配置
      if (!this.config || !this.config.whWechatUrl) {
        throw new Error('whWechatMessage 配置缺失');
      }

      if (!this.config.whWechatApiKey) {
        throw new Error('API Key 配置缺失');
      }

      // 验证系统标识
      if (!sid) {
        throw new Error('系统标识(sid)不能为空');
      }

      // 验证消息数据
      const validation = this.validateMessageData(messageData);
      if (!validation.valid) {
        throw new Error(`消息数据验证失败：${validation.errors.join('; ')}`);
      }

      // 构造请求负载
      const payload = this.buildMessagePayload(sid, messageData);

      // 保存请求日志
      await this.saveRequestLog(payload);

      this.ctx.logger.info('准备发送微信消息推送', {
        sid,
        messageCount: Array.isArray(messageData) ? messageData.length : 1,
      });

      // 打印请求参数到控制台
      console.log('========== 微信消息推送请求参数 ==========');
      console.log('请求URL:', this.baseUrl);
      console.log('系统标识(sid):', sid);
      console.log('消息数量:', Array.isArray(messageData) ? messageData.length : 1);
      console.log('请求头信息:');
      console.log('  Content-Type: application/json');
      console.log('  X-Ca-API-Key:', this.config.whWechatApiKey ? `${this.config.whWechatApiKey.substring(0, 8)}***` : '未配置');
      console.log('请求负载(payload):');
      console.log(JSON.stringify(payload, null, 2));
      console.log('==========================================');

      // 发送请求
      const response = await axios.post(this.baseUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
          'X-Ca-API-Key': this.config.whWechatApiKey,
        },
        timeout: 30000, // 30秒超时
      });

      // 打印响应结果到控制台
      console.log('========== 微信消息推送响应结果 ==========');
      console.log('响应状态码:', response.status);
      console.log('响应状态文本:', response.statusText);
      console.log('响应头信息:');
      console.log(JSON.stringify(response.headers, null, 2));
      console.log('响应数据:');
      console.log(JSON.stringify(response.data, null, 2));
      console.log('==========================================');

      this.ctx.logger.info('微信消息推送成功', {
        statusCode: response.status,
        responseData: response.data,
      });

      return {
        success: true,
        statusCode: response.status,
        data: response.data,
        message: '消息推送成功',
      };

    } catch (error) {
      // 打印错误信息到控制台
      console.log('========== 微信消息推送错误信息 ==========');
      console.log('错误类型:', error.name);
      console.log('错误消息:', error.message);
      console.log('系统标识(sid):', sid);
      if (error.response) {
        console.log('HTTP响应状态码:', error.response.status);
        console.log('HTTP响应状态文本:', error.response.statusText);
        console.log('HTTP响应数据:');
        console.log(JSON.stringify(error.response.data, null, 2));
      } else if (error.request) {
        console.log('请求信息:', error.request);
        console.log('无响应数据 - 可能是网络问题或超时');
      } else {
        console.log('其他错误:', error.message);
      }
      console.log('错误堆栈:');
      console.log(error.stack);
      console.log('==========================================');

      this.ctx.logger.error('微信消息推送失败', {
        error: error.message,
        sid,
        messageData,
        stack: error.stack,
      });

      return {
        success: false,
        error: error.message,
        statusCode: error.response?.status,
        responseData: error.response?.data,
        message: '消息推送失败',
      };
    }
  }

  /**
   * 保存请求日志到文件
   * @param {Object} payload - 请求负载
   */
  async saveRequestLog(payload) {
    try {
      const logDir = path.join(this.ctx.app.baseDir, 'logs', 'wechat-message');
      const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss-SSS');
      const logFile = path.join(logDir, `wechat_message_${timestamp}.json`);

      // 确保日志目录存在
      await fs.mkdir(logDir, { recursive: true });

      // 写入日志
      await fs.writeFile(logFile, JSON.stringify(payload, null, 2), 'utf8');

      this.ctx.logger.info(`微信消息推送请求日志已保存: ${logFile}`);
    } catch (writeError) {
      this.ctx.logger.error('保存微信消息推送日志失败:', writeError);
    }
  }

  /**
   * 批量发送消息（支持分批发送以避免单次请求过大）
   * @param {string} sid - 系统标识
   * @param {Array} messageList - 消息列表
   * @param {number} batchSize - 批次大小，默认为10
   * @return {Promise<Object>} 批量发送结果
   */
  async sendBatchMessages(sid, messageList, batchSize = 10) {
    if (!Array.isArray(messageList) || messageList.length === 0) {
      return {
        success: false,
        error: '消息列表不能为空',
      };
    }

    const results = [];
    const total = messageList.length;
    let successCount = 0;
    let failCount = 0;

    this.ctx.logger.info(`开始批量发送微信消息，总数：${total}，批次大小：${batchSize}`);

    // 打印批量发送概况到控制台
    console.log('========== 批量微信消息推送开始 ==========');
    console.log('系统标识(sid):', sid);
    console.log('消息总数:', total);
    console.log('批次大小:', batchSize);
    console.log('总批次数:', Math.ceil(messageList.length / batchSize));
    console.log('=========================================');

    // 分批发送
    for (let i = 0; i < messageList.length; i += batchSize) {
      const batch = messageList.slice(i, i + batchSize);
      const batchIndex = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(messageList.length / batchSize);

      console.log(`---------- 第 ${batchIndex}/${totalBatches} 批消息 ----------`);
      console.log(`批次消息数: ${batch.length}`);

      this.ctx.logger.info(`发送第 ${batchIndex}/${totalBatches} 批消息，包含 ${batch.length} 条消息`);

      const result = await this.sendMessage(sid, batch);
      results.push({
        batchIndex,
        messageCount: batch.length,
        ...result,
      });

      if (result.success) {
        successCount += batch.length;
      } else {
        failCount += batch.length;
      }

      // 批次间延迟，避免请求过快
      if (i + batchSize < messageList.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // 打印批量发送结果到控制台
    console.log('========== 批量微信消息推送结果 ==========');
    console.log('总消息数:', total);
    console.log('成功数量:', successCount);
    console.log('失败数量:', failCount);
    console.log('成功率:', `${total > 0 ? ((successCount / total) * 100).toFixed(2) : 0}%`);
    console.log('各批次详细结果:');
    results.forEach(result => {
      console.log(`  批次${result.batchIndex}: ${result.success ? '成功' : '失败'} - ${result.messageCount}条消息`);
      if (!result.success) {
        console.log(`    失败原因: ${result.error}`);
      }
    });
    console.log('==========================================');

    return {
      success: failCount === 0,
      total,
      successCount,
      failCount,
      results,
      message: `批量发送完成，成功：${successCount}，失败：${failCount}`,
    };
  }
}

module.exports = whWechatMessageUtil;
