const {
  authToken,
} = require('@utils');
const _ = require('lodash');
module.exports = (options, app) => {

  const routeWhiteList = [];

  return async function authGovToken(ctx, next) {
    ctx.session.superUser = '';
    const userToken = ctx.get('Authorization') || ctx.cookies.get('admin_' + app.config.auth_govcookie_name, {
      path: '/',
      maxAge: app.config.adminUserMaxAge,
      signed: true,
      httpOnly: false,
    });
    if (userToken) {
      const checkToken = await authToken.checkToken(userToken, app.config.encrypt_key, ctx);
      if (checkToken) {
        if (typeof checkToken === 'object') {
          const _id = checkToken._id;
          const targetUser = await ctx.service.superUser.item(ctx, {
            query: {
              _id,
            },
            populate: 'none',
            files: {
              password: 0,
              email: 0,
            },
          });
          if (!_.isEmpty(targetUser)) {
            const {
              userName,
              _id,
              group,
              regAdd,
            } = targetUser;

            ctx.session.superUser = {
              userName,
              _id,
              group,
              regAdd,
            };

            await next();
          } else {
            ctx.helper.renderFail(ctx, {
              message: '哎呀！网页开小差了！重新登录试试？',
            });
          }
        } else {
          ctx.helper.renderFail(ctx, {
            message: '哎呀！网页开小差了！重新登录试试？',
          });
        }

      } else {
        ctx.helper.renderFail(ctx, {
          message: '哎呀！网页开小差了！重新登录试试？',
        });
      }
    } else {
      // 没有配置但是包含在白名单内的路由校验
      if (!_.isEmpty(routeWhiteList)) {
        const checkWhiteRouter = _.filter(routeWhiteList, item => {
          return ctx.originalUrl.indexOf(item) >= 0;
        });
        if (!_.isEmpty(checkWhiteRouter)) {
          await next();
        } else {
          ctx.helper.renderFail(ctx, {
            message: '哎呀！网页开小差了！重新登录试试？',
          });
        }
      } else {
        ctx.helper.renderFail(ctx, {
          message: '哎呀！网页开小差了！重新登录试试？',
        });
      }

    }

  };

};
