// px培训支付
const Service = require('egg').Service;

class TrainPayService extends Service {
  // 创建取证订单
  async createCertificateOrder(data) {
    const { ctx } = this;
    try {
      data.price = data.amount.total;
      // 查寻是否点过支付了，如果有就关闭上一个订单
      const personalTrainingData = await this.ctx.model.PersonalTraining.aggregate([
        {
          $match: {
            _id: data.personalTrainingId,
          },
        }, {
          $lookup: {
            from: 'payInfo',
            localField: 'payInfoID',
            foreignField: '_id',
            as: 'payInfo',
          },
        },
      ]);
      if (personalTrainingData[0].payInfo.length > 0) {
        await this.ctx.service.pay.closeOrder(personalTrainingData[0].payInfo[0]);
      }
      const initialization = await ctx.model.PayInfo.create(data);
      await ctx.model.PersonalTraining.updateOne({ _id: data.personalTrainingId }, { payInfoID: initialization._id });
      // 微信支付
      if (data.type === 'wxpay' && !data.openId) { // 如果有openid 说明是微信小程序的
        const res = await ctx.service.pay.wxNativePay(data);
        if (res.status === 200) {
          return {
            code_url: res.code_url,
            time_expire: res.time_expire,
          };
        }
        return res.message;
      } else if (data.type === 'wxpay' && data.openId) {
        const res = await ctx.service.pay.wxSpPay(data);
        if (res.status === 200) {
          return res;
        }
        return res.message;
      } else if (data.type === 'alipay') {
        const res = await ctx.service.pay.aliNativePay(data);
        return {
          code_url: res.alipay_trade_precreate_response.qr_code,
          time_expire: res.time_expire,
        };
      }
    } catch (error) {
      ctx.auditLog('培训创建取证订单', `${JSON.stringify(error)}`, 'error');
    }
  }
  // 创建团购订单
  async createGroupOrder(data) {
    const { config, ctx } = this;
    // 创建一条团购码记录
    try {
      data.price = data.amount.total;
      data.productCategory = 2;// 产品类别为团购
      // 查寻是否点过支付了，如果有就关闭上一个订单--待添加
      // 生成cdk
      // 获取当前时间戳
      const cdkey = ctx.helper.hashSha256(data.userID + new Date().getTime(), config.salt_sha2_key);
      const groupData = {
        buyerUserID: data.userID,
        quota: data.quota,
        status: 0, // 未支付
        conversionCode: cdkey,
      };
      const groupInfo = await ctx.model.TrainingGroupPurchase.create(groupData);
      const initialization = await ctx.model.PayInfo.create(data);
      await ctx.model.TrainingGroupPurchase.updateOne({ _id: groupInfo._id }, { payInfoID: initialization._id });
      // 微信支付
      if (data.type === 'wxpay') {
        const res = await ctx.service.pay.wxNativePay(data);
        if (res.status === 200) {
          return {
            code_url: res.code_url,
            time_expire: res.time_expire,
            cdkeyID: groupInfo._id,
          };
        }
        return res.message;
      } else if (data.type === 'alipay') {
        const res = await ctx.service.pay.aliNativePay(data);
        return {
          code_url: res.alipay_trade_precreate_response.qr_code,
          time_expire: res.time_expire,
          cdkeyID: groupInfo._id,
        };
      }
    } catch (error) {
      ctx.auditLog('培训创建团购订单', `${JSON.stringify(error)}`, 'error');
    }

  }
}
module.exports = TrainPayService;
