module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const DoublePreventionSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    unitCode: {
      type: String,
    },
    BH: {
      // 编号
      type: String,
    },
    YHLY: {
      // 隐患来源
      type: String,
    },
    FXFXDX: {
      // 单位/风险分析对象
      type: String,
    },
    FXFXDY: {
      // 风险分析单元
      type: String,
    },
    YHMS: {
      // 隐患描述
      type: String,
    },
    YHLB: {
      // 隐患类别
      type: String,
    },
    YHDJ: {
      // 隐患等级
      type: String,
    },
    SQSJ: {
      // 申请时间
      type: String,
    },
    ZLLX: {
      // 治理类型
      type: String,
    },
    ZGR: {
      // 整改人
      type: String,
    },
    ZGQX: {
      // 整改日期
      type: String,
    },
    YSR: {
      // 验收人
      type: String,
    },
    DWFXFXDXSSBMCODE: {
      // 单位/风险分析对象所属部门编码
      type: String,
    },
    YHDJCODE: {
      // '隐患等级Code'
      type: String,
    },
    ZGRCODE: {
      // 整改人Code',
      type: String,
    },
    YSRCODE: {
      // '验收人Code',
      type: String,
    },
    EnterpriseID: {
      // 企业ID
      type: String,
      ref: 'Adminorg',
    },
    ZT: {
      // 状态
      type: String,
    },
  });

  return mongoose.model(
    'DoublePrevention',
    DoublePreventionSchema,
    'doublePreventions'
  );
};
