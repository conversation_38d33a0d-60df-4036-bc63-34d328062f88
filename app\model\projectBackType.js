/*
 * @Author: 黄婷婷
 * @Date: 2020-11-06 09:00
 * @LastEditors: 黄婷婷
 * @LastEditTime: 2020-11-06 09:00
 * @Description: 项目回执方式
 *
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const projectBackTypeSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    backType: String, // 回执方式
    postType: Array, // 消息发送方式
    superUserID: String, // 监管账号id
  });

  return mongoose.model('ProjectBackType', projectBackTypeSchema, 'projectBackType');

};
