
/**
 * 体检端-体检端资质资质表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const upload_http_path = app.config.upload_http_path || '';

  const PhysicalExamDetectionMechanismSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    org_id: {
      type: String,
      ref: 'PhysicalExamOrg',
      require: true,
    },
    mechanism_name: { // 证书名称
      type: String,
      default: '',
    },
    files: [{ // 证书文件原件
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileName: String,
      url: {
        type: String,
        set(val) {
          return (val && val.indexOf(upload_http_path) !== -1) ? val.split(upload_http_path)[1] : val;
        },
      },
    }],
    NO: String, // 证书编号
    lineOfBusiness: [ String ], // 备案类别 `
    harmFactors: [ String ], // 危害因素
    diagnosis: [ // 诊断项目
      // category: { // 类别名称
      //   type: String,
      //   trim: true,
      // },
      // children: [ String ], // 职业病
    ],
    filingTime: { // 备案证书时间
      type: Date,
    },
    validTime: { // 证书有效期至
      type: Date,
    },
    type: { // 证书类别
      type: String,
      enum: [
        '0', // 备案证书
        '1', // 诊断证书
      ],
      default: '0',
    },
    ctime: { // 创建/更改时间
      type: Date,
      default: Date.now,
    },
    status: { // 存续/启用状态
      type: Boolean,
      default: true,
    },
  });

  return mongoose.model('PhysicalExamDetectionMechanism', PhysicalExamDetectionMechanismSchema, 'physicalExamDetectionMechanism');

};
