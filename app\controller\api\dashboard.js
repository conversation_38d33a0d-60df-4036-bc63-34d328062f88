
// 监管大屏的统计数据
const Controller = require('egg').Controller;
// 1.	申报率、体检率、检测率、培训率
// 2.	企业数量（家）、危害严重企业（家）、检测数量（家）、超标数量（个）、申报数量（家）、体检数量（家）；
// 3.	注册企业（数量）、申报情况（% 保留小数点后两位数）、体检情况（%）、检测情况（%）、培训情况（%）、预警（数量）
// 4.	疑似职业病（人数）、累计职业病（人数）、禁忌症（人数）
// 5.	检测机构：机构名称、项目数量（个）、已申报（个）、已完成（个）、已更新（个）
// 6.	体检机构：机构名称、所在区域（省市）、项目数量

class DashboardController extends Controller {
  async jgStatistics(ctx) {
    let adcode = ctx.query.areaCode || ctx.query.code;
    if (!adcode) { // 没有传adcode，就是默认是全国
      adcode = '100000';
    } else if (adcode.length !== 12) {
      ctx.helper.renderCustom(ctx, {
        message: 'adcode必须是12位的字符串。',
      }, 400);
      return;
    }
    const adname = await ctx.service.dashboard.getAdCodeName(adcode);
    if (!adname) {
      ctx.helper.renderCustom(ctx, {
        message: '未查询到相关行政区域。',
      }, 404);
      return;
    }
    const version = 'V' + ctx.helper.getAPIVersion(ctx.header.accept, 'application/jg.zyws.v', '+json');
    // console.log(111111111111, ctx.header.accept, version);
    if (!ctx.service.dashboard['EnterpriseList' + version]) {
      ctx.helper.renderCustom(ctx, {
        message: '版本错误。',
      }, 403);
      return;
    }
    const statistics = await ctx.service.dashboard['EnterpriseList' + version](adcode, adname);
    // 获取首页预警数据
    const warningCount = await ctx.service.dashboard['countWarningData' + version](adname);

    ctx.helper.renderCustom(ctx, {
      message: '数据获取成功',
      data: {
        curArea: {
          adcode,
          adname,
        },
        statistics,
        warningCount, // 预警数量
        operation: {
          reportedRate: (statistics.reportJobHealth / statistics.jobHealth).toFixed(4), // 申报率
          physicalExamRate: (statistics.healthCheck / statistics.totle).toFixed(4), // 体检率
          detectionRate: (statistics.jobHealth / statistics.totle).toFixed(4), // 检测率
          trainingRate: (statistics.trainingCount / statistics.totle).toFixed(4), // 培训率
        },
      },
    });
  }


}

module.exports = DashboardController;
