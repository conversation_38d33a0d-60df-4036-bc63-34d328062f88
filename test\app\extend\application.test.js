const { app, mock, assert } = require('egg-mock/bootstrap');
const pluginConfig = {
    adminApi: [{
      url: 'admin/index',
      method: 'get',
      controllerName: 'index',
      details: '获取日志列表',
    },
    {
      url: 'admin/removes',
      method: 'get',
      controllerName: 'removes',
      details: '删除日志列表',
    }],
    fontApi: [{
      url: 'admin/index',
      method: 'get',
      controllerName: 'index',
      details: '获取日志列表',
    },
    {
      url: 'admin/removes',
      method: 'get',
      controllerName: 'removes',
      details: '发送验证码',
      authToken: true, // 此属性用来表示是否需要前台登录后才能使用，具体使用在app
    }],
  },
  pluginManageController = {
    async index() {
      // ctx.helper.renderSuccess(ctx, { message: '来自测试admin模块的数据' });
    },
  },
  pluginApiController = {
    async index() {
      // ctx.helper.renderSuccess(ctx, { data: res, message: '来自测试admin模块Api的数据' });
    },
    async removes() {
      // console.log(removes);
    },
  };
before(() => {
  // 等待 app 启动成功，才能执行测试用例
  return app.ready();
});
afterEach(mock.restore);

describe('test/extend/application.test.js', () => {

  it('app.getExtendApiList', () => {
    assert(app.getExtendApiList());
  });

  it('app.initPluginRouter', () => {
    const ctx = app.mockContext();
    app.initPluginRouter(ctx);
    ctx.request.method = 'GET';
    ctx.request.url = '/manage/admin/index';
    app.initPluginRouter(ctx, pluginConfig, pluginManageController);
    ctx.request.url = '/api/admin/index';
    app.initPluginRouter(ctx, pluginConfig, pluginManageController, pluginApiController);
    ctx.request.url = '/api/admin/removes';
    ctx.helper.renderFail = () => {
      // 模拟renderFail方法
    };
    app.initPluginRouter(ctx, pluginConfig, pluginManageController, pluginApiController);
    ctx.session.user = 'Hgfvbv';
    app.initPluginRouter(ctx, pluginConfig, pluginManageController, pluginApiController);
  });
});
