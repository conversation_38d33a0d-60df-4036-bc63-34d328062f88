module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const occupationHealthCheckListReviewSchema = new Schema(
    {
      _id: {
        type: String,
        default: shortid.generate,
      },
      // 审核人
      reviewerId: {
        type: String,
        default: '',
      },
      // 审核人公司
      reviewerCompany: {
        type: String,
        default: '',
      },
      // 审核时间
      reviewTime: {
        type: Date,
        default: Date.now,
      },
      // 审核结果 拒绝，同意，待审核
      reviewResult: {
        type: Number,
        default: 2, // 0拒绝 1同意 2待审核
      },
      // 审核附件
      reviewAttachment: [
        {
          _id: {
            type: String,
            default: shortid.generate,
          },
          originName: {
            type: String,
          },
          staticName: {
            type: String,
          },
        },
      ],
      // 审核意见
      reviewOpinion: {
        type: String,
        default: '',
      },
      // 审核的公司
      EnterpriseID: {
        type: String,
      },
      // 审核的清单的年份
      year: {
        type: String,
        default: '',
      },
      // 审核的清单的id
      occupationHealthCheckListId: {
        type: Array,
        ref: 'OccupationalHealthCheckList',
      },
    },
    { timestamps: true }
  );
  return mongoose.model('occupationHealthCheckListReview', occupationHealthCheckListReviewSchema);
};
