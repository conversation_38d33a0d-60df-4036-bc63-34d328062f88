const Service = require('egg').Service;
// 课程及视频  xxn create
class CourseService extends Service {

  // 获取一个课程详情
  async getOneCourseDetail(_id, updateView = false) {
    const { ctx } = this;
    const course = await this.getCourseOne(_id);
    course.sort.sort((a, b) => a.sequence - b.sequence);

    const contentPromises = course.sort.map(async sortItem => {
      const element = course[sortItem.contentType].find(item => item._id === sortItem.ID);

      if (sortItem.contentType === 'videoInfos') {
        const Video = await ctx.helper.request_alivod('GetVideoInfo', {
          VideoId: element.VideoId,
        }, {});

        if (!Video.Video) return null;

        const response = await ctx.helper.request_alivod('GetPlayInfo', {
          VideoId: element.VideoId,
          Formats: 'mp4',
        }, {});

        return {
          author: element.author,
          times: element.times,
          classHours: element.classHours,
          duration: response.PlayInfoList.PlayInfo[0].Duration || element.duration,
          videoSrc: response.PlayInfoList.PlayInfo[0].PlayURL,
          videoSize: response.PlayInfoList.PlayInfo[0].Size,
          contentType: sortItem.contentType,
          _id: sortItem._id,
          ID: sortItem.ID,
          VideoId: element.VideoId,
          name: Video.Video.Title,
          cover: Video.Video.CoverURL,
          Description: Video.Video.Description,
          date: element.date,
        };
      } else if (sortItem.contentType === 'documents') {
        return {
          author: element.author,
          times: element.times,
          classHours: element.classHours,
          contentType: sortItem.contentType,
          _id: sortItem._id,
          ID: sortItem.ID,
          htmlContent: element.htmlContent,
          name: element.name,
          cover: element.cover,
          Description: element.Description,
          date: element.date,
        };
      }
      return null;
    });

    const contentList = (await Promise.all(contentPromises)).filter(Boolean);
    const newCourse = JSON.parse(JSON.stringify(course));
    newCourse.contentList = contentList;
    // 更新视频观看数
    if (updateView) {
      ctx.model.Courses.updateOne({ _id }, { $inc: { views: 1 } }).then();
    }
    return newCourse;
  }
  async getOneCourseDetail_old(_id, updateView = false) {
    const { ctx } = this;
    const course = await this.getCourseOne(_id);
    const contentList = [];
    course.sort.sort(function(a, b) {
      return a.sequence - b.sequence;
    });
    // console.log(course)
    for (let index = 0; index < course.sort.length; index++) {
      const element = course[course.sort[index].contentType].find(function(item) {
        return item._id === course.sort[index].ID;
      });
      let Video;
      switch (course.sort[index].contentType) {
        case 'videoInfos':
          Video = await ctx.helper.request_alivod('GetVideoInfo', {
            VideoId: element.VideoId,
          }, {});
          if (Video.Video) {
            // 拿视频
            const response = await this.ctx.helper.request_alivod('GetPlayInfo', {
              VideoId: element.VideoId,
              Formats: 'mp4',
            }, {});
            contentList.push({
              author: element.author,
              times: element.times,
              classHours: element.classHours,
              // duration: element.duration,
              duration: response.PlayInfoList.PlayInfo[0].Duration || element.duration,
              videoSrc: response.PlayInfoList.PlayInfo[0].PlayURL,
              videoSize: response.PlayInfoList.PlayInfo[0].Size,
              contentType: course.sort[index].contentType,
              _id: course.sort[index]._id,
              ID: course.sort[index].ID,
              VideoId: element.VideoId,
              name: Video.Video.Title,
              cover: Video.Video.CoverURL,
              Description: Video.Video.Description,
              date: element.date, // 创建时间
            });
          }
          break;
        case 'documents':
          contentList.push({
            author: element.author,
            times: element.times,
            classHours: element.classHours,
            contentType: course.sort[index].contentType,
            _id: course.sort[index]._id,
            ID: course.sort[index].ID,
            htmlContent: element.htmlContent,
            name: element.name,
            cover: element.cover,
            Description: element.Description,
            date: element.date,
          });
          break;
        default:
          break;
      }
    }
    const newCourse = JSON.parse(JSON.stringify(course));
    newCourse.contentList = contentList;
    // 更新视频观看数
    if (updateView) {
      ctx.model.Courses.updateOne({ _id }, { $inc: { views: 1 } }).then();
    }
    return newCourse;
  }

  async getCourseOne(_id) {
    const course = await this.ctx.model.Courses.findOne({
      _id,
    }).populate([
      {
        path: 'authorID',
        select: 'name _id',
      },
      {
        path: 'videoInfos',
        select: 'VideoId Size times author date classHours duration',
      },
      {
        path: 'documents',
      }]);
    return course;
  }

  async createCourse(newData) {
    const backData = await this.ctx.model.Courses.create(newData);
    return backData;
  }

  async updateCourse(id, newData) {
    const backData = await this.ctx.model.Courses.updateOne({
      _id: id,
    }, newData);
    return backData;
  }
  // 内容排序
  async resortContent(_id, id1, id2) {
    const course = await this.ctx.model.Courses.findOne({
      _id,
    }, {
      sort: 1,
    });
    console.log(course);
    let index1,
      index2;
    for (let index = 0; index < course.sort.length; index++) {
      if (course.sort[index]._id === id1) index1 = index;
      if (course.sort[index]._id === id2) index2 = index;
    }
    const temp = course.sort[index1].sequence;
    course.sort[index1].sequence = course.sort[index2].sequence;
    course.sort[index2].sequence = temp;
    const back = await this.ctx.model.Courses.updateOne({
      _id,
    }, {
      $set: {
        sort: course.sort,
      },
    });
    return back;
  }
  // 添加视频/内容
  async insertContent(_id, contentType, project) {
    let contentID = '';
    if (contentType === 'videoInfos') {
      let response = {};
      try {
        response = await this.ctx.helper.request_alivod('GetPlayInfo', {
          VideoId: project.VideoId,
          Formats: 'mp4',
        }, {});
      } catch (error) {
        this.ctx.logger.error(error);
      }
      project.duration = response.PlayInfoList.PlayInfo[0].Duration || 0;
      const newVideoInfos = await this.ctx.model.VideoInfos.create(project);
      contentID = newVideoInfos._id;
    } else if (contentType === 'documents') {
      const newTrainingDocument = await this.ctx.model.TrainingDocument.create(project);
      contentID = newTrainingDocument._id;
    }
    await this.ctx.model.Courses.updateOne({
      _id,
    }, {
      $push: {
        [contentType]: contentID,
      },
      $inc: {
        classHours: Number(project.classHours),
      },
    });
    const course = await this.ctx.model.Courses.findOne({
      _id,
    }, {
      sort: 1,
    });
    this.ctx.auditLog('追加章节', `${contentID} ** ${_id} ** ${contentID} ** ${JSON.stringify(course)}`, 'info');
    const backData = await this.ctx.model.Courses.updateOne({
      _id,
    }, {
      $push: {
        sort: {
          contentType,
          ID: contentID,
          sequence: course.sort.length + 1,
        },
      },
    });
    return backData;
  }

  async count(query) {
    return await this.ctx.model.Courses.count(query);
  }


}

module.exports = CourseService;
