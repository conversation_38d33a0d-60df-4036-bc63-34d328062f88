// 投诉举报
// 一个举报就是一条数据， 但是用户上传的文件是按照userId放在一起的
module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const complaintsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    userId: {
      type: String,
      ref: 'User',
      required: true,
    },
    userName: { // 用户名
      type: String,
      default: '',
    },
    realName: { // 是否为实名举报
      type: Boolean,
      default: true,
    },
    companyId: {
      type: String,
      ref: 'Adminorg',
      required: true,
    },
    companyName: {
      type: String,
      required: true,
    },
    supervisionId: { // 监管单位ID
      type: String,
      ref: 'SuperUser',
      required: true,
    },
    replyMsg: String, // 监管端回复的结果
    status: {
      type: Number,
      default: 0, // 0代表已提交,未受理 1已受理 2待补充资料 3已办结 4已撤销
      enum: [ 0, 1, 2, 3, 4 ],
    },
    addStatus: { // 补充资料的状态
      type: Number,
      default: 0, // 0代表不需要补充资料  1待补充 2已补充
      enum: [ 0, 1, 2 ],
    },
    time: { // 申请时间
      type: Date,
      default: Date.now,
      index: true,
    },
    scoring: { // 评价
      starNum: { // 星级
        type: Number,
        enum: [ 1, 2, 3, 4, 5 ],
      },
      text: String, // 文字描述
    },
    comments: [{ // 留言/对话
      role: { // 角色
        type: Number,
        default: 1, // 0代表监管  1代表用户
        enum: [ 0, 1 ],
      },
      time: {
        type: Date,
        default: Date.now,
        set() {
          return Date.now();
        },
      },
      files: [{ // 上传的文件
        type: String,
      }],
      msg: String,
    }],

  }, { timestamps: true });
  return mongoose.model('Complaints', complaintsSchema, 'complaints');
};
