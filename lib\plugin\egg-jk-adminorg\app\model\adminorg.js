module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;
  const moment = require('moment');

  const workAddressSchema = new Schema({
    districts: { // 区域
      type: Array,
    },
    address: String, // 详细地址
    _id: {
      type: String,
      default: shortid.generate,
    },
    point: Array, // 经纬度
  });

  const AdminorgSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    unitCode: {
      type: String,
    }, // 单位编码
    nameUsedBefore: [ // 曾用名 记录了企业的所有名称以及生效日期，包括当前正在使用的企业名称信息
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        name: String, // 公司名称
        note: { // 备注
          type: String,
          enum: [ '报告导入', '修改用人单位名称' ],
        },
        sourceModel: {
          type: String,
          default: 'ApiUser',
        }, // 修改来源model 注意首字母大写
        orgId: String, // 各来源id 比如如果是机构端 那就是该机构的id
        entryIntoForceAt: {
          type: Date,
          default: Date.now,
        }, // 生效日期
        createdAt: {
          type: Date,
          default: Date.now,
        }, // 创建日期
      },
    ],
    harmStatistics: [ // 工作场所接害人数统计
      {
        _id: String,
        name: String, // 车间/厂房名称/总计
        count: [{
          _id: String,
          label: String, // 总人数/粉尘/化学。。。。
          value: Number, // 接害数量
          harmFactors: [{ // 具体的危害因素接害情况
            _id: String,
            label: String, // 危害因素名称
            employee: Array, // 危害因素接害员工
            value: Number, // 接害人数
          }],
          employee: [{ type: String, ref: 'Employees' }], // 接害员工id
        }],
        sort: String, // 车间/厂房/总计 all/item //总计一般我会放在数组的最后一个元素
      },
    ],
    reportTime: {
      type: Date,
    }, // 最新一次的检测时间
    checkResult: { // 检测结果（检测结果/现状评价）
      all: { // 总数统计
        exceed: String, // 超标点数
        point: String, // 点数
      },
      dust: { // 粉尘
        name: { type: String, default: '粉尘' },
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{ // 不符合/超标检测
            _id: String,
            checkItemId: String, // id
            station: String,
            workspace: String }],
        }],
        point: String,
        exceed: String,
        harmFactors: [{
          name: String, // 危害因素名称
          point: String, // 检测点数
          exceed: String, // 超标点数
        }],
      },
      heat: { // 高温
        name: { type: String, default: '高温' },
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{ // 不符合/超标检测
            _id: String,
            checkItemId: String, // id
            station: String,
            workspace: String }],
        }],
      },
      chemical: { // 化学
        harmFactors: [{
          name: String, // 危害因素名称
          point: String, // 检测点数
          exceed: String, // 超标点数
        }],
        name: { type: String, default: '化学' },
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{ // 不符合/超标检测
            _id: String,
            checkItemId: String, // id
            station: String,
            workspace: String }],
        }],
      },
      radiation: { // 辐射
        name: { type: String, default: '辐射' },
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{
            _id: String,
            checkItemId: String, // id
            checkAddress: String }],
        }],
      },
      biological: { // 生物
        name: { type: String, default: '生物' },
        harmFactors: [{
          name: String, // 危害因素名称
          point: String, // 检测点数
          exceed: String, // 超标点数
        }],
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{
            _id: String,
            checkItemId: String, // id
            checkAddress: String }],
        }],
      },
      noise: { // 噪声
        name: { type: String, default: '噪声' },
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{
            _id: String,
            checkItemId: String, // id
            station: String,
            workspace: String }],
        }],
      },
      powerFrequencyElectric: { // 工频电场
        name: { type: String, default: '工频电场' },
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{
            _id: String,
            checkItemId: String, // id
            station: String,
            workspace: String }],
        }],
      },
      ultraHighRadiation: { // 超高频辐射
        name: { type: String, default: '超高频辐射' },
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{
            _id: String,
            checkItemId: String, // id
            station: String,
            workspace: String }],
        }],
      },
      handBorneVibration: { // 手传振动
        name: { type: String, default: '手传振动' },
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{
            _id: String,
            checkItemId: String, // id
            checkAddress: String }],
        }],
      },
      highFrequencyEle: { // 高频电磁场
        name: { type: String, default: '高频电磁场' },
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{
            _id: String,
            checkItemId: String, // id
            checkAddress: String }],
        }],
      },
      laser: { // 激光辐射
        name: { type: String, default: '激光辐射' },
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{
            _id: String,
            checkItemId: String, // id
            checkAddress: String }],
        }],
      },
      microwave: { // 微波辐射
        name: { type: String, default: '微波辐射' },
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{
            _id: String,
            checkItemId: String, // id
            checkAddress: String }],
        }],
      },
      ultraviolet: { // 紫外线辐射
        name: { type: String, default: '紫外线辐射' },
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{
            _id: String,
            checkItemId: String, // id
            checkAddress: String }],
        }],
      },
      ionizatioRadial: { // 电离辐射-射线装置
        name: { type: String, default: '电离辐射-射线装置' },
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{
            _id: String,
            checkItemId: String, // id
            station: String,
            workspace: String }],
        }],
      },
      ionizatioSource: { // 电离辐射-含源装置
        name: { type: String, default: '电离辐射-含源装置' },
        point: String,
        exceed: String,
        jobHealth: [{
          _id: String,
          checkTime: Date, // 检测日期
          jobHealthId: String,
          checkResult: [{
            _id: String,
            checkItemId: String, // id
            station: String,
            workspace: String }],
        }],
      },
    },
    dingInfo: {
      AppKey: String,
      AppSecret: String,
      CorpId: String,
      aesKey: String,
      token: String,
    }, // 钉钉应用相关信息
    charge: String, // 企业（职业卫生）负责人 adminorg / role 的 name或者id
    orgManagers: String, // 企业（职业卫生）管理人员 adminorg / role 的 name或者id
    serviceID: [{
      _id: String, // 机构id
      EnterpriseContractName: String, // 机构的企业联系人
      EnterprisePhoneNum: String, // 机构的企业联系电话
      ServiceContractName: String, // 企业的机构联系人
      ServicePhoneNum: String, // 企业的机构联系电话
      mainContract: { // 是否是机构的企业主要联系人
        type: Boolean,
        default: false,
      },
      describe: String, // 联系人描述

    }], // 机构id
    physicalExaminationOrgID: [{ // 体检/诊断机构
      _id: { type: String, ref: 'PhysicalExamOrg' }, // 机构id
      EnterpriseContractName: String, // 机构的企业联系人
      EnterprisePhoneNum: String, // 机构的企业联系电话
      ServiceContractName: String, // 企业的机构联系人
      ServicePhoneNum: String, // 企业的机构联系电话
    }],
    createTime: {
      type: Date,
      default: Date.now,
    },
    updateTime: {
      type: Date,
      default: Date.now,
    },
    cname: String, // 单位名称
    shortName: String, // 单位简称
    code: String, // 统一社会信用代码
    regAdd: String, // 注册地址
    // workAdd: String, // 作业场所地址
    corp: String, // 法人
    industryCategory: [{
      type: Array,
    }], // 行业分类
    licensePic: String, // 营业执照（盖章）
    officialSeal: String, // 公章
    exposeRiskLevel: {
      type: Number,
      // default: 2,
    }, // 企业职业病暴露风险等级 0为低风险，1为中风险，2为高风险
    assessmentResult: {
      type: Number,
      // default: 0,
    }, // 企业职业病危害等级分类 1为丙类，2为乙类，3为甲类
    assessManageLevel: Number,
    contract: String, // 联系人
    phoneNum: String, // 联系方式
    projectID: [{ type: String, ref: 'JobHealth' }], // 机构项目ID
    companyScale: {
      type: String,
    }, // 单位规模 (大、中、小、微)
    companyCategory: { // 企业类型 集团 分公司 独立公司
      type: String,
      enum: [ 'group', 'branch', 'subsidiary' ],
      default: 'subsidiary',
    },
    regType: {
      type: String,
      default: '',
    }, // 注册类型
    introduce: { type: String }, // 企业介绍
    money: { type: String }, // 企业总投资
    area: { type: String }, // 占地面积
    type: { type: String }, // 企业类型
    level: { type: String }, // 职业病风险等级
    img: { type: Object }, // 总平面图
    districtRegAdd: [ String ], // 注册地址 [ 省，市，区 ]
    // districtWorkAdd: { type: Object },
    workAddress: [ workAddressSchema ],
    // 银行
    accountsBank: { // 开户行
      type: String,
      default: '',
    },
    accountsAddress: { // 开户地址
      type: String,
      default: '',
    },
    accountsNumber: { // 银行账户
      type: String,
      default: '',
    },
    accountsPhoneNum: { // 开户电话
      type: String,
      default: '',
    },
    PID: {
      type: String,
      default: 'root',
    }, // 父节点
    adminUserId: { // 初始化管理员ID
      type: String,
      ref: 'AdminUser',
    },
    adminArray: { // 组织架构中管理人员ID
      type: Object,
      default: [],
    },
    isactive: { // 申请状态
      type: String, // 0 未审核，0.5.暂不审核（为了排序/(ㄒoㄒ)/~~）， 1 审核通过，2 申请打回，3.未注册，4 体验账号
      default: '0',
      enum: [ '0', '0.5', '1', '2', '3', '4' ],
    },
    experienceToDate: Date, // 体验到期时间
    message: String,
    bookStatus: [{
      sname: { type: String, ref: 'ServiceOrg' }, // 0 未预约， 1 预约
      status: { type: String, default: '0' },
    }],
    others: {
      point: { type: String, default: '0' },
      exceed: { type: String, default: '0' },
    },
    healcheckInfo: {
      actuallNum: String, // 实检人数
      recheck: String, // 复查
      suspected: String, // 疑似
      forbid: String, // 禁忌证
      occupational: String, // 职业病
      recentDay: Date, // 体检时间
    }, // 健康监护汇总
    onlineDeclarationFiles: {
      monthD: Date, // 申报日期
    }, // 危害申报
    completeList: { // 档案完成状态
      type: Object,
      default: {},
    },
    recordList: {
      type: Object,
      default: {},
    }, // 档案完成列表
    riskSortTime: {
      type: String,
    },
    riskSortLevel: { // 企业职业病危害等级分类
      type: Number,
      default: 1, // 1为丙类，2为乙类，3为甲类
    },
    leadIn: {
      type: String, // 0 自己注册， 1 行政导入和审核的，2 检测机构导入，3 体检机构导入，4 oapi导入 5 运营创建 6 全流程
      default: '4',
    },
    punish: {
      punishReason: {
        type: String,
      }, // 处罚原因
      punishDate: Date, // 处罚时间
    },
    isDelete: { // 企业是否处于被删除的状态，true为被删除状态，false为没有被删除
      type: Boolean,
      default: false,
    },
    productionStatus: { // 企业生产状态
      type: String, // 0 注销， 1 暂时停产，2 正常生产
      default: '2',
    },
    totalCompletion: { // 总完成度(档案完成度)
      type: Number,
      default: 0,
    },
    activeUser: { // 审核人
      type: String,
      ref: 'OperateUser',
    },
    // zlc加 用于企业集团 2021.7.12
    parentId: { // 上级单位ID组
      type: Array,
      default: [],
    },
    childrenId: { // 下级单位ID组
      type: Array,
      default: [],
    },
    // zlc加 套餐校验 2021.12.18
    payInfoID: { // 支付信息ID
      type: String,
      ref: 'PayInfo',
    },
    payCategoryID: { // 套餐ID
      type: String,
      // ref: 'PayCategory',
      // default: app.config.defaultPayCategoryID,
    },
    payCategoryPower: [
      {
        type: [ String ],
        required: true,
        ref: 'AdminResource',
      },
    ],
    personCount: {
      // 套餐人数
      type: Number,
    },
    effectiveDate: {
      // 套餐有效期
      type: Date,
      required: true,
      default: new Date('2200-12-31'),
    },
    isVIP: { // 会员类型：战略客户、vip客户、普通客户
      type: String, // svip、vip、ordinary
      default: 'ordinary',
    },
    lobarIsEdit: { // 员工是否可以在劳动者端编辑个人职业史
      type: Boolean,
      default: false,
    },
  });

  AdminorgSchema.index({ 'workAddress._id': 1 });
  AdminorgSchema.index({ 'workAddress.districts': 1 });
  AdminorgSchema.index({ reportTime: -1 });
  AdminorgSchema.index({ industryCategory: 1 });
  AdminorgSchema.index({ exposeRiskLevel: 1 });
  AdminorgSchema.index({ assessmentResult: 1 });
  AdminorgSchema.index({ companyScale: 1 });
  AdminorgSchema.index({ regType: 1 });
  AdminorgSchema.index({ level: 1 });
  AdminorgSchema.index({ adminUserId: 1 });
  AdminorgSchema.index({ 'adminUserId._id': 1 });
  AdminorgSchema.index({ 'adminUserId.phoneNum': 1 });
  AdminorgSchema.index({ adminArray: 1 });
  AdminorgSchema.index({ leadIn: 1 });
  AdminorgSchema.index({ 'punish.punishReason': 1 });
  AdminorgSchema.index({ isDelete: 1 });
  AdminorgSchema.index({ productionStatus: 1 });
  AdminorgSchema.index({ createTime: -1 });
  AdminorgSchema.index({ cname: 1 });
  AdminorgSchema.index({ regAdd: 1 });
  AdminorgSchema.index({ corp: 1 });
  AdminorgSchema.index({ code: 1 });
  AdminorgSchema.index({ activeUser: 1 });

  AdminorgSchema.set('toJSON', {
    getters: true,
    virtuals: true,
  });
  AdminorgSchema.set('toObject', {
    getters: true,
    virtuals: true,
  });

  AdminorgSchema.path('cname').set(function(v) {
    return v.replace('(', '（').replace(')', '）');
  });
  AdminorgSchema.path('code').set(function(v) {
    return v.toUpperCase();
  });
  AdminorgSchema.path('createTime').get(function(v) {
    return moment(v).format('YYYY-MM-DD HH:mm:ss');
  });
  AdminorgSchema.path('updateTime').get(function(v) {
    return moment(v).format('YYYY-MM-DD HH:mm:ss');
  });

  return mongoose.model('Adminorg', AdminorgSchema, 'adminorgs');
};
