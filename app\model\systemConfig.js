/**
 * Created by Zhanglc on 2022/3/18.
 * 原DoraCMS前台系统配置
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const SystemConfigSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    date: {
      // 创建时间
      type: Date,
      default: Date.now,
    },
    siteName: {
      // 网站名称
      type: String,
      default: '前端开发俱乐部',
    },
    ogTitle: {
      // 标题
      type: String,
      default: '',
    },
    siteDomain: {
      // 网站Domain
      type: String,
      default: 'https://www.html-js.cn',
    },
    siteDiscription: {
      // 网站描述
      type: String,
      default: '前端开发',
    },
    siteKeywords: String, // 关键字
    siteAltKeywords: String, // 标签内的alt关键字
    siteEmailServer: String, // 网站邮箱服务
    siteEmail: String, // 邮箱
    siteEmailPwd: String, // 邮箱密码
    registrationNo: {
      // 网站登记号
      type: String,
      default: '',
    },
    mongodbInstallPath: String, // 数据库安装路径
    databackForderPath: String, // 数据包路径
    showImgCode: {
      type: Boolean,
      default: false,
    }, // 是否显示验证码
    bakDatabyTime: {
      type: Boolean,
      default: false,
    }, // 是否自动备份数据
    bakDataRate: {
      type: String,
      default: '1',
    }, // 数据备份频率
    statisticalCode: {
      type: String,
      default: '',
    }, // 百度统计链接
    selectionQuota: {
      type: Number,
      default: 600,
    }, // 40以上选检额度
    selectionQuota2: {
      type: Number,
      default: 300,
    }, // 40以下选检额度
    answerLink: {
      type: String,
      default: '',
    }, // 答卷链接
    warningConfig: [
      // 预警配置
      {
        level: Number,
        enableUploadFiles: Boolean,
        releaseWarningBySelf: Boolean,
      },
    ],
  });

  return mongoose.model('SystemConfig', SystemConfigSchema, 'systemconfigs');
};
