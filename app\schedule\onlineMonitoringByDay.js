// 处理在线监测统计的问题，每天凌晨跑一次

module.exports = app => {
  return {
    schedule: {
      cron: app.config.backUpTick,
      disable: app.config.disabledOnlineMonitor,
      immediate: false, // 启动服务时就立即执行一次任务
      type: 'worker', // 指定每次只有一个 随机的worker 执行
    },

    // 定时执行的任务
    async task(ctx) {
      // 计算噪声的值的方法
      const dealWithType1 = dataList => {
        const times = dataList.length;
        let integral = 0;
        for (let k = 0; k < times; k++) {
          const curVal = dataList[k].value;
          integral += Math.pow(10, 0.1 * curVal);
        }
        return 10 * (Math.log((1 / times) * integral) / Math.log(10));
      };
      // 计算粉尘的均值的方法
      const dealWithType2 = dataList => {
        const sum = dataList.reduce((a, b) => a.value + b.value, 0);
        return sum / dataList.length;
      };
      // 获取所有已经开启的设备ID
      const allDevices = await ctx.model.Devices.find(
        { connectionStatus: true, status: true },
        { deviceID: 1, minuteData: 1, dayData: 1, monthData: 1, deviceType: 1 }
      );
      // 遍历每一个设备
      const curDate = new Date(),
        endTime = curDate.getTime(), // 当前时间戳
        prevDate = new Date(curDate.setDate(curDate.getDate() - 1)),
        startTime = prevDate.getTime();
        // console.log(1111111, startTime, endTime);
      const deathTime = new Date(curDate.setDate(curDate.getDate() - 7));

      for (let i = 0; i < allDevices.length; i++) {
        // 处理每个设备
        const curDeviceData = allDevices[i];
        const curDeviceType = +curDeviceData.deviceType; // 1是噪声 2是粉尘
        const targetHourData = []; // 最终要更新的小时数据（一天内）

        // 1、筛选出 7天内 的分钟数据
        const targetMinuteData = curDeviceData.minuteData.filter(ele => ele.dateTime >= deathTime);

        // 2、把当前设备一天当中的所有分钟数据拿出来
        const originHourData = targetMinuteData.filter(ele => (ele.value && (ele.dateTime.getTime() < endTime) && (ele.dateTime.getTime() >= startTime)));
        // console.log(555555555, originHourData);

        // 3、转化成小时数据，按照每天24小时分组
        const timesArr = []; // 把一天的时间戳分成24组，分别对应的是24小时
        let difTime = startTime;
        for (let j = 0; j < 24; j++) {
          timesArr.push([ difTime, difTime += 3600000 ]);
          // 4、处理每个小时的数据 （由分钟数据转化计算来的）
          const hourData = originHourData.filter(ele => (ele.dateTime.getTime() < timesArr[j][1] && ele.dateTime.getTime() >= timesArr[j][0]));
          let value = 0; // 没有数据就认为是 0
          if (hourData.length) {
            if (curDeviceType === 1) { // 噪声
              value = dealWithType1(hourData);
            } else { // 粉尘
              value = dealWithType2(hourData);
            }
          }
          targetHourData.push({
            dateTime: new Date((timesArr[j][0] + timesArr[j][1]) / 2),
            value,
          });
        }

        // 5、计算当前设备的 天数据 （根据小时计算来的）
        const targetDayData = {
          dateTime: new Date((startTime + endTime) / 2),
          value: 0,
        };
        const originDayData = targetHourData.filter(ele => ele.value);
        if (originDayData.length) {
          targetDayData.value = curDeviceType === 1 ? dealWithType1(originDayData) : dealWithType2(originDayData);
        }

        // 6、计算当前设备的当月的数据, 需要加上当天的
        const curYear = curDate.getFullYear(),
          curMonth = curDate.getMonth();
        const firstDay = new Date(curYear, curMonth, 1, 0, 0, 0); // 当月第一天
        const originMonthData = curDeviceData.dayData.concat([ targetDayData ]).filter(ele => ((ele.dateTime >= firstDay) && ele.value));

        let curMonthValue = 0;
        if (originMonthData.length) {
          curMonthValue = curDeviceType === 1 ? dealWithType1(originMonthData) : dealWithType2(originMonthData);
        }

        const targetMonthData = [ ...curDeviceData.monthData ];
        const curMonthDateTime = curYear + '-' + curMonth;
        if (targetMonthData.some(ele => ele.dateTime === curMonthDateTime)) {
          // 有当月值 更新
          for (let q = 0; q < targetMonthData.length; q++) {
            if (targetMonthData[q].dateTime === curMonthDateTime) {
              targetMonthData[q].value = curMonthValue;
              break;
            }
          }
        } else {
          // 没有当月值 push
          targetMonthData.push({
            dateTime: curMonthDateTime,
            value: curMonthValue,
          });
        }

        // 7、更新当前设备当天的小时数据、天数据、以及当月的数据
        const deviceID = curDeviceData.deviceID;
        const updateRes = await ctx.model.Devices.updateOne(
          { deviceID },
          {
            $push: {
              hourData: { $each: targetHourData },
              dayData: targetDayData,
            },
            $set: {
              minuteData: targetMinuteData,
              monthData: targetMonthData,
            },
          }
        );

        if (updateRes.nModified) {
          console.log(666668888, curDate);
          // 8、 清空当前设备的每秒数据
          await ctx.model.DevicesData.deleteMany();
        }
      }
    },
  };
};
