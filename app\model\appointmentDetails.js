module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const appointmentDetailsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    employeeId: { // 员工_id
      type: String,
      required: true,
      ref: 'Employee',
    },
    adminUserId: { // 代预约管理员_id
      type: String,
      ref: 'Employee',
    },
    tjPlanId: { // 体检计划_id
      type: String,
      required: true,
      ref: 'tjPlan',
    },
    physicalExaminationOrgId: { // 体检机构_id
      type: String,
      ref: 'physicalExaminationOrg',
    },
    appointName: { // 预约名称
      type: String,
    },
    physicalExaminationOrgName: { // 体检机构名称
      type: String,
    },
    address: { // 体检地点（体检机构详细地址address
      type: String,
    },
    appointDate: { // 预约日期
      type: Date,
      required: true,
    },
    appointTime: { // 预约时间段索引
      type: Number,
    },
    physicalExaminationName: { // 体检套餐名称 男/女+上/下
      type: String,
    },
    // checkItems: { // 检查项目列表
    //     type: Array,
    // },
    checkItems: [{ // 检查项目列表
      _id: {
        type: String,
        default: shortid.generate,
      },
      checkItemId: { // 体检项目_id
        type: String,
        ref: 'checkItems',
      },
      name: { // 体检项目名称
        type: String,
      },
      price: { // 体检项目价格
        type: Number,
      },
      comments: { // 体检项目描述
        type: Array,
      },
      payType: { // 付费类型 0-单位付费 1-自费
        type: String,
        default: '0',
      },
      type: { // 体检项目类型 0-必检 1-自选
        type: String,
        default: '0',
      },
    }],
    totalPrice: { // 总价
      type: Number,
    },
    enterprisePay: { // 单位付费
      type: Number,
    },
    selfPay: { // 自费
      type: Number,
    },
    status: { // 预约状态 0-已预约 1-已体检 2-体检中
      type: String,
      default: '0',
    },
    selectionQuota: { // 选检额度
      type: Number,
    },
    isCheckIn: {
      type: String,
      default: '0',
    }, // 是否签到
  }, { timestamps: true });
  return mongoose.model('appointmentDetails', appointmentDetailsSchema);
};
