module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const auditCheckItemsSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    organizationId: { // 检查医院_id
      type: String,
      required: true,
      ref: 'PhysicalExamOrg',
    },
    checkItemId: { // 检查项目_id
      type: String,
      require: true,
      ref: 'CheckItems',
    },
    newPrice: { // 待审核检查项目优惠价
      type: Number,
    },
    oldPrice: { // 已审核检查项目优惠价
      type: Number,
    },
    status: { // 审核状态 0待审核 1已审核 2已驳回
      type: String,
      default: '0',
    },
    comment: { // 审核意见
      type: String,
    },
  }, { timestamps: true });
  return mongoose.model('auditCheckItems', auditCheckItemsSchema);
};
