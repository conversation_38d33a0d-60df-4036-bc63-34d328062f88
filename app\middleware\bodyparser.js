// xxn add 2024-03-25
const bodyParser = require('koa-bodyparser');
const xmlParser = require('koa-xml-body');

module.exports = () => {
  return async function bodyparser(ctx, next) {
    try {
      if (ctx.is('xml') && !(ctx.originalUrl === '/api/shaanxiReportCard')) {
        await xmlParser()(ctx, next);
      } else {
        await bodyParser({
          jsonLimit: '5mb',
          encoding: 'utf8',
          enableTypes: [ 'json', 'form', 'text', 'multipart' ],
          extendTypes: {
            text: [ 'text/xml', 'application/xml' ],
          },
        })(ctx, next);
      }
    } catch (err) {
      ctx.auditLog('服务器无法理解或不支持客户端发送的数据类型', err, 'error');
      ctx.helper.renderCustom(ctx, {
        status: 415,
        message: '服务器无法理解或不支持客户端发送的数据类型',
      });
      return;
    }
  };
};
