const Service = require('egg').Service;
const _ = require('lodash');
const fs = require('fs');
const path = require('path');
const { tools } = require('@utils');
const awaitWriteStream = require('await-stream-ready').write;
const sendToWormhole = require('stream-wormhole');
const reportValidate = require('../validate/tjreport');
const { siteFunc } = require('../utils');
class HealthCheckPartial extends Service {
  async createNewCompany(newCompany, createOrgId, checkDate) {
    const { ctx } = this;
    const places = await this.getPoint(newCompany.workAddress);
    let info = { workAddName: '' };
    if (places && places.status === '1' && places.geocodes[0]) {
      const addrDetail = places.geocodes[0];
      const streetDetail = addrDetail.street + addrDetail.number;
      info = {
        workAdd: [ addrDetail.adcode.slice(0, 2) + '**********', addrDetail.adcode.slice(0, 4) + '00000000', addrDetail.adcode.slice(0, 6) + '000000', '' ],
        location: addrDetail.location,
        workAddName: addrDetail.province + '/' + addrDetail.city + '/' + addrDetail.district,
        name: streetDetail || addrDetail.formatted_address || '',
      };
    }
    if (!newCompany.code) {
      const firms = await this.ctx.model.Firm.findOne({
        jgmc: {
          $regex: newCompany.regenterpriseName,
        },
      }, 'jgxydm -_id');
      newCompany.code = firms ? firms.jgxydm || '' : '';
    }
    // 曾用名
    const nameUsedBefore = [{
      name: newCompany.enterpriseName,
      entryIntoForceAt: checkDate,
      note: '报告导入',
      sourceModel: 'PhysicalExamOrg',
      orgId: createOrgId,
    }];

    const adminorgParams = {
      nameUsedBefore,
      cname: newCompany.enterpriseName,
      workAddress: [
        {
          districts: info.workAddName.split('/'),
          address: newCompany.workAddress,
          point: info.location ? info.location.split(',') : [],
        },
      ],
      districtRegAdd: info.workAddName.split('/'),
      regAdd: newCompany.workAddress,
      code: newCompany.code,
      leadIn: '4', // 0 自己注册， 1 行政导入，2 机构导入，3 体检机构导入，4 oapi导入
      isactive: '3', // 0 未审核， 1 审核通过，2 申请打回，3.未注册
    };
    // const newAdminorg = await ctx.service.adminorg.create(adminorgParams, { w: 'majority' });
    // const session = await (app.mongoose.startSession());
    // (await session).startTransaction({
    //   readConcern: { level: 'majority' },
    //   writeConcern: { w: 'majority' },
    // });
    // let newAdminorg = {};
    // try {
    // const Adminorg = await ctx.service.adminorg.item(ctx, { query: adminorg_query_params, files: 'workAddress cname' }, { session });
    //   if (!Adminorg) { newAdminorg = await ctx.service.adminorg.create(adminorgParams, { session,w: 'majority' }); }
    //   (await session).commitTransaction();
    // } catch (error) {
    //   (await session).abortTransaction();
    //   ctx.auditLog('错误', `体检项目创建企业异常：${error.stack} 。`, 'error');
    // }
    const newAdminorg = await ctx.service.adminorg.create(adminorgParams, { w: 'majority', wtimeout: 10000 });
    return {
      EnterpriseID: newAdminorg._id,
      workAddress: newAdminorg.workAddress,
    };
  }

  async createPhysicalData(createProject, peopleInfoList, PhysicalExamOrgId, PhysicalExamOrgName, isSubmit) {
    const { ctx, app } = this;
    const createEmployeeIDArray = [];
    let newHelthCheck = {};
    let newHelthCheckID = '';
    try {
      const EnterpriseID = createProject.EnterpriseID;
      const peopleInfoListCount = peopleInfoList.length;
      createProject.organization = PhysicalExamOrgName;
      for (let i = 0; i < peopleInfoListCount; i++) {
        const item = peopleInfoList[i];
        if (item.CwithO === '目前未见异常' || item.CwithO === '') {
          createProject.normal++;
        } else if (item.CwithO === '复查') {
          createProject.re_examination++;
        } else if (item.CwithO === '疑似职业病') {
          createProject.suspected++;
        } else if (item.CwithO === '禁忌证') {
          createProject.forbid++;
        } else if (item.CwithO === '其他疾病或异常') {
          createProject.otherDisease++;
        }
      }
      newHelthCheck = await ctx.service.healthcheck.create(createProject);
      ctx.auditLog('********************0', JSON.stringify(newHelthCheck, null, 2), 'info');
      newHelthCheckID = newHelthCheck._id;
      const newHelthCheckType = newHelthCheck.checkType;

      if (isSubmit) {
        // 预警判断 jhw
        ctx.service.report.updateHealthCheck(newHelthCheckID);
      }

      for (let i = 0; i < peopleInfoListCount; i++) {
        const item = peopleInfoList[i],
          IDNum = item.IDCard || '';
        let persion = {};
        if (IDNum !== '') {
          persion = await ctx.service.employee.item(ctx, { query: {
            IDNum,
            EnterpriseID,
          }, files: '_id' });
        } else {
          persion = await ctx.service.employee.item(ctx, { query: {
            name: item.name,
            age: item.age,
            gender: item.gender,
            EnterpriseID,
          }, files: '_id' });
        }
        if (!persion) {
          persion = await ctx.model.Employee.create({
            IDNum,
            name: item.name,
            gender: item.gender,
            age: isNaN(item.age) ? 0 : item.age,
            workYears: item.workYears,
            EnterpriseID,
            workType: item.workType,
            status: newHelthCheckType === '2' ? 0 : 1,
          });
          const statusChanges = newHelthCheckType === '2' ? [{
            changType: 4,
            EnterpriseID,
          }, {
            changType: 1,
            EnterpriseID,
          }] : [{
            changType: 4,
            EnterpriseID,
          }];
          await ctx.model.EmployeeStatusChange.create({
            employee: persion._id,
            statusChanges,
          });
          createEmployeeIDArray.push(persion._id);
        }
        item.employeeId = persion._id;
        item.EnterpriseID = EnterpriseID;
        item.organization = PhysicalExamOrgName;

        item.checkType = newHelthCheckType;
        item.checkDate = newHelthCheck.checkDate;
        item.batch = newHelthCheckID;
        item.source = 'oapi';

        await ctx.service.suspect.create(item);
      }

      const physicalExamOrg = await ctx.model.PhysicalExamOrg.findOne({ _id: PhysicalExamOrgId });
      const physicalExaminationOrgIDData = {
        _id: PhysicalExamOrgId,
        EnterpriseContractName: createProject.enterpriseContactsPhoneNumber,
        EnterprisePhoneNum: createProject.enterpriseContactsPhonNumber,
        ServiceContractName: physicalExamOrg ? physicalExamOrg.contract : '',
        ServicePhoneNum: physicalExamOrg ? physicalExamOrg.phoneNum : '',
      };
      await ctx.model.Adminorg.updateOne({ _id: EnterpriseID }, { $addToSet: { physicalExaminationOrgID: physicalExaminationOrgIDData } });
      siteFunc.synchronization_adminorg_workaddress(ctx, { _id: EnterpriseID }, createProject.workAddress[0]);
      const phoneNum = createProject.enterpriseContactsPhonNumber;
      /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(phoneNum) && siteFunc.create_adminorg_manage_user(ctx, app, { _id: EnterpriseID, phoneNum, name: createProject.enterpriseContactsName });
      return true;
    } catch (error) {
      ctx.auditLog('错误', `创建体检数据错误：${error.stack} 。`, 'error');
      ctx.service.employee.removes(ctx, createEmployeeIDArray.join(','));
      ctx.model.EmployeeStatusChange.deleteMany({
        employee: {
          $in: createEmployeeIDArray,
        },
      });
      ctx.service.healthcheck.removes(ctx, newHelthCheckID || '');
      ctx.service.suspect.removes(ctx, newHelthCheckID, 'batch');
      return false;
    }
  }

  async FormatTable(array) {
    const groupArray = _.groupBy(array, item => {
      return item.name + ' ++ ' + item.gender + ' ++ ' + item.age;
    });
    array = [];
    _.forEach(groupArray, item => {
      const itemCount = item.length;
      if (itemCount > 1) {
        const ITEM0 = item[0];
        const resetJson = {
          name: ITEM0.name,
          gender: ITEM0.gender,
          age: ITEM0.age,
          workType: ITEM0.workType,
          workYears: ITEM0.workYears,
          harmFactors: ITEM0.harmFactors,
          abnormalIndex: ITEM0.abnormalIndex,
          opinion: ITEM0.opinion,
          dedicalAdvice: ITEM0.dedicalAdvice,
          CwithO: ITEM0.CwithO,
          EnterpriseID: ITEM0.EnterpriseID,
          IDCard: ITEM0.IDCard || '',
        };
        for (let i = 1; i < itemCount; i++) {
          const ELEMENT = item[i];
          try {
            if (resetJson.harmFactors !== ELEMENT.harmFactors && !resetJson.harmFactors.includes(ELEMENT.harmFactors)) {
              resetJson.harmFactors += ` ${ELEMENT.harmFactors}`;
            }
          } catch (error) {
            console.log('harmFactors', error);
          }

          if (resetJson.abnormalIndex !== ELEMENT.abnormalIndex) {
            resetJson.abnormalIndex += ` ${ELEMENT.abnormalIndex}`;
          }
          if (resetJson.opinion !== ELEMENT.opinion) {
            resetJson.opinion += ` ${ELEMENT.opinion}`;
          }
          if (resetJson.dedicalAdvice !== ELEMENT.dedicalAdvice) {
            resetJson.dedicalAdvice += ` ${ELEMENT.dedicalAdvice}`;
          }
          switch (ELEMENT.CwithO) {
            case '疑似职业病':
              resetJson.CwithO = '疑似职业病';
              break;
            case '禁忌证':
              if (resetJson.CwithO !== '疑似职业病') {
                resetJson.CwithO = '禁忌证';
              }
              break;
            case '复查':
              if (
                [ '疑似职业病', '禁忌证' ].indexOf(resetJson.CwithO) === -1
              ) {
                resetJson.CwithO = '复查';
              }
              break;
            case '其他疾病或异常':
              if (
                [ '疑似职业病', '禁忌证', '复查' ].indexOf(
                  resetJson.CwithO
                ) === -1
              ) {
                resetJson.CwithO = '其他疾病或异常';
              }
              break;
            case '目前未见异常':
            default:
              if (
                [ '疑似职业病', '禁忌证', '复查', '其他疾病或异常' ].indexOf(
                  resetJson.CwithO
                ) === -1
              ) {
                resetJson.CwithO = '目前未见异常';
              }
              break;
          }
        }
        array.push(resetJson);
      } else if (itemCount === 1) {
        array.push(item[0]);
      }
    });
    return array;
  }

  async getPoint(address, gmap = this.app.config.gmap) {
    const resBack = await this.ctx.curl(`${gmap.url}?key=${gmap.key}&address=${address}`);
    return JSON.parse(resBack.data.toString());
  }

  async formatPhyReportData(ctx) {
    const config = this.config;
    const newFile = { fileName: '', staticFileName: '' };
    let stream = null,
      savePath = '',
      creditCode = '',
      physicalExamOrgId = '',
      projectNumber = '';
    try {
      const parts = await ctx.multipart({
        autoFields: true,
        limits: {
          fileSize: 1024 * 1024 * 1024, // 限制文件大小为1G
        },
      });
      ctx.auditLog('上传体检报告文件', parts, 'info');
      const fileType = config.phyReportType || [];
      while ((stream = await parts()) != null) {
        const info = JSON.parse(parts.field.info);
        ctx.auditLog('上传体检报告文件开始', info, 'info');
        if (creditCode === '') {
          const {
            info: infoValidate,
          } = reportValidate;
          creditCode = info.creditCode || '';
          const phyinfo = { creditCode };
          const infoValidArr = infoValidate.validate(phyinfo);
          if (infoValidArr.length > 0) {
            const error = [];
            infoValidArr.forEach(i => error.push(i.message));
            return {
              status: 400,
              message: error.join(','),
            };
          }

          const physicalExamOrg = await ctx.service.physicalExamOrg.item(ctx, { query: { organization: creditCode }, files: '_id' });
          if (_.isEmpty(physicalExamOrg)) {
            ctx.auditLog('体检机构无数据', `保存体检报告营业执照为${creditCode}的体检机构未注册！`, 'info');
            return {
              status: 400,
              message: '该体检机构还未注册, 请注册后再使用',
            };
          }
          physicalExamOrgId = physicalExamOrg._id;
          projectNumber = info.projectNumber;
          savePath = path.join(config.upload_phy_report_path, physicalExamOrgId);
          ctx.auditLog('上传体检报告文件路径===', savePath, 'info');
        }
        const sFileName = stream.filename.toString('UTF-8');
        const extname = path.extname(sFileName).toLowerCase();
        if (!sFileName || !fileType.includes(extname)) {
          // 此处可能出现因无文件传入导致的流问题
          await sendToWormhole(stream);
          ctx.auditLog('错误', '上传体检报告无传入文件或发现一个非法后缀文件，已跳过！', 'error');
          return {
            status: 400,
            message: '未传入文件或发现无效后缀文件，已跳过！',
          };
        }
        const filename = Math.random().toString(36).substr(2) + new Date().getTime() + extname.toLocaleLowerCase();
        ctx.auditLog('上传体检报告文件名===', filename, 'info');
        let writeStream = null;
        try {
          tools.makeEnterpriseDir(savePath);
          writeStream = fs.createWriteStream(path.resolve(savePath, filename));
          await awaitWriteStream(stream.pipe(writeStream));
          const fileExists = fs.existsSync(path.resolve(savePath, filename));
          if (!fileExists) {
            return {
              status: 500,
              message: '文件写入失败',
            };
          }
          if (newFile.fileName === '' || newFile.staticFileName === '') {
            newFile.fileName = sFileName;
            newFile.staticFileName = filename;
            ctx.auditLog('上传体检报告文件成功！！newFile===', newFile, 'info');
            break;
          }
        } catch (e) {
          ctx.auditLog('错误', `上传体检报告循环读取数据错误：${e.stack} 。`, 'error');
          // 如果出现错误，关闭管道,防止浏览器响应卡死
          await sendToWormhole(stream);
          writeStream.destroy();
          continue;
        }
      }
    } catch (error) {
      ctx.auditLog('错误', `上传体检报告读取数据错误：${error.stack} 。`, 'error');
      return {
        status: 400,
        message: '参数错误',
      };
    }

    return {
      physicalExamOrgId,
      newFile,
      projectNumber,
    };
  }

  // 温州体检系统对接接收体检项目总报告 xxn
  async uploadPhyReport(ctx) {
    try {
      const stream = await ctx.getFileStream();
      let { info } = stream.fields;
      if (info && typeof info === 'string') {
        info = JSON.parse(info);
      } else {
        throw new Error('info参数错误');
      }
      const { creditCode, projectNumber } = info;
      if (!creditCode || !projectNumber) {
        throw new Error('creditCode或projectNumber参数错误');
      }

      const physicalExamOrg = await ctx.service.physicalExamOrg.item(ctx, { query: { organization: creditCode }, files: '_id' });
      if (!physicalExamOrg) {
        throw new Error('体检机构creditCode找不到');
      }
      const physicalExamOrgId = physicalExamOrg._id;
      const savePath = path.join(this.config.upload_phy_report_path, physicalExamOrgId);

      const extname = path.extname(stream.filename); // 文件后缀
      if (![ '.pdf', '.docx', '.doc' ].includes(extname)) {
        throw new Error('文件格式不正确');
      }

      const staticFileName = Math.random().toString(36).substr(2) + new Date().getTime() + extname.toLocaleLowerCase();
      const target = path.resolve(savePath, staticFileName);
      ctx.auditLog('温州上传体检报告文件完整路径', target, 'info');
      await ctx.helper.pipe({
        readableStream: stream,
        target,
      });
      const fileName = stream.filename.toString('UTF-8'); // 原文件名
      // if (!fs.existsSync(target)) {
      //   throw new Error('文件写入失败 - ' + fileName);
      // }
      return await this.updateTjFile({
        physicalExamOrgId,
        newFile: { fileName, staticFileName },
        projectNumber,
      });
    } catch (err) {
      ctx.auditLog('上传体检报告读取数据错误', err.message, 'error');
      throw new Error(err.message || '上传体检报告失败');
    }
  }
  // 更新体检项目报告文件
  async updateTjFile(params) {
    const { ctx } = this;
    const { physicalExamOrgId, projectNumber, newFile } = params;
    if (!newFile || !physicalExamOrgId || !projectNumber) {
      throw new Error('更新体检报告失败：参数不全');
    }
    const query = { projectNumber, physicalExaminationOrgID: physicalExamOrgId };
    ctx.auditLog('更新体检报告（文件）开始：', query, 'info');
    const res = await ctx.model.Healthcheck.update(query, { $set: { medicalExaminationReport: {
      fileName: newFile.fileName,
      url: `${physicalExamOrgId}/${newFile.staticFileName}`,
      source: 'oapi',
    } } });

    if (!res || res.nModified === 0) {
      throw new Error('更新体检报告失败：项目找不到');
    }
    ctx.auditLog('更新体检报告（文件）完成：', res, 'info');
    return res;
  }

  async formatPhyReportDataOld(ctx) {
    const config = this.config;
    const newFiles = [];
    let stream = null,
      savePath = '',
      creditCode = '',
      physicalExamOrgId = '',
      fileList = [];
    try {
      const parts = await ctx.multipart({ autoFields: true });
      const fileType = config.phyReportType || [];
      while ((stream = await parts()) != null) {
        const info = JSON.parse(parts.field.info);
        if (creditCode === '') {
          const {
            info: infoValidate,
          } = reportValidate;
          creditCode = info.creditCode || '';
          const phyinfo = { creditCode };
          const infoValidArr = infoValidate.validate(phyinfo);
          if (infoValidArr.length > 0) {
            const error = [];
            infoValidArr.forEach(i => error.push(i.message));
            return {
              status: 400,
              message: error.join(','),
            };
          }

          const physicalExamOrg = await ctx.service.physicalExamOrg.item(ctx, { query: { organization: creditCode }, files: '_id' });
          if (_.isEmpty(physicalExamOrg)) {
            ctx.auditLog('体检机构无数据', `保存体检报告营业执照为${creditCode}的体检机构未注册！`, 'info');
            return {
              status: 400,
              message: '该体检机构还未注册, 请注册后再使用',
            };
          }
          physicalExamOrgId = physicalExamOrg._id;
          fileList = info.fileList;
          savePath = path.join(config.upload_phy_report_path, physicalExamOrgId);
        }
        const sFileName = stream.filename.toString('UTF-8');
        const extname = path.extname(sFileName).toLowerCase();
        if (!sFileName || !fileType.includes(extname)) {
          // 此处可能出现因无文件传入导致的流问题
          await sendToWormhole(stream);
          console.log('无传入文件或发现一个非法后缀文件！已跳过！');
        } else {
          const filename = Math.random().toString(36).substr(2) + new Date().getTime() + extname.toLocaleLowerCase();
          let writeStream = null;
          try {
            tools.makeEnterpriseDir(savePath);
            writeStream = fs.createWriteStream(path.resolve(savePath, filename));
            await awaitWriteStream(stream.pipe(writeStream));
            newFiles.push({ fileName: sFileName, staticFileName: filename });
          } catch (e) {
            ctx.auditLog('错误', `上传体检报告循环读取数据错误：${e.stack} 。`, 'error');
            // 如果出现错误，关闭管道,防止浏览器响应卡死
            await sendToWormhole(stream);
            writeStream.destroy();
            continue;
          }
        }
      }
    } catch (error) {
      ctx.auditLog('错误', `上传体检报告读取数据错误：${error.stack} 。`, 'error');
      return {
        status: 400,
        message: '参数错误',
      };
    }

    return {
      physicalExamOrgId,
      newFiles,
      fileList,
    };
  }
}

module.exports = HealthCheckPartial;
