/*
 * @Author: doramart
 * @Date: 2019-08-15 10:51:41
 * @Last Modified by: doramart
 * @Last Modified time: 2019-09-29 17:59:07
 */

const adminorgRule = ctx => {
  return {
    cname: {
      type: 'string',
      required: true,
      min: 1,
      max: 75,
      message: ctx.__('validate_error_field', [ ctx.__('单位名称1到35个字符') ]),
    },
    code: {
      type: 'string',
      required: true,
      message: ctx.__('validate_error_field', [ ctx.__('组织机构代码为必填') ]),
    },
    regAdd: {
      type: 'string',
      required: true,
      message: ctx.__('validate_error_field', [ ctx.__('注册地址为必填') ]),
    },
    workAdd: {
      type: 'string',
      // required: true,
      message: ctx.__('validate_error_field', [ ctx.__('作业场所地址') ]),
    },
    corp: {
      type: 'string',
      required: true,
      message: ctx.__('validate_error_field', [ ctx.__('法人姓名为必填') ]),
    },
    // industryCategory: {
    //   type: 'string',
    //   // required: true,
    //   message: ctx.__('validate_error_field', [ ctx.__('行业分类') ]),
    // },
    licensePic: {
      type: 'string',
      required: true,
      message: ctx.__('validate_error_field', [ ctx.__('营业执照（盖章）') ]),
    },
    PID: {
      type: 'string',
      required: false,
      message: ctx.__('validate_error_field', [ ctx.__('父节点') ]),
    },
    // adminUserId: {
    //   type: 'string',
    //   required: false,
    //   message: ctx.__('validate_error_field', [ ctx.__('初始化管理员ID') ]),
    // },
  };
};
module.exports = {
  adminorgRule,
};

