// 浙江标准 体检危害因素编码表
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const ZjTjHazardSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    code: { // 编码
      type: String,
      unique: true,
    },
    name: String, // 危害因素名称
    activeMonitoring: String,
  });
  return mongoose.model('ZjTjHazard', ZjTjHazardSchema, 'zj_tj_hazard');
};
