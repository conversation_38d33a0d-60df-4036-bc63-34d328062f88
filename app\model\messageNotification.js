/**
 * 站内消息表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;


  const MessageNotificationSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    date: { // 消息产生的时间
      type: Date,
      default: Date.now,
    },
    authorID: { // 发送消息的监管单位ID，目前只有监管端发送给其他端/监管单位
      type: String,
    },
    infoCompany: String, // 给体检预约使用，发送预约人的所属公司
    informer: String, // 发这个通知的人的人名
    authorGroup: {
      type: String,
      default: app.config.groupID.userGroupID,
    },
    title: { // 消息标题
      type: String,
      require: true,
    },
    message: { // 消息主要内容
      type: String,
    },
    type: String, // jhw  通知类型: 培训1 体检2 申报3 检测4 其他5
    sendWay: { // 发送方式、通知方式
      type: String,
      enum: [ 'sms', 'systemMessage', 'both' ], // 手机短信、站内信息、两者都要
      default: 'systemMessage',
    },
    reader: [{ // 这个东西就是对应的用户的短ID、用户名、用户类型。
      readerID: {
        type: String,
        require: true,
      },
      readerGroup: {
        type: String,
        // default: app.config.groupID.userGroupID,未知、，，创建消息的时候必填吧
      }, // 读者的类型ID
      isRead: {
        type: Number,
        default: 0,
        enum: [ 0, 1 ], // 1已读 0未读
      },
      _id: false,
    }],
    files: [ String ], // 上传的文件 `/static${app.config.upload_http_path}/messageNotification/${_id}/文件名`
    state: { // 状态
      type: Number,
      default: 1,
      enum: [ 0, 1 ], // 1正常 0删除
    },
  });
  MessageNotificationSchema.pre('save', function(next) {
    const defaultReaderGroup = app.config.groupID.userGroupID;
    this.reader.forEach(reader => {
      if (!reader.readerGroup) {
        reader.readerGroup = defaultReaderGroup;
      }
    });
    next();
  });
  return mongoose.model('MessageNotification', MessageNotificationSchema, 'messageNotification');
};
