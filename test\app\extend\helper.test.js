const { app, mock, assert } = require('egg-mock/bootstrap');
const Axios = require('axios');

before(() => {
  // 等待 app 启动成功，才能执行测试用例
  return app.ready();
});
afterEach(mock.restore);

describe('test/extend/helper.test.js', () => {

  it('helper.reqJsonData', function* () {
    const ctx = app.mockContext();
    mock(Axios, 'get', () => {
      return { status: 200, data: { status: 200, data: 'Hgfvbv', message: 'success' } };
    });
    const resultAoios = yield ctx.helper.reqJsonData('manage/');
    const resultAoios1 = yield ctx.helper.reqJsonData('');
    mock(Axios, 'post', () => {
      return { status: 200, data: { status: 500, data: 'Hgfvbv', message: '模拟Error' } };
    });
    const resultError = yield ctx.helper.reqJsonData('https://www.hgfvbv.xyz', {}, 'post');
    assert(resultAoios && resultAoios1 && resultError.status === 500);
  });

  it('helper.renderSuccess', () => {
    const ctx = app.mockContext();
    ctx.helper.renderSuccess(ctx);
    assert(ctx.status === 200);
  });

  it('helper.renderFail', () => {
    const ctx = app.mockContext();
    ctx.helper.renderFail(ctx);
    ctx.helper.renderFail(ctx, { message: { message: 'Hgfvbv' } });
    assert(ctx.status === 500);
  });

  // it('helper.getAdminPower', async () => {
  //   const ctx = app.mockContext();
  //   // 必须设置，否则会报_id获取不到的错误
  //   ctx.session.user = { _id: 'Hgfvbv' };
  //   app.mockService('user', 'item', async () => {
  //     return {
  //       group: {
  //         power: null,
  //       },
  //     };
  //   });
  //   const result = await ctx.helper.getAdminPower(ctx);
  //   app.mockService('user', 'item', async () => {
  //     return {
  //       group: {
  //         power: [ 'Hgfvbv' ],
  //       },
  //     };
  //   });
  //   const result1 = await ctx.helper.getAdminPower(ctx);
  //   ctx.session.user = null;
  //   assert(result && result1);
  // });
});
