module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 培训文档
  const TrainingDocumentSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    cover: String, // 封面
    name: {
      type: String,
      default: '',
    },
    Description: {
      type: String,
      default: '',
    },
    htmlContent: String, // 保存的富文本内容
    documentsUrl: {
      type: Array,
      default: [],
    }, // 存储上传的文档地址，方便删除
  });


  return mongoose.model('trainingDocument', TrainingDocumentSchema, 'trainingDocument');
};
