/**
 * 课程管理表
 */

module.exports = app => {
  const mongoose = app.mongoose;
  const shortid = require('shortid');
  const Schema = mongoose.Schema;

  const CoursesSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },

    name: {
      type: String,
    }, // 课程名
    explain: {
      type: String,
      default: '',
    }, // 课程介绍

    authorID: {
      type: String,
      ref: 'OperateUser', // 也有可能是User
    }, // 创建人的ID

    credit: {
      type: Number,
      default: 0,
      set: val => Number(val),
    }, // 学分

    classHours: {
      type: Number,
      default: 0,
      set: val => Number(val),
    }, // 学时

    updateTima: {
      type: Date,
      default: Date.now,
    }, // 更新时间

    openTime: {
      type: Date,
      default: Date.now,
    }, // 发布时间

    date: {
      type: Date,
      default: Date.now,
    }, // 创建时间

    classification: [{
      type: String,
      ref: 'CourseClassification',
    }], // 课程分类

    labels: [{
      type: String,
    }], // 课程标签，玩玩推荐算法？

    allowToOpen: {
      type: Boolean,
      default: false,
    }, // 是否对外开放

    complete: {
      type: Boolean,
      default: false,
    }, // 课程是否创建完成

    cover: {
      type: String,
      default: '',
    }, // 课程封面

    videoInfos: [{
      type: String,
      ref: 'videoInfos',
    }],

    documents: [{
      type: String,
      ref: 'trainingDocument',
    }], // 文档集合

    sort: [{
      _id: {
        type: String,
        default: shortid.generate,
      },
      contentType: {
        type: String,
        default: 'videoInfos',
      }, // 这个内容的类型，视频'videoInfos' 文档'documents'，可以用这个字段当字段名查找内容
      ID: {
        type: String,
      }, // 内容的具体ID，就是上面各种数组元素的_id
      sequence: {
        type: Number,
      }, // rank 顺序，防止错位
    }], // 内容排序，按照一定顺序排列视频、图片、文档、考试。。。

    views: {
      type: Number,
      default: 0,
    }, // 查看数

    likes: {
      type: Number,
      default: 0,
    }, // 点赞数

    allowComment: {
      type: Boolean,
      default: true,
    }, // 允许评论

    commentLength: {
      type: Number,
      default: 0,
    }, // 评论数

    questionBank: {
      type: String,
      ref: 'QuestionBank',
      default: '',
    }, // 相关题库

    price: {
      type: Number,
      default: 0,
      set: val => Number(val),
    }, // 课程价格
    source: { // 数据来源
      type: String,
      default: 'user',
      emun: [ 'user', 'operate', 'org', 'super' ], // user代表专家 operate是运营人员 org代表培训机构/组织
    },
    createRange: [ String ], // 监管端创建范围区域 ['浙江省','杭州市']
    // 有权限使用这个课程的机构/企业/监管单位
    powerStatus: { type: Boolean, default: false }, // 是否已开启权限配置
    superUsers: [{ type: String, ref: 'SuperUser' }],
    serviceOrgs: [{ type: String, ref: 'ServiceOrg' }],
    physicalExamOrgs: [{ type: String, ref: 'PhysicalExamOrg' }],
    adminOrgs: [{ type: String, ref: 'Adminorg' }],

  });

  return mongoose.model('Courses', CoursesSchema, 'Courses');


};
