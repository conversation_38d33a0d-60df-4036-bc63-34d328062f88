
module.exports = app => {
  const shortid = require('shortid');
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;

  // 试题
  const TopicSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    questionBankID: { // 题库ID
      type: 'String',
      ref: 'QuestionBank',
    },
    topicType: Number, // 题目类型 1：单选 2：多选 3：判断 4：填空 5：问答（4，5暂时用不上）
    steam: String, // 题干
    steamPic: [{ // 题干图片
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileType: String,
      name: String,
      staticName: String,
      staticSrc: String,
    }],
    options: [{ // 选项
      _id: {
        type: String,
        default: shortid.generate,
      },
      // optionItem: String, // 选项：可选择A，B，C..以此类推。判断题：A-正确，B-错误。
      optionText: String, // 选项的文字介绍
      optionPic: String, // 选项的图片
    }],
    answer: Array, // 答案。单选：[ 0 ]，多选：[ 0, 1, 2 ] 表示 0 表示 options 里的索引。填空题：[ [春,春天]、[秋,秋天] ]
    answerAnalysis: String, // 答案解析
    answerAnalysisPic: [{ // 答案解析的图片
      _id: {
        type: String,
        default: shortid.generate,
      },
      fileType: String,
      name: String,
      staticName: String,
      staticSrc: String,
    }],
    labels: Array, // 标签
    createAt: { // 创建日期
      type: Date,
    },
    updateAt: { // 更新时间
      type: Date,
    },
    difficultyLevel: String, // 难易程度
    knowledge: String, // 知识点
    knowledgeLevel: String, // 知识点程度
    outline: Array, // 大纲
    topicSource: String, // 题目来源?
    answersNumber: { // 答题次数
      type: Number,
      default: 0,
    },
    correctNumber: { // 答对次数
      type: Number,
      default: 0,
    },
  }, { timestamps: true });


  return mongoose.model('Topic', TopicSchema, 'topic');
};
