/*!
 * vue-i18n v8.14.0 
 * (c) 2019 ka<PERSON><PERSON> ka<PERSON>
 * Released under the MIT License.
 */
var t,e;t=this,e=function(){"use strict";var t=["style","currency","currencyDisplay","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","localeMatcher","formatMatcher"];function e(t,e){"undefined"!=typeof console&&(console.warn("[vue-i18n] "+t),e&&console.warn(e.stack))}function n(t){return null!==t&&"object"==typeof t}var r=Object.prototype.toString,i="[object Object]";function a(t){return r.call(t)===i}function s(t){return null==t}function o(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];var r=null,i=null;return 1===t.length?n(t[0])||Array.isArray(t[0])?i=t[0]:"string"==typeof t[0]&&(r=t[0]):2===t.length&&("string"==typeof t[0]&&(r=t[0]),(n(t[1])||Array.isArray(t[1]))&&(i=t[1])),{locale:r,params:i}}function l(t){return JSON.parse(JSON.stringify(t))}var c=Object.prototype.hasOwnProperty;function u(t,e){return c.call(t,e)}function f(t){for(var e=arguments,r=Object(t),i=1;i<arguments.length;i++){var a=e[i];if(null!=a){var s=void 0;for(s in a)u(a,s)&&(n(a[s])?r[s]=f(r[s],a[s]):r[s]=a[s])}}return r}function h(t,e){if(t===e)return!0;var r=n(t),i=n(e);if(!r||!i)return!r&&!i&&String(t)===String(e);try{var a=Array.isArray(t),s=Array.isArray(e);if(a&&s)return t.length===e.length&&t.every(function(t,n){return h(t,e[n])});if(a||s)return!1;var o=Object.keys(t),l=Object.keys(e);return o.length===l.length&&o.every(function(n){return h(t[n],e[n])})}catch(t){return!1}}var p={beforeCreate:function(){var t=this.$options;if(t.i18n=t.i18n||(t.__i18n?{}:null),t.i18n){if(t.i18n instanceof Q){if(t.__i18n)try{var e={};t.__i18n.forEach(function(t){e=f(e,JSON.parse(t))}),Object.keys(e).forEach(function(n){t.i18n.mergeLocaleMessage(n,e[n])})}catch(t){}this._i18n=t.i18n,this._i18nWatcher=this._i18n.watchI18nData()}else if(a(t.i18n)){if(this.$root&&this.$root.$i18n&&this.$root.$i18n instanceof Q&&(t.i18n.root=this.$root,t.i18n.formatter=this.$root.$i18n.formatter,t.i18n.fallbackLocale=this.$root.$i18n.fallbackLocale,t.i18n.formatFallbackMessages=this.$root.$i18n.formatFallbackMessages,t.i18n.silentTranslationWarn=this.$root.$i18n.silentTranslationWarn,t.i18n.silentFallbackWarn=this.$root.$i18n.silentFallbackWarn,t.i18n.pluralizationRules=this.$root.$i18n.pluralizationRules,t.i18n.preserveDirectiveContent=this.$root.$i18n.preserveDirectiveContent),t.__i18n)try{var n={};t.__i18n.forEach(function(t){n=f(n,JSON.parse(t))}),t.i18n.messages=n}catch(t){}var r=t.i18n.sharedMessages;r&&a(r)&&(t.i18n.messages=f(t.i18n.messages,r)),this._i18n=new Q(t.i18n),this._i18nWatcher=this._i18n.watchI18nData(),(void 0===t.i18n.sync||t.i18n.sync)&&(this._localeWatcher=this.$i18n.watchLocale())}}else this.$root&&this.$root.$i18n&&this.$root.$i18n instanceof Q?this._i18n=this.$root.$i18n:t.parent&&t.parent.$i18n&&t.parent.$i18n instanceof Q&&(this._i18n=t.parent.$i18n)},beforeMount:function(){var t=this.$options;t.i18n=t.i18n||(t.__i18n?{}:null),t.i18n?t.i18n instanceof Q?(this._i18n.subscribeDataChanging(this),this._subscribing=!0):a(t.i18n)&&(this._i18n.subscribeDataChanging(this),this._subscribing=!0):this.$root&&this.$root.$i18n&&this.$root.$i18n instanceof Q?(this._i18n.subscribeDataChanging(this),this._subscribing=!0):t.parent&&t.parent.$i18n&&t.parent.$i18n instanceof Q&&(this._i18n.subscribeDataChanging(this),this._subscribing=!0)},beforeDestroy:function(){if(this._i18n){var t=this;this.$nextTick(function(){t._subscribing&&(t._i18n.unsubscribeDataChanging(t),delete t._subscribing),t._i18nWatcher&&(t._i18nWatcher(),t._i18n.destroyVM(),delete t._i18nWatcher),t._localeWatcher&&(t._localeWatcher(),delete t._localeWatcher),t._i18n=null})}}},m={name:"i18n",functional:!0,props:{tag:{type:String},path:{type:String,required:!0},locale:{type:String},places:{type:[Array,Object]}},render:function(t,e){var n=e.data,r=e.parent,i=e.props,a=e.slots,s=r.$i18n;if(s){var o=i.path,l=i.locale,c=i.places,u=a(),f=s.i(o,l,function(t){var e;for(e in t)if("default"!==e)return!1;return Boolean(e)}(u)||c?function(t,e){var n=e?function(t){return Array.isArray(t)?t.reduce(_,{}):Object.assign({},t)}(e):{};if(!t)return n;var r=t.every(v);return t.reduce(r?g:_,n)}(u.default,c):u),h=i.tag||"span";return h?t(h,n,f):f}}};function g(t,e){return e.data&&e.data.attrs&&e.data.attrs.place&&(t[e.data.attrs.place]=e),t}function _(t,e,n){return t[n]=e,t}function v(t){return Boolean(t.data&&t.data.attrs&&t.data.attrs.place)}var y,b={name:"i18n-n",functional:!0,props:{tag:{type:String,default:"span"},value:{type:Number,required:!0},format:{type:[String,Object]},locale:{type:String}},render:function(e,r){var i=r.props,a=r.parent,s=r.data,o=a.$i18n;if(!o)return null;var l=null,c=null;"string"==typeof i.format?l=i.format:n(i.format)&&(i.format.key&&(l=i.format.key),c=Object.keys(i.format).reduce(function(e,n){var r;return t.includes(n)?Object.assign({},e,((r={})[n]=i.format[n],r)):e},null));var u=i.locale||o.locale,f=o._ntp(i.value,u,l,c),h=f.map(function(t,e){var n,r=s.scopedSlots&&s.scopedSlots[t.type];return r?r(((n={})[t.type]=t.value,n.index=e,n.parts=f,n)):t.value});return e(i.tag,{attrs:s.attrs,class:s.class,staticClass:s.staticClass},h)}};function d(t,e,n){w(t,n)&&$(t,e,n)}function F(t,e,n,r){if(w(t,n)){var i=n.context.$i18n;(function(t,e){var n=e.context;return t._locale===n.$i18n.locale})(t,n)&&h(e.value,e.oldValue)&&h(t._localeMessage,i.getLocaleMessage(i.locale))||$(t,e,n)}}function k(t,n,r,i){if(r.context){var a=r.context.$i18n||{};n.modifiers.preserve||a.preserveDirectiveContent||(t.textContent=""),t._vt=void 0,delete t._vt,t._locale=void 0,delete t._locale,t._localeMessage=void 0,delete t._localeMessage}else e("Vue instance does not exists in VNode context")}function w(t,n){var r=n.context;return r?!!r.$i18n||(e("VueI18n instance does not exists in Vue instance"),!1):(e("Vue instance does not exists in VNode context"),!1)}function $(t,n,r){var i,s,o=function(t){var e,n,r,i;"string"==typeof t?e=t:a(t)&&(e=t.path,n=t.locale,r=t.args,i=t.choice);return{path:e,locale:n,args:r,choice:i}}(n.value),l=o.path,c=o.locale,u=o.args,f=o.choice;if(l||c||u)if(l){var h=r.context;t._vt=t.textContent=f?(i=h.$i18n).tc.apply(i,[l,f].concat(M(c,u))):(s=h.$i18n).t.apply(s,[l].concat(M(c,u))),t._locale=h.$i18n.locale,t._localeMessage=h.$i18n.getLocaleMessage(h.$i18n.locale)}else e("`path` is required in v-t directive");else e("value type not supported")}function M(t,e){var n=[];return t&&n.push(t),e&&(Array.isArray(e)||a(e))&&n.push(e),n}function D(t){D.installed=!0;(y=t).version&&Number(y.version.split(".")[0]);!function(t){t.prototype.hasOwnProperty("$i18n")||Object.defineProperty(t.prototype,"$i18n",{get:function(){return this._i18n}}),t.prototype.$t=function(t){for(var e=[],n=arguments.length-1;n-- >0;)e[n]=arguments[n+1];var r=this.$i18n;return r._t.apply(r,[t,r.locale,r._getMessages(),this].concat(e))},t.prototype.$tc=function(t,e){for(var n=[],r=arguments.length-2;r-- >0;)n[r]=arguments[r+2];var i=this.$i18n;return i._tc.apply(i,[t,i.locale,i._getMessages(),this,e].concat(n))},t.prototype.$te=function(t,e){var n=this.$i18n;return n._te(t,n.locale,n._getMessages(),e)},t.prototype.$d=function(t){for(var e,n=[],r=arguments.length-1;r-- >0;)n[r]=arguments[r+1];return(e=this.$i18n).d.apply(e,[t].concat(n))},t.prototype.$n=function(t){for(var e,n=[],r=arguments.length-1;r-- >0;)n[r]=arguments[r+1];return(e=this.$i18n).n.apply(e,[t].concat(n))}}(y),y.mixin(p),y.directive("t",{bind:d,update:F,unbind:k}),y.component(m.name,m),y.component(b.name,b),y.config.optionMergeStrategies.i18n=function(t,e){return void 0===e?t:e}}var T=function(){this._caches=Object.create(null)};T.prototype.interpolate=function(t,e){if(!e)return[t];var r=this._caches[t];return r||(r=function(t){var e=[],n=0,r="";for(;n<t.length;){var i=t[n++];if("{"===i){r&&e.push({type:"text",value:r}),r="";var a="";for(i=t[n++];void 0!==i&&"}"!==i;)a+=i,i=t[n++];var s="}"===i,o=L.test(a)?"list":s&&I.test(a)?"named":"unknown";e.push({value:a,type:o})}else"%"===i?"{"!==t[n]&&(r+=i):r+=i}return r&&e.push({type:"text",value:r}),e}(t),this._caches[t]=r),function(t,e){var r=[],i=0,a=Array.isArray(e)?"list":n(e)?"named":"unknown";if("unknown"===a)return r;for(;i<t.length;){var s=t[i];switch(s.type){case"text":r.push(s.value);break;case"list":r.push(e[parseInt(s.value,10)]);break;case"named":"named"===a&&r.push(e[s.value])}i++}return r}(r,e)};var L=/^(?:\d)+/,I=/^(?:\w)+/,x=0,W=1,O=2,C=3,j=0,N=4,S=5,A=6,H=7,R=8,V=[];V[j]={ws:[j],ident:[3,x],"[":[N],eof:[H]},V[1]={ws:[1],".":[2],"[":[N],eof:[H]},V[2]={ws:[2],ident:[3,x],0:[3,x],number:[3,x]},V[3]={ident:[3,x],0:[3,x],number:[3,x],ws:[1,W],".":[2,W],"[":[N,W],eof:[H,W]},V[N]={"'":[S,x],'"':[A,x],"[":[N,O],"]":[1,C],eof:R,else:[N,x]},V[S]={"'":[N,x],eof:R,else:[S,x]},V[A]={'"':[N,x],eof:R,else:[A,x]};var E=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function P(t){if(null==t)return"eof";switch(t.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return t;case 95:case 36:case 45:return"ident";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"ws"}return"ident"}function z(t){var e,n,r,i=t.trim();return("0"!==t.charAt(0)||!isNaN(t))&&(r=i,E.test(r)?(n=(e=i).charCodeAt(0))!==e.charCodeAt(e.length-1)||34!==n&&39!==n?e:e.slice(1,-1):"*"+i)}var J=function(){this._cache=Object.create(null)};J.prototype.parsePath=function(t){var e=this._cache[t];return e||(e=function(t){var e,n,r,i,a,s,o,l=[],c=-1,u=j,f=0,h=[];function p(){var e=t[c+1];if(u===S&&"'"===e||u===A&&'"'===e)return c++,r="\\"+e,h[x](),!0}for(h[W]=function(){void 0!==n&&(l.push(n),n=void 0)},h[x]=function(){void 0===n?n=r:n+=r},h[O]=function(){h[x](),f++},h[C]=function(){if(f>0)f--,u=N,h[x]();else{if(f=0,!1===(n=z(n)))return!1;h[W]()}};null!==u;)if("\\"!==(e=t[++c])||!p()){if(i=P(e),(a=(o=V[u])[i]||o.else||R)===R)return;if(u=a[0],(s=h[a[1]])&&(r=void 0===(r=a[2])?e:r,!1===s()))return;if(u===H)return l}}(t))&&(this._cache[t]=e),e||[]},J.prototype.getPathValue=function(t,e){if(!n(t))return null;var r=this.parsePath(e);if(0===r.length)return null;for(var i=r.length,a=t,s=0;s<i;){var o=a[r[s]];if(void 0===o)return null;a=o,s++}return a};var U,q=/<\/?[\w\s="\/.':;#-\/]+>/,B=/(?:@(?:\.[a-z]+)?:(?:[\w\-_|.]+|\([\w\-_|.]+\)))/g,G=/^@(?:\.([a-z]+))?:/,X=/[()]/g,Z={upper:function(t){return t.toLocaleUpperCase()},lower:function(t){return t.toLocaleLowerCase()}},K=new T,Q=function(t){var e=this;void 0===t&&(t={}),!y&&"undefined"!=typeof window&&window.Vue&&D(window.Vue);var n=t.locale||"en-US",r=t.fallbackLocale||"en-US",i=t.messages||{},a=t.dateTimeFormats||{},o=t.numberFormats||{};this._vm=null,this._formatter=t.formatter||K,this._missing=t.missing||null,this._root=t.root||null,this._sync=void 0===t.sync||!!t.sync,this._fallbackRoot=void 0===t.fallbackRoot||!!t.fallbackRoot,this._formatFallbackMessages=void 0!==t.formatFallbackMessages&&!!t.formatFallbackMessages,this._silentTranslationWarn=void 0!==t.silentTranslationWarn&&t.silentTranslationWarn,this._silentFallbackWarn=void 0!==t.silentFallbackWarn&&!!t.silentFallbackWarn,this._dateTimeFormatters={},this._numberFormatters={},this._path=new J,this._dataListeners=[],this._preserveDirectiveContent=void 0!==t.preserveDirectiveContent&&!!t.preserveDirectiveContent,this.pluralizationRules=t.pluralizationRules||{},this._warnHtmlInMessage=t.warnHtmlInMessage||"off",this._exist=function(t,n){return!(!t||!n)&&(!s(e._path.getPathValue(t,n))||!!t[n])},"warn"!==this._warnHtmlInMessage&&"error"!==this._warnHtmlInMessage||Object.keys(i).forEach(function(t){e._checkLocaleMessage(t,e._warnHtmlInMessage,i[t])}),this._initVM({locale:n,fallbackLocale:r,messages:i,dateTimeFormats:a,numberFormats:o})},Y={vm:{configurable:!0},messages:{configurable:!0},dateTimeFormats:{configurable:!0},numberFormats:{configurable:!0},availableLocales:{configurable:!0},locale:{configurable:!0},fallbackLocale:{configurable:!0},formatFallbackMessages:{configurable:!0},missing:{configurable:!0},formatter:{configurable:!0},silentTranslationWarn:{configurable:!0},silentFallbackWarn:{configurable:!0},preserveDirectiveContent:{configurable:!0},warnHtmlInMessage:{configurable:!0}};return Q.prototype._checkLocaleMessage=function(t,n,r){var i=function(t,n,r,s){if(a(r))Object.keys(r).forEach(function(e){var o=r[e];a(o)?(s.push(e),s.push("."),i(t,n,o,s),s.pop(),s.pop()):(s.push(e),i(t,n,o,s),s.pop())});else if(Array.isArray(r))r.forEach(function(e,r){a(e)?(s.push("["+r+"]"),s.push("."),i(t,n,e,s),s.pop(),s.pop()):(s.push("["+r+"]"),i(t,n,e,s),s.pop())});else if("string"==typeof r){if(q.test(r)){var o="Detected HTML in message '"+r+"' of keypath '"+s.join("")+"' at '"+n+"'. Consider component interpolation with '<i18n>' to avoid XSS. See https://bit.ly/2ZqJzkp";"warn"===t?e(o):"error"===t&&function(t,e){"undefined"!=typeof console&&(console.error("[vue-i18n] "+t),e&&console.error(e.stack))}(o)}}};i(n,t,r,[])},Q.prototype._initVM=function(t){var e=y.config.silent;y.config.silent=!0,this._vm=new y({data:t}),y.config.silent=e},Q.prototype.destroyVM=function(){this._vm.$destroy()},Q.prototype.subscribeDataChanging=function(t){this._dataListeners.push(t)},Q.prototype.unsubscribeDataChanging=function(t){!function(t,e){if(t.length){var n=t.indexOf(e);if(n>-1)t.splice(n,1)}}(this._dataListeners,t)},Q.prototype.watchI18nData=function(){var t=this;return this._vm.$watch("$data",function(){for(var e=t._dataListeners.length;e--;)y.nextTick(function(){t._dataListeners[e]&&t._dataListeners[e].$forceUpdate()})},{deep:!0})},Q.prototype.watchLocale=function(){if(!this._sync||!this._root)return null;var t=this._vm;return this._root.$i18n.vm.$watch("locale",function(e){t.$set(t,"locale",e),t.$forceUpdate()},{immediate:!0})},Y.vm.get=function(){return this._vm},Y.messages.get=function(){return l(this._getMessages())},Y.dateTimeFormats.get=function(){return l(this._getDateTimeFormats())},Y.numberFormats.get=function(){return l(this._getNumberFormats())},Y.availableLocales.get=function(){return Object.keys(this.messages).sort()},Y.locale.get=function(){return this._vm.locale},Y.locale.set=function(t){this._vm.$set(this._vm,"locale",t)},Y.fallbackLocale.get=function(){return this._vm.fallbackLocale},Y.fallbackLocale.set=function(t){this._vm.$set(this._vm,"fallbackLocale",t)},Y.formatFallbackMessages.get=function(){return this._formatFallbackMessages},Y.formatFallbackMessages.set=function(t){this._formatFallbackMessages=t},Y.missing.get=function(){return this._missing},Y.missing.set=function(t){this._missing=t},Y.formatter.get=function(){return this._formatter},Y.formatter.set=function(t){this._formatter=t},Y.silentTranslationWarn.get=function(){return this._silentTranslationWarn},Y.silentTranslationWarn.set=function(t){this._silentTranslationWarn=t},Y.silentFallbackWarn.get=function(){return this._silentFallbackWarn},Y.silentFallbackWarn.set=function(t){this._silentFallbackWarn=t},Y.preserveDirectiveContent.get=function(){return this._preserveDirectiveContent},Y.preserveDirectiveContent.set=function(t){this._preserveDirectiveContent=t},Y.warnHtmlInMessage.get=function(){return this._warnHtmlInMessage},Y.warnHtmlInMessage.set=function(t){var e=this,n=this._warnHtmlInMessage;if(this._warnHtmlInMessage=t,n!==t&&("warn"===t||"error"===t)){var r=this._getMessages();Object.keys(r).forEach(function(t){e._checkLocaleMessage(t,e._warnHtmlInMessage,r[t])})}},Q.prototype._getMessages=function(){return this._vm.messages},Q.prototype._getDateTimeFormats=function(){return this._vm.dateTimeFormats},Q.prototype._getNumberFormats=function(){return this._vm.numberFormats},Q.prototype._warnDefault=function(t,e,n,r,i){if(!s(n))return n;if(this._missing){var a=this._missing.apply(null,[t,e,r,i]);if("string"==typeof a)return a}if(this._formatFallbackMessages){var l=o.apply(void 0,i);return this._render(e,"string",l.params,e)}return e},Q.prototype._isFallbackRoot=function(t){return!t&&!s(this._root)&&this._fallbackRoot},Q.prototype._isSilentFallbackWarn=function(t){return this._silentFallbackWarn instanceof RegExp?this._silentFallbackWarn.test(t):this._silentFallbackWarn},Q.prototype._isSilentFallback=function(t,e){return this._isSilentFallbackWarn(e)&&(this._isFallbackRoot()||t!==this.fallbackLocale)},Q.prototype._isSilentTranslationWarn=function(t){return this._silentTranslationWarn instanceof RegExp?this._silentTranslationWarn.test(t):this._silentTranslationWarn},Q.prototype._interpolate=function(t,e,n,r,i,o,l){if(!e)return null;var c,u=this._path.getPathValue(e,n);if(Array.isArray(u)||a(u))return u;if(s(u)){if(!a(e))return null;if("string"!=typeof(c=e[n]))return null}else{if("string"!=typeof u)return null;c=u}return(c.indexOf("@:")>=0||c.indexOf("@.")>=0)&&(c=this._link(t,e,c,r,"raw",o,l)),this._render(c,i,o,n)},Q.prototype._link=function(t,e,n,r,i,a,s){var o=n,l=o.match(B);for(var c in l)if(l.hasOwnProperty(c)){var u=l[c],f=u.match(G),h=f[0],p=f[1],m=u.replace(h,"").replace(X,"");if(s.includes(m))return o;s.push(m);var g=this._interpolate(t,e,m,r,"raw"===i?"string":i,"raw"===i?void 0:a,s);if(this._isFallbackRoot(g)){if(!this._root)throw Error("unexpected error");var _=this._root.$i18n;g=_._translate(_._getMessages(),_.locale,_.fallbackLocale,m,r,i,a)}g=this._warnDefault(t,m,g,r,Array.isArray(a)?a:[a]),Z.hasOwnProperty(p)&&(g=Z[p](g)),s.pop(),o=g?o.replace(u,g):o}return o},Q.prototype._render=function(t,e,n,r){var i=this._formatter.interpolate(t,n,r);return i||(i=K.interpolate(t,n,r)),"string"===e?i.join(""):i},Q.prototype._translate=function(t,e,n,r,i,a,o){var l=this._interpolate(e,t[e],r,i,a,o,[r]);return s(l)&&s(l=this._interpolate(n,t[n],r,i,a,o,[r]))?null:l},Q.prototype._t=function(t,e,n,r){for(var i,a=[],s=arguments.length-4;s-- >0;)a[s]=arguments[s+4];if(!t)return"";var l=o.apply(void 0,a),c=l.locale||e,u=this._translate(n,c,this.fallbackLocale,t,r,"string",l.params);if(this._isFallbackRoot(u)){if(!this._root)throw Error("unexpected error");return(i=this._root).$t.apply(i,[t].concat(a))}return this._warnDefault(c,t,u,r,a)},Q.prototype.t=function(t){for(var e,n=[],r=arguments.length-1;r-- >0;)n[r]=arguments[r+1];return(e=this)._t.apply(e,[t,this.locale,this._getMessages(),null].concat(n))},Q.prototype._i=function(t,e,n,r,i){var a=this._translate(n,e,this.fallbackLocale,t,r,"raw",i);if(this._isFallbackRoot(a)){if(!this._root)throw Error("unexpected error");return this._root.$i18n.i(t,e,i)}return this._warnDefault(e,t,a,r,[i])},Q.prototype.i=function(t,e,n){return t?("string"!=typeof e&&(e=this.locale),this._i(t,e,this._getMessages(),null,n)):""},Q.prototype._tc=function(t,e,n,r,i){for(var a,s=[],l=arguments.length-5;l-- >0;)s[l]=arguments[l+5];if(!t)return"";void 0===i&&(i=1);var c={count:i,n:i},u=o.apply(void 0,s);return u.params=Object.assign(c,u.params),s=null===u.locale?[u.params]:[u.locale,u.params],this.fetchChoice((a=this)._t.apply(a,[t,e,n,r].concat(s)),i)},Q.prototype.fetchChoice=function(t,e){if(!t&&"string"!=typeof t)return null;var n=t.split("|");return n[e=this.getChoiceIndex(e,n.length)]?n[e].trim():t},Q.prototype.getChoiceIndex=function(t,e){var n,r;return this.locale in this.pluralizationRules?this.pluralizationRules[this.locale].apply(this,[t,e]):(n=t,r=e,n=Math.abs(n),2===r?n?n>1?1:0:1:n?Math.min(n,2):0)},Q.prototype.tc=function(t,e){for(var n,r=[],i=arguments.length-2;i-- >0;)r[i]=arguments[i+2];return(n=this)._tc.apply(n,[t,this.locale,this._getMessages(),null,e].concat(r))},Q.prototype._te=function(t,e,n){for(var r=[],i=arguments.length-3;i-- >0;)r[i]=arguments[i+3];var a=o.apply(void 0,r).locale||e;return this._exist(n[a],t)},Q.prototype.te=function(t,e){return this._te(t,this.locale,this._getMessages(),e)},Q.prototype.getLocaleMessage=function(t){return l(this._vm.messages[t]||{})},Q.prototype.setLocaleMessage=function(t,e){("warn"!==this._warnHtmlInMessage&&"error"!==this._warnHtmlInMessage||(this._checkLocaleMessage(t,this._warnHtmlInMessage,e),"error"!==this._warnHtmlInMessage))&&this._vm.$set(this._vm.messages,t,e)},Q.prototype.mergeLocaleMessage=function(t,e){("warn"!==this._warnHtmlInMessage&&"error"!==this._warnHtmlInMessage||(this._checkLocaleMessage(t,this._warnHtmlInMessage,e),"error"!==this._warnHtmlInMessage))&&this._vm.$set(this._vm.messages,t,f(this._vm.messages[t]||{},e))},Q.prototype.getDateTimeFormat=function(t){return l(this._vm.dateTimeFormats[t]||{})},Q.prototype.setDateTimeFormat=function(t,e){this._vm.$set(this._vm.dateTimeFormats,t,e)},Q.prototype.mergeDateTimeFormat=function(t,e){this._vm.$set(this._vm.dateTimeFormats,t,f(this._vm.dateTimeFormats[t]||{},e))},Q.prototype._localizeDateTime=function(t,e,n,r,i){var a=e,o=r[a];if((s(o)||s(o[i]))&&(o=r[a=n]),s(o)||s(o[i]))return null;var l=o[i],c=a+"__"+i,u=this._dateTimeFormatters[c];return u||(u=this._dateTimeFormatters[c]=new Intl.DateTimeFormat(a,l)),u.format(t)},Q.prototype._d=function(t,e,n){if(!n)return new Intl.DateTimeFormat(e).format(t);var r=this._localizeDateTime(t,e,this.fallbackLocale,this._getDateTimeFormats(),n);if(this._isFallbackRoot(r)){if(!this._root)throw Error("unexpected error");return this._root.$i18n.d(t,n,e)}return r||""},Q.prototype.d=function(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];var i=this.locale,a=null;return 1===e.length?"string"==typeof e[0]?a=e[0]:n(e[0])&&(e[0].locale&&(i=e[0].locale),e[0].key&&(a=e[0].key)):2===e.length&&("string"==typeof e[0]&&(a=e[0]),"string"==typeof e[1]&&(i=e[1])),this._d(t,i,a)},Q.prototype.getNumberFormat=function(t){return l(this._vm.numberFormats[t]||{})},Q.prototype.setNumberFormat=function(t,e){this._vm.$set(this._vm.numberFormats,t,e)},Q.prototype.mergeNumberFormat=function(t,e){this._vm.$set(this._vm.numberFormats,t,f(this._vm.numberFormats[t]||{},e))},Q.prototype._getNumberFormatter=function(t,e,n,r,i,a){var o=e,l=r[o];if((s(l)||s(l[i]))&&(l=r[o=n]),s(l)||s(l[i]))return null;var c,u=l[i];if(a)c=new Intl.NumberFormat(o,Object.assign({},u,a));else{var f=o+"__"+i;(c=this._numberFormatters[f])||(c=this._numberFormatters[f]=new Intl.NumberFormat(o,u))}return c},Q.prototype._n=function(t,e,n,r){if(!Q.availabilities.numberFormat)return"";if(!n)return(r?new Intl.NumberFormat(e,r):new Intl.NumberFormat(e)).format(t);var i=this._getNumberFormatter(t,e,this.fallbackLocale,this._getNumberFormats(),n,r),a=i&&i.format(t);if(this._isFallbackRoot(a)){if(!this._root)throw Error("unexpected error");return this._root.$i18n.n(t,Object.assign({},{key:n,locale:e},r))}return a||""},Q.prototype.n=function(e){for(var r=[],i=arguments.length-1;i-- >0;)r[i]=arguments[i+1];var a=this.locale,s=null,o=null;return 1===r.length?"string"==typeof r[0]?s=r[0]:n(r[0])&&(r[0].locale&&(a=r[0].locale),r[0].key&&(s=r[0].key),o=Object.keys(r[0]).reduce(function(e,n){var i;return t.includes(n)?Object.assign({},e,((i={})[n]=r[0][n],i)):e},null)):2===r.length&&("string"==typeof r[0]&&(s=r[0]),"string"==typeof r[1]&&(a=r[1])),this._n(e,a,s,o)},Q.prototype._ntp=function(t,e,n,r){if(!Q.availabilities.numberFormat)return[];if(!n)return(r?new Intl.NumberFormat(e,r):new Intl.NumberFormat(e)).formatToParts(t);var i=this._getNumberFormatter(t,e,this.fallbackLocale,this._getNumberFormats(),n,r),a=i&&i.formatToParts(t);if(this._isFallbackRoot(a)){if(!this._root)throw Error("unexpected error");return this._root.$i18n._ntp(t,e,n,r)}return a||[]},Object.defineProperties(Q.prototype,Y),Object.defineProperty(Q,"availabilities",{get:function(){if(!U){var t="undefined"!=typeof Intl;U={dateTimeFormat:t&&void 0!==Intl.DateTimeFormat,numberFormat:t&&void 0!==Intl.NumberFormat}}return U}}),Q.install=D,Q.version="8.14.0",Q},"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.VueI18n=e();