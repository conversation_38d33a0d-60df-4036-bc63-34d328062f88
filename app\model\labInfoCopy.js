/**
 *
 * created by JHw
 * 实验室标准物质与试剂表
 * @param app
 */
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');
  const labInfoCopy = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },

    transportContition: String, // 运输条件
    supplier: String, // 供货商
    unitPrice: String, // 单价

    type: String, // 1代表标准液 2代表质控样 3试剂、标准品
    serviceOrgId: String, // 机构id 2022-09-21添加 之前的数据需要跑库添加
    enable: { // 是否可用
      type: Boolean,
      default: true,
    },

    // -----------试剂管理字段 start------------
    character: String, // 性状
    reagentPurity: String, // 试剂纯度
    storageSite: String, // 存储场所
    storageCondition: String, // 存储条件
    stockCapacity: String, // 库存容量
    specifications: String, // 规格
    // totalCapacity: String, // 总容量 默认 规格*采购数量
    isLongTermEffective: { // 是否长期有效
      type: Boolean,
      default: false,
    },
    // used_skuCount: {
    //   type: Number,
    //   default: 0,
    // }, // 使用数量
    isAvoidLight: { // 是否避光
      type: Boolean,
      default: false,
    },
    issueRecords: [// 出库记录
      {
        _id: {
          type: String,
          default: shortid.generate,
        },
        data: Date, // 日期
        issueEmployee: String, // 领用人 employeeID
        issueCount: String, // 领用数量
        comment: String, // 备注
        cancelledEmployee: String, // 撤销人 employeeID
        cancellationDate: Date, // 撤销时间
        status: { // 1出库 2 撤销
          type: Number,
          default: 1,
        },
        // 试剂会在储备液中出库，也会在质控样中出库
        stockSolutionId: String, // 储备液id
        qualityControlSampleId: String, // 质控样id
        // used_id: String, // 被使用到哪 关联的表 stockSolution(储备液)|qualityControlSample（质控样）
      },
    ],
    // -----------试剂管理字段 end------------

    StandardProduct: String, // 标准物质名称
    batch: String, // 批号
    standardValue: String, // 标准值
    standardValue_unit: String, // 标准值单位
    expirationDate: Date, // 有效日期
    mark: String, // 标示
    buyCount: String, // 期初数量(采购数量)
    buyCount_unit: String, // 采购数量单位
    indeterminacy: String, // 不确定度*
    indeterminacy_unit: {
      type: String,
      default: '%',
    }, // 不确定度单位
    manufacturer: String, // 制造单位
    certificateNo: String, // 证书编号*
    decideDate: Date, // 定值日期*
    shoppingDate: Date, // 购进日期(采购日期)

    /** *************************************************************************** */
    /** 用于库存管理与编号字段*/

    stock: Array, // 剩余库存
    newMark: String, // 标示 eg:A-1-2
    used_sku: [{ // 2处做用：1.领用记录 2.已使用的子标识
      _id: {
        type: String,
        default: shortid.generate,
      },
      receiptRecordId: String,
    }],

    // remaining_qty: String, // 剩余数量 表格导入的数据 eg:四只支
    singleSku: Array, // 该批次所有子标识
    comment: String, // 备注

    // 废弃字段：
    // oldName: String, // 标准物质名称 (表格刚导入为了规范 可废弃)
    // Remaining_Qty: Array, // 剩余数量 为剩余数量编号 eg: ["A-1-2-2"] 好像可以和库存(stock)共用 帮我看看是否可以废弃 推荐用(stock)
    // QualityControlSample: String

    //   ________验收__________

    isPackage: {
      type: String,
      default: '是',
    }, // 包装是否完好
    transportationCondition: {
      type: String,
      default: '常温',
    }, // 运输条件
    // storageCondition: {
    //   type: String,
    //   default: '常温',
    // }, // 保存条件
    isClear: {
      type: String,
      default: '是',
    }, // 标识是否清晰
    isCorrespond: {
      type: String,
      default: '是',
    }, // 证书是否对应
    inValidPeriod: {
      type: String,
      default: '是',
    }, // 是否在有效期限内
    isMeetDetectRequire: {
      type: String,
      default: '是',
    }, // 是否符合检测需求
    acceptanceRes: {
      type: String,
      enum: [ '合格', '不合格' ],
      default: '合格',
    }, // 验收结果
    acceptanceDate: {
      type: Date,
      default: Date.now,
    }, // 验收时间
    fileName: String, // 验收表

    //   _______核查__________

    hasCertification: {
      type: String,
      default: '是',
    }, // 是否有证书
    standStatus: {
      type: String,
      default: '固体',
    }, // 标准物质状态
    isPackageUnbroken: {
      type: String,
      default: '完整',
    }, // 包装完整程度
    isColorNormal: {
      type: String,
      default: '是',
    }, // 颜色是否正常
    isMarkClear: {
      type: String,
      default: '是',
    }, // 标识内容是否清晰
    isValidity: {
      type: String,
      default: '是',
    }, // 是否在有效期内
    whatStorageCondition: {
      type: String,
      default: '室温',
    }, // 储存条件
    checkRes: {
      type: String,
      default: '合格',
    }, // 期间核查结果
    checkDate: {
      type: Date,
      default: Date.now,
    }, // 期间核查日期
    checkFileName: String, // 核查表


    createAt: { // 创建日期
      type: Date,
    },
    updateAt: { // 更新时间
      type: Date,
    },

  }, { timestamps: true });
  return mongoose.model('labInfoCopys', labInfoCopy, 'labInfoCopy');
};

