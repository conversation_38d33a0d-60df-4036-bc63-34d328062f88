
// 小游戏的企业列表
module.exports = app => {
  const mongoose = app.mongoose;
  const Schema = mongoose.Schema;
  const shortid = require('shortid');

  const GameEnterpriseSchema = new Schema({
    _id: {
      type: String,
      default: shortid.generate,
    },
    cname: { // 企业名称
      type: String,
      required: true,
      trim: true,
    },
    area: [{ // 企业所在区域:省-市-区
      type: String, // 区域名称
    }],
    gameEventId: { // 活动id
      type: String,
      required: true,
      ref: 'GameEvents',
      index: true,
    },
    totalScores: { // 总积分
      type: Number,
      default: 0, // 默认值为 0Score
    },
    enable: { // 是否可用, 逻辑删除
      type: Boolean,
      default: true,
    },
  }, { timestamps: true });

  // gameEventId和cname组合在一起时是唯一的
  GameEnterpriseSchema.index({ gameEventId: 1, cname: 1 }, { unique: true });

  return mongoose.model('GameEnterprise', GameEnterpriseSchema, 'gameEnterprise');
};
