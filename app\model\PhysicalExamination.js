module.exports = app => {
  // 体检项目
  const mongoose = app.mongoose;
  const conn = mongoose.createConnection(app.config.mongoose.tools.url, app.config.mongoose.tools.options);
  // const conn = app.mongooseDB.get('tools'); // 需要在配置文件中使用mongoose.clients.tools: {} 配置，现配置中间缺少一层clients
  const Schema = mongoose.Schema;
  const physicalExaminationSchema = new Schema({
    keyword: String, // 有害因素
    sort: String, // 类别 1粉尘  2化学因素  3生物因素  4特殊作业  5物理因素
    status: [ // 检查类型,在岗，上岗前，离岗
      {
        statusCode: String, // 检查类型
        examItem: String, // 必检项目
        workDiseases: [ String ], // 职业病
        cycle: [ String ], // 检查周期
        forbid: [ String ], // 职业禁忌证
      },
    ],
  });
  return conn.model('PhysicalExaminations', physicalExaminationSchema, 'physicalExaminations');
};
